// React hooks for ZiExplorer functionality

import { useState, useEffect, useCallback, useRef } from 'react'
import { 
  FileItem, 
  ZiExplorerState, 
  FileChange, 
  ClipboardItem, 
  ImportResult,
  ContextMenuAction,
  ZiExplorerConfig
} from '@/lib/ziexplorer/types'
import { ZiExplorerManager } from '@/lib/ziexplorer/ziexplorer-manager'

// Main ZiExplorer hook
export function useZiExplorer(config?: Partial<ZiExplorerConfig>) {
  const [manager] = useState(() => new ZiExplorerManager(config))
  const [isInitialized, setIsInitialized] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const initializeManager = async () => {
      try {
        await manager.initialize()
        setIsInitialized(true)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to initialize ZiExplorer')
      }
    }

    initializeManager()
  }, [manager])

  return {
    manager,
    isInitialized,
    error,
    capabilities: manager.capabilities,
    platformInfo: manager.platformInfo
  }
}

// File system operations hook
export function useFileSystem(path: string) {
  const [files, setFiles] = useState<FileItem[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { manager, isInitialized } = useZiExplorer()

  const loadFiles = useCallback(async () => {
    if (!isInitialized || !path) return

    setIsLoading(true)
    setError(null)

    try {
      const items = await manager.listDirectory(path)
      setFiles(items)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load files')
      setFiles([])
    } finally {
      setIsLoading(false)
    }
  }, [manager, isInitialized, path])

  // Watch for file changes
  useEffect(() => {
    if (!isInitialized || !path) return

    const handleFileChanges = (changes: FileChange[]) => {
      setFiles(currentFiles => {
        let updatedFiles = [...currentFiles]

        for (const change of changes) {
          switch (change.type) {
            case 'added':
              if (change.item && !updatedFiles.find(f => f.path === change.path)) {
                updatedFiles.push(change.item)
              }
              break
            case 'deleted':
              updatedFiles = updatedFiles.filter(f => f.path !== change.path)
              break
            case 'modified':
              if (change.item) {
                const index = updatedFiles.findIndex(f => f.path === change.path)
                if (index !== -1) {
                  updatedFiles[index] = change.item
                }
              }
              break
          }
        }

        return updatedFiles.sort((a, b) => {
          // Folders first, then files
          if (a.type !== b.type) {
            return a.type === 'folder' ? -1 : 1
          }
          return a.name.localeCompare(b.name)
        })
      })
    }

    manager.watchFolder(path, handleFileChanges)
    loadFiles()

    return () => {
      manager.unwatchFolder(path)
    }
  }, [manager, isInitialized, path, loadFiles])

  return {
    files,
    isLoading,
    error,
    reload: loadFiles
  }
}

// File operations hook
export function useFileOperations() {
  const { manager, isInitialized } = useZiExplorer()
  const [isOperating, setIsOperating] = useState(false)

  const createFolder = useCallback(async (path: string, name: string): Promise<boolean> => {
    if (!isInitialized) return false

    setIsOperating(true)
    try {
      return await manager.createFolder(path, name)
    } catch (error) {
      console.error('Failed to create folder:', error)
      return false
    } finally {
      setIsOperating(false)
    }
  }, [manager, isInitialized])

  const importFiles = useCallback(async (files: FileList, targetPath: string): Promise<ImportResult[]> => {
    if (!isInitialized) return []

    setIsOperating(true)
    try {
      return await manager.importFiles(files, targetPath)
    } catch (error) {
      console.error('Failed to import files:', error)
      return []
    } finally {
      setIsOperating(false)
    }
  }, [manager, isInitialized])

  const copyFiles = useCallback(async (sourcePaths: string[], targetPath: string): Promise<boolean> => {
    if (!isInitialized) return false

    setIsOperating(true)
    try {
      return await manager.copyFiles(sourcePaths, targetPath)
    } catch (error) {
      console.error('Failed to copy files:', error)
      return false
    } finally {
      setIsOperating(false)
    }
  }, [manager, isInitialized])

  const moveFiles = useCallback(async (sourcePaths: string[], targetPath: string): Promise<boolean> => {
    if (!isInitialized) return false

    setIsOperating(true)
    try {
      return await manager.moveFiles(sourcePaths, targetPath)
    } catch (error) {
      console.error('Failed to move files:', error)
      return false
    } finally {
      setIsOperating(false)
    }
  }, [manager, isInitialized])

  const deleteFiles = useCallback(async (paths: string[]): Promise<boolean> => {
    if (!isInitialized) return false

    setIsOperating(true)
    try {
      return await manager.deleteFiles(paths)
    } catch (error) {
      console.error('Failed to delete files:', error)
      return false
    } finally {
      setIsOperating(false)
    }
  }, [manager, isInitialized])

  const renameFile = useCallback(async (oldPath: string, newName: string): Promise<boolean> => {
    if (!isInitialized) return false

    setIsOperating(true)
    try {
      return await manager.renameFile(oldPath, newName)
    } catch (error) {
      console.error('Failed to rename file:', error)
      return false
    } finally {
      setIsOperating(false)
    }
  }, [manager, isInitialized])

  const shareFiles = useCallback(async (paths: string[]): Promise<boolean> => {
    if (!isInitialized) return false

    setIsOperating(true)
    try {
      return await manager.shareFiles(paths)
    } catch (error) {
      console.error('Failed to share files:', error)
      return false
    } finally {
      setIsOperating(false)
    }
  }, [manager, isInitialized])

  return {
    createFolder,
    importFiles,
    copyFiles,
    moveFiles,
    deleteFiles,
    renameFile,
    shareFiles,
    isOperating
  }
}

// Clipboard operations hook
export function useClipboard() {
  const { manager, isInitialized } = useZiExplorer()
  const [clipboard, setClipboard] = useState<ClipboardItem | null>(null)

  // Update clipboard state when manager clipboard changes
  useEffect(() => {
    if (!isInitialized) return

    const updateClipboard = () => {
      setClipboard(manager.getClipboard())
    }

    // Check clipboard periodically (since we don't have events)
    const interval = setInterval(updateClipboard, 1000)
    updateClipboard() // Initial check

    return () => clearInterval(interval)
  }, [manager, isInitialized])

  const copy = useCallback((paths: string[]) => {
    if (!isInitialized) return
    manager.copyToClipboard(paths)
    setClipboard(manager.getClipboard())
  }, [manager, isInitialized])

  const cut = useCallback((paths: string[]) => {
    if (!isInitialized) return
    manager.cutToClipboard(paths)
    setClipboard(manager.getClipboard())
  }, [manager, isInitialized])

  const paste = useCallback(async (targetPath: string): Promise<boolean> => {
    if (!isInitialized) return false
    
    const success = await manager.pasteFromClipboard(targetPath)
    setClipboard(manager.getClipboard())
    return success
  }, [manager, isInitialized])

  const clear = useCallback(() => {
    if (!isInitialized) return
    manager.clearClipboard()
    setClipboard(null)
  }, [manager, isInitialized])

  return {
    clipboard,
    copy,
    cut,
    paste,
    clear,
    hasClipboard: clipboard !== null
  }
}

// Context menu hook
export function useContextMenu() {
  const [contextMenu, setContextMenu] = useState<{
    x: number
    y: number
    item: FileItem
  } | null>(null)

  const { copy, cut, paste, hasClipboard } = useClipboard()
  const { deleteFiles, renameFile, shareFiles } = useFileOperations()

  const showContextMenu = useCallback((event: React.MouseEvent, item: FileItem) => {
    event.preventDefault()
    setContextMenu({
      x: event.clientX,
      y: event.clientY,
      item
    })
  }, [])

  const hideContextMenu = useCallback(() => {
    setContextMenu(null)
  }, [])

  const getContextMenuActions = useCallback((item: FileItem): ContextMenuAction[] => {
    const actions: ContextMenuAction[] = [
      {
        id: 'open',
        label: 'Open',
        icon: '📂',
        action: () => {
          // Handle open action
          console.log('Opening:', item.path)
          hideContextMenu()
        }
      },
      {
        id: 'copy',
        label: 'Copy',
        icon: '📋',
        action: () => {
          copy([item.path])
          hideContextMenu()
        },
        shortcut: 'Ctrl+C'
      },
      {
        id: 'cut',
        label: 'Cut',
        icon: '✂️',
        action: () => {
          cut([item.path])
          hideContextMenu()
        },
        shortcut: 'Ctrl+X'
      },
      {
        id: 'paste',
        label: 'Paste',
        icon: '📄',
        action: async () => {
          if (item.type === 'folder') {
            await paste(item.path)
          }
          hideContextMenu()
        },
        disabled: !hasClipboard || item.type !== 'folder',
        shortcut: 'Ctrl+V'
      },
      { separator: true } as ContextMenuAction,
      {
        id: 'delete',
        label: 'Delete',
        icon: '🗑️',
        action: async () => {
          if (confirm(`Are you sure you want to delete "${item.name}"?`)) {
            await deleteFiles([item.path])
          }
          hideContextMenu()
        },
        shortcut: 'Delete'
      },
      {
        id: 'rename',
        label: 'Rename',
        icon: '✏️',
        action: () => {
          const newName = prompt('Enter new name:', item.name)
          if (newName && newName !== item.name) {
            renameFile(item.path, newName)
          }
          hideContextMenu()
        },
        shortcut: 'F2'
      },
      { separator: true } as ContextMenuAction,
      {
        id: 'share',
        label: 'Share',
        icon: '📤',
        action: async () => {
          await shareFiles([item.path])
          hideContextMenu()
        }
      },
      {
        id: 'properties',
        label: 'Properties',
        icon: 'ℹ️',
        action: () => {
          // Handle properties action
          console.log('Properties for:', item.path)
          hideContextMenu()
        }
      }
    ]

    return actions
  }, [copy, cut, paste, hasClipboard, deleteFiles, renameFile, shareFiles, hideContextMenu])

  return {
    contextMenu,
    showContextMenu,
    hideContextMenu,
    getContextMenuActions
  }
}

// File selection hook
export function useFileSelection() {
  const [selectedFiles, setSelectedFiles] = useState<string[]>([])

  const selectFile = useCallback((path: string, multiSelect = false) => {
    setSelectedFiles(current => {
      if (multiSelect) {
        return current.includes(path)
          ? current.filter(p => p !== path)
          : [...current, path]
      } else {
        return [path]
      }
    })
  }, [])

  const selectAll = useCallback((files: FileItem[]) => {
    setSelectedFiles(files.map(f => f.path))
  }, [])

  const clearSelection = useCallback(() => {
    setSelectedFiles([])
  }, [])

  const isSelected = useCallback((path: string) => {
    return selectedFiles.includes(path)
  }, [selectedFiles])

  return {
    selectedFiles,
    selectFile,
    selectAll,
    clearSelection,
    isSelected,
    hasSelection: selectedFiles.length > 0,
    selectionCount: selectedFiles.length
  }
}
