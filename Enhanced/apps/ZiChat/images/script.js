const container = document.querySelector(".container");
const chatsContainer = document.querySelector(".chats-container");
const promptForm = document.querySelector(".prompt-form");
const promptInput = promptForm.querySelector(".prompt-input");
const fileInput = promptForm.querySelector("#file-input");
const fileUploadWrapper = promptForm.querySelector(".file-upload-wrapper");
const themeToggleBtn = document.querySelector("#theme-toggle-btn");
const closeBtn = document.querySelector("#close-btn");

// Add event listener to close button
closeBtn.addEventListener("click", () => {
  container.classList.toggle("hidden");
});
// API Setup
const API_KEY = "AIzaSyApxjQ4oKieKXwjoogbn7tSQ10VzUyUO68";
const API_URL = `https://generativelanguage.googleapis.com/v1/models/gemini-1.5-flash:generateContent?key=${API_KEY}`;

let controller, typingInterval;
const chatHistory = [];
const userData = { message: "", file: {} };
// Set initial theme from local storage
const isLightTheme = localStorage.getItem("themeColor") === "light_mode";
document.body.classList.toggle("light-theme", isLightTheme);
themeToggleBtn.textContent = isLightTheme ? "dark_mode" : "light_mode";
// Function to create message elements
const createMessageElement = (content, ...classes) => {
  const div = document.createElement("div");
  div.classList.add("message", ...classes);
  div.innerHTML = content;
  return div;
};
// Scroll to the bottom of the container
const scrollToBottom = () => container.scrollTo({ top: container.scrollHeight, behavior: "smooth" });
// Simulate typing effect for bot responses
const typingEffect = (text, textElement, botMsgDiv) => {
  textElement.textContent = "";
  const words = text.split(" ");
  let wordIndex = 0;
  // Set an interval to type each word
  typingInterval = setInterval(() => {
    if (wordIndex < words.length) {
      textElement.textContent += (wordIndex === 0 ? "" : " ") + words[wordIndex++];
      scrollToBottom();
    } else {
      clearInterval(typingInterval);
      botMsgDiv.classList.remove("loading");
      document.body.classList.remove("bot-responding");
    }
  }, 40); // 40 ms delay
};
// Make the API call and generate the bot's response
const generateResponse = async (botMsgDiv) => {
  const textElement = botMsgDiv.querySelector(".message-text");
  controller = new AbortController();
  // Add user message and file data to the chat history
  chatHistory.push({
    role: "user",
    parts: [{ text: userData.message }, ...(userData.file.data ? [{ inline_data: (({ fileName, isImage, ...rest }) => rest)(userData.file) }] : [])],
  });
  try {
    // Send the chat history to the API to get a response
    const response = await fetch(API_URL, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ contents: chatHistory }),
      signal: controller.signal,
    });
    const data = await response.json();
    if (!response.ok) throw new Error(data.error.message);
    // Process the response text and display with typing effect
    const responseText = data.candidates[0].content.parts[0].text.replace(/\*\*([^*]+)\*\*/g, "$1").trim();
    typingEffect(responseText, textElement, botMsgDiv);
    chatHistory.push({ role: "model", parts: [{ text: responseText }] });
  } catch (error) {
    textElement.textContent = error.name === "AbortError" ? "Response generation stopped." : error.message;
    textElement.style.color = "#d62939";
    botMsgDiv.classList.remove("loading");
    document.body.classList.remove("bot-responding");
    scrollToBottom();
  } finally {
    userData.file = {};
  }
};
// Handle the form submission
const handleFormSubmit = (e) => {
  e.preventDefault();
  const userMessage = promptInput.value.trim();
  if (!userMessage || document.body.classList.contains("bot-responding")) return;
  userData.message = userMessage;
  promptInput.value = "";
  document.body.classList.add("chats-active", "bot-responding");
  fileUploadWrapper.classList.remove("file-attached", "img-attached", "active");
  // Generate user message HTML with optional file attachment
  const userMsgHTML = `
    <p class="message-text"></p>
    ${userData.file.data ? (userData.file.isImage ? `<img src="data:${userData.file.mime_type};base64,${userData.file.data}" class="img-attachment" />` : `<p class="file-attachment"><span class="material-symbols-rounded">description</span>${userData.file.fileName}</p>`) : ""}
  `;
  const userMsgDiv = createMessageElement(userMsgHTML, "user-message");
  userMsgDiv.querySelector(".message-text").textContent = userData.message;
  chatsContainer.appendChild(userMsgDiv);
  scrollToBottom();
  setTimeout(() => {
    // Generate bot message HTML and add in the chat container
    const botMsgHTML = `<img class="avatar" src="gemini.svg" /> <p class="message-text">Just a sec...</p>`;
    const botMsgDiv = createMessageElement(botMsgHTML, "bot-message", "loading");
    chatsContainer.appendChild(botMsgDiv);
    scrollToBottom();
    generateResponse(botMsgDiv);
  }, 600); // 600 ms delay
};
// Handle file input change (file upload)
fileInput.addEventListener("change", () => {
  const file = fileInput.files[0];
  if (!file) return;
  const isImage = file.type.startsWith("image/");
  const reader = new FileReader();
  reader.readAsDataURL(file);
  reader.onload = (e) => {
    fileInput.value = "";
    const base64String = e.target.result.split(",")[1];
    fileUploadWrapper.querySelector(".file-preview").src = e.target.result;
    fileUploadWrapper.classList.add("active", isImage ? "img-attached" : "file-attached");
    // Store file data in userData obj
    userData.file = { fileName: file.name, data: base64String, mime_type: file.type, isImage };
  };
});
// Cancel file upload
document.querySelector("#cancel-file-btn").addEventListener("click", () => {
  userData.file = {};
  fileUploadWrapper.classList.remove("file-attached", "img-attached", "active");
});
// Stop Bot Response
document.querySelector("#stop-response-btn").addEventListener("click", () => {
  controller?.abort();
  userData.file = {};
  clearInterval(typingInterval);
  chatsContainer.querySelector(".bot-message.loading").classList.remove("loading");
  document.body.classList.remove("bot-responding");
});
// Toggle dark/light theme
themeToggleBtn.addEventListener("click", () => {
  const isLightTheme = document.body.classList.toggle("light-theme");
  localStorage.setItem("themeColor", isLightTheme ? "light_mode" : "dark_mode");
  themeToggleBtn.textContent = isLightTheme ? "dark_mode" : "light_mode";
});
// Delete all chats
document.querySelector("#delete-chats-btn").addEventListener("click", () => {
  chatHistory.length = 0;
  chatsContainer.innerHTML = "";
  document.body.classList.remove("chats-active", "bot-responding");
});
// Handle suggestions click
document.querySelectorAll(".suggestions-item").forEach((suggestion) => {
  suggestion.addEventListener("click", () => {
    promptInput.value = suggestion.querySelector(".text").textContent;
    promptForm.dispatchEvent(new Event("submit"));
  });
});
// Show/hide controls for mobile on prompt input focus
document.addEventListener("click", ({ target }) => {
  const wrapper = document.querySelector(".prompt-wrapper");
  const shouldHide = target.classList.contains("prompt-input") || (wrapper.classList.contains("hide-controls") && (target.id === "add-file-btn" || target.id === "stop-response-btn"));
  wrapper.classList.toggle("hide-controls", shouldHide);
});
// Add event listeners for form submission and file input click
promptForm.addEventListener("submit", handleFormSubmit);
promptForm.querySelector("#add-file-btn").addEventListener("click", () => fileInput.click());
// Toggle sidebar
const toggleSidebar = () => {
  const sidebar = document.querySelector('.sidebar');
  const mainContent = document.querySelector('.main-content');
  
  sidebar.classList.toggle('collapsed');
  mainContent.classList.toggle('expanded');
  
  // Save sidebar state
  localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
};
// Chat management functions
const saveChatsToLocalStorage = (chats) => {
  localStorage.setItem('chatHistory', JSON.stringify(chats));
};

const loadChatsFromLocalStorage = () => {
  const saved = localStorage.getItem('chatHistory');
  return saved ? JSON.parse(saved) : [];
};

// Create new chat
const createNewChat = () => {
  const chatList = document.querySelector('.chat-list');
  const timestamp = new Date();
  const newChat = {
    id: 'chat_' + Date.now(),
    title: 'New Chat',
    timestamp: timestamp.toISOString(),
    messages: []
  };

  const chatItem = document.createElement('div');
  chatItem.className = 'chat-item';
  chatItem.dataset.chatId = newChat.id;
  chatItem.innerHTML = `
    <div class="chat-item-content">
      <div class="chat-item-title">${newChat.title}</div>
      <div class="chat-item-timestamp">${timestamp.toLocaleString()}</div>
    </div>
    <div class="chat-item-actions">
      <button class="material-symbols-rounded" onclick="renameChat(this)">edit</button>
      <button class="material-symbols-rounded" onclick="deleteChat(this)">delete</button>
    </div>
  `;
  
  chatList.prepend(chatItem);

  // Save to localStorage
  const chats = loadChatsFromLocalStorage();
  chats.unshift(newChat);
  saveChatsToLocalStorage(chats);

  return newChat;
};

// Rename chat
const renameChat = (button) => {
  const chatItem = button.closest('.chat-item');
  const chatTitle = chatItem.querySelector('.chat-item-title');
  const newTitle = prompt('Enter new chat name:', chatTitle.textContent);
  
  if (newTitle && newTitle.trim()) {
    chatTitle.textContent = newTitle.trim();
    
    // Update in localStorage
    const chats = loadChatsFromLocalStorage();
    const chatIndex = chats.findIndex(chat => chat.id === chatItem.dataset.chatId);
    if (chatIndex !== -1) {
      chats[chatIndex].title = newTitle.trim();
      saveChatsToLocalStorage(chats);
    }
  }
};

// Delete chat
const deleteChat = (button) => {
  if (confirm('Are you sure you want to delete this chat?')) {
    const chatItem = button.closest('.chat-item');
    
    // Remove from localStorage
    const chats = loadChatsFromLocalStorage();
    const filteredChats = chats.filter(chat => chat.id !== chatItem.dataset.chatId);
    saveChatsToLocalStorage(filteredChats);
    
    // Remove from DOM
    chatItem.remove();
  }
};

// Initialize sidebar and load saved chats
document.addEventListener('DOMContentLoaded', () => {
  const sidebar = document.querySelector('.sidebar');
  const mainContent = document.querySelector('.main-content');
  const toggleBtn = document.getElementById('toggle-sidebar-btn');
  const newChatBtn = document.getElementById('new-chat-btn');
  
  // Set initial state based on screen size
  if (window.innerWidth <= 768) {
    sidebar.classList.add('collapsed');
    mainContent.classList.add('expanded');
  }

  // Load saved chats
  const savedChats = loadChatsFromLocalStorage();
  const chatList = document.querySelector('.chat-list');
  
  savedChats.forEach(chat => {
    const chatItem = document.createElement('div');
    chatItem.className = 'chat-item';
    chatItem.dataset.chatId = chat.id;
    chatItem.innerHTML = `
      <div class="chat-item-content">
        <div class="chat-item-title">${chat.title}</div>
        <div class="chat-item-timestamp">${new Date(chat.timestamp).toLocaleString()}</div>
      </div>
      <div class="chat-item-actions">
        <button class="material-symbols-rounded" onclick="renameChat(this)">edit</button>
        <button class="material-symbols-rounded" onclick="deleteChat(this)">delete</button>
      </div>
    `;
    chatList.appendChild(chatItem);
  });

  // Add event listeners
  toggleBtn.addEventListener('click', toggleSidebar);
  newChatBtn.addEventListener('click', createNewChat);
  
  // Close sidebar when clicking outside on mobile
  document.addEventListener('click', (e) => {
    if (window.innerWidth <= 768) {
      if (!sidebar.contains(e.target) && 
          !toggleBtn.contains(e.target) && 
          !sidebar.classList.contains('collapsed')) {
        toggleSidebar();
      }
    }
  });
});

// Add window resize handler
window.addEventListener('resize', () => {
  const sidebar = document.querySelector('.sidebar');
  const mainContent = document.querySelector('.main-content');
  
  if (window.innerWidth <= 768 && !sidebar.classList.contains('collapsed')) {
    sidebar.classList.add('collapsed');
    mainContent.classList.add('expanded');
  }
});
const logo = document.getElementById("movable-logo");

// Move with keyboard arrow keys
document.addEventListener("keydown", (event) => {
    let currentTop = parseInt(window.getComputedStyle(logo).top) || 0;
    
    if (event.key === "ArrowUp") {
        logo.style.top = `${currentTop - 10}px`;  // Move up
    } else if (event.key === "ArrowDown") {
        logo.style.top = `${currentTop + 10}px`;  // Move down
    }
});

// Dragging functionality
let isDragging = false, startY, startTop;

logo.addEventListener("mousedown", (event) => {
    isDragging = true;
    startY = event.clientY;
    startTop = parseInt(window.getComputedStyle(logo).top) || 0;
    document.body.style.userSelect = "none"; // Prevent text selection while dragging
});

document.addEventListener("mousemove", (event) => {
    if (!isDragging) return;
    const moveAmount = event.clientY - startY;
    logo.style.top = `${startTop + moveAmount}px`;
});

document.addEventListener("mouseup", () => {
    isDragging = false;
    document.body.style.userSelect = "auto"; // Restore text selection
});

document.addEventListener("DOMContentLoaded", function () {
  const chatContainer = document.getElementById("chat-container");
  const promptInput = document.getElementById("prompt-input");
  const sendButton = document.getElementById("send-button");
  const apiKey = "YOUR_GEMINI_API_KEY";

  sendButton.addEventListener("click", sendMessage);
  promptInput.addEventListener("keypress", function (event) {
    if (event.key === "Enter") {
      event.preventDefault();
      sendMessage();
    }
  });

  function sendMessage() {
    const userMessage = promptInput.value.trim();
    if (!userMessage) return;

    displayMessage(userMessage, "user");
    promptInput.value = "";
    
    const botMsgHTML = `<img class="avatar" src="zi.png" /> <p class="message-text">Zi is thinking...</p>`;
    const botMessageElement = document.createElement("div");
    botMessageElement.classList.add("message", "bot");
    botMessageElement.innerHTML = botMsgHTML;
    chatContainer.appendChild(botMessageElement);
    chatContainer.scrollTop = chatContainer.scrollHeight;

    fetchGeminiResponse(userMessage, botMessageElement);
  }

  function displayMessage(text, sender) {
    const messageElement = document.createElement("div");
    messageElement.classList.add("message", sender);
    messageElement.innerHTML = `<p class="message-text">${text}</p>`;
    chatContainer.appendChild(messageElement);
    chatContainer.scrollTop = chatContainer.scrollHeight;
  }

  function fetchGeminiResponse(userMessage, botMessageElement) {
    const requestBody = {
      contents: [{ role: "user", parts: [{ text: userMessage.replace(/gemini/gi, "Zi") }] }],
    };

    fetch("https://generativelanguage.googleapis.com/v1/models/gemini-pro:generateContent?key=" + apiKey, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(requestBody),
    })
      .then((response) => response.json())
      .then((data) => {
        let responseText = data.candidates[0]?.content?.parts[0]?.text || "I'm sorry, I didn't understand that.";
        responseText = responseText.replace(/Gemini/g, "Zi");
        botMessageElement.innerHTML = `<img class="avatar" src="zi.svg" /> <p class="message-text">${responseText}</p>`;
        chatContainer.scrollTop = chatContainer.scrollHeight;
      })
      .catch((error) => {
        console.error("Error fetching response:", error);
        botMessageElement.innerHTML = `<img class="avatar" src="zi.svg" /> <p class="message-text">Oops! Something went wrong.</p>`;
        chatContainer.scrollTop = chatContainer.scrollHeight;
      });
  }
});
function addChatMessage(text, isBot = true) {
  const chatContainer = document.querySelector(".chats-container");

  const messageDiv = document.createElement("div");
  messageDiv.classList.add("message");

  if (isBot) {
      messageDiv.innerHTML = `
          <div class="avatar">
              <img src="images/Zi.png" alt="Zi Avatar">
          </div>
          <div class="message-content">
              <p>${text}</p>
          </div>
      `;
  } else {
      messageDiv.innerHTML = `
          <div class="message-content">
              <p>${text}</p>
          </div>
      `;
  }

  chatContainer.appendChild(messageDiv);
  chatContainer.scrollTop = chatContainer.scrollHeight; // Auto-scroll to the latest message
}
// Get all the suggestions items
const suggestionsItems = document.querySelectorAll('.suggestions-item');

// Set the dragging functionality
suggestionsItems.forEach(item => {
  let isDragging = false;
  let startX = 0;
  let initialLeft = 0;

  // When the mouse is down, start dragging
  item.addEventListener('mousedown', (e) => {
    isDragging = true;
    startX = e.clientX; // Get the starting X position of the mouse
    initialLeft = item.offsetLeft; // Get the initial position of the item
    item.style.cursor = 'grabbing'; // Change the cursor to indicate dragging
  });

  // When the mouse moves, update the position of the item
  item.addEventListener('mousemove', (e) => {
    if (isDragging) {
      const diffX = e.clientX - startX; // Calculate the difference in X position
      item.style.left = `${initialLeft + diffX}px`; // Set the new left position
    }
  });

  // When the mouse is up, stop dragging
  item.addEventListener('mouseup', () => {
    isDragging = false;
    item.style.cursor = 'grab'; // Reset the cursor
  });

  // When the mouse leaves, also stop dragging
  item.addEventListener('mouseleave', () => {
    isDragging = false;
    item.style.cursor = 'grab'; // Reset the cursor
  });
});
const speedFactor = 0.5; // Adjust the speed of the drag
item.style.left = `${initialLeft + (diffX * speedFactor)}px`;
const containerWidth = suggestionsItems[0].parentElement.offsetWidth; // Get container width
const itemWidth = item.offsetWidth;

const leftLimit = 0; // Left boundary
const rightLimit = containerWidth - itemWidth; // Right boundary

// Ensure the item stays within the bounds
item.style.left = Math.max(leftLimit, Math.min(initialLeft + diffX, rightLimit)) + 'px';


