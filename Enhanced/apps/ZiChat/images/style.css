/* Import Google Font - Poppins */
@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap");
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Poppins", sans-serif;
}
:root {
  /* Dark theme colors */
  --text-color: #edf3ff;
  --subheading-color: #97a7ca;
  --placeholder-color: #c3cdde;
  --primary-color: #101623;
  --secondary-color: #283045;
  --secondary-hover-color: #333e58;
  --scrollbar-color: #626a7f;
  --secondary-color-rgb: 40, 48, 69;  /* RGB value for secondary color */
  --scrollbar-color-rgb: 98, 106, 127;  /* RGB value for scrollbar color */
}
body.light-theme {
  /* Light theme colors */
  --text-color: #090c13;
  --subheading-color: #7b8cae;
  --placeholder-color: #606982;
  --primary-color: #f3f7ff;
  --secondary-color: #dce6f9;
  --secondary-hover-color: #d2ddf2;
  --scrollbar-color: #a2aac2;
  --secondary-color-rgb: 220, 230, 249;
  --scrollbar-color-rgb: 162, 170, 194;
}
body {
  color: var(--text-color);
  background: var(--primary-color);
}
.container {
  overflow-y: auto;
  padding: 32px 0 60px;
  height: calc(100vh - 127px);
  scrollbar-color: var(--scrollbar-color) transparent;
  display: flex;
  position: relative;
}
.container :where(.app-header, .suggestions, .message, .prompt-wrapper) {
  position: relative;
  margin: 0 auto;
  width: 100%;
  padding: 0 20px;
  max-width: 990px;
}
.container .app-header {
  margin-top: 3vh;
  position: relative;
  display: flex;
  flex-direction: column;
  padding-left: 70px !important;
  padding-top: 60px !important;
}
.app-header .heading {
  width: fit-content;
  font-size: 3rem;
  background: linear-gradient(to right, #1d7efd, #8f6fff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-left: 0;
}
.app-header .sub-heading {
  font-size: 2.6rem;
  margin-top: -5px;
  color: var(--subheading-color);
}

#close-btn {
  position: absolute;  /* Ensures it positions relative to the nearest positioned ancestor */
  top: 10px;  /* Adjust to your preference */
  right: 10px; /* Pushes the button to the right corner */
  z-index: 1000; /* Ensures it stays above other elements */
  cursor: pointer;
   background: none;
  border: none;
  font-size: 2rem;
  cursor: pointer;
  color: var(--text-color);
}
.scroll-buttons {
  position: sticky;
  top: 10px;
  display: flex;
  gap: 20px;
  z-index: 10;
  justify-content: center;
  width: 100%;
  padding: 10px;
}

.scroll-btn {
  width: 40px;
  height: 40px;
  margin-top: 10px;
  background: rgba(255, 255, 255, 0.2); /* Light transparent white */
  backdrop-filter: blur(10px); /* Glassmorphism blur effect */
  border: 2px solid rgba(255, 255, 255, 0.3); /* Semi-transparent border */
  border-radius: 50%;
  color: var(--accent);
  font-size: 18px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15); /* Soft shadow for depth */
}

.scroll-btn:hover {
  background: rgba(255, 255, 255, 0.3); /* Slightly more opaque on hover */
  color: var(--bg-primary);
  transform: translateY(-2px);
}

.container .suggestions {
  width: 100%;
  list-style: none;
  display: flex;
  gap: 15px;
  margin-top: 9.5vh;
  overflow-x: auto;
  scroll-snap-type: x mandatory;
  scrollbar-width: none;
  
  position: relative;
  left: var(--position, 0); /* Default position, can be adjusted */
}

/* For Desktop (default) */
@media (min-width: 768px) {
  .container .suggestions {
    --position: -165px; /* Move to the right on desktop */
  }
}

/* For Mobile (adjust the position or set it to 0 to prevent breaking) */
@media (max-width: 767px) {
  .container .suggestions {
    --position: 0; /* Reset position on mobile for better alignment */
    /* Alternatively, you could set a smaller value, like -50px, for a slight shift */
  }
}


.container .suggestions {
  width: 100%;
  list-style: none;
  display: flex;
  gap: 15px;
  margin-top: 9.5vh;
  overflow-x: auto;
  scroll-snap-type: x mandatory;
  scrollbar-width: none;
  
  /* Add the position parameter */
  position: relative; /* Make the element's position relative so we can move it */
  left: var(--position, 0); /* Default position is 0, adjust via CSS variable */
}

body.chats-active .container :where(.app-header, .suggestions) {
  display: none;
}
.suggestions .suggestions-item {
  cursor: pointer;
  padding: 18px;
  width: 228px;
  flex-shrink: 0;
  display: flex;
  scroll-snap-align: center;
  flex-direction: column;
  align-items: flex-end;
  border-radius: 12px;
  justify-content: space-between;
  background: var(--secondary-color);
  transition: 0.3s ease;
}
.suggestions .suggestions-item:hover {
  background: var(--secondary-hover-color);
}
.suggestions .suggestions-item .text {
  font-size: 1.1rem;
}
.suggestions .suggestions-item .icon {
  width: 45px;
  height: 45px;
  display: flex;
  font-size: 1.4rem;
  margin-top: 35px;
  align-self: flex-end;
  align-items: center;
  border-radius: 50%;
  justify-content: center;
  color: #1d7efd;
  background: var(--primary-color);
}
.suggestions .suggestions-item:nth-child(2) .icon {
  color: #28a745;
}
.suggestions .suggestions-item:nth-child(3) .icon {
  color: #ffc107;
}
.suggestions .suggestions-item:nth-child(4) .icon {
  color: #6f42c1;
}
.container .chats-container {
  display: flex;
  gap: 20px;
  flex-direction: column;
}
.chats-container .message {
  display: flex;
  gap: 11px;
  align-items: center;
}
.chats-container .message .avatar {
  width: 70px; /* Default size */
  height: 70px;
  flex-shrink: 0;
  align-self: flex-start;
  border-radius: 50%;
  padding: 1px;
  margin-right: -7px;
  background: var(--secondary-color);
  border: 1px solid var(--secondary-hover-color);
  object-fit: cover;
  content: url("images/Zi.png"); /* Set the chatbot avatar to the logo */
}

/* Loading animation */
.chats-container .message.loading .avatar {
  animation: rotate 3s linear infinite;
}

/* Keyframes for the rotating effect */
@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}

/* Make the avatar smaller on mobile screens */
@media screen and (max-width: 768px) {
  .chats-container .message .avatar {
    width: 50px;
    height: 50px;
  }
}

@media screen and (max-width: 480px) {
  .chats-container .message .avatar {
    width: 40px;
    height: 40px;
  }
}

.chats-container .message .message-text {
  padding: 3px 16px;
  word-wrap: break-word;
  white-space: pre-line;
}
.chats-container .bot-message {
  margin: 9px auto;
}
.chats-container .user-message {
  flex-direction: column;
  align-items: flex-end;
}
.chats-container .user-message .message-text {
  padding: 12px 16px;
  max-width: 75%;
  background: var(--secondary-color);
  border-radius: 13px 13px 3px 13px;
}
.chats-container .user-message .img-attachment {
  margin-top: -7px;
  width: 50%;
  border-radius: 13px 3px 13px 13px;
}
.chats-container .user-message .file-attachment {
  display: flex;
  gap: 6px;
  align-items: center;
  padding: 10px;
  margin-top: -7px;
  border-radius: 13px 3px 13px 13px;
  background: var(--secondary-color);
}
.chats-container .user-message .file-attachment span {
  color: #1d7efd;
}
.container .prompt-container {
  position: fixed;
  width: 100%;
  left: 0;
  bottom: 0;
  padding: 16px 0;
  background: var(--primary-color);
}
.prompt-container :where(.prompt-wrapper, .prompt-form, .prompt-actions) {
  display: flex;
  gap: 12px;
  height: 56px;
  align-items: center;
}
.prompt-container .prompt-form {
  height: 100%;
  width: 100%;
  border-radius: 130px;
  background: var(--secondary-color);
}
.prompt-form .prompt-input {
  width: 100%;
  height: 100%;
  background: none;
  outline: none;
  border: none;
  font-size: 1rem;
  color: var(--text-color);
  padding-left: 24px;
}
.prompt-form .prompt-input::placeholder {
  color: var(--placeholder-color);
}
.prompt-wrapper button {
  width: 56px;
  height: 100%;
  flex-shrink: 0;
  cursor: pointer;
  border-radius: 50%;
  font-size: 1.4rem;
  border: none;
  color: var(--text-color);
  background: var(--secondary-color);
  transition: 0.3s ease;
}
.prompt-wrapper :is(button:hover, #cancel-file-btn, .file-icon) {
  background: var(--secondary-hover-color);
}
.prompt-form .prompt-actions {
  gap: 5px;
  margin-right: 7px;
}
.prompt-wrapper .prompt-form :where(.file-upload-wrapper, button, img) {
  position: relative;
  height: 45px;
  width: 45px;
}
.prompt-form .prompt-actions #send-prompt-btn {
  color: #fff;
  display: none;
  background: #1d7efd;
}
.prompt-form .prompt-input:valid~.prompt-actions #send-prompt-btn {
  display: block;
}
.prompt-form #send-prompt-btn:hover {
  background: #0264e3;
}
.prompt-form .file-upload-wrapper :where(button, img) {
  display: none;
  border-radius: 50%;
  object-fit: cover;
  position: absolute;
}
.prompt-form .file-upload-wrapper.active #add-file-btn {
  display: none;
}
.prompt-form .file-upload-wrapper #add-file-btn,
.prompt-form .file-upload-wrapper.active.img-attached img,
.prompt-form .file-upload-wrapper.active.file-attached .file-icon,
.prompt-form .file-upload-wrapper.active:hover #cancel-file-btn {
  display: block;
}
.prompt-form :is(#stop-response-btn:hover, #cancel-file-btn) {
  color: #d62939;
}
.prompt-wrapper .prompt-form .file-icon {
  color: #1d7efd;
}
.prompt-form #stop-response-btn,
body.bot-responding .prompt-form .file-upload-wrapper {
  display: none;
}
body.bot-responding .prompt-form #stop-response-btn {
  display: block;
}
.prompt-container .disclaimer-text {
  font-size: 0.9rem;
  text-align: center;
  padding: 16px 20px 0;
  color: var(--placeholder-color);
}
/* Responsive media query code for small screens */
@media (max-width: 768px) {
  .container {
    padding: 20px 0 100px;
  }
  .app-header :is(.heading, .sub-heading) {
    font-size: 2rem;
    line-height: 1.4;
  }
  .app-header .sub-heading {
    font-size: 1.7rem;
  }
  .container .chats-container {
    gap: 15px;
  }
  .chats-container .bot-message {
    margin: 4px auto;
  }
  .prompt-container :where(.prompt-wrapper, .prompt-form, .prompt-actions) {
    gap: 8px;
    height: 53px;
  }
  .prompt-container button {
    width: 53px;
  }
  .prompt-form :is(.file-upload-wrapper, button, img) {
    height: 42px;
    width: 42px;
  }
  .prompt-form .prompt-input {
    padding-left: 20px;
  }
  .prompt-form .file-upload-wrapper.active #cancel-file-btn {
    opacity: 0;
  }
  .prompt-wrapper.hide-controls :where(#theme-toggle-btn, #delete-chats-btn) {
    display: none;
  }
  #toggle-sidebar-btn {
    top: 5px;
    width: 35px;
    height: 35px;
  }
  .app-header {
    padding-top: 50px !important;
  }
}

/* Sidebar base styles */
.sidebar {
  width: 280px;
  height: 100vh;
  background: rgba(var(--secondary-color-rgb), 0.85);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border-right: 1px solid rgba(var(--scrollbar-color-rgb), 0.2);
  transition: transform 0.3s ease;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1000;
}

/* Collapsed state */
.sidebar.collapsed {
  transform: translateX(-280px);
}

.sidebar-header {
  padding: 20px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(var(--scrollbar-color-rgb), 0.2);
}

.sidebar-header h3 {
  color: var(--text-color);
  font-size: 1.2rem;
}

.chat-list {
  padding: 12px;
  overflow-y: auto;
  height: calc(100vh - 60px);
}

.chat-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border-radius: 8px;
  cursor: pointer;
  margin-bottom: 0;
  transition: all 0.2s ease;
  border-bottom: 1px solid rgba(var(--scrollbar-color-rgb), 0.1);
  background: transparent;
}

.chat-item:hover {
  background: rgba(var(--secondary-color-rgb), 0.15);
}

.chat-item-content {
  flex: 1;
  min-width: 0; /* Prevent text overflow */
}

.chat-item-title {
  color: var(--text-color);
  font-size: 0.95rem;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.chat-item-timestamp {
  color: var(--subheading-color);
  font-size: 0.75rem;
  opacity: 0.8;
}

.chat-item-actions {
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.chat-item:hover .chat-item-actions {
  opacity: 1;
}

.chat-item-actions button {
  padding: 4px;
  border-radius: 4px;
  background: transparent;
  color: var(--text-color);
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.chat-item-actions button:hover {
  background: rgba(var(--secondary-color-rgb), 0.3);
}




/* Position the toggle button independently */
#toggle-sidebar-btn {
  position: absolute;
  left: 290px; /* Sidebar width (280px) + 10px */
  top: 20px;
  background: rgba(var(--secondary-color-rgb), 0.9);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid rgba(var(--scrollbar-color-rgb), 0.3);
  border-radius: 50%;
  width: 38px;
  height: 38px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-color);
  z-index: 1000;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Button position when sidebar is collapsed */
.sidebar.collapsed ~ .main-content #toggle-sidebar-btn {
  left: 10px;
}

.sidebar.collapsed {
  transform: translateX(-280px); /* Hide sidebar when collapsed */
}

/* Main content styles */
.main-content {
  margin-left: 280px; /* Sidebar width */
  padding-left: 60px;
  transition: margin-left 0.3s ease;
  z-index: 1; /* Make sure it stays above sidebar */
}

/* Header styles */
.app-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
}

/* Main content styles */
.main-content {
  flex: 1;
  margin-left: 280px;
  padding-left: 60px;
  transition: all 0.3s ease;
}


/* Adjust existing container styles */
.container {
  max-width: none;
  margin: 0;
  padding: 0;
}

/* Update suggestions list for mobile */
@media (max-width: 768px) {
  .container .suggestions {
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
    margin-top: 5vh;
    padding: 0 10px;
  }

  .suggestions .suggestions-item {
    width: calc(50% - 10px);
    min-width: 140px;
    padding: 15px;
  }

  .suggestions .suggestions-item .text {
    font-size: 0.9rem;
  }

  .suggestions .suggestions-item .icon {
    width: 35px;
    height: 35px;
    font-size: 1.2rem;
    margin-top: 20px;
  }

  .chat-list {
    padding: 12px;
  }

  .chat-item {
    padding: 10px;
  }

  .container :where(.app-header, .suggestions, .message, .prompt-wrapper) {
    padding-left: 50px;
    padding-right: 15px;
  }

  /* Adjust main content padding for mobile */
  .main-content {
    margin-left: 0;
    padding-left: 20px;
  }

  /* Hide sidebar by default on mobile */
  @media (max-width: 768px) {
    .sidebar {
      transform: translateX(-280px);
    }
    
    .main-content {
      margin-left: 0;
    }
  }

  /* Adjust container padding */
  .container {
    padding: 10px 0 100px;
  }

  /* Adjust app header padding for mobile */
  .app-header {
    padding-top: 50px !important;
  }

  #toggle-sidebar-btn {
    left: 20px; /* Always 10px from left on mobile */
    top: 15px;
    width: 35px;
    height: 35px;
  }

  /* Ensure proper spacing for content */
  .app-header {
    padding-left: 55px !important; /* Button width (35px) + 20px */
  }
}

/* Add hover effects for chat items */
.chat-item:hover {
  background: rgba(var(--secondary-color-rgb), 0.15);
}

/* Improve chat item actions visibility */
.chat-item-actions {
  opacity: 0.7;
}

.chat-item:hover .chat-item-actions {
  opacity: 1;
}

/* Add subtle transitions */
.chat-item,
.chat-item-actions,
#toggle-sidebar-btn {
  transition: all 0.2s ease;
}

/* Mobile styles */
@media (max-width: 768px) {
  /* Default state for mobile - sidebar hidden */
  .sidebar {
    transform: translateX(-280px);
  }

  /* When sidebar is not collapsed on mobile */
  .sidebar:not(.collapsed) {
    transform: translateX(0);
  }

  /* Main content on mobile */
  .main-content {
    margin-left: 0;
    padding-left: 20px;
  }

  /* Ensure the toggle button is visible */
  #toggle-sidebar-btn {
    position: absolute;
    left: 290px;
    top: 5px;
    width: 35px;
    height: 35px;
    z-index: 1000;
  }

  /* Adjust header spacing */
  .app-header {
    padding-top: 50px !important;
    padding-left: 50px !important;
  }
}
/* Flex container to align text & image */
.content {
  display: flex;
  align-items: center;
  gap: 12px;
  opacity: 0;  /* Initially hidden for animation */
  transform: translateY(20px);
  animation: fadeInUp 1s ease-in-out forwards;
}



/* Floating animation with customizable up and down movement */
@keyframes float {
  0% {
      transform: translateY(0); /* Starting position */
  }
  50% {
      transform: translateY(var(--float-distance, -10px)); /* Move up by a customizable distance */
  }
  100% {
      transform: translateY(0); /* Return to original position */
  }
}

/* Style for logo */
.logo {
  width: 115px; /* Default size for desktops */
  height: auto;
  max-width: 100%;
  position: relative; /* Allows movement */
  cursor: grab;
  animation: float var(--float-duration, 1.1s) ease-in-out infinite; /* Floating effect */
  transition: width 0.3s ease-in-out; /* Smooth transition for resizing */
}

/* When dragging */
.logo:active {
  cursor: grabbing;
}

/* Hover effects */
.logo:hover {
  animation: float 0.8s ease-in-out infinite; /* Slightly faster floating */
}

/* Floating animation */
@keyframes float {
  0% {
      transform: translateY(0);
  }
  50% {
      transform: translateY(-10px);
  }
  100% {
      transform: translateY(0);
  }
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .logo {
      width: 90px; /* Reduce size for tablets */
  }
}

@media (max-width: 768px) {
  .logo {
      width: 70px; /* Adjust for smaller tablets */
  }
}

@media (max-width: 480px) {
  .logo {
      width: 50px; /* Keep it visible but compact for mobile */
  }
}
/* Default Centered Heading */
.heading {
  display: flex; /* Align text and logo in a row */
  align-items: center; /* Centers items vertically */
  justify-content: center; /* Centers heading by default */
  gap: 10px; /* Spacing between text and logo */
  white-space: nowrap; /* Prevents wrapping */
  overflow-x: auto; /* Allows scrolling if heading overflows */
  position: relative; /* Allows z-index to work */
  z-index: 10; /* Set z-index to ensure heading is above other elements */
}

/* Logo Resizing */
@media (max-width: 1024px) {
  .logo {
    width: 90px; /* Reduce size for tablets */
  }
}

@media (max-width: 768px) {
  .logo {
    width: 70px; /* Adjust for smaller tablets */
  }
}

@media (max-width: 480px) {
  .logo {
    width: 70px; /* Keep it visible but compact for mobile */
  }
}

/* Allow movement on smaller screens */
@media (max-width: 480px) {
  .heading {
    font-size: 1rem; /* Reduce text size slightly for mobile */
    justify-content: flex-start; /* Moves heading to the left */
    padding-left: 10px; /* Add space on the left */
    overflow-x: visible; /* Make sure content doesn't get clipped */
    z-index: 20; /* Bring it forward on smaller screens */
  }

  /* Alternative: If you want to move it to the right */
  .heading.right {
    justify-content: flex-end; /* Moves heading to the right */
    padding-right: 0px;
    overflow-x: visible; /* Ensure no clipping */
    z-index: 20; /* Bring it forward on smaller screens */
  }
}
