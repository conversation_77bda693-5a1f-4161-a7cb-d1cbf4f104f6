<!DOCTYPE html>
<!-- Coding By CodingNepal - youtube.com/@codingnepal -->
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Zi <PERSON> | BlackTech</title>
    <!-- Linking Google Fonts For Icons -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@32,400,0,0" />
    <link rel="stylesheet" href="style.css" />
  </head>
  <body>
    <div class="container">
      <!-- Sidebar/Toolbar -->
      <div class="sidebar">
        <div class="sidebar-header">
          <h3>Chat History</h3>
          <button id="new-chat-btn" class="material-symbols-rounded">add_circle</button>
        </div>
        <div class="chat-list">
          <!-- Chat history items will be added here dynamically -->
        </div>
      </div>

      <!-- Main Chat Area -->
      <div class="main-content">
        <!-- Menu Toggle Button -->
        <button id="toggle-sidebar-btn" class="material-symbols-rounded">menu</button>
        <!-- App Header -->
        <header class="app-header">
      
        <div class="scroll-buttons">
            <button class="scroll-btn" onclick="scrollToTop()">⬆</button>
            <button class="scroll-btn" onclick="scrollToBottom()">⬇</button>
        </div>
        <h1 class="heading right">Greetings, Scholar <img src="images/Zi.png" alt="Icon" class="logo"></h1>

          
          
          <h4 class="sub-heading">How can I help you today?</h4>
          <button id="close-btn" class="material-symbols-rounded">close</button>
        </header>
        <!-- Suggestions List -->
        <ul class="suggestions">
          <li class="suggestions-item">
            <p class="text">Tell me about the African Continent.</p>
            <span class="icon material-symbols-rounded">draw</span>
          </li>
          <li class="suggestions-item">
            <p class="text">How can I level up my my studies ...?</p>
            <span class="icon material-symbols-rounded">lightbulb</span>
          </li>
          <li class="suggestions-item">
            <p class="text">Suggest some useful tips on how to be succesful in life.</p>
            <span class="icon material-symbols-rounded">explore</span>
          </li>
          <li class="suggestions-item">
            <p class="text">Write a to-do list app with modern Ui.</p>
            <span class="icon material-symbols-rounded">code_blocks</span>
          </li>
        </ul>
        <!-- Chats -->
        <div class="chats-container"></div>
        <!-- Prompt Input -->
        <div class="prompt-container">
          <div class="prompt-wrapper">
            <form action="#" class="prompt-form">
              <input type="text" placeholder="Ask ZiChat" class="prompt-input" required />
              <div class="prompt-actions">
                <!-- File Upload Wrapper -->
                <div class="file-upload-wrapper">
                  <img src="#" class="file-preview" />
                  <input id="file-input" type="file" accept="image/*, .pdf, .txt, .csv" hidden />
                  <button type="button" class="file-icon material-symbols-rounded">description</button>
                  <button id="cancel-file-btn" type="button" class="material-symbols-rounded">close</button>
                  <button id="add-file-btn" type="button" class="material-symbols-rounded">attach_file</button>
                </div>
                <!-- Send Prompt and Stop Response Buttons -->
                <button id="stop-response-btn" type="button" class="material-symbols-rounded">stop_circle</button>
                <button id="send-prompt-btn" class="material-symbols-rounded">arrow_upward</button>
              </div>
            </form>
            <!-- Theme and Delete Chats Buttons -->
            <button id="theme-toggle-btn" class="material-symbols-rounded">light_mode</button>
            <button id="delete-chats-btn" class="material-symbols-rounded">delete</button>
          </div>
          <p class="disclaimer-text">ZiChat can make mistakes, it is always good to double-check.</p>
        </div>
      </div>
    </div>
    <script src="script.js"></script>
  </body>
</html>
