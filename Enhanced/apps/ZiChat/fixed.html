<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fixed Annotation App</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="fixed-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="app-container">
        <!-- Main Toolbar - Vertical -->
        <div class="main-toolbar vertical">
            <button class="toggle-toolbar">
                <i class="fas fa-chevron-left"></i>
            </button>
            
            <div class="tool-section bg-control-section">
                <h3>Background</h3>
                <div class="bg-controls">
                    <div class="bg-preview" title="Background Type">
                        <div class="bg-icon transparent active" data-bg-type="transparent">
                            <i class="fas fa-border-none"></i>
                        </div>
                        <div class="bg-icon solid" data-bg-type="solid">
                            <i class="fas fa-square"></i>
                        </div>
                    </div>
                    <input type="color" id="bgColorPicker" value="#ffffff" title="Background Color">
                </div>
            </div>
            
            <div class="tool-section">
                <h3>Draw</h3>
                <button class="tool-btn" data-tool="pen" title="Pen">
                    <i class="fas fa-pen"></i>
                </button>
                <button class="tool-btn" data-tool="highlighter" title="Highlighter">
                    <i class="fas fa-highlighter"></i>
                </button>
                <button class="tool-btn" data-tool="eraser" title="Eraser">
                    <i class="fas fa-eraser"></i>
                </button>
            </div>
            
            <div class="tool-section">
                <h3>Add</h3>
                <button class="tool-btn" data-tool="text" title="Text">
                    <i class="fas fa-font"></i>
                </button>
                <button class="tool-btn" data-tool="shape" title="Shapes">
                    <i class="fas fa-shapes"></i>
                </button>
                <button class="tool-btn" data-tool="sticker" title="Stickers">
                    <i class="fas fa-star"></i>
                </button>
                <button class="tool-btn" data-tool="emoji" title="Emojis">
                    <i class="fas fa-smile"></i>
                </button>
            </div>
            
            <div class="tool-section">
                <h3>View</h3>
                <button class="tool-btn" data-tool="grid" title="Toggle Grid">
                    <i class="fas fa-border-all"></i>
                </button>
            </div>
            
            <div class="tool-section">
                <h3>Edit</h3>
                <button class="tool-btn" data-tool="undo" title="Undo">
                    <i class="fas fa-undo"></i>
                </button>
                <button class="tool-btn" data-tool="redo" title="Redo">
                    <i class="fas fa-redo"></i>
                </button>
                <button class="tool-btn" data-tool="clear" title="Clear All">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>

        <!-- Canvas Container -->
        <div class="canvas-container">
            <div class="grid-overlay"></div>
            <canvas id="annotationCanvas"></canvas>
            <div id="textInput" class="text-input" style="display: none;">
                <input type="text" placeholder="Type here...">
            </div>
        </div>
    </div>
    <script src="script.js"></script>
    <script>
        // Force grid to be visible
        document.addEventListener('DOMContentLoaded', function() {
            const gridOverlay = document.querySelector('.grid-overlay');
            if (gridOverlay) {
                gridOverlay.classList.add('visible');
                gridOverlay.style.display = 'block';
                gridOverlay.style.opacity = '0.7';
                gridOverlay.style.backgroundSize = '20px 20px';
                gridOverlay.style.backgroundImage = `
                    linear-gradient(rgba(0, 0, 0, 0.8) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(0, 0, 0, 0.8) 1px, transparent 1px)
                `;
                console.log('Grid forced visible');
            }
        });
    </script>
</body>
</html> 