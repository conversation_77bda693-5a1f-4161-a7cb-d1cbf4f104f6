:root {
    --primary-color: #2C3E50;
    --secondary-color: #3498DB;
    --accent-color: #E74C3C;
    --background-color: rgba(255, 255, 255, 0.95);
    --toolbar-bg: rgba(255, 255, 255, 0.98);
    --border-radius: 12px;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --section-spacing: 15px;
    --toolbar-width: 70px;
    --panel-width: 250px;
    --handle-size: 8px;
    --handle-color: #3498DB;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: transparent;
    height: 100vh;
    overflow: hidden;
    color: var(--primary-color);
}

.app-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: var(--background-color);
    backdrop-filter: blur(10px);
}

/* Main Toolbar Styles - Vertical */
.main-toolbar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    width: var(--toolbar-width);
    background: var(--toolbar-bg);
    padding: 15px 10px;
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
    box-shadow: var(--shadow);
    display: flex;
    flex-direction: column;
    gap: var(--section-spacing);
    z-index: 1000;
    transition: transform 0.3s ease;
    overflow-y: auto;
}

.main-toolbar.collapsed {
    transform: translateX(calc(-1 * var(--toolbar-width)));
}

.toggle-toolbar {
    position: absolute;
    right: -30px;
    top: 20px;
    width: 30px;
    height: 40px;
    border: none;
    background: var(--toolbar-bg);
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow);
    color: var(--primary-color);
}

.tool-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.tool-section:last-child {
    border-bottom: none;
}

.tool-section h3 {
    font-size: 10px;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 5px;
    text-align: center;
}

.tool-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 8px;
    background: transparent;
    color: var(--primary-color);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tool-btn:hover {
    background: rgba(52, 152, 219, 0.1);
    color: var(--secondary-color);
}

.tool-btn.active {
    background: var(--secondary-color);
    color: white;
    transform: scale(1.05);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Dynamic Property Panels */
.property-panels {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    transition: transform 0.3s ease;
}

.property-panels.collapsed {
    transform: translateX(calc(var(--panel-width) + 40px));
}

.toggle-properties {
    position: absolute;
    left: -30px;
    top: 20px;
    width: 30px;
    height: 40px;
    border: none;
    background: var(--toolbar-bg);
    border-radius: var(--border-radius) 0 0 var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow);
    color: var(--primary-color);
}

.property-panel {
    background: var(--toolbar-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 15px;
    width: var(--panel-width);
    display: none;
    animation: fadeIn 0.3s ease;
}

.property-panel.active {
    display: block;
}

.property-panel h3 {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 15px;
    text-align: center;
}

.property-item {
    margin-bottom: 15px;
    transition: opacity 0.3s ease;
}

.property-item label {
    display: block;
    margin-bottom: 5px;
    font-size: 12px;
    color: #555;
    transition: color 0.2s ease;
}

.property-item:hover label {
    color: var(--secondary-color);
}

.property-item input[type="color"] {
    width: 100%;
    height: 40px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    padding: 0;
}

.property-item input[type="range"] {
    width: 100%;
    height: 4px;
    -webkit-appearance: none;
    background: #ddd;
    border-radius: 2px;
    outline: none;
}

.property-item input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    background: var(--secondary-color);
    border-radius: 50%;
    cursor: pointer;
}

.property-item select {
    width: 100%;
    padding: 8px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    background: white;
    color: var(--primary-color);
}

.property-item span {
    display: inline-block;
    margin-left: 8px;
    font-size: 12px;
    color: #666;
}

/* Toggle Switch */
.toggle-switch {
    display: flex;
    align-items: center;
    gap: 10px;
}

.toggle-switch input[type="checkbox"] {
    height: 0;
    width: 0;
    visibility: hidden;
    position: absolute;
}

.toggle-switch label {
    cursor: pointer;
    width: 40px;
    height: 20px;
    background: #ddd;
    display: block;
    border-radius: 100px;
    position: relative;
}

.toggle-switch label:after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 16px;
    height: 16px;
    background: #fff;
    border-radius: 50%;
    transition: 0.3s;
}

.toggle-switch input:checked + label {
    background: var(--secondary-color);
}

.toggle-switch input:checked + label:after {
    left: calc(100% - 2px);
    transform: translateX(-100%);
}

.toggle-switch span {
    font-size: 12px;
    color: #666;
}

.shape-options, .sticker-grid, .emoji-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
}

.shape-btn, .sticker-btn, .emoji-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 8px;
    background: transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: var(--primary-color);
}

.shape-btn:hover, .sticker-btn:hover, .emoji-btn:hover {
    background: rgba(52, 152, 219, 0.1);
    color: var(--secondary-color);
}

.shape-btn.active, .sticker-btn.active, .emoji-btn.active {
    background: var(--secondary-color);
    color: white;
}

/* Canvas Container Styles */
.canvas-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background: transparent;
    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAEKADAAQAAAABAAAAEAAAAAAXnVPIAAAAFElEQVQ4EWP8z8AARBQM4wGjgUQAACMLEAoONrUDAAAAAElFTkSuQmCC");
    z-index: 0;
}

.grid-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(rgba(0, 0, 0, 0.8) 1px, transparent 1px),
                      linear-gradient(90deg, rgba(0, 0, 0, 0.8) 1px, transparent 1px);
    background-size: 20px 20px;
    pointer-events: none;
    display: none;
    z-index: 0;
    opacity: 0.7;
}

.grid-overlay.visible {
    display: block !important;
}

#annotationCanvas {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2;
    cursor: crosshair;
    width: 100%;
    height: 100%;
    background: transparent;
}

.text-input {
    position: absolute;
    background: transparent;
    padding: 8px;
    border-radius: 8px;
    z-index: 1001;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.text-input input {
    border: none;
    outline: none;
    font-size: 16px;
    padding: 5px;
    width: 200px;
    color: var(--primary-color);
    background: transparent;
    -webkit-user-select: text;
    user-select: text;
}

.text-input input::selection {
    background: rgba(52, 152, 219, 0.3);
}

/* Draggable Elements */
.draggable {
    position: absolute;
    cursor: move;
    transform-origin: center center;
    user-select: none;
    z-index: 20;
    transition: transform 0.1s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 10px;
    min-height: 10px;
}

.draggable.text {
    padding: 5px;
    border-radius: 3px;
    background: white;
    min-width: 20px;
    min-height: 20px;
    display: inline-block;
    white-space: nowrap;
}

.draggable.emoji-sticker {
    font-size: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    user-select: none;
    box-sizing: border-box;
}

.draggable.shape {
    border-style: solid;
    background: transparent;
}

/* Pen and Highlighter Strokes */
.draggable.pen-stroke,
.draggable.highlighter-stroke {
    position: absolute;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
    pointer-events: auto;
    z-index: 10;
    border: none;
    box-shadow: none;
    outline: none;
}

.draggable.pen-stroke {
    border-radius: 0;
}

.draggable.highlighter-stroke {
    border-radius: 0;
}

.draggable.selected {
    z-index: 20;
}

.bounding-box {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 2px dashed #3498db;
    box-sizing: border-box;
    pointer-events: none;
    z-index: 1;
    display: none;
    opacity: 0.8;
}

.draggable.selected .bounding-box {
    display: block !important;
    border: 2px dashed #3498db !important;
}

.draggable:hover .bounding-box {
    display: block;
}

.control-handle {
    position: absolute;
    width: 10px;
    height: 10px;
    background: #fff;
    border: 2px solid #3498db;
    border-radius: 50%;
    z-index: 2;
    cursor: pointer;
}

/* Position handles precisely at corners and edges */
.handle-nw { top: -5px; left: -5px; cursor: nwse-resize; }
.handle-n { top: -5px; left: calc(50% - 5px); cursor: ns-resize; }
.handle-ne { top: -5px; right: -5px; cursor: nesw-resize; }
.handle-e { top: calc(50% - 5px); right: -5px; cursor: ew-resize; }
.handle-se { bottom: -5px; right: -5px; cursor: nwse-resize; }
.handle-s { bottom: -5px; left: calc(50% - 5px); cursor: ns-resize; }
.handle-sw { bottom: -5px; left: -5px; cursor: nesw-resize; }
.handle-w { top: calc(50% - 5px); left: -5px; cursor: ew-resize; }

/* Rotate handle */
.rotate-handle {
    position: absolute;
    width: 10px;
    height: 10px;
    background-color: #fff;
    border: 1.5px solid #2196F3;
    border-radius: 50%;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    pointer-events: auto;
    cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21.5 2v6h-6M2.5 22v-6h6M2 11.5a10 10 0 0 1 18.8-4.3M22 12.5a10 10 0 0 1-18.8 4.2"/></svg>'), auto;
    z-index: 1000;
    box-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

/* Rotation line */
.rotation-line {
    position: absolute;
    width: 1px;
    height: 20px;
    background-color: #2196F3;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    pointer-events: none;
    z-index: 999;
}

/* Shape styles */
.draggable.shape {
    background: transparent;
    position: absolute;
    cursor: move;
    z-index: 10;
}

.draggable.shape[data-shape-type="circle"] {
    border-radius: 50%;
}

.draggable.shape[data-shape-type="line"] {
    height: 0 !important;
    border-top-width: 2px;
    border-bottom-width: 0;
    border-left-width: 0;
    border-right-width: 0;
}

.draggable.shape[data-shape-type="arrow"] {
    height: 0 !important;
    border-top-width: 2px;
    border-bottom-width: 0;
    border-left-width: 0;
    border-right-width: 0;
    position: relative;
}

.draggable.shape[data-shape-type="arrow"]::after {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 5px 10px 5px;
    border-color: transparent transparent currentColor transparent;
    transform: rotate(90deg);
    right: -5px;
    top: calc(50% - 5px);
}

/* iOS-style animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    :root {
        --toolbar-width: 50px;
        --panel-width: 200px;
    }
    
    .tool-btn {
        width: 35px;
        height: 35px;
    }
    
    .property-panel {
        padding: 10px;
    }
}

@media (max-width: 480px) {
    :root {
        --toolbar-width: 45px;
        --panel-width: 180px;
    }
    
    .tool-btn {
        width: 30px;
        height: 30px;
    }
    
    .property-panel h3 {
        font-size: 10px;
    }
    
    .property-item label {
        font-size: 10px;
    }
}

/* Add canvas background toggle */
.canvas-settings {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background: var(--toolbar-bg);
    padding: 10px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    z-index: 1000;
    display: flex;
    gap: 15px;
}

.canvas-settings .property-item {
    margin-bottom: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Sticker and Emoji Baker Styles */
.sticker-baker,
.emoji-baker {
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    padding-top: 15px;
    margin-top: 15px;
}

.upload-btn {
    width: 100%;
    padding: 8px;
    background: var(--secondary-color);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.2s ease;
}

.upload-btn:hover {
    background: #2980b9;
}

.custom-stickers-grid,
.custom-emojis-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
    margin-top: 15px;
    max-height: 200px;
    overflow-y: auto;
    padding: 5px;
}

.custom-sticker-btn,
.custom-emoji-btn {
    width: 40px;
    height: 40px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    cursor: pointer;
    transition: all 0.2s ease;
}

.custom-sticker-btn:hover,
.custom-emoji-btn:hover {
    border-color: var(--secondary-color);
    transform: scale(1.05);
}

.custom-sticker-btn.active,
.custom-emoji-btn.active {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 2px var(--secondary-color);
}

/* Emoji and Sticker styling */
.draggable.emoji-sticker {
    font-size: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    user-select: none;
    box-sizing: border-box;
}

/* Background Controls Styles */
.bg-control-section {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding-bottom: 15px;
}

.bg-controls {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    width: 100%;
}

.bg-preview {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    gap: 5px;
}

.bg-icon {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border: 1px solid rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
}

.bg-icon.transparent {
    background: transparent;
    color: var(--primary-color);
}

.bg-icon.solid {
    color: var(--primary-color);
}

.bg-icon.active {
    border-color: var(--secondary-color);
    background-color: rgba(52, 152, 219, 0.1);
    box-shadow: 0 0 0 2px var(--secondary-color);
}

#bgColorPicker {
    width: 40px;
    height: 24px;
    border: none;
    border-radius: 4px;
    background: transparent;
    cursor: pointer;
}

/* Improved Property Panel Interaction Styles */
.property-panel input[type="range"],
.property-panel input[type="color"],
.property-panel select {
    transition: all 0.2s ease;
}

.property-panel input[type="range"]:active,
.property-panel input[type="color"]:active,
.property-panel select:focus {
    box-shadow: 0 0 0 2px var(--secondary-color);
}

.property-panel input[type="color"] {
    cursor: pointer;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.property-panel input[type="color"]:hover {
    transform: scale(1.05);
}

/* Real-time property changes */
.property-item {
    margin-bottom: 15px;
    transition: opacity 0.3s ease;
}

.property-item label {
    display: block;
    margin-bottom: 5px;
    font-size: 12px;
    color: #555;
    transition: color 0.2s ease;
}

.property-item:hover label {
    color: var(--secondary-color);
}

/* Canvas cursor styles based on tool */
#annotationCanvas.pen-cursor,
#annotationCanvas.highlighter-cursor {
    cursor: crosshair;
}

#annotationCanvas.eraser-cursor {
    cursor: pointer;
}

.delete-btn {
    position: absolute;
    top: -25px;
    right: -5px;
    background: #ff4d4d;
    color: white;
    border: none;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    cursor: pointer;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    transition: all 0.2s ease;
    box-shadow: 0 2px 5px rgba(0,0,0,0.3);
}

.delete-btn:hover {
    background: #ff3333;
    transform: scale(1.1);
}

.delete-btn:active {
    transform: scale(0.95);
}
