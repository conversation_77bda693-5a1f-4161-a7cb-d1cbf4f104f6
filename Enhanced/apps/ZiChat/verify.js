// This script will verify that our changes are being applied
document.addEventListener('DOMContentLoaded', function() {
    console.log('Verification script loaded');
    
    // Verify grid overlay
    const gridOverlay = document.querySelector('.grid-overlay');
    if (gridOverlay) {
        console.log('Grid overlay found');
        console.log('Grid overlay styles:', {
            display: window.getComputedStyle(gridOverlay).display,
            backgroundImage: window.getComputedStyle(gridOverlay).backgroundImage,
            backgroundSize: window.getComputedStyle(gridOverlay).backgroundSize,
            opacity: window.getComputedStyle(gridOverlay).opacity,
            zIndex: window.getComputedStyle(gridOverlay).zIndex
        });
        
        // Force grid to be visible
        gridOverlay.classList.add('visible');
        gridOverlay.style.display = 'block';
        gridOverlay.style.opacity = '0.7';
        gridOverlay.style.backgroundSize = '20px 20px';
        gridOverlay.style.backgroundImage = `
            linear-gradient(rgba(0, 0, 0, 0.8) 1px, transparent 1px),
            linear-gradient(90deg, rgba(0, 0, 0, 0.8) 1px, transparent 1px)
        `;
        console.log('Grid forced visible');
    } else {
        console.error('Grid overlay not found');
    }
    
    // Create a test element to verify resizing
    const testElement = document.createElement('div');
    testElement.className = 'draggable text';
    testElement.style.fontFamily = 'Arial';
    testElement.style.fontSize = '16px';
    testElement.style.color = '#000000';
    testElement.style.opacity = 1;
    testElement.style.background = 'white';
    testElement.style.padding = '5px';
    testElement.style.whiteSpace = 'nowrap';
    testElement.textContent = 'Test Element';
    testElement.style.position = 'absolute';
    testElement.style.left = '100px';
    testElement.style.top = '100px';
    testElement.style.width = '100px';
    testElement.style.height = '50px';
    
    // Create bounding box
    const boundingBox = document.createElement('div');
    boundingBox.className = 'bounding-box';
    testElement.appendChild(boundingBox);
    
    // Create control handles
    const handles = ['nw', 'n', 'ne', 'e', 'se', 's', 'sw', 'w'];
    handles.forEach(handle => {
        const handleElement = document.createElement('div');
        handleElement.className = `control-handle handle-${handle}`;
        handleElement.dataset.handle = handle;
        testElement.appendChild(handleElement);
    });
    
    // Add to canvas container
    const canvasContainer = document.querySelector('.canvas-container');
    if (canvasContainer) {
        canvasContainer.appendChild(testElement);
        console.log('Test element added to canvas container');
        
        // Select the element
        testElement.classList.add('selected');
    } else {
        console.error('Canvas container not found');
    }
}); 