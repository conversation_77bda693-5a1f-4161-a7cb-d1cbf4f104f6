<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verification Page</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Force important styles for verification */
        .grid-overlay {
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            background-image: linear-gradient(rgba(0, 0, 0, 0.8) 1px, transparent 1px),
                            linear-gradient(90deg, rgba(0, 0, 0, 0.8) 1px, transparent 1px) !important;
            background-size: 20px 20px !important;
            pointer-events: none !important;
            z-index: 0 !important;
            opacity: 0.7 !important;
        }

        .grid-overlay.visible {
            display: block !important;
        }

        .bounding-box {
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            border: 2px dashed #3498db !important;
            box-sizing: border-box !important;
            pointer-events: none !important;
            z-index: 1 !important;
            display: none !important;
            opacity: 0.8 !important;
        }

        .draggable.selected .bounding-box {
            display: block !important;
        }

        .draggable:hover .bounding-box {
            display: block !important;
        }

        .control-handle {
            position: absolute !important;
            width: 10px !important;
            height: 10px !important;
            background: #fff !important;
            border: 2px solid #3498db !important;
            border-radius: 50% !important;
            z-index: 2 !important;
            cursor: pointer !important;
        }

        .handle-nw { top: -5px !important; left: -5px !important; cursor: nwse-resize !important; }
        .handle-n { top: -5px !important; left: calc(50% - 5px) !important; cursor: ns-resize !important; }
        .handle-ne { top: -5px !important; right: -5px !important; cursor: nesw-resize !important; }
        .handle-e { top: calc(50% - 5px) !important; right: -5px !important; cursor: ew-resize !important; }
        .handle-se { bottom: -5px !important; right: -5px !important; cursor: nwse-resize !important; }
        .handle-s { bottom: -5px !important; left: calc(50% - 5px) !important; cursor: ns-resize !important; }
        .handle-sw { bottom: -5px !important; left: -5px !important; cursor: nesw-resize !important; }
        .handle-w { top: calc(50% - 5px) !important; left: -5px !important; cursor: ew-resize !important; }

        /* Text Properties Panel */
        .text-properties-panel {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 250px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            padding: 15px;
            z-index: 100;
        }

        .text-properties-panel h3 {
            margin-top: 0;
            margin-bottom: 15px;
            color: #3498db;
            font-size: 16px;
        }

        .property-item {
            margin-bottom: 12px;
        }

        .property-item label {
            display: block;
            margin-bottom: 5px;
            font-size: 14px;
            color: #555;
        }

        .property-item select,
        .property-item input[type="range"],
        .property-item input[type="color"] {
            width: 100%;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .property-item input[type="color"] {
            height: 30px;
        }

        .font-weight-options {
            display: flex;
            gap: 5px;
        }

        .font-weight-btn {
            flex: 1;
            padding: 5px;
            border: 1px solid #ddd;
            background: #f5f5f5;
            cursor: pointer;
            border-radius: 4px;
            text-align: center;
        }

        .font-weight-btn.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        .transparent-bg-toggle {
            display: flex;
            align-items: center;
            margin-top: 5px;
        }

        .transparent-bg-toggle input {
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Main Toolbar - Vertical -->
        <div class="main-toolbar vertical">
            <button class="toggle-toolbar">
                <i class="fas fa-chevron-left"></i>
            </button>
            
            <div class="tool-section">
                <h3>View</h3>
                <button class="tool-btn" id="toggleGridBtn" title="Toggle Grid">
                    <i class="fas fa-border-all"></i>
                </button>
            </div>
            
            <div class="tool-section">
                <h3>Test</h3>
                <button class="tool-btn" id="createElementBtn" title="Create Test Element">
                    <i class="fas fa-plus"></i>
                </button>
            </div>
        </div>

        <!-- Canvas Container -->
        <div class="canvas-container">
            <div class="grid-overlay"></div>
            <canvas id="annotationCanvas"></canvas>
        </div>

        <!-- Text Properties Panel -->
        <div class="text-properties-panel">
            <h3>Text Properties</h3>
            <div class="property-item">
                <label>Font Family</label>
                <select id="fontFamily">
                    <option value="Arial">Arial</option>
                    <option value="Verdana">Verdana</option>
                    <option value="Times New Roman">Times New Roman</option>
                    <option value="Courier New">Courier New</option>
                    <option value="Georgia">Georgia</option>
                    <option value="Tahoma">Tahoma</option>
                    <option value="Trebuchet MS">Trebuchet MS</option>
                </select>
            </div>
            <div class="property-item">
                <label>Font Size</label>
                <input type="range" id="fontSize" min="10" max="72" value="16">
                <span id="fontSizeValue">16px</span>
            </div>
            <div class="property-item">
                <label>Font Weight</label>
                <div class="font-weight-options">
                    <div class="font-weight-btn" data-weight="normal">Normal</div>
                    <div class="font-weight-btn" data-weight="bold">Bold</div>
                    <div class="font-weight-btn" data-weight="lighter">Light</div>
                </div>
            </div>
            <div class="property-item">
                <label>Text Color</label>
                <input type="color" id="textColor" value="#000000">
            </div>
            <div class="property-item">
                <label>Background Color</label>
                <input type="color" id="bgColor" value="#ffffff">
                <div class="transparent-bg-toggle">
                    <input type="checkbox" id="transparentBg">
                    <label for="transparentBg">Transparent Background</label>
                </div>
            </div>
            <div class="property-item">
                <label>Opacity</label>
                <input type="range" id="textOpacity" min="10" max="100" value="100">
                <span id="textOpacityValue">100%</span>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Verification page loaded');
            
            // Text properties
            const fontFamily = document.getElementById('fontFamily');
            const fontSize = document.getElementById('fontSize');
            const fontSizeValue = document.getElementById('fontSizeValue');
            const fontWeightBtns = document.querySelectorAll('.font-weight-btn');
            const textColor = document.getElementById('textColor');
            const bgColor = document.getElementById('bgColor');
            const transparentBg = document.getElementById('transparentBg');
            const textOpacity = document.getElementById('textOpacity');
            const textOpacityValue = document.getElementById('textOpacityValue');
            
            // Default text properties
            let textProperties = {
                fontFamily: 'Arial',
                fontSize: '16px',
                fontWeight: 'normal',
                color: '#000000',
                backgroundColor: '#ffffff',
                transparentBg: false,
                opacity: 1
            };
            
            // Set active font weight
            fontWeightBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    fontWeightBtns.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    textProperties.fontWeight = this.dataset.weight;
                    updateTestElement();
                });
                
                // Set initial active state
                if (btn.dataset.weight === textProperties.fontWeight) {
                    btn.classList.add('active');
                }
            });
            
            // Font family change
            fontFamily.addEventListener('change', function() {
                textProperties.fontFamily = this.value;
                updateTestElement();
            });
            
            // Font size change
            fontSize.addEventListener('input', function() {
                textProperties.fontSize = `${this.value}px`;
                fontSizeValue.textContent = `${this.value}px`;
                updateTestElement();
            });
            
            // Text color change
            textColor.addEventListener('input', function() {
                textProperties.color = this.value;
                updateTestElement();
            });
            
            // Background color change
            bgColor.addEventListener('input', function() {
                textProperties.backgroundColor = this.value;
                if (!textProperties.transparentBg) {
                    updateTestElement();
                }
            });
            
            // Transparent background toggle
            transparentBg.addEventListener('change', function() {
                textProperties.transparentBg = this.checked;
                updateTestElement();
            });
            
            // Opacity change
            textOpacity.addEventListener('input', function() {
                textProperties.opacity = this.value / 100;
                textOpacityValue.textContent = `${this.value}%`;
                updateTestElement();
            });
            
            // Function to update test element with current properties
            function updateTestElement() {
                const testElement = document.querySelector('.draggable.text');
                if (testElement) {
                    testElement.style.fontFamily = textProperties.fontFamily;
                    testElement.style.fontSize = textProperties.fontSize;
                    testElement.style.fontWeight = textProperties.fontWeight;
                    testElement.style.color = textProperties.color;
                    testElement.style.background = textProperties.transparentBg ? 'transparent' : textProperties.backgroundColor;
                    testElement.style.opacity = textProperties.opacity;
                }
            }
            
            // Toggle grid button
            const toggleGridBtn = document.getElementById('toggleGridBtn');
            toggleGridBtn.addEventListener('click', function() {
                const gridOverlay = document.querySelector('.grid-overlay');
                if (gridOverlay) {
                    const isVisible = gridOverlay.classList.contains('visible');
                    if (isVisible) {
                        gridOverlay.classList.remove('visible');
                        gridOverlay.style.display = 'none';
                    } else {
                        gridOverlay.classList.add('visible');
                        gridOverlay.style.display = 'block';
                        gridOverlay.style.opacity = '0.7';
                        gridOverlay.style.backgroundSize = '20px 20px';
                        gridOverlay.style.backgroundImage = `
                            linear-gradient(rgba(0, 0, 0, 0.8) 1px, transparent 1px),
                            linear-gradient(90deg, rgba(0, 0, 0, 0.8) 1px, transparent 1px)
                        `;
                    }
                    console.log('Grid visibility toggled:', !isVisible);
                }
            });
            
            // Create element button
            const createElementBtn = document.getElementById('createElementBtn');
            createElementBtn.addEventListener('click', function() {
                // Remove any existing test elements
                const existingElement = document.querySelector('.draggable.text');
                if (existingElement) {
                    existingElement.remove();
                }
                
                // Create a test element with current properties
                const testElement = document.createElement('div');
                testElement.className = 'draggable text';
                testElement.style.fontFamily = textProperties.fontFamily;
                testElement.style.fontSize = textProperties.fontSize;
                testElement.style.fontWeight = textProperties.fontWeight;
                testElement.style.color = textProperties.color;
                testElement.style.background = textProperties.transparentBg ? 'transparent' : textProperties.backgroundColor;
                testElement.style.opacity = textProperties.opacity;
                testElement.style.padding = '5px';
                testElement.style.whiteSpace = 'nowrap';
                testElement.textContent = 'Test Element';
                testElement.style.position = 'absolute';
                testElement.style.left = '100px';
                testElement.style.top = '100px';
                testElement.style.width = '150px';
                testElement.style.height = '50px';
                
                // Create bounding box
                const boundingBox = document.createElement('div');
                boundingBox.className = 'bounding-box';
                testElement.appendChild(boundingBox);
                
                // Create control handles
                const handles = ['nw', 'n', 'ne', 'e', 'se', 's', 'sw', 'w'];
                handles.forEach(handle => {
                    const handleElement = document.createElement('div');
                    handleElement.className = `control-handle handle-${handle}`;
                    handleElement.dataset.handle = handle;
                    testElement.appendChild(handleElement);
                });
                
                // Add to canvas container
                const canvasContainer = document.querySelector('.canvas-container');
                if (canvasContainer) {
                    canvasContainer.appendChild(testElement);
                    console.log('Test element added to canvas container');
                    
                    // Select the element
                    testElement.classList.add('selected');
                    
                    // Make element draggable
                    let isDragging = false;
                    let startX, startY;
                    
                    testElement.addEventListener('mousedown', function(e) {
                        if (e.target === testElement) {
                            isDragging = true;
                            startX = e.clientX - parseInt(testElement.style.left);
                            startY = e.clientY - parseInt(testElement.style.top);
                        }
                    });
                    
                    document.addEventListener('mousemove', function(e) {
                        if (isDragging) {
                            testElement.style.left = (e.clientX - startX) + 'px';
                            testElement.style.top = (e.clientY - startY) + 'px';
                        }
                    });
                    
                    document.addEventListener('mouseup', function() {
                        isDragging = false;
                    });
                    
                    // Make element resizable
                    const resizeHandles = testElement.querySelectorAll('.control-handle');
                    resizeHandles.forEach(handle => {
                        handle.addEventListener('mousedown', function(e) {
                            e.stopPropagation();
                            const handleType = handle.dataset.handle;
                            const startRect = testElement.getBoundingClientRect();
                            const startX = e.clientX;
                            const startY = e.clientY;
                            
                            const handleResize = function(e) {
                                const deltaX = (e.clientX - startX) * 4;
                                const deltaY = (e.clientY - startY) * 4;
                                
                                let newWidth = startRect.width;
                                let newHeight = startRect.height;
                                let newLeft = startRect.left;
                                let newTop = startRect.top;
                                
                                switch (handleType) {
                                    case 'nw':
                                        newWidth = Math.max(startRect.width - deltaX, 20);
                                        newHeight = Math.max(startRect.height - deltaY, 20);
                                        newLeft = startRect.left + (startRect.width - newWidth);
                                        newTop = startRect.top + (startRect.height - newHeight);
                                        break;
                                    case 'n':
                                        newHeight = Math.max(startRect.height - deltaY, 20);
                                        newTop = startRect.top + (startRect.height - newHeight);
                                        break;
                                    case 'ne':
                                        newWidth = Math.max(startRect.width + deltaX, 20);
                                        newHeight = Math.max(startRect.height - deltaY, 20);
                                        newTop = startRect.top + (startRect.height - newHeight);
                                        break;
                                    case 'e':
                                        newWidth = Math.max(startRect.width + deltaX, 20);
                                        break;
                                    case 'se':
                                        newWidth = Math.max(startRect.width + deltaX, 20);
                                        newHeight = Math.max(startRect.height + deltaY, 20);
                                        break;
                                    case 's':
                                        newHeight = Math.max(startRect.height + deltaY, 20);
                                        break;
                                    case 'sw':
                                        newWidth = Math.max(startRect.width - deltaX, 20);
                                        newHeight = Math.max(startRect.height + deltaY, 20);
                                        newLeft = startRect.left + (startRect.width - newWidth);
                                        break;
                                    case 'w':
                                        newWidth = Math.max(startRect.width - deltaX, 20);
                                        newLeft = startRect.left + (startRect.width - newWidth);
                                        break;
                                }
                                
                                testElement.style.width = `${newWidth}px`;
                                testElement.style.height = `${newHeight}px`;
                                testElement.style.left = `${newLeft}px`;
                                testElement.style.top = `${newTop}px`;
                                
                                // Adjust font size for text elements
                                const fontSize = Math.max(12, Math.min(newHeight * 0.8, newWidth * 0.2));
                                testElement.style.fontSize = `${fontSize}px`;
                            };
                            
                            const stopResize = function() {
                                document.removeEventListener('mousemove', handleResize);
                                document.removeEventListener('mouseup', stopResize);
                            };
                            
                            document.addEventListener('mousemove', handleResize);
                            document.addEventListener('mouseup', stopResize);
                        });
                    });
                }
            });
        });
    </script>
</body>
</html> 