class AnnotationApp {
    constructor() {
        this.canvas = document.getElementById('annotationCanvas');
        this.ctx = this.canvas.getContext('2d', { alpha: true }); // Ensure alpha channel is supported
        this.currentTool = 'pen';
        this.isDrawing = false;
        this.lastX = 0;
        this.lastY = 0;
        this.color = '#000000';
        this.size = 5;
        this.history = [];
        this.redoStack = [];
        this.gridVisible = false;
        this.gridSize = 20; // Default grid size
        this.gridOpacity = 0.7; // Default grid opacity
        this.selectedShape = null;
        this.selectedSticker = null;
        this.selectedEmoji = null;
        this.fontFamily = 'Arial';
        this.fontSize = '16px';
        this.textInput = document.getElementById('textInput');
        this.textInputInput = this.textInput.querySelector('input');
        this.draggableElements = [];
        this.selectedElement = null;
        this.isDragging = false;
        this.isResizing = false;
        this.dragOffset = { x: 0, y: 0 };
        this.activePropertyPanel = null;
        this.isDrawingShape = false;
        this.currentShape = null;
        this.shapeStartPos = { x: 0, y: 0 };
        this.currentStroke = null;
        this.tempCanvas = null;
        this.tempCtx = null;
        this.drawingEnabled = true;
        this.canvasBackground = {
            solid: false,  // Default to transparent
            color: '#ffffff',
            opacity: 1
        };
        
        // Store canvas strokes data for direct canvas drawing
        this.canvasStrokes = [];

        // Tool-specific properties
        this.toolProperties = {
            pen: { color: '#000000', size: 5, opacity: 1, tipStyle: 'round' },
            highlighter: { color: '#FFFF00', size: 20, opacity: 0.5 },
            eraser: { size: 20 },
            text: { color: '#000000', fontFamily: 'Arial', fontSize: '16px', opacity: 1, transparent: false },
            shape: { color: '#000000', lineWidth: 3, type: 'rectangle', opacity: 1, fill: false },
            sticker: { type: '⭐', size: 30, opacity: 1 },
            emoji: { type: '😊', size: 30, opacity: 1 },
            grid: { size: 20, opacity: 0.7 }
        };

        // Add custom stickers/emojis storage
        this.customStickers = new Map();
        this.customEmojis = new Map();
        
        // Initialize baker functionality
        this.initializeBaker();

        this.initializeCanvas();
        this.setupEventListeners();
        this.setupToolbar();
        this.setupBackgroundControls();
        this.setupPropertyPanels();
        this.setupCanvasSettings();
    }

    initializeCanvas() {
        this.resizeCanvas();
        window.addEventListener('resize', () => this.resizeCanvas());
        
        // Initialize grid properties
        const gridOverlay = document.querySelector('.grid-overlay');
        if (gridOverlay) {
            // Set initial grid size
            const gridSize = this.toolProperties.grid.size || 20;
            gridOverlay.style.backgroundSize = `${gridSize}px ${gridSize}px`;
            
            // Set initial grid opacity
            const gridOpacity = this.toolProperties.grid.opacity || 0.7;
            gridOverlay.style.opacity = gridOpacity;
            
            // Set grid background image with darker lines
            gridOverlay.style.backgroundImage = `
                linear-gradient(rgba(0, 0, 0, 0.8) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 0, 0, 0.8) 1px, transparent 1px)
            `;
            
            // Set CSS variable for grid opacity
            document.documentElement.style.setProperty('--grid-opacity', gridOpacity);
        }
    }

    resizeCanvas() {
        this.canvas.width = window.innerWidth;
        this.canvas.height = window.innerHeight;
        this.redrawCanvas();
    }

    setupEventListeners() {
        // Mouse events with throttling
        let mousedownTimer = null;
        this.canvas.addEventListener('mousedown', (e) => {
            // Clear any existing timer
            if (mousedownTimer) {
                clearTimeout(mousedownTimer);
            }
            
            // Set a new timer to prevent multiple rapid calls
            mousedownTimer = setTimeout(() => {
                this.startDrawing(e);
                mousedownTimer = null;
            }, 10);
        });
        
        // Throttle mousemove events
        let lastDrawTime = 0;
        this.canvas.addEventListener('mousemove', (e) => {
            const now = Date.now();
            if (now - lastDrawTime > 10) { // Throttle to about 100 FPS
                this.draw(e);
                lastDrawTime = now;
            }
        });
        
        // Only process mouseup once
        let isProcessingMouseUp = false;
        this.canvas.addEventListener('mouseup', (e) => {
            if (!isProcessingMouseUp) {
                isProcessingMouseUp = true;
                this.stopDrawing();
                // Reset flag after a short delay
                setTimeout(() => {
                    isProcessingMouseUp = false;
                }, 50);
            }
        });
        
        this.canvas.addEventListener('mouseout', this.stopDrawing.bind(this));

        // Touch events
        this.canvas.addEventListener('touchstart', this.handleTouch.bind(this));
        this.canvas.addEventListener('touchmove', this.handleTouch.bind(this));
        this.canvas.addEventListener('touchend', this.stopDrawing.bind(this));

        // Text input events
        this.textInputInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.addText();
            }
        });

        // Draggable elements events
        document.addEventListener('mousedown', this.handleDraggableMouseDown.bind(this));
        document.addEventListener('mousemove', this.handleDraggableMouseMove.bind(this));
        document.addEventListener('mouseup', this.handleDraggableMouseUp.bind(this));

        // Grid properties - REMOVED to avoid duplicate event listeners
        // (These are now handled in setupPropertyPanels)
        
        // Add keyboard event for delete key
        document.addEventListener('keydown', (e) => {
            if ((e.key === 'Delete' || e.key === 'Backspace') && this.selectedElement) {
                e.preventDefault();
                this.deleteSelectedElement();
            }
        });
    }

    setupToolbar() {
        // Set up tool buttons
        document.querySelectorAll('.tool-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const tool = btn.getAttribute('data-tool');
                this.setTool(tool);
            });
        });
        
        // Set up shape buttons
        document.querySelectorAll('.shape-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const shape = btn.getAttribute('data-shape');
                this.selectedShape = shape;
                this.toolProperties.shape.type = shape;
                
                // Update active state
                document.querySelectorAll('.shape-btn').forEach(b => {
                    b.classList.remove('active');
                });
                btn.classList.add('active');
            });
        });
        
        // Set up sticker buttons
        document.querySelectorAll('.sticker-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const sticker = btn.textContent;
                this.toolProperties.sticker.type = sticker;
                
                // Update active state
                document.querySelectorAll('.sticker-btn').forEach(b => {
                    b.classList.remove('active');
                });
                btn.classList.add('active');
                
                // If sticker tool is active, add the sticker immediately
                if (this.currentTool === 'sticker') {
                    this.addSticker();
                }
            });
        });
        
        // Set up emoji buttons
        document.querySelectorAll('.emoji-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const emoji = btn.textContent;
                this.toolProperties.emoji.type = emoji;
                
                // Update active state
                document.querySelectorAll('.emoji-btn').forEach(b => {
                    b.classList.remove('active');
                });
                btn.classList.add('active');
                
                // If emoji tool is active, add the emoji immediately
                if (this.currentTool === 'emoji') {
                    this.addEmoji();
                }
            });
        });
        
        // Set initial tool
        this.setTool('pen');
    }

    setupPropertyPanels() {
        // Pen panel
        const penColorPicker = document.getElementById('penColorPicker');
        const penSizeSlider = document.getElementById('penSizeSlider');
        const penOpacitySlider = document.getElementById('penOpacitySlider');
        const penOpacityValue = document.getElementById('penOpacityValue');
        const penTipStyle = document.getElementById('penTipStyle');
        
        // Set initial values based on tool properties
        if (penColorPicker) penColorPicker.value = this.toolProperties.pen.color;
        if (penSizeSlider) penSizeSlider.value = this.toolProperties.pen.size;
        if (penOpacitySlider) penOpacitySlider.value = this.toolProperties.pen.opacity * 100;
        if (penOpacityValue) penOpacityValue.textContent = `${this.toolProperties.pen.opacity * 100}%`;
        if (penTipStyle) penTipStyle.value = this.toolProperties.pen.tipStyle;
        
        penColorPicker.addEventListener('input', (e) => {
            this.toolProperties.pen.color = e.target.value;
            
            // If currently drawing with pen, update the active stroke
            if (this.isDrawing && this.currentTool === 'pen' && this.currentStroke) {
                this.ctx.strokeStyle = e.target.value;
                this.currentStroke.properties.color = e.target.value;
            }
        });
        
        penSizeSlider.addEventListener('input', (e) => {
            this.toolProperties.pen.size = parseInt(e.target.value);
            
            // If currently drawing with pen, update the active stroke
            if (this.isDrawing && this.currentTool === 'pen' && this.currentStroke) {
                this.ctx.lineWidth = parseInt(e.target.value);
                this.currentStroke.properties.size = parseInt(e.target.value);
            }
        });
        
        if (penOpacitySlider && penOpacityValue) {
            penOpacitySlider.addEventListener('input', (e) => {
                const opacity = e.target.value / 100;
                this.toolProperties.pen.opacity = opacity;
                penOpacityValue.textContent = `${e.target.value}%`;
                
                // If currently drawing with pen, update the active stroke
                if (this.isDrawing && this.currentTool === 'pen' && this.currentStroke) {
                    this.ctx.globalAlpha = opacity;
                    this.currentStroke.properties.opacity = opacity;
                }
            });
        }
        
        if (penTipStyle) {
            penTipStyle.addEventListener('change', (e) => {
                this.toolProperties.pen.tipStyle = e.target.value;
                
                // If currently drawing with pen, update the active stroke
                if (this.isDrawing && this.currentTool === 'pen' && this.currentStroke) {
                    this.ctx.lineCap = e.target.value;
                    this.currentStroke.properties.tipStyle = e.target.value;
                }
            });
        }

        // Highlighter panel
        const highlighterColorPicker = document.getElementById('highlighterColorPicker');
        const highlighterSizeSlider = document.getElementById('highlighterSizeSlider');
        const highlighterOpacitySlider = document.getElementById('highlighterOpacitySlider');
        const highlighterOpacityValue = document.getElementById('highlighterOpacityValue');
        
        // Set initial values based on tool properties
        if (highlighterColorPicker) highlighterColorPicker.value = this.toolProperties.highlighter.color;
        if (highlighterSizeSlider) highlighterSizeSlider.value = this.toolProperties.highlighter.size;
        if (highlighterOpacitySlider) highlighterOpacitySlider.value = this.toolProperties.highlighter.opacity * 100;
        if (highlighterOpacityValue) highlighterOpacityValue.textContent = `${this.toolProperties.highlighter.opacity * 100}%`;
        
        highlighterColorPicker.addEventListener('input', (e) => {
            this.toolProperties.highlighter.color = e.target.value;
            
            // If currently drawing with highlighter, update the active stroke
            if (this.isDrawing && this.currentTool === 'highlighter' && this.currentStroke) {
                this.ctx.strokeStyle = e.target.value;
                this.currentStroke.properties.color = e.target.value;
            }
        });
        
        highlighterSizeSlider.addEventListener('input', (e) => {
            this.toolProperties.highlighter.size = parseInt(e.target.value);
            
            // If currently drawing with highlighter, update the active stroke
            if (this.isDrawing && this.currentTool === 'highlighter' && this.currentStroke) {
                this.ctx.lineWidth = parseInt(e.target.value);
                this.currentStroke.properties.size = parseInt(e.target.value);
            }
        });
        
        if (highlighterOpacitySlider && highlighterOpacityValue) {
            highlighterOpacitySlider.addEventListener('input', (e) => {
                const opacity = e.target.value / 100;
                this.toolProperties.highlighter.opacity = opacity;
                highlighterOpacityValue.textContent = `${e.target.value}%`;
                
                // If currently drawing with highlighter, update the active stroke
                if (this.isDrawing && this.currentTool === 'highlighter' && this.currentStroke) {
                    this.ctx.globalAlpha = opacity;
                    this.currentStroke.properties.opacity = opacity;
                }
            });
        }

        // Eraser panel
        const eraserSizeSlider = document.getElementById('eraserSizeSlider');
        
        // Set initial value based on tool properties
        if (eraserSizeSlider) eraserSizeSlider.value = this.toolProperties.eraser.size;
        
        eraserSizeSlider.addEventListener('input', (e) => {
            this.toolProperties.eraser.size = parseInt(e.target.value);
        });

        // Text panel
        const textColorPicker = document.getElementById('textColorPicker');
        const fontFamily = document.getElementById('fontFamily');
        const fontSizeSlider = document.getElementById('fontSizeSlider');
        const fontSizeValue = document.getElementById('fontSizeValue');
        const textOpacitySlider = document.getElementById('textOpacitySlider');
        const textOpacityValue = document.getElementById('textOpacityValue');
        const textBackgroundToggle = document.getElementById('textBackgroundToggle');
        
        // Set initial values based on tool properties
        if (textColorPicker) textColorPicker.value = this.toolProperties.text.color;
        if (fontFamily) fontFamily.value = this.toolProperties.text.fontFamily;
        if (fontSizeSlider) fontSizeSlider.value = parseInt(this.toolProperties.text.fontSize);
        if (fontSizeValue) fontSizeValue.textContent = this.toolProperties.text.fontSize;
        if (textOpacitySlider) textOpacitySlider.value = this.toolProperties.text.opacity * 100;
        if (textOpacityValue) textOpacityValue.textContent = `${this.toolProperties.text.opacity * 100}%`;
        if (textBackgroundToggle) textBackgroundToggle.checked = this.toolProperties.text.transparent;
        
        textColorPicker.addEventListener('input', (e) => {
            this.toolProperties.text.color = e.target.value;
            if (this.selectedElement && this.selectedElement.classList.contains('text')) {
                this.selectedElement.style.color = e.target.value;
            }
        });
        
        fontFamily.addEventListener('change', (e) => {
            this.toolProperties.text.fontFamily = e.target.value;
            this.fontFamily = e.target.value;
            if (this.selectedElement && this.selectedElement.classList.contains('text')) {
                this.selectedElement.style.fontFamily = e.target.value;
            }
        });
        
        if (fontSizeSlider && fontSizeValue) {
            fontSizeSlider.addEventListener('input', (e) => {
                const size = e.target.value;
                this.toolProperties.text.fontSize = `${size}px`;
                this.fontSize = `${size}px`;
                fontSizeValue.textContent = `${size}px`;
                
                if (this.selectedElement && this.selectedElement.classList.contains('text')) {
                    this.selectedElement.style.fontSize = `${size}px`;
                }
            });
        }
        
        if (textOpacitySlider && textOpacityValue) {
            textOpacitySlider.addEventListener('input', (e) => {
                const opacity = e.target.value / 100;
                this.toolProperties.text.opacity = opacity;
                textOpacityValue.textContent = `${e.target.value}%`;
                
                if (this.selectedElement && this.selectedElement.classList.contains('text')) {
                    this.selectedElement.style.opacity = opacity;
                }
            });
        }
        
        if (textBackgroundToggle) {
            textBackgroundToggle.addEventListener('change', (e) => {
                this.toolProperties.text.transparent = e.target.checked;
                if (this.selectedElement && this.selectedElement.classList.contains('text')) {
                    this.selectedElement.style.background = e.target.checked ? 'transparent' : 'white';
                }
            });
        }

        // Shape panel
        const shapeColorPicker = document.getElementById('shapeColorPicker');
        const shapeLineWidthSlider = document.getElementById('shapeLineWidthSlider');
        const shapeFillToggle = document.getElementById('shapeFillToggle');
        const shapeOpacitySlider = document.getElementById('shapeOpacitySlider');
        const shapeOpacityValue = document.getElementById('shapeOpacityValue');
        
        // Set initial values based on tool properties
        if (shapeColorPicker) shapeColorPicker.value = this.toolProperties.shape.color;
        if (shapeLineWidthSlider) shapeLineWidthSlider.value = this.toolProperties.shape.lineWidth;
        if (shapeFillToggle) shapeFillToggle.checked = this.toolProperties.shape.fill;
        if (shapeOpacitySlider) shapeOpacitySlider.value = this.toolProperties.shape.opacity * 100;
        if (shapeOpacityValue) shapeOpacityValue.textContent = `${this.toolProperties.shape.opacity * 100}%`;
        
        // Setup shape type buttons
        document.querySelectorAll('.shape-btn').forEach(btn => {
            // Set active state for the currently selected shape
            if (btn.dataset.shape === this.toolProperties.shape.type) {
                btn.classList.add('active');
            }
            
            btn.addEventListener('click', (e) => {
                const shapeType = e.currentTarget.dataset.shape;
                this.selectedShape = shapeType;
                this.toolProperties.shape.type = shapeType;
                
                // Update active state
                document.querySelectorAll('.shape-btn').forEach(b => b.classList.remove('active'));
                e.currentTarget.classList.add('active');
                
                // Update selected element if it's a shape
                if (this.selectedElement && this.selectedElement.classList.contains('shape')) {
                    this.selectedElement.dataset.shapeType = shapeType;
                    
                    // Re-render the shape with the new type
                    const width = parseInt(this.selectedElement.style.width);
                    const height = parseInt(this.selectedElement.style.height);
                    const color = this.selectedElement.style.borderColor;
                    const lineWidth = parseInt(this.selectedElement.style.borderWidth);
                    const fill = this.selectedElement.style.backgroundColor !== '';
                    
                    // Apply shape-specific styling
                    if (shapeType === 'rectangle') {
                        this.selectedElement.style.borderRadius = '0';
                    } else if (shapeType === 'circle') {
                        this.selectedElement.style.borderRadius = '50%';
                    } else if (shapeType === 'line' || shapeType === 'arrow') {
                        // Specialized rendering for lines and arrows...
                    }
                }
            });
        });
        
        shapeColorPicker.addEventListener('input', (e) => {
            this.toolProperties.shape.color = e.target.value;
            
            // Update selected element if it's a shape
            if (this.selectedElement && this.selectedElement.classList.contains('shape')) {
                this.selectedElement.style.borderColor = e.target.value;
                if (this.toolProperties.shape.fill) {
                    this.selectedElement.style.backgroundColor = e.target.value;
                }
            }
            
            // If drawing a shape, update the preview
            if (this.isDrawingShape) {
                this.redrawCanvas();
            }
        });
        
        shapeLineWidthSlider.addEventListener('input', (e) => {
            this.toolProperties.shape.lineWidth = parseInt(e.target.value);
            
            // Update selected element if it's a shape
            if (this.selectedElement && this.selectedElement.classList.contains('shape')) {
                this.selectedElement.style.borderWidth = `${e.target.value}px`;
            }
            
            // If drawing a shape, update the preview
            if (this.isDrawingShape) {
                this.redrawCanvas();
            }
        });
        
        shapeFillToggle.addEventListener('change', (e) => {
            this.toolProperties.shape.fill = e.target.checked;
            
            // Update selected element if it's a shape
            if (this.selectedElement && this.selectedElement.classList.contains('shape')) {
                if (e.target.checked) {
                    this.selectedElement.style.backgroundColor = this.toolProperties.shape.color;
                } else {
                    this.selectedElement.style.backgroundColor = '';
                }
            }
            
            // If drawing a shape, update the preview
            if (this.isDrawingShape) {
                this.redrawCanvas();
            }
        });
        
        if (shapeOpacitySlider && shapeOpacityValue) {
            shapeOpacitySlider.addEventListener('input', (e) => {
                const opacity = e.target.value / 100;
                this.toolProperties.shape.opacity = opacity;
                shapeOpacityValue.textContent = `${e.target.value}%`;
                
                // Update selected element if it's a shape
                if (this.selectedElement && this.selectedElement.classList.contains('shape')) {
                    this.selectedElement.style.opacity = opacity;
                }
                
                // If drawing a shape, update the preview
                if (this.isDrawingShape) {
                    this.redrawCanvas();
                }
            });
        }

        // Sticker and Emoji panels
        ['sticker', 'emoji'].forEach(tool => {
            // Set initial values based on tool properties
            const sizeSlider = document.getElementById(`${tool}SizeSlider`);
            const sizeValue = document.getElementById(`${tool}SizeValue`);
            const opacitySlider = document.getElementById(`${tool}OpacitySlider`);
            const opacityValue = document.getElementById(`${tool}OpacityValue`);
            
            if (sizeSlider) sizeSlider.value = this.toolProperties[tool].size;
            if (sizeValue) sizeValue.textContent = `${this.toolProperties[tool].size}px`;
            if (opacitySlider) opacitySlider.value = this.toolProperties[tool].opacity * 100;
            if (opacityValue) opacityValue.textContent = `${this.toolProperties[tool].opacity * 100}%`;
            
            // Setup sticker/emoji type buttons
            document.querySelectorAll(`.${tool}-btn`).forEach(btn => {
                // Set active state for the currently selected sticker/emoji
                if (btn.dataset[tool] === this.toolProperties[tool].type) {
                    btn.classList.add('active');
                }
                
                btn.addEventListener('click', (e) => {
                    const type = e.currentTarget.dataset[tool];
                    this.toolProperties[tool].type = type;
                    this.toolProperties[tool].isCustom = false;
                    
                    // Update active state
                    document.querySelectorAll(`.${tool}-btn, .custom-${tool}-btn`).forEach(b => b.classList.remove('active'));
                    e.currentTarget.classList.add('active');
                    
                    // Update selected element if it's a sticker/emoji
                    if (this.selectedElement && this.selectedElement.classList.contains('emoji-sticker')) {
                        // Only update if we're in the correct tool mode
                        const content = this.selectedElement.textContent;
                        const isSticker = ['⭐', '❤️', '✅', '❌', '🏠', '🔑', '📍', '💵'].includes(content);
                        
                        if ((tool === 'sticker' && isSticker) || (tool === 'emoji' && !isSticker)) {
                            this.selectedElement.textContent = type;
                            this.selectedElement.style.backgroundImage = '';
                        }
                    }
                });
            });
            
            if (sizeSlider && sizeValue) {
                sizeSlider.addEventListener('input', (e) => {
                    const size = parseInt(e.target.value);
                    this.toolProperties[tool].size = size;
                    sizeValue.textContent = `${size}px`;
                    
                    // Update selected element if it's a sticker/emoji
                    if (this.selectedElement && this.selectedElement.classList.contains('emoji-sticker')) {
                        // Only update if we're in the correct tool mode
                        const content = this.selectedElement.textContent;
                        const isSticker = ['⭐', '❤️', '✅', '❌', '🏠', '🔑', '📍', '💵'].includes(content);
                        const isCustom = this.selectedElement.style.backgroundImage !== '';
                        
                        if ((tool === 'sticker' && (isSticker || isCustom)) || 
                            (tool === 'emoji' && (!isSticker || isCustom))) {
                            this.selectedElement.style.fontSize = `${size}px`;
                            this.selectedElement.style.width = `${size}px`;
                            this.selectedElement.style.height = `${size}px`;
                        }
                    }
                });
            }
            
            if (opacitySlider && opacityValue) {
                opacitySlider.addEventListener('input', (e) => {
                    const opacity = e.target.value / 100;
                    this.toolProperties[tool].opacity = opacity;
                    opacityValue.textContent = `${e.target.value}%`;
                    
                    // Update selected element if it's a sticker/emoji
                    if (this.selectedElement && this.selectedElement.classList.contains('emoji-sticker')) {
                        // Only update if we're in the correct tool mode
                        const content = this.selectedElement.textContent;
                        const isSticker = ['⭐', '❤️', '✅', '❌', '🏠', '🔑', '📍', '💵'].includes(content);
                        const isCustom = this.selectedElement.style.backgroundImage !== '';
                        
                        if ((tool === 'sticker' && (isSticker || isCustom)) || 
                            (tool === 'emoji' && (!isSticker || isCustom))) {
                            this.selectedElement.style.opacity = opacity;
                        }
                    }
                });
            }
        });

        // Grid Properties Panel
        const gridSizeSlider = document.getElementById('gridSizeSlider');
        const gridOpacitySlider = document.getElementById('gridOpacitySlider');
        const gridSizeValue = document.getElementById('gridSizeValue');
        const gridOpacityValue = document.getElementById('gridOpacityValue');
        
        if (gridSizeSlider) {
            gridSizeSlider.value = this.toolProperties.grid.size;
            if (gridSizeValue) gridSizeValue.textContent = `${this.toolProperties.grid.size}px`;
            
            gridSizeSlider.addEventListener('input', (e) => {
                const size = parseInt(e.target.value);
                this.toolProperties.grid.size = size;
                if (gridSizeValue) gridSizeValue.textContent = `${size}px`;
                this.updateGridSize();
            });
        }
        
        if (gridOpacitySlider) {
            gridOpacitySlider.value = this.toolProperties.grid.opacity * 100;
            if (gridOpacityValue) gridOpacityValue.textContent = `${Math.round(this.toolProperties.grid.opacity * 100)}%`;
            
            gridOpacitySlider.addEventListener('input', (e) => {
                const opacity = e.target.value / 100;
                this.toolProperties.grid.opacity = opacity;
                if (gridOpacityValue) gridOpacityValue.textContent = `${e.target.value}%`;
                
                // Apply opacity directly to the grid-overlay element
                const gridOverlay = document.querySelector('.grid-overlay');
                if (gridOverlay && this.gridVisible) {
                    gridOverlay.style.opacity = opacity;
                }
                
                // Also set CSS variable for future use
                document.documentElement.style.setProperty('--grid-opacity', opacity);
            });
        }
    }

    setTool(tool) {
        console.log('Setting tool:', tool);
        
        // Hide all property panels
        document.querySelectorAll('.property-panel').forEach(panel => {
            panel.classList.remove('active');
        });
        
        // Deactivate all tool buttons
        document.querySelectorAll('.tool-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        
        // Activate the selected tool button
        const toolBtn = document.querySelector(`.tool-btn[data-tool="${tool}"]`);
        if (toolBtn) {
            toolBtn.classList.add('active');
        }
        
        // Special handling for specific tools
        if (tool === 'grid') {
            // Toggle grid visibility
            this.toggleGrid();
            
            // Revert to previous tool
            if (this.previousTool) {
                setTimeout(() => {
                    this.setTool(this.previousTool);
                }, 100);
            }
            return;
        } else if (tool === 'undo') {
            this.undo();
            if (this.previousTool) {
                setTimeout(() => {
                    this.setTool(this.previousTool);
                }, 100);
            }
            return;
        } else if (tool === 'redo') {
            this.redo();
            if (this.previousTool) {
                setTimeout(() => {
                    this.setTool(this.previousTool);
                }, 100);
            }
            return;
        } else if (tool === 'clear') {
            if (confirm('Are you sure you want to clear the canvas?')) {
                this.clearCanvas();
            }
            if (this.previousTool) {
                setTimeout(() => {
                    this.setTool(this.previousTool);
                }, 100);
            }
            return;
        }
        
        // Show the corresponding property panel
        const propertyPanel = document.getElementById(`${tool}-panel`);
        if (propertyPanel) {
            propertyPanel.classList.add('active');
            this.activePropertyPanel = propertyPanel;
        }
        
        // Set the current tool
        this.currentTool = tool;
        
        // Set appropriate cursor based on the tool
        switch (tool) {
            case 'pen':
            case 'highlighter':
                this.canvas.style.cursor = 'crosshair';
                break;
            case 'eraser':
                this.canvas.style.cursor = `url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="${this.toolProperties.eraser.size}" height="${this.toolProperties.eraser.size}" viewBox="0 0 ${this.toolProperties.eraser.size} ${this.toolProperties.eraser.size}"><circle cx="${this.toolProperties.eraser.size/2}" cy="${this.toolProperties.eraser.size/2}" r="${this.toolProperties.eraser.size/2}" fill="rgba(255,0,0,0.3)"/></svg>') ${this.toolProperties.eraser.size/2} ${this.toolProperties.eraser.size/2}, auto`;
                break;
            case 'text':
                this.canvas.style.cursor = 'text';
                break;
            case 'shape':
                this.canvas.style.cursor = 'crosshair';
                // Set the selectedShape if it's not already set
                if (!this.selectedShape) {
                    this.selectedShape = this.toolProperties.shape.type;
                }
                break;
            case 'sticker':
            case 'emoji':
                this.canvas.style.cursor = 'pointer';
                break;
            default:
                this.canvas.style.cursor = 'default';
        }
        
        // Store previous tool for tools that should revert
        if (!['undo', 'redo', 'clear', 'grid'].includes(tool)) {
            this.previousTool = tool;
        }
    }

    updatePropertyPanelValues(tool) {
        if (!this.selectedElement) return;

        switch (tool) {
            case 'text':
                const textColorPicker = document.getElementById('textColorPicker');
                const fontFamily = document.getElementById('fontFamily');
                const fontSizeSlider = document.getElementById('fontSizeSlider');
                const textOpacitySlider = document.getElementById('textOpacitySlider');
                const textBackgroundToggle = document.getElementById('textBackgroundToggle');

                if (textColorPicker) {
                    const color = this.selectedElement.style.color || this.toolProperties.text.color;
                    textColorPicker.value = color;
                    this.toolProperties.text.color = color;
                    this.color = color;
                }
                
                if (fontFamily) {
                    const family = this.selectedElement.style.fontFamily || this.toolProperties.text.fontFamily;
                    fontFamily.value = family.replace(/['"]/g, '');
                    this.toolProperties.text.fontFamily = family;
                    this.fontFamily = family;
                }
                
                if (fontSizeSlider) {
                    const fontSize = parseInt(this.selectedElement.style.fontSize) || parseInt(this.toolProperties.text.fontSize);
                    fontSizeSlider.value = fontSize;
                    document.getElementById('fontSizeValue').textContent = `${fontSize}px`;
                    this.toolProperties.text.fontSize = `${fontSize}px`;
                    this.fontSize = `${fontSize}px`;
                }
                
                if (textOpacitySlider) {
                    const opacity = Math.round((parseFloat(this.selectedElement.style.opacity) || this.toolProperties.text.opacity) * 100);
                    textOpacitySlider.value = opacity;
                    document.getElementById('textOpacityValue').textContent = `${opacity}%`;
                    this.toolProperties.text.opacity = opacity / 100;
                }
                
                if (textBackgroundToggle) {
                    const isTransparent = this.selectedElement.style.background === 'transparent';
                    textBackgroundToggle.checked = isTransparent;
                    this.toolProperties.text.transparent = isTransparent;
                }
                break;

            case 'shape':
                const shapeColorPicker = document.getElementById('shapeColorPicker');
                const shapeLineWidthSlider = document.getElementById('shapeLineWidthSlider');
                const shapeFillToggle = document.getElementById('shapeFillToggle');
                const shapeOpacitySlider = document.getElementById('shapeOpacitySlider');

                if (shapeColorPicker) {
                    const color = this.selectedElement.style.borderColor || this.toolProperties.shape.color;
                    shapeColorPicker.value = color;
                    this.toolProperties.shape.color = color;
                    this.color = color;
                }
                
                if (shapeLineWidthSlider) {
                    const lineWidth = parseInt(this.selectedElement.style.borderWidth) || this.toolProperties.shape.lineWidth;
                    shapeLineWidthSlider.value = lineWidth;
                    this.toolProperties.shape.lineWidth = lineWidth;
                    this.size = lineWidth;
                }
                
                if (shapeFillToggle) {
                    const isFilled = this.selectedElement.style.backgroundColor !== 'transparent' && this.selectedElement.style.backgroundColor !== '';
                    shapeFillToggle.checked = isFilled;
                    this.toolProperties.shape.fill = isFilled;
                }
                
                if (shapeOpacitySlider && shapeOpacityValue) {
                    const opacity = Math.round((parseFloat(this.selectedElement.style.opacity) || this.toolProperties.shape.opacity) * 100);
                    shapeOpacitySlider.value = opacity;
                    document.getElementById('shapeOpacityValue').textContent = `${opacity}%`;
                    this.toolProperties.shape.opacity = opacity / 100;
                }
                
                // Update shape type based on the selected element
                if (this.selectedElement.dataset.shapeType) {
                    this.selectedShape = this.selectedElement.dataset.shapeType;
                    this.toolProperties.shape.type = this.selectedElement.dataset.shapeType;
                    
                    // Update shape buttons
                    document.querySelectorAll('.shape-btn').forEach(btn => {
                        btn.classList.remove('active');
                    });
                    const shapeBtn = document.querySelector(`.shape-btn[data-shape="${this.selectedShape}"]`);
                    if (shapeBtn) {
                        shapeBtn.classList.add('active');
                    }
                }
                break;

            case 'sticker':
            case 'emoji':
                const sizeSlider = document.getElementById(`${tool}SizeSlider`);
                const opacitySlider = document.getElementById(`${tool}OpacitySlider`);

                if (sizeSlider) {
                    const size = parseInt(this.selectedElement.style.fontSize) || this.toolProperties[tool].size;
                    sizeSlider.value = size;
                    this.toolProperties[tool].size = size;
                    this.size = size;
                }
                
                if (opacitySlider && opacityValue) {
                    const opacity = Math.round((parseFloat(this.selectedElement.style.opacity) || this.toolProperties[tool].opacity) * 100);
                    opacitySlider.value = opacity;
                    document.getElementById(`${tool}OpacityValue`).textContent = `${opacity}%`;
                    this.toolProperties[tool].opacity = opacity / 100;
                }
                
                // Update the selected sticker/emoji type based on content
                const content = this.selectedElement.textContent;
                this.toolProperties[tool].type = content;
                
                // Update sticker/emoji buttons
                document.querySelectorAll(`.${tool}-btn`).forEach(btn => {
                    btn.classList.remove('active');
                });
                const btn = document.querySelector(`.${tool}-btn[data-${tool}="${content}"]`);
                if (btn) {
                    btn.classList.add('active');
                }
                break;
            
            case 'pen':
                const penColorPicker = document.getElementById('penColorPicker');
                const penSizeSlider = document.getElementById('penSizeSlider');
                const penOpacitySlider = document.getElementById('penOpacitySlider');
                const penTipStyle = document.getElementById('penTipStyle');
                
                if (this.selectedElement.classList.contains('pen-stroke')) {
                    if (penColorPicker) {
                        // For pen strokes, we get the color from the background image
                        // Since we can't easily extract it, we'll use the stored property
                        const color = this.toolProperties.pen.color;
                        penColorPicker.value = color;
                        this.toolProperties.pen.color = color;
                        this.color = color;
                    }
                    
                    if (penSizeSlider) {
                        // For pen strokes, we get the size from the stored property
                        const size = this.toolProperties.pen.size;
                        penSizeSlider.value = size;
                        this.toolProperties.pen.size = size;
                        this.size = size;
                    }
                    
                    if (penOpacitySlider) {
                        const opacity = Math.round((parseFloat(this.selectedElement.style.opacity) || this.toolProperties.pen.opacity) * 100);
                        penOpacitySlider.value = opacity;
                        document.getElementById('penOpacityValue').textContent = `${opacity}%`;
                        this.toolProperties.pen.opacity = opacity / 100;
                    }
                    
                    if (penTipStyle && this.selectedElement.dataset.tipStyle) {
                        penTipStyle.value = this.selectedElement.dataset.tipStyle;
                        this.toolProperties.pen.tipStyle = this.selectedElement.dataset.tipStyle;
                    }
                }
                break;
            
            case 'highlighter':
                const highlighterColorPicker = document.getElementById('highlighterColorPicker');
                const highlighterSizeSlider = document.getElementById('highlighterSizeSlider');
                const highlighterOpacitySlider = document.getElementById('highlighterOpacitySlider');
                
                if (this.selectedElement.classList.contains('highlighter-stroke')) {
                    if (highlighterColorPicker) {
                        // For highlighter strokes, we get the color from the stored property
                        const color = this.toolProperties.highlighter.color;
                        highlighterColorPicker.value = color;
                        this.toolProperties.highlighter.color = color;
                        this.color = color;
                    }
                    
                    if (highlighterSizeSlider) {
                        // For highlighter strokes, we get the size from the stored property
                        const size = this.toolProperties.highlighter.size;
                        highlighterSizeSlider.value = size;
                        this.toolProperties.highlighter.size = size;
                        this.size = size;
                    }
                    
                    if (highlighterOpacitySlider) {
                        const opacity = Math.round((parseFloat(this.selectedElement.style.opacity) || this.toolProperties.highlighter.opacity) * 100);
                        highlighterOpacitySlider.value = opacity;
                        document.getElementById('highlighterOpacityValue').textContent = `${opacity}%`;
                        this.toolProperties.highlighter.opacity = opacity / 100;
                    }
                }
                break;
        }
    }

    getCenterPosition() {
        return {
            x: this.canvas.width / 2,
            y: this.canvas.height / 2
        };
    }

    startDrawing(e) {
        if (!this.drawingEnabled) return;
        
        const pos = this.getPosition(e);
        this.isDrawing = true;
        this.lastX = pos.x;
        this.lastY = pos.y;
        
        // For shape drawing
        if (this.currentTool === 'shape' && this.selectedShape) {
            this.isDrawingShape = true;
            this.shapeStartPos = { x: pos.x, y: pos.y };
        }
        
        // For text input
        if (this.currentTool === 'text') {
            this.textInput.style.display = 'block';
            this.textInput.style.left = `${pos.x}px`;
            this.textInput.style.top = `${pos.y}px`;
            this.textInputInput.focus();
        }
        
        // For stickers and emojis
        if ((this.currentTool === 'sticker' && this.toolProperties.sticker.type) || 
            (this.currentTool === 'emoji' && this.toolProperties.emoji.type)) {
            // Temporarily disable drawing to prevent duplicates
            this.drawingEnabled = false;
            
            const element = document.createElement('div');
            element.className = 'draggable emoji-sticker';
            
            if (this.currentTool === 'sticker') {
                element.style.fontSize = `${this.toolProperties.sticker.size}px`;
                element.style.opacity = this.toolProperties.sticker.opacity;
                element.textContent = this.toolProperties.sticker.type;
            } else {
                element.style.fontSize = `${this.toolProperties.emoji.size}px`;
                element.style.opacity = this.toolProperties.emoji.opacity;
                element.textContent = this.toolProperties.emoji.type;
            }
            
            this.addDraggableElement(element, pos.x, pos.y);
            
            // Re-enable drawing after a short delay
            setTimeout(() => {
                this.isDrawing = false;
                this.drawingEnabled = true;
            }, 100);
            
            return;
        }
        
        // For pen and highlighter, initialize the stroke data for direct canvas drawing
        if (this.currentTool === 'pen' || this.currentTool === 'highlighter') {
            // Make sure we're not already drawing a stroke
            if (this.currentStroke) {
                this.stopDrawing();
            }
            
            // Create a new stroke object
            this.currentStroke = {
                tool: this.currentTool,
                points: [{x: pos.x, y: pos.y}],
                properties: this.currentTool === 'pen' 
                    ? {...this.toolProperties.pen} 
                    : {...this.toolProperties.highlighter}
            };
            
            // Save current canvas state before drawing
            this.saveState();
            
            // Start drawing on the canvas directly
            this.ctx.save();
            this.ctx.beginPath();
            this.ctx.moveTo(pos.x, pos.y);
            
            if (this.currentTool === 'pen') {
                this.ctx.strokeStyle = this.toolProperties.pen.color;
                this.ctx.lineWidth = this.toolProperties.pen.size;
                this.ctx.lineCap = this.toolProperties.pen.tipStyle;
                this.ctx.lineJoin = 'round';
                this.ctx.globalAlpha = this.toolProperties.pen.opacity;
            } else { // highlighter
                this.ctx.strokeStyle = this.toolProperties.highlighter.color;
                this.ctx.lineWidth = this.toolProperties.highlighter.size;
                this.ctx.lineCap = 'square';
                this.ctx.lineJoin = 'round';
                this.ctx.globalAlpha = this.toolProperties.highlighter.opacity;
            }
        }
    }

    draw(e) {
        if (!this.isDrawing || !this.drawingEnabled) return;

        const pos = this.getPosition(e);
        
        if (this.isDrawingShape && this.selectedShape) {
            // Clear the canvas and redraw
            this.redrawCanvas();
            
            // Draw the shape preview
            this.ctx.strokeStyle = this.toolProperties.shape.color;
            this.ctx.lineWidth = this.toolProperties.shape.lineWidth;
            this.ctx.globalAlpha = this.toolProperties.shape.opacity;
            
            const width = pos.x - this.shapeStartPos.x;
            const height = pos.y - this.shapeStartPos.y;
            
            switch (this.selectedShape) {
                case 'rectangle':
                    if (this.toolProperties.shape.fill) {
                        this.ctx.fillStyle = this.toolProperties.shape.color;
                        this.ctx.fillRect(this.shapeStartPos.x, this.shapeStartPos.y, width, height);
                    }
                    this.ctx.strokeRect(this.shapeStartPos.x, this.shapeStartPos.y, width, height);
                    break;
                case 'circle':
                    const centerX = this.shapeStartPos.x + width / 2;
                    const centerY = this.shapeStartPos.y + height / 2;
                    const radius = Math.max(Math.abs(width), Math.abs(height)) / 2;
                    
                    this.ctx.beginPath();
                    this.ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
                    if (this.toolProperties.shape.fill) {
                        this.ctx.fillStyle = this.toolProperties.shape.color;
                        this.ctx.fill();
                    }
                    this.ctx.stroke();
                    break;
                case 'line':
                    this.ctx.beginPath();
                    this.ctx.moveTo(this.shapeStartPos.x, this.shapeStartPos.y);
                    this.ctx.lineTo(pos.x, pos.y);
                    this.ctx.stroke();
                    break;
                case 'arrow':
                    // Draw the line
                    this.ctx.beginPath();
                    this.ctx.moveTo(this.shapeStartPos.x, this.shapeStartPos.y);
                    this.ctx.lineTo(pos.x, pos.y);
                    this.ctx.stroke();
                    
                    // Calculate the arrow head
                    const angle = Math.atan2(pos.y - this.shapeStartPos.y, pos.x - this.shapeStartPos.x);
                    const headLength = 15; // Length of arrow head
                    
                    // Draw the arrow head
                    this.ctx.beginPath();
                    this.ctx.moveTo(pos.x, pos.y);
                    this.ctx.lineTo(
                        pos.x - headLength * Math.cos(angle - Math.PI / 6),
                        pos.y - headLength * Math.sin(angle - Math.PI / 6)
                    );
                    this.ctx.moveTo(pos.x, pos.y);
                    this.ctx.lineTo(
                        pos.x - headLength * Math.cos(angle + Math.PI / 6),
                        pos.y - headLength * Math.sin(angle + Math.PI / 6)
                    );
                    this.ctx.stroke();
                    break;
            }
            
            this.ctx.globalAlpha = 1;
        } else if ((this.currentTool === 'pen' || this.currentTool === 'highlighter') && this.currentStroke) {
            // Direct canvas drawing for pen and highlighter
            this.ctx.lineTo(pos.x, pos.y);
            this.ctx.stroke();
            
            // Keep the path open for continued drawing
            this.ctx.beginPath();
            this.ctx.moveTo(pos.x, pos.y);
            
            // Record the point for stroke history
            this.currentStroke.points.push({x: pos.x, y: pos.y});
        } else if (this.currentTool === 'eraser') {
            // Direct canvas eraser
            const eraserSize = this.toolProperties.eraser.size;
            
            // First check for draggable elements to erase
            const elementsToRemove = [];
            this.draggableElements.forEach(element => {
                const rect = element.getBoundingClientRect();
                const elementCenterX = rect.left + rect.width / 2;
                const elementCenterY = rect.top + rect.height / 2;
                
                const distance = Math.sqrt(
                    Math.pow(elementCenterX - e.clientX, 2) + 
                    Math.pow(elementCenterY - e.clientY, 2)
                );
                
                if (distance < eraserSize) {
                    elementsToRemove.push(element);
                }
            });
            
            elementsToRemove.forEach(element => {
                element.remove();
                this.draggableElements = this.draggableElements.filter(el => el !== element);
                
                if (this.selectedElement === element) {
                    this.selectedElement = null;
                }
            });
            
            // Then erase from canvas strokes
            // Use composite operation for visual feedback
            this.ctx.save();
            this.ctx.globalCompositeOperation = 'destination-out';
            this.ctx.beginPath();
            this.ctx.arc(pos.x, pos.y, eraserSize/2, 0, Math.PI * 2);
            this.ctx.fill();
            this.ctx.restore();
            
            // Mark strokes that intersect with the eraser for removal or modification
            const eraserX = pos.x;
            const eraserY = pos.y;
            const eraserRadius = eraserSize / 2;
            
            // Check each stroke to see if it intersects with the eraser
            this.canvasStrokes = this.canvasStrokes.filter(stroke => {
                // For simple intersection testing, check if any point in the stroke
                // is within the eraser radius
                let anyPointErased = false;
                
                for (let i = 0; i < stroke.points.length; i++) {
                    const point = stroke.points[i];
                    const distance = Math.sqrt(
                        Math.pow(point.x - eraserX, 2) + 
                        Math.pow(point.y - eraserY, 2)
                    );
                    
                    if (distance <= eraserRadius) {
                        anyPointErased = true;
                        break;
                    }
                }
                
                // If any point was erased, remove the entire stroke
                // (In a more sophisticated implementation, you could split the stroke)
                return !anyPointErased;
            });
            
            // Redraw the canvas to reflect the erased strokes
            this.redrawCanvas();
        }
        
        this.lastX = pos.x;
        this.lastY = pos.y;
    }

    stopDrawing() {
        // If not drawing, do nothing
        if (!this.isDrawing) return;
        
        if (this.isDrawingShape && this.selectedShape) {
            // Temporarily disable drawing to prevent duplicates
            this.drawingEnabled = false;
            
            // Create a shape element
            const shape = document.createElement('div');
            shape.classList.add('draggable', 'shape');
            shape.dataset.shapeType = this.selectedShape;
            
            // Calculate dimensions
            const width = Math.abs(this.lastX - this.shapeStartPos.x);
            const height = Math.abs(this.lastY - this.shapeStartPos.y);
            const left = Math.min(this.lastX, this.shapeStartPos.x);
            const top = Math.min(this.lastY, this.shapeStartPos.y);
            
            // Only create shapes with meaningful dimensions
            if (width > 5 && height > 5) {
                // Set shape properties
                shape.style.borderColor = this.toolProperties.shape.color;
                shape.style.borderWidth = `${this.toolProperties.shape.lineWidth}px`;
                shape.style.opacity = this.toolProperties.shape.opacity;
                
                if (this.toolProperties.shape.fill) {
                    shape.style.backgroundColor = this.toolProperties.shape.color;
                }
                
                // Add the shape to the canvas
                this.addDraggableElement(shape, left + width/2, top + height/2);
                shape.style.width = `${width}px`;
                shape.style.height = `${height}px`;
            }
            
            // Clear the canvas preview
            this.redrawCanvas();
            
            // Reset drawing shape state
            this.isDrawingShape = false;
            
            // Re-enable drawing after a short delay
            setTimeout(() => {
                this.drawingEnabled = true;
            }, 100);
        } else if ((this.currentTool === 'pen' || this.currentTool === 'highlighter') && this.currentStroke) {
            // For direct canvas drawing, add the stroke to our history
            if (this.currentStroke.points.length > 1) {
                this.canvasStrokes.push({...this.currentStroke});
                this.currentStroke = null;
                
                // Restore canvas drawing state
                this.ctx.restore();
                
                // Save state after completing the stroke
                this.saveState();
            }
        }
        
        // Reset drawing state
        this.isDrawing = false;
        this.isDrawingShape = false;
    }

    drawArrow(fromX, fromY, toX, toY) {
        const headLength = 20;
        const angle = Math.atan2(toY - fromY, toX - fromX);
        
        // Draw the line
        this.ctx.beginPath();
        this.ctx.moveTo(fromX, fromY);
        this.ctx.lineTo(toX, toY);
        this.ctx.stroke();
        
        // Draw the arrow head
        this.ctx.beginPath();
        this.ctx.moveTo(toX, toY);
        this.ctx.lineTo(
            toX - headLength * Math.cos(angle - Math.PI/6),
            toY - headLength * Math.sin(angle - Math.PI/6)
        );
        this.ctx.moveTo(toX, toY);
        this.ctx.lineTo(
            toX - headLength * Math.cos(angle + Math.PI/6),
            toY - headLength * Math.sin(angle + Math.PI/6)
        );
        this.ctx.stroke();
    }

    addSticker(pos) {
        if (!pos) {
            pos = this.getCenterPosition();
        }
        
        const sticker = document.createElement('div');
        sticker.classList.add('draggable', 'emoji-sticker');
        
        if (this.toolProperties.sticker.isCustom) {
            const imageUrl = this.customStickers.get(this.toolProperties.sticker.type);
            sticker.style.backgroundImage = `url(${imageUrl})`;
            sticker.style.backgroundSize = 'contain'; // Ensure the image is contained
            sticker.textContent = '';
            
            // Set fixed dimensions for custom stickers
            const size = this.toolProperties.sticker.size;
            sticker.style.width = `${size}px`;
            sticker.style.height = `${size}px`;
        } else {
            sticker.textContent = this.toolProperties.sticker.type;
            sticker.style.fontSize = `${this.toolProperties.sticker.size}px`;
            sticker.style.width = `${this.toolProperties.sticker.size}px`;
            sticker.style.height = `${this.toolProperties.sticker.size}px`;
        }
        
        sticker.style.opacity = this.toolProperties.sticker.opacity;
        
        this.addDraggableElement(sticker, pos.x, pos.y);
    }

    addEmoji(pos) {
        if (!pos) {
            pos = this.getCenterPosition();
        }
        
        const emoji = document.createElement('div');
        emoji.classList.add('draggable', 'emoji-sticker');
        
        if (this.toolProperties.emoji.isCustom) {
            const imageUrl = this.customEmojis.get(this.toolProperties.emoji.type);
            emoji.style.backgroundImage = `url(${imageUrl})`;
            emoji.style.backgroundSize = 'contain'; // Ensure the image is contained
            emoji.textContent = '';
            
            // Set fixed dimensions for custom emojis
            const size = this.toolProperties.emoji.size;
            emoji.style.width = `${size}px`;
            emoji.style.height = `${size}px`;
        } else {
            emoji.textContent = this.toolProperties.emoji.type;
            emoji.style.fontSize = `${this.toolProperties.emoji.size}px`;
            emoji.style.width = `${this.toolProperties.emoji.size}px`;
            emoji.style.height = `${this.toolProperties.emoji.size}px`;
        }
        
        emoji.style.opacity = this.toolProperties.emoji.opacity;
        
        this.addDraggableElement(emoji, pos.x, pos.y);
    }

    startTextInput(pos) {
        console.log('Starting text input at:', pos);
        
        // Position and show the text input
        this.textInput.style.display = 'block';
        this.textInput.style.left = pos.x + 'px';
        this.textInput.style.top = pos.y + 'px';
        
        // Set initial width based on tool properties
        const fontSize = parseInt(this.toolProperties.text.fontSize);
        this.textInput.style.minWidth = (fontSize * 10) + 'px';
        
        // Focus the input field
        this.textInputInput.value = ''; // Clear any previous text
        this.textInputInput.focus();
        
        // Add event listener for Enter key to add the text
        this.textInputInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.addText();
            }
        });
    }

    addText() {
        const text = this.textInputInput.value.trim();
        console.log('Adding text:', text);
        
        if (text) {
            // Create text element
            const element = document.createElement('div');
            element.className = 'draggable text';
            
            // Apply text styling from tool properties
            element.style.fontFamily = this.toolProperties.text.fontFamily;
            element.style.fontSize = this.toolProperties.text.fontSize;
            element.style.color = this.toolProperties.text.color;
            element.style.opacity = this.toolProperties.text.opacity;
            element.style.background = this.toolProperties.text.transparent ? 'transparent' : 'white';
            element.style.padding = '5px';
            element.style.whiteSpace = 'nowrap';
            element.textContent = text;
            
            // Get position from text input
            const x = parseInt(this.textInput.style.left);
            const y = parseInt(this.textInput.style.top);
            
            // Add to canvas
            this.addDraggableElement(element, x, y);
            
            // Save state
            this.saveState();
        }

        // Hide and reset text input
        this.textInput.style.display = 'none';
        this.textInputInput.value = '';
        
        // Remove event listener
        this.textInputInput.removeEventListener('keydown', null);
    }

    handleDraggableMouseDown(e) {
        // Prevent default to avoid text selection
        e.preventDefault();
        
        // Find the clicked element
        let clickedElement = null;
        let target = e.target;
        
        // Check if we clicked a control handle
        const isControlHandle = target.classList.contains('control-handle');
        const isRotateHandle = target.classList.contains('rotate-handle');
        
        if (isControlHandle || isRotateHandle) {
            // Get the parent draggable element
            clickedElement = target.closest('.draggable');
            
            if (isControlHandle) {
                this.isResizing = true;
                // Extract handle position (nw, n, ne, etc.) from dataset or class name
                this.resizeHandle = target.dataset.handle || 
                                    Array.from(target.classList)
                                        .find(cls => cls.startsWith('handle-'))?.replace('handle-', '') || 
                                    'se'; // Default to southeast if we can't determine
                
                // Get accurate element dimensions using getBoundingClientRect
                const rect = clickedElement.getBoundingClientRect();
                this.startPoint = { x: e.clientX, y: e.clientY };
                
                // Store initial size - use getBoundingClientRect for accuracy
                this.initialSize = { 
                    width: rect.width, 
                    height: rect.height 
                };
                
                // Store initial position - use getBoundingClientRect for accuracy
                this.initialPosition = { 
                    left: rect.left, 
                    top: rect.top 
                };
                
                console.log('Resize start:', {
                    handle: this.resizeHandle,
                    initialSize: this.initialSize,
                    initialPosition: this.initialPosition,
                    element: clickedElement
                });
            } else if (isRotateHandle) {
                this.isRotating = true;
                
                // Initialize rotation properties
                const rect = clickedElement.getBoundingClientRect();
                this.rotationCenter = {
                    x: rect.left + rect.width / 2,
                    y: rect.top + rect.height / 2
                };
                
                this.initialRotation = {
                    angle: Math.atan2(e.clientY - this.rotationCenter.y, e.clientX - this.rotationCenter.x) * (180 / Math.PI),
                    current: this.getElementRotation(clickedElement)
                };
            }
        } else {
            // Check if we clicked directly on a draggable element
            while (target && target !== document) {
                if (target.classList.contains('draggable')) {
                    clickedElement = target;
                    break;
                }
                target = target.parentNode;
            }
        }

        // If clicking a draggable element
        if (clickedElement) {
            // If clicking a different element than the currently selected one
            if (this.selectedElement !== clickedElement) {
                if (this.selectedElement) {
                    this.selectedElement.classList.remove('selected');
                }
                this.selectedElement = clickedElement;
                this.selectedElement.classList.add('selected');
                
                // Determine the tool type and update property panel
                let tool = '';
                if (clickedElement.classList.contains('text')) {
                    tool = 'text';
                } else if (clickedElement.classList.contains('shape')) {
                    tool = 'shape';
                } else if (clickedElement.classList.contains('emoji-sticker')) {
                    // Determine if it's a sticker or emoji based on the content
                    const content = clickedElement.textContent;
                    tool = ['⭐', '❤️', '✅', '❌', '🏠', '🔑', '📍', '💵'].includes(content) ? 'sticker' : 'emoji';
                } else if (clickedElement.classList.contains('pen-stroke')) {
                    tool = 'pen';
                } else if (clickedElement.classList.contains('highlighter-stroke')) {
                    tool = 'highlighter';
                }
                
                if (tool) {
                    this.currentTool = tool;
                    this.setTool(tool);
                    this.updatePropertyPanelValues(tool);
                }
            }
            
            if (!this.isResizing && !this.isRotating) {
                this.isDragging = true;
                const rect = clickedElement.getBoundingClientRect();
                this.dragOffset = {
                    x: e.clientX - rect.left,
                    y: e.clientY - rect.top
                };
            }
        } else {
            // Only deselect if clicking outside any draggable element
            if (this.selectedElement && !e.target.closest('.bounding-box')) {
                this.selectedElement.classList.remove('selected');
                this.selectedElement = null;
            }
        }
    }

    // Helper method to get the current rotation of an element
    getElementRotation(element) {
        const transform = element.style.transform || '';
        const match = transform.match(/rotate\(([-0-9.]+)deg\)/);
        return match ? parseFloat(match[1]) : 0;
    }

    handleDraggableMouseMove(e) {
        if (!this.isDragging && !this.isResizing && !this.isRotating) return;

        if (this.isDragging && this.selectedElement) {
            // Handle dragging
            const x = e.clientX - this.dragOffset.x;
            const y = e.clientY - this.dragOffset.y;
            
            this.selectedElement.style.left = `${x}px`;
            this.selectedElement.style.top = `${y}px`;
            
        } else if (this.isResizing && this.selectedElement && this.initialSize && this.startPoint) {
            // Get current mouse position
            const currentX = e.clientX;
            const currentY = e.clientY;
            
            // Calculate mouse movement with increased sensitivity
            const deltaX = (currentX - this.startPoint.x) * 3; // Increased sensitivity
            const deltaY = (currentY - this.startPoint.y) * 3; // Increased sensitivity
            
            // Get handle being used
            const handle = this.resizeHandle;
            
            // Get original dimensions and position
            const origWidth = this.initialSize.width;
            const origHeight = this.initialSize.height;
            const origLeft = this.initialPosition.left;
            const origTop = this.initialPosition.top;
            
            // Initialize new dimensions and position
            let newWidth = origWidth;
            let newHeight = origHeight;
            let newLeft = origLeft;
            let newTop = origTop;
            
            // Calculate new dimensions based on which handle is being dragged
            switch (handle) {
                case 'nw': // Northwest
                    newWidth = Math.max(origWidth - deltaX, 20);
                    newHeight = Math.max(origHeight - deltaY, 20);
                    newLeft = origLeft + (origWidth - newWidth);
                    newTop = origTop + (origHeight - newHeight);
                    break;
                case 'n': // North
                    newHeight = Math.max(origHeight - deltaY, 20);
                    newTop = origTop + (origHeight - newHeight);
                    break;
                case 'ne': // Northeast
                    newWidth = Math.max(origWidth + deltaX, 20);
                    newHeight = Math.max(origHeight - deltaY, 20);
                    newTop = origTop + (origHeight - newHeight);
                    break;
                case 'e': // East
                    newWidth = Math.max(origWidth + deltaX, 20);
                    break;
                case 'se': // Southeast
                    newWidth = Math.max(origWidth + deltaX, 20);
                    newHeight = Math.max(origHeight + deltaY, 20);
                    break;
                case 's': // South
                    newHeight = Math.max(origHeight + deltaY, 20);
                    break;
                case 'sw': // Southwest
                    newWidth = Math.max(origWidth - deltaX, 20);
                    newHeight = Math.max(origHeight + deltaY, 20);
                    newLeft = origLeft + (origWidth - newWidth);
                    break;
                case 'w': // West
                    newWidth = Math.max(origWidth - deltaX, 20);
                    newLeft = origLeft + (origWidth - newWidth);
                    break;
            }
            
            // Special handling for circles and emoji/stickers to maintain aspect ratio
            if ((this.selectedElement.classList.contains('shape') && 
                this.selectedElement.dataset.shapeType === 'circle') ||
                this.selectedElement.classList.contains('emoji-sticker')) {
                
                // Use the larger dimension to determine size
                const size = Math.max(newWidth, newHeight);
                
                // Calculate position adjustments to maintain center
                const widthDiff = size - newWidth;
                const heightDiff = size - newHeight;
                
                // Adjust position based on which handle is being dragged
                if (handle.includes('w')) {
                    newLeft -= widthDiff / 2;
                } else if (handle.includes('e')) {
                    newLeft -= widthDiff / 2;
                }
                
                if (handle.includes('n')) {
                    newTop -= heightDiff / 2;
                } else if (handle.includes('s')) {
                    newTop -= heightDiff / 2;
                }
                
                // Set new dimensions
                newWidth = size;
                newHeight = size;
            }
            
            // Apply new dimensions and position
            this.selectedElement.style.width = `${newWidth}px`;
            this.selectedElement.style.height = `${newHeight}px`;
            this.selectedElement.style.left = `${newLeft}px`;
            this.selectedElement.style.top = `${newTop}px`;
            
            // Special handling for text elements
            if (this.selectedElement.classList.contains('text')) {
                // Adjust font size proportionally
                const fontSize = Math.max(12, Math.min(newHeight * 0.8, newWidth * 0.2));
                this.selectedElement.style.fontSize = `${fontSize}px`;
            }
            
            // Update bounding box
            const boundingBox = this.selectedElement.querySelector('.bounding-box');
            if (boundingBox) {
                boundingBox.style.width = '100%';
                boundingBox.style.height = '100%';
                boundingBox.style.top = '0';
                boundingBox.style.left = '0';
            }
            
            // Update handles
            const handles = this.selectedElement.querySelectorAll('.control-handle');
            if (handles.length > 0) {
                // Handles are automatically positioned by CSS
            }
            
        } else if (this.isRotating && this.selectedElement) {
            // Handle rotation
            const currentAngle = Math.atan2(
                e.clientY - this.rotationCenter.y,
                e.clientX - this.rotationCenter.x
            ) * (180 / Math.PI);
            
            const angleDiff = currentAngle - this.initialRotation.angle;
            const newRotation = this.initialRotation.current + angleDiff;
            
            this.selectedElement.style.transform = `rotate(${newRotation}deg)`;
            this.selectedElement.style.transformOrigin = 'center center';
        }
    }

    handleDraggableMouseUp() {
        // Only remove flags, keep the element selected
        this.isDragging = false;
        this.isResizing = false;
        this.isRotating = false;
        
        // Save the state if we were modifying something
        this.saveState();
    }

    handleTouch(e) {
        e.preventDefault();
        const touch = e.touches[0];
        const mouseEvent = new MouseEvent(e.type === 'touchstart' ? 'mousedown' : 'mousemove', {
            clientX: touch.clientX,
            clientY: touch.clientY
        });
        this.canvas.dispatchEvent(mouseEvent);
    }

    getPosition(e) {
        const rect = this.canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        return { x, y };
    }

    saveState() {
        const state = {
            canvasImage: this.canvas.toDataURL(),
            canvasStrokes: [...this.canvasStrokes],
            draggableElements: [...this.draggableElements]
        };
        this.history.push(state);
        this.redoStack = [];
    }

    undo() {
        if (this.history.length > 0) {
            // Save current state to redo stack
            this.redoStack.push(this.captureCurrentState());
            
            // Load previous state
            const prevState = this.history.pop();
            this.restoreState(prevState);
        }
    }

    redo() {
        if (this.redoStack.length > 0) {
            // Save current state to history
            this.saveState();
            
            // Load next state
            const nextState = this.redoStack.pop();
            this.restoreState(nextState);
        }
    }

    loadState(state) {
        if (!state) return;
        
        // Load canvas image
        const img = new Image();
        img.onload = () => {
            this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
            this.ctx.drawImage(img, 0, 0);
        };
        img.src = state.canvasImage;
        
        // Load canvas strokes
        this.canvasStrokes = state.canvasStrokes ? [...state.canvasStrokes] : [];
        
        // Load draggable elements
        this.draggableElements = state.draggableElements ? [...state.draggableElements] : [];
        
        // Redraw all canvas strokes
        this.canvasStrokes.forEach(stroke => {
            if (stroke.points.length > 1) {
                this.ctx.save();
                
                // Set stroke properties
                if (stroke.tool === 'pen') {
                    this.ctx.strokeStyle = stroke.properties.color;
                    this.ctx.lineWidth = stroke.properties.size;
                    this.ctx.lineCap = stroke.properties.tipStyle;
                    this.ctx.lineJoin = 'round';
                    this.ctx.globalAlpha = stroke.properties.opacity;
                } else if (stroke.tool === 'highlighter') {
                    this.ctx.strokeStyle = stroke.properties.color;
                    this.ctx.lineWidth = stroke.properties.size;
                    this.ctx.lineCap = 'square';
                    this.ctx.lineJoin = 'round';
                    this.ctx.globalAlpha = stroke.properties.opacity;
                }
                
                // Draw the stroke
                this.ctx.beginPath();
                this.ctx.moveTo(stroke.points[0].x, stroke.points[0].y);
                for (let i = 1; i < stroke.points.length; i++) {
                    this.ctx.lineTo(stroke.points[i].x, stroke.points[i].y);
                }
                this.ctx.stroke();
                
                this.ctx.restore();
            }
        });
    }

    clearCanvas() {
        // Clear canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Clear strokes data
        this.canvasStrokes = [];
        
        // Remove all draggable elements
        this.draggableElements.forEach(element => element.remove());
        this.draggableElements = [];
        this.selectedElement = null;
        
        // Save the clear state
        this.saveState();
    }

    toggleGrid() {
        const gridOverlay = document.querySelector('.grid-overlay');
        if (!gridOverlay) return;
        
        // Toggle grid visibility
        this.gridVisible = !this.gridVisible;
        
        console.log('Toggling grid:', this.gridVisible);
        
        // Apply the toggle by adding/removing the visible class
        if (this.gridVisible) {
            gridOverlay.classList.add('visible');
            gridOverlay.style.display = 'block'; // Force display block
            
            // Update grid size and properties
            this.updateGridSize();
            
            // Show grid property panel
            const gridPanel = document.getElementById('grid-panel');
            if (gridPanel) {
                // Hide all other panels first
                document.querySelectorAll('.property-panel').forEach(panel => {
                    panel.classList.remove('active');
                });
                
                // Show grid panel
                gridPanel.classList.add('active');
                this.activePropertyPanel = gridPanel;
            }
        } else {
            gridOverlay.classList.remove('visible');
            gridOverlay.style.display = 'none'; // Force display none
            
            // Hide grid property panel if it's active
            const gridPanel = document.getElementById('grid-panel');
            if (gridPanel && gridPanel.classList.contains('active')) {
                gridPanel.classList.remove('active');
                this.activePropertyPanel = null;
            }
        }
    }

    updateGridSize() {
        const gridOverlay = document.querySelector('.grid-overlay');
        if (!gridOverlay) return;
        
        // Get grid size from tool properties
        const size = this.toolProperties.grid.size || 20;
        const opacity = this.toolProperties.grid.opacity || 0.7;
        
        console.log('Updating grid size:', size, 'opacity:', opacity);
        
        // Update grid size
        gridOverlay.style.backgroundSize = `${size}px ${size}px`;
        
        // Update grid opacity
        gridOverlay.style.opacity = opacity;
        
        // Update grid background image with darker lines
        gridOverlay.style.backgroundImage = `
            linear-gradient(rgba(0, 0, 0, 0.8) 1px, transparent 1px),
            linear-gradient(90deg, rgba(0, 0, 0, 0.8) 1px, transparent 1px)
        `;
    }

    redrawCanvas() {
        // Clear the canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Redraw all canvas strokes
        this.canvasStrokes.forEach(stroke => {
            if (stroke.points.length > 1) {
                this.ctx.save();
                
                // Set stroke properties
                if (stroke.tool === 'pen') {
                    this.ctx.strokeStyle = stroke.properties.color;
                    this.ctx.lineWidth = stroke.properties.size;
                    this.ctx.lineCap = stroke.properties.tipStyle;
                    this.ctx.lineJoin = 'round';
                    this.ctx.globalAlpha = stroke.properties.opacity;
                } else if (stroke.tool === 'highlighter') {
                    this.ctx.strokeStyle = stroke.properties.color;
                    this.ctx.lineWidth = stroke.properties.size;
                    this.ctx.lineCap = 'square';
                    this.ctx.lineJoin = 'round';
                    this.ctx.globalAlpha = stroke.properties.opacity;
                }
                
                // Draw the stroke
                this.ctx.beginPath();
                this.ctx.moveTo(stroke.points[0].x, stroke.points[0].y);
                for (let i = 1; i < stroke.points.length; i++) {
                    this.ctx.lineTo(stroke.points[i].x, stroke.points[i].y);
                }
                this.ctx.stroke();
                
                this.ctx.restore();
            }
        });
    }

    createBoundingBox(element) {
        // Remove any existing bounding box
        const existingBox = element.querySelector('.bounding-box');
        if (existingBox) {
            element.removeChild(existingBox);
        }
        
        // Create bounding box
        const boundingBox = document.createElement('div');
        boundingBox.className = 'bounding-box';
        element.appendChild(boundingBox);
        
        // Create rotation line
        const rotationLine = document.createElement('div');
        rotationLine.className = 'rotation-line';
        boundingBox.appendChild(rotationLine);
        
        // Create rotation handle
        const rotateHandle = document.createElement('div');
        rotateHandle.className = 'rotate-handle';
        boundingBox.appendChild(rotateHandle);
        
        // Create resize handles
        const handlePositions = ['nw', 'n', 'ne', 'e', 'se', 's', 'sw', 'w'];
        handlePositions.forEach(pos => {
            const handle = document.createElement('div');
            handle.className = `control-handle handle-${pos}`;
            boundingBox.appendChild(handle);
        });
        
        // Add event listeners for the element
        element.addEventListener('mousedown', this.handleDraggableMouseDown.bind(this));
        element.addEventListener('touchstart', this.handleTouch.bind(this), { passive: false });
    }

    addDraggableElement(element, x, y) {
        // Add appropriate class based on content type if not already added
        if (!element.classList.contains('text') && 
            !element.classList.contains('shape') && 
            !element.classList.contains('emoji-sticker') && 
            !element.classList.contains('pen-stroke') && 
            !element.classList.contains('highlighter-stroke')) {
            
            if (element.textContent && element.textContent.length <= 2) {
                element.classList.add('emoji-sticker');
            }
        }
        
        // Ensure draggable class is added
        if (!element.classList.contains('draggable')) {
            element.classList.add('draggable');
        }
        
        // Position element at click position or center if no position provided
        if (x !== undefined && y !== undefined) {
            this.canvas.parentElement.appendChild(element);
            element.style.left = (x - element.offsetWidth / 2) + 'px';
            element.style.top = (y - element.offsetHeight / 2) + 'px';
        } else {
            const center = this.getCenterPosition();
            this.canvas.parentElement.appendChild(element);
            element.style.left = (center.x - element.offsetWidth / 2) + 'px';
            element.style.top = (center.y - element.offsetHeight / 2) + 'px';
        }
        
        // Add bounding box with control handles
        this.createBoundingBox(element);
        
        this.draggableElements.push(element);
        
        // Make the element selected by default
        if (this.selectedElement) {
            this.selectedElement.classList.remove('selected');
        }
        this.selectedElement = element;
        element.classList.add('selected');
        
        // Update property panel based on element type
        let tool = '';
        if (element.classList.contains('text')) {
            tool = 'text';
        } else if (element.classList.contains('shape')) {
            tool = 'shape';
        } else if (element.classList.contains('emoji-sticker')) {
            // Determine if it's a sticker or emoji based on the content
            const content = element.textContent;
            tool = ['⭐', '❤️', '✅', '❌', '🏠', '🔑', '📍', '💵'].includes(content) ? 'sticker' : 'emoji';
        } else if (element.classList.contains('pen-stroke')) {
            tool = 'pen';
        } else if (element.classList.contains('highlighter-stroke')) {
            tool = 'highlighter';
        }
        
        if (tool) {
            this.currentTool = tool;
            this.setTool(tool);
            this.updatePropertyPanelValues(tool);
        }
        
        this.saveState();
    }

    setupBackgroundControls() {
        const bgIcons = document.querySelectorAll('.bg-icon');
        const bgColorPicker = document.getElementById('bgColorPicker');
        
        // Set initial state
        this.updateCanvasBackground();
        
        // Handle background type selection
        bgIcons.forEach(icon => {
            icon.addEventListener('click', () => {
                // Remove active class from all icons
                bgIcons.forEach(i => i.classList.remove('active'));
                
                // Add active class to clicked icon
                icon.classList.add('active');
                
                // Update background state
                const bgType = icon.dataset.bgType;
                this.canvasBackground.solid = (bgType === 'solid');
                
                // Update background color in the picker
                if (bgType === 'solid') {
                    bgColorPicker.parentElement.classList.add('color-active');
                } else {
                    bgColorPicker.parentElement.classList.remove('color-active');
                }
                
                // Apply the background change
                this.updateCanvasBackground();
            });
        });
        
        // Handle color picker changes
        bgColorPicker.addEventListener('input', (e) => {
            this.canvasBackground.color = e.target.value;
            
            // If solid background is active, update immediately
            if (this.canvasBackground.solid) {
                this.updateCanvasBackground();
            }
        });
    }

    updateCanvasBackground() {
        const container = this.canvas.parentElement;
        
        if (this.canvasBackground.solid) {
            const rgba = this.hexToRGBA(this.canvasBackground.color, this.canvasBackground.opacity);
            container.style.background = rgba;
            
            // Update the color picker to match
            const bgColorPicker = document.getElementById('bgColorPicker');
            if (bgColorPicker) {
                bgColorPicker.value = this.canvasBackground.color;
            }
            
            // Update the icons
            const solidIcon = document.querySelector('.bg-icon.solid');
            const transparentIcon = document.querySelector('.bg-icon.transparent');
            
            if (solidIcon && transparentIcon) {
                solidIcon.classList.add('active');
                transparentIcon.classList.remove('active');
            }
        } else {
            container.style.background = 'transparent';
            
            // Update the icons
            const solidIcon = document.querySelector('.bg-icon.solid');
            const transparentIcon = document.querySelector('.bg-icon.transparent');
            
            if (solidIcon && transparentIcon) {
                solidIcon.classList.remove('active');
                transparentIcon.classList.add('active');
            }
        }
    }

    hexToRGBA(hex, alpha) {
        const r = parseInt(hex.slice(1, 3), 16);
        const g = parseInt(hex.slice(3, 5), 16);
        const b = parseInt(hex.slice(5, 7), 16);
        return `rgba(${r}, ${g}, ${b}, ${alpha})`;
    }

    updateStrokeElement(element, type, property, value) {
        // Get the stroke data from the element
        const width = parseInt(element.style.width);
        const height = parseInt(element.style.height);
        
        if (width <= 0 || height <= 0) return;
        
        // Update the dataset with the new property value
        if (property === 'color') {
            element.dataset.color = value;
        } else if (property === 'size') {
            element.dataset.size = value;
        } else if (property === 'tipStyle') {
            element.dataset.tipStyle = value;
        }
        
        // Create a canvas to redraw the stroke
        const strokeCanvas = document.createElement('canvas');
        strokeCanvas.width = width;
        strokeCanvas.height = height;
        const strokeCtx = strokeCanvas.getContext('2d');
        
        // Set the stroke properties
        if (type === 'pen') {
            strokeCtx.strokeStyle = element.dataset.color || this.toolProperties.pen.color;
            strokeCtx.lineWidth = element.dataset.size || this.toolProperties.pen.size;
            strokeCtx.lineCap = element.dataset.tipStyle || this.toolProperties.pen.tipStyle;
            strokeCtx.lineJoin = 'round';
            strokeCtx.globalAlpha = this.toolProperties.pen.opacity;
        } else if (type === 'highlighter') {
            strokeCtx.strokeStyle = element.dataset.color || this.toolProperties.highlighter.color;
            strokeCtx.lineWidth = element.dataset.size || this.toolProperties.highlighter.size;
            strokeCtx.lineCap = 'square';
            strokeCtx.lineJoin = 'round';
            strokeCtx.globalAlpha = this.toolProperties.highlighter.opacity;
        }
        
        // Get the stored points from the element's dataset
        const points = element.dataset.points ? JSON.parse(element.dataset.points) : [];
        
        if (points.length > 0) {
            // Redraw the stroke using the original points
            strokeCtx.beginPath();
            points.forEach(point => {
                strokeCtx.moveTo(point.x - element.dataset.minX, point.y - element.dataset.minY);
                strokeCtx.lineTo(point.x2 - element.dataset.minX, point.y2 - element.dataset.minY);
            });
            strokeCtx.stroke();
        } else {
            // Fallback if no points data (legacy strokes)
            strokeCtx.beginPath();
            strokeCtx.moveTo(0, 0);
            strokeCtx.lineTo(width, height);
            strokeCtx.moveTo(width, 0);
            strokeCtx.lineTo(0, height);
            strokeCtx.stroke();
        }
        
        // Update the element's background image
        element.style.backgroundImage = `url(${strokeCanvas.toDataURL()})`;
    }

    clearTempCanvas() {
        if (this.tempCanvas) {
            this.tempCtx.clearRect(0, 0, this.tempCanvas.width, this.tempCanvas.height);
            this.tempCanvas = null;
            this.tempCtx = null;
        }
    }

    initializeBaker() {
        // Sticker baker
        const stickerUploadBtn = document.getElementById('stickerUploadBtn');
        const stickerUpload = document.getElementById('stickerUpload');
        const customStickersGrid = document.querySelector('.custom-stickers-grid');

        stickerUploadBtn.addEventListener('click', () => {
            stickerUpload.click();
        });

        stickerUpload.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const id = `sticker-${Date.now()}`;
                    this.customStickers.set(id, e.target.result);
                    this.addCustomStickerButton(id, e.target.result, customStickersGrid);
                };
                reader.readAsDataURL(file);
            }
        });

        // Emoji baker
        const emojiUploadBtn = document.getElementById('emojiUploadBtn');
        const emojiUpload = document.getElementById('emojiUpload');
        const customEmojisGrid = document.querySelector('.custom-emojis-grid');

        emojiUploadBtn.addEventListener('click', () => {
            emojiUpload.click();
        });

        emojiUpload.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const id = `emoji-${Date.now()}`;
                    this.customEmojis.set(id, e.target.result);
                    this.addCustomEmojiButton(id, e.target.result, customEmojisGrid);
                };
                reader.readAsDataURL(file);
            }
        });
    }

    addCustomStickerButton(id, imageUrl, container) {
        const button = document.createElement('button');
        button.className = 'custom-sticker-btn';
        button.dataset.sticker = id;
        button.style.backgroundImage = `url(${imageUrl})`;
        button.style.backgroundSize = 'contain'; // Ensure the image is contained within the button
        
        button.addEventListener('click', () => {
            this.toolProperties.sticker.type = id;
            this.toolProperties.sticker.isCustom = true;
            
            document.querySelectorAll('.custom-sticker-btn, .sticker-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            button.classList.add('active');
            
            if (this.currentTool === 'sticker') {
                this.addSticker();
            }
        });
        
        container.appendChild(button);
    }

    addCustomEmojiButton(id, imageUrl, container) {
        const button = document.createElement('button');
        button.className = 'custom-emoji-btn';
        button.dataset.emoji = id;
        button.style.backgroundImage = `url(${imageUrl})`;
        button.style.backgroundSize = 'contain'; // Ensure the image is contained within the button
        
        button.addEventListener('click', () => {
            this.toolProperties.emoji.type = id;
            this.toolProperties.emoji.isCustom = true;
            
            document.querySelectorAll('.custom-emoji-btn, .emoji-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            button.classList.add('active');
            
            if (this.currentTool === 'emoji') {
                this.addEmoji();
            }
        });
        
        container.appendChild(button);
    }

    setupCanvasSettings() {
        const backgroundToggle = document.getElementById('canvasBackgroundToggle');
        const colorPicker = document.getElementById('canvasColorPicker');
        const opacitySlider = document.getElementById('canvasOpacitySlider');
        const opacityValue = document.getElementById('canvasOpacityValue');

        backgroundToggle.addEventListener('change', (e) => {
            this.canvasBackground.solid = e.target.checked;
            this.updateCanvasBackground();
        });

        colorPicker.addEventListener('input', (e) => {
            this.canvasBackground.color = e.target.value;
            if (this.canvasBackground.solid) {
                this.updateCanvasBackground();
            }
        });

        opacitySlider.addEventListener('input', (e) => {
            this.canvasBackground.opacity = e.target.value / 100;
            opacityValue.textContent = `${e.target.value}%`;
            this.updateCanvasBackground();
        });
    }

    // Capture current state including all elements
    captureCurrentState() {
        return {
            canvas: this.canvas.toDataURL(),
            elements: Array.from(document.querySelectorAll('.draggable')).map(el => el.outerHTML)
        };
    }

    // Restore a previous state
    restoreState(state) {
        // Restore canvas
        const img = new Image();
        img.onload = () => {
            this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
            this.ctx.drawImage(img, 0, 0);
        };
        img.src = state.canvas;
        
        // Clear all current elements
        document.querySelectorAll('.draggable').forEach(el => el.remove());
        
        // Restore elements
        const container = document.querySelector('.canvas-container');
        const fragment = document.createDocumentFragment();
        
        state.elements.forEach(htmlString => {
            const div = document.createElement('div');
            div.innerHTML = htmlString;
            
            // Get the element and reattach event listeners
            const element = div.firstChild;
            this.makeElementDraggable(element);
            
            fragment.appendChild(element);
        });
        
        container.appendChild(fragment);
        
        // Clear selection
        this.selectedElement = null;
    }

    selectElement(element) {
        // Deselect previously selected element
        if (this.selectedElement) {
            this.selectedElement.classList.remove('selected');
            this.removeHandles(this.selectedElement);
        }
        
        this.selectedElement = element;
        
        // Select new element if not null
        if (element) {
            element.classList.add('selected');
            this.addHandles(element);
            
            // Add delete button to the bounding box
            const boundingBox = element.querySelector('.bounding-box');
            if (boundingBox && !boundingBox.querySelector('.delete-btn')) {
                const deleteBtn = document.createElement('button');
                deleteBtn.className = 'delete-btn';
                deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
                deleteBtn.style.position = 'absolute';
                deleteBtn.style.top = '-25px';
                deleteBtn.style.right = '-5px';
                deleteBtn.style.background = '#ff4d4d';
                deleteBtn.style.color = 'white';
                deleteBtn.style.border = 'none';
                deleteBtn.style.borderRadius = '50%';
                deleteBtn.style.width = '20px';
                deleteBtn.style.height = '20px';
                deleteBtn.style.cursor = 'pointer';
                deleteBtn.style.zIndex = '10';
                deleteBtn.title = 'Delete element';
                
                deleteBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.deleteSelectedElement();
                });
                
                boundingBox.appendChild(deleteBtn);
            }
            
            // Update property panel based on element type
            if (element.classList.contains('text')) {
                this.showPropertyPanel('text');
            } else if (element.classList.contains('shape')) {
                this.showPropertyPanel('shape');
            } else if (element.classList.contains('emoji-sticker')) {
                if (element.dataset.type === 'sticker') {
                    this.showPropertyPanel('sticker');
                } else if (element.dataset.type === 'emoji') {
                    this.showPropertyPanel('emoji');
                }
            }
        }
    }

    deleteSelectedElement() {
        if (!this.selectedElement) return;
        
        const element = this.selectedElement;
        
        // Save state before deletion for undo
        this.saveState();
        
        // Remove element from DOM
        element.remove();
        this.selectedElement = null;
        
        // Show appropriate property panel for current tool
        this.showPropertyPanel(this.currentTool);
    }

    addHandles(element) {
        // Check if element already has a bounding box
        if (element.querySelector('.bounding-box')) return;
        
        // Create bounding box
        const boundingBox = document.createElement('div');
        boundingBox.className = 'bounding-box';
        element.appendChild(boundingBox);
        
        // Add resize handles
        const handlePositions = ['nw', 'n', 'ne', 'e', 'se', 's', 'sw', 'w'];
        handlePositions.forEach(pos => {
            const handle = document.createElement('div');
            handle.className = `control-handle handle-${pos}`;
            handle.dataset.handle = pos; // Set data-handle attribute
            boundingBox.appendChild(handle);
        });
        
        // Add rotation handle
        const rotateHandle = document.createElement('div');
        rotateHandle.className = 'rotate-handle';
        
        // Add rotation line
        const rotationLine = document.createElement('div');
        rotationLine.className = 'rotation-line';
        rotateHandle.appendChild(rotationLine);
        
        boundingBox.appendChild(rotateHandle);
        
        // Add delete button
        const deleteBtn = document.createElement('button');
        deleteBtn.className = 'delete-btn';
        deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
        deleteBtn.title = 'Delete element';
        
        deleteBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.deleteSelectedElement();
        });
        
        boundingBox.appendChild(deleteBtn);
    }

    removeHandles(element) {
        const boundingBox = element.querySelector('.bounding-box');
        if (boundingBox) {
            boundingBox.remove();
        }
    }

    showPropertyPanel(panel) {
        // Hide all panels first
        document.querySelectorAll('.property-panel').forEach(p => {
            p.classList.remove('active');
        });
        
        // Show the specified panel
        const panelElement = document.getElementById(`${panel}-panel`);
        if (panelElement) {
            panelElement.classList.add('active');
        }
    }

    hidePropertyPanel(panel) {
        const panelElement = document.getElementById(`${panel}-panel`);
        if (panelElement) {
            panelElement.classList.remove('active');
        }
    }

    // Add after handleDraggableMouseDown method

    // Helper method to maintain aspect ratio when resizing
    maintainAspectRatio(width, height, originalWidth, originalHeight) {
        const originalRatio = originalWidth / originalHeight;
        const newRatio = width / height;
        
        if (newRatio > originalRatio) {
            // Width is too large for height
            return { width: height * originalRatio, height };
        } else if (newRatio < originalRatio) {
            // Height is too large for width
            return { width, height: width / originalRatio };
        }
        
        // Ratio is the same
        return { width, height };
    }
}

// Initialize the app when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new AnnotationApp();
});