class AnnotationApp {
    constructor() {
        this.canvas = document.getElementById('annotationCanvas');
        this.ctx = this.canvas.getContext('2d', { alpha: true }); // Ensure alpha channel is supported
        this.currentTool = 'pen';
        this.isDrawing = false;
        this.lastX = 0;
        this.lastY = 0;
        this.color = '#000000';
        this.size = 5;
        this.history = [];
        this.redoStack = [];
        this.gridVisible = false;
        this.gridSize = 20; // Default grid size
        this.gridOpacity = 0.7; // Default grid opacity
        this.selectedShape = null;
        this.selectedSticker = null;
        this.selectedEmoji = null;
        this.fontFamily = 'Arial';
        this.fontSize = '16px';
        this.textInput = document.getElementById('textInput');
        this.textInputInput = this.textInput.querySelector('input');
        this.draggableElements = [];
        this.selectedElement = null;
        this.isDragging = false;
        this.isResizing = false;
        this.dragOffset = { x: 0, y: 0 };
        this.activePropertyPanel = null;
        this.isDrawingShape = false;
        this.currentShape = null;
        this.shapeStartPos = { x: 0, y: 0 };
        this.currentStroke = null;
        this.tempCanvas = null;
        this.tempCtx = null;
        this.drawingEnabled = true;
        this.canvasBackground = {
            solid: false,  // Default to transparent
            color: '#ffffff',
            opacity: 1
        };
        
        // Store canvas strokes data for direct canvas drawing
        this.canvasStrokes = [];

        // Tool-specific properties
        this.toolProperties = {
            pen: { 
                color: '#000000', 
                size: 5, 
                opacity: 1, 
                tipStyle: 'round' 
            },
            highlighter: { color: '#FFFF00', size: 20, opacity: 0.5 },
            eraser: { size: 20, mode: 'pixel' },
            text: { 
                color: '#000000', 
                fontFamily: 'Arial', 
                fontSize: '16px', 
                fontWeight: 'normal',
                opacity: 1, 
                transparent: false,
                backgroundColor: '#ffffff'
            },
            shape: { color: '#000000', lineWidth: 3, type: 'rectangle', opacity: 1, fill: false },
            sticker: { type: '⭐', size: 30, opacity: 1 },
            emoji: { type: '😊', size: 30, opacity: 1 },
            grid: { size: 20, opacity: 0.7 }
        };

        // Event handler references to prevent duplicate listeners
        this._penColorSwatchHandler = null;
        this._penCustomColorChangeHandler = null;
        this._penSizeChangeHandler = null;
        this._penOpacityChangeHandler = null;
        this._penTipStyleChangeHandler = null;

        // Add custom stickers/emojis storage
        this.customStickers = new Map();
        this.customEmojis = new Map();
        
        // Initialize baker functionality
        this.initializeBaker();

        this.initializeCanvas();
        this.setupEventListeners();
        this.setupToolbar();
        this.setupBackgroundControls();
        this.setupPropertyPanels();
        this.setupCanvasSettings();
        
        // Initialize the pen properties panel
        this.initializePenProperties();
        
        // Show the pen panel by default
        const penPanel = document.getElementById('pen-panel');
        if (penPanel) {
            penPanel.classList.add('active');
            this.activePropertyPanel = penPanel;
        }
    }

    initializeCanvas() {
        this.resizeCanvas();
        window.addEventListener('resize', () => this.resizeCanvas());
        
        // Initialize grid properties
        const gridOverlay = document.querySelector('.grid-overlay');
        if (gridOverlay) {
            // Set initial grid size
            const gridSize = this.toolProperties.grid.size || 20;
            gridOverlay.style.backgroundSize = `${gridSize}px ${gridSize}px`;
            
            // Set initial grid opacity
            const gridOpacity = this.toolProperties.grid.opacity || 0.7;
            gridOverlay.style.opacity = gridOpacity;
            
            // Set grid background image with darker lines
            gridOverlay.style.backgroundImage = `
                linear-gradient(rgba(0, 0, 0, 0.8) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 0, 0, 0.8) 1px, transparent 1px)
            `;
            
            // Set CSS variable for grid opacity
            document.documentElement.style.setProperty('--grid-opacity', gridOpacity);
        }
    }

    resizeCanvas() {
        this.canvas.width = window.innerWidth;
        this.canvas.height = window.innerHeight;
        this.redrawCanvas();
    }

    setupEventListeners() {
        // Canvas event listeners
        this.canvas.addEventListener('mousedown', this.startDrawing.bind(this));
        this.canvas.addEventListener('mousemove', this.draw.bind(this));
        this.canvas.addEventListener('mouseup', this.stopDrawing.bind(this));
        this.canvas.addEventListener('mouseout', this.stopDrawing.bind(this));

        // Touch event listeners for mobile
        this.canvas.addEventListener('touchstart', this.handleTouch.bind(this));
        this.canvas.addEventListener('touchmove', this.handleTouch.bind(this));
        this.canvas.addEventListener('touchend', this.stopDrawing.bind(this));

        // Window resize event
        window.addEventListener('resize', this.resizeCanvas.bind(this));
        
        // Toggle toolbar
        const toggleToolbarBtn = document.querySelector('.toggle-toolbar');
        const mainToolbar = document.querySelector('.main-toolbar');
        
        if (toggleToolbarBtn && mainToolbar) {
            toggleToolbarBtn.addEventListener('click', () => {
                mainToolbar.classList.toggle('collapsed');
                
                // Update toggle button icon
                const icon = toggleToolbarBtn.querySelector('i');
                if (icon) {
                    if (mainToolbar.classList.contains('collapsed')) {
                        icon.className = 'fas fa-chevron-right';
                } else {
                        icon.className = 'fas fa-chevron-left';
                    }
                }
            });
        }
        
        // Toggle property panels
        const togglePropertiesBtn = document.querySelector('.toggle-properties');
                const propertyPanels = document.querySelector('.property-panels');
        
        if (togglePropertiesBtn && propertyPanels) {
            togglePropertiesBtn.addEventListener('click', () => {
                propertyPanels.classList.toggle('collapsed');
                
                // Update toggle button icon
                const icon = togglePropertiesBtn.querySelector('i');
                if (icon) {
                if (propertyPanels.classList.contains('collapsed')) {
                        icon.className = 'fas fa-chevron-left';
                } else {
                        icon.className = 'fas fa-chevron-right';
                    }
                }
            });
        }

        // Text input events
        this.textInputInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.addText();
            }
        });

        // Draggable elements events
        document.addEventListener('mousedown', this.handleDraggableMouseDown.bind(this));
        document.addEventListener('mousemove', this.handleDraggableMouseMove.bind(this));
        document.addEventListener('mouseup', this.handleDraggableMouseUp.bind(this));
        
        // Add keyboard event for delete key
        document.addEventListener('keydown', (e) => {
            if ((e.key === 'Delete' || e.key === 'Backspace') && this.selectedElement) {
                e.preventDefault();
                this.deleteSelectedElement();
            }
        });
    }

    setupToolbar() {
        const toolButtons = document.querySelectorAll('.tool-btn');
        
        toolButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const tool = btn.getAttribute('data-tool');
                if (tool) {
                this.setTool(tool);
                }
            });
        });
        
        // Set the initial tool
        const initialToolBtn = document.querySelector(`.tool-btn[data-tool="${this.currentTool}"]`);
        if (initialToolBtn) {
            initialToolBtn.classList.add('active');
            
            // Show the corresponding property panel
            const propertyPanel = document.getElementById(`${this.currentTool}-panel`);
            if (propertyPanel) {
                propertyPanel.classList.add('active');
                this.activePropertyPanel = propertyPanel;
            }
        }
        
        // Initialize pen properties
        this.initializePenProperties();
    }

    setupPropertyPanels() {
        // Pen panel
        const penColorPicker = document.getElementById('penColorPicker');
        const penSizeSlider = document.getElementById('penSizeSlider');
        const penOpacitySlider = document.getElementById('penOpacitySlider');
        const penOpacityValue = document.getElementById('penOpacityValue');
        const penTipStyle = document.getElementById('penTipStyle');
        
        // Set initial values based on tool properties
        if (penColorPicker) penColorPicker.value = this.toolProperties.pen.color;
        if (penSizeSlider) penSizeSlider.value = this.toolProperties.pen.size;
        if (penOpacitySlider) penOpacitySlider.value = this.toolProperties.pen.opacity * 100;
        if (penOpacityValue) penOpacityValue.textContent = `${Math.round(this.toolProperties.pen.opacity * 100)}%`;
        if (penTipStyle) penTipStyle.value = this.toolProperties.pen.tipStyle || 'round';
        
        // Add event listeners for pen properties
        if (penColorPicker) {
        penColorPicker.addEventListener('input', (e) => {
                console.log('Pen color changed:', e.target.value);
            this.toolProperties.pen.color = e.target.value;
            
            // If currently drawing with pen, update the active stroke
            if (this.isDrawing && this.currentTool === 'pen' && this.currentStroke) {
                this.ctx.strokeStyle = e.target.value;
                this.currentStroke.properties.color = e.target.value;
            }
        });
        }
        
        if (penSizeSlider) {
        penSizeSlider.addEventListener('input', (e) => {
                console.log('Pen size changed:', e.target.value);
            this.toolProperties.pen.size = parseInt(e.target.value);
            
            // If currently drawing with pen, update the active stroke
            if (this.isDrawing && this.currentTool === 'pen' && this.currentStroke) {
                this.ctx.lineWidth = parseInt(e.target.value);
                this.currentStroke.properties.size = parseInt(e.target.value);
            }
        });
        }
        
        if (penOpacitySlider && penOpacityValue) {
            penOpacitySlider.addEventListener('input', (e) => {
                console.log('Pen opacity changed:', e.target.value);
                const opacity = parseInt(e.target.value) / 100;
                this.toolProperties.pen.opacity = opacity;
                penOpacityValue.textContent = `${e.target.value}%`;
                
                // If currently drawing with pen, update the active stroke
                if (this.isDrawing && this.currentTool === 'pen' && this.currentStroke) {
                    this.ctx.globalAlpha = opacity;
                    this.currentStroke.properties.opacity = opacity;
                }
            });
        }
        
        if (penTipStyle) {
            penTipStyle.addEventListener('change', (e) => {
                console.log('Pen tip style changed:', e.target.value);
                this.toolProperties.pen.tipStyle = e.target.value;
                
                // If currently drawing with pen, update the active stroke
                if (this.isDrawing && this.currentTool === 'pen' && this.currentStroke) {
                    this.currentStroke.properties.tipStyle = e.target.value;
                    
                    // Reset line dash array when changing styles
                    if (e.target.value !== 'dotted') {
                        this.ctx.setLineDash([]);
                    }
                }
            });
        }

        // Highlighter panel
        const highlighterColorPicker = document.getElementById('highlighterColorPicker');
        const highlighterSizeSlider = document.getElementById('highlighterSizeSlider');
        const highlighterOpacitySlider = document.getElementById('highlighterOpacitySlider');
        const highlighterOpacityValue = document.getElementById('highlighterOpacityValue');
        
        // Set initial values based on tool properties
        if (highlighterColorPicker) highlighterColorPicker.value = this.toolProperties.highlighter.color;
        if (highlighterSizeSlider) highlighterSizeSlider.value = this.toolProperties.highlighter.size;
        if (highlighterOpacitySlider) highlighterOpacitySlider.value = this.toolProperties.highlighter.opacity * 100;
        if (highlighterOpacityValue) highlighterOpacityValue.textContent = `${Math.round(this.toolProperties.highlighter.opacity * 100)}%`;
        
        highlighterColorPicker.addEventListener('input', (e) => {
            this.toolProperties.highlighter.color = e.target.value;
            
            // If currently drawing with highlighter, update the active stroke
            if (this.isDrawing && this.currentTool === 'highlighter' && this.currentStroke) {
                this.ctx.strokeStyle = e.target.value;
                this.currentStroke.properties.color = e.target.value;
            }
        });
        
        highlighterSizeSlider.addEventListener('input', (e) => {
            this.toolProperties.highlighter.size = parseInt(e.target.value);
            
            // If currently drawing with highlighter, update the active stroke
            if (this.isDrawing && this.currentTool === 'highlighter' && this.currentStroke) {
                this.ctx.lineWidth = parseInt(e.target.value);
                this.currentStroke.properties.size = parseInt(e.target.value);
            }
        });
        
        if (highlighterOpacitySlider && highlighterOpacityValue) {
            highlighterOpacitySlider.addEventListener('input', (e) => {
                const opacity = e.target.value / 100;
                this.toolProperties.highlighter.opacity = opacity;
                highlighterOpacityValue.textContent = `${e.target.value}%`;
                
                // If currently drawing with highlighter, update the active stroke
                if (this.isDrawing && this.currentTool === 'highlighter' && this.currentStroke) {
                    this.ctx.globalAlpha = opacity;
                    this.currentStroke.properties.opacity = opacity;
                }
            });
        }

        // Eraser panel
        const eraserSizeSlider = document.getElementById('eraserSizeSlider');
        const eraserSizeValue = document.getElementById('eraserSizeValue');
        const eraserPreviewCircle = document.querySelector('.eraser-preview-circle');
        const eraserModeSelect = document.getElementById('eraserModeSelect');
        
        // Set initial value based on tool properties
        if (eraserSizeSlider) eraserSizeSlider.value = this.toolProperties.eraser.size;
        if (eraserSizeValue) eraserSizeValue.textContent = `${this.toolProperties.eraser.size}px`;
        if (eraserPreviewCircle) {
            eraserPreviewCircle.style.width = `${this.toolProperties.eraser.size / 2}px`;
            eraserPreviewCircle.style.height = `${this.toolProperties.eraser.size / 2}px`;
        }
        if (eraserModeSelect) {
            eraserModeSelect.value = this.toolProperties.eraser.mode;
            this.updateEraserModeDescription(this.toolProperties.eraser.mode);
        }
        
        if (eraserSizeSlider) {
            eraserSizeSlider.addEventListener('input', (e) => {
                const size = parseInt(e.target.value);
                this.toolProperties.eraser.size = size;
                if (eraserSizeValue) eraserSizeValue.textContent = `${size}px`;
                
                // Update preview circle
                if (eraserPreviewCircle) {
                    eraserPreviewCircle.style.width = `${size / 2}px`;
                    eraserPreviewCircle.style.height = `${size / 2}px`;
                }
                
                // Update cursor if currently using eraser
                if (this.currentTool === 'eraser' && this.isDrawing) {
                    const cursor = document.querySelector('.eraser-cursor');
                    if (cursor) {
                        cursor.style.width = `${size}px`;
                        cursor.style.height = `${size}px`;
                    }
                }
            });
        }
        
        if (eraserModeSelect) {
            eraserModeSelect.addEventListener('change', (e) => {
                const mode = e.target.value;
                this.toolProperties.eraser.mode = mode;
                
                // Update cursor appearance based on mode
                if (this.currentTool === 'eraser') {
                    const cursor = document.querySelector('.eraser-cursor');
                    if (cursor) {
                        cursor.dataset.mode = mode;
                        
                        // Change cursor appearance based on mode
                        cursor.className = 'eraser-cursor'; // Reset classes
                        cursor.classList.add(`${mode}-mode`);
                    }
                }
                
                // Update mode description
                this.updateEraserModeDescription(mode);
                
                // Update preview to match mode
                if (eraserPreviewCircle) {
                    if (mode === 'stroke') {
                        eraserPreviewCircle.style.borderRadius = '5px';
                        eraserPreviewCircle.style.border = '2px solid rgba(200, 0, 0, 0.5)';
                        eraserPreviewCircle.style.backgroundColor = 'rgba(255, 100, 100, 0.15)';
                    } else if (mode === 'precision') {
                        eraserPreviewCircle.style.borderRadius = '50%';
                        eraserPreviewCircle.style.border = '2px solid rgba(0, 0, 200, 0.5)';
                        eraserPreviewCircle.style.backgroundColor = 'rgba(100, 100, 255, 0.15)';
                    } else { // pixel mode
                        eraserPreviewCircle.style.borderRadius = '50%';
                        eraserPreviewCircle.style.border = '2px solid rgba(0, 0, 0, 0.5)';
                        eraserPreviewCircle.style.backgroundColor = 'rgba(255, 255, 255, 0.7)';
                    }
                }
            });
        }

        // Text panel
        const textColorPicker = document.getElementById('textColorPicker');
        const textBgColorPicker = document.getElementById('textBgColorPicker');
        const fontFamily = document.getElementById('fontFamily');
        const fontSizeSlider = document.getElementById('fontSizeSlider');
        const fontSizeValue = document.getElementById('fontSizeValue');
        const fontWeightBtns = document.querySelectorAll('.font-weight-btn');
        const textOpacitySlider = document.getElementById('textOpacitySlider');
        const textOpacityValue = document.getElementById('textOpacityValue');
        const textBackgroundToggle = document.getElementById('textBackgroundToggle');
        
        // Set initial values based on tool properties
        if (textColorPicker) textColorPicker.value = this.toolProperties.text.color;
        if (textBgColorPicker) textBgColorPicker.value = this.toolProperties.text.backgroundColor || '#ffffff';
        if (fontFamily) fontFamily.value = this.toolProperties.text.fontFamily;
        if (fontSizeSlider) fontSizeSlider.value = parseInt(this.toolProperties.text.fontSize);
        if (fontSizeValue) fontSizeValue.textContent = this.toolProperties.text.fontSize;
        if (textOpacitySlider) textOpacitySlider.value = this.toolProperties.text.opacity * 100;
        if (textOpacityValue) textOpacityValue.textContent = `${this.toolProperties.text.opacity * 100}%`;
        if (textBackgroundToggle) textBackgroundToggle.checked = this.toolProperties.text.transparent;
        
        // Set active font weight button
        if (fontWeightBtns) {
            fontWeightBtns.forEach(btn => {
                if (btn.dataset.weight === this.toolProperties.text.fontWeight) {
                    btn.classList.add('active');
                } else {
                    btn.classList.remove('active');
                }
                
                // Add click event listener
                btn.addEventListener('click', (e) => {
                    // Update active state
                    fontWeightBtns.forEach(b => b.classList.remove('active'));
                    btn.classList.add('active');
                    
                    // Update tool property
                    this.toolProperties.text.fontWeight = btn.dataset.weight;
                    
                    // Update selected element if it's a text
                    if (this.selectedElement && this.selectedElement.classList.contains('text')) {
                        this.selectedElement.style.fontWeight = btn.dataset.weight;
                    }
                });
            });
        }
        
        textColorPicker.addEventListener('input', (e) => {
            this.toolProperties.text.color = e.target.value;
            if (this.selectedElement && this.selectedElement.classList.contains('text')) {
                this.selectedElement.style.color = e.target.value;
            }
        });
        
        if (textBgColorPicker) {
            textBgColorPicker.addEventListener('input', (e) => {
                this.toolProperties.text.backgroundColor = e.target.value;
                if (this.selectedElement && this.selectedElement.classList.contains('text') && !this.toolProperties.text.transparent) {
                    this.selectedElement.style.background = e.target.value;
                }
            });
        }
        
        fontFamily.addEventListener('change', (e) => {
            this.toolProperties.text.fontFamily = e.target.value;
            this.fontFamily = e.target.value;
            if (this.selectedElement && this.selectedElement.classList.contains('text')) {
                this.selectedElement.style.fontFamily = e.target.value;
            }
        });
        
        if (fontSizeSlider && fontSizeValue) {
            fontSizeSlider.addEventListener('input', (e) => {
                const size = e.target.value;
                this.toolProperties.text.fontSize = `${size}px`;
                this.fontSize = `${size}px`;
                fontSizeValue.textContent = `${size}px`;
                
                if (this.selectedElement && this.selectedElement.classList.contains('text')) {
                    this.selectedElement.style.fontSize = `${size}px`;
                }
            });
        }
        
        if (textOpacitySlider && textOpacityValue) {
            textOpacitySlider.addEventListener('input', (e) => {
                const opacity = e.target.value / 100;
                this.toolProperties.text.opacity = opacity;
                textOpacityValue.textContent = `${e.target.value}%`;
                
                if (this.selectedElement && this.selectedElement.classList.contains('text')) {
                    this.selectedElement.style.opacity = opacity;
                }
            });
        }
        
        if (textBackgroundToggle) {
            textBackgroundToggle.addEventListener('change', (e) => {
                this.toolProperties.text.transparent = e.target.checked;
                if (this.selectedElement && this.selectedElement.classList.contains('text')) {
                    this.selectedElement.style.background = e.target.checked ? 'transparent' : (this.toolProperties.text.backgroundColor || '#ffffff');
                }
            });
        }

        // Shape panel
        const shapeColorPicker = document.getElementById('shapeColorPicker');
        const shapeLineWidthSlider = document.getElementById('shapeLineWidthSlider');
        const shapeFillToggle = document.getElementById('shapeFillToggle');
        const shapeOpacitySlider = document.getElementById('shapeOpacitySlider');
        const shapeOpacityValue = document.getElementById('shapeOpacityValue');
        
        // Set initial values based on tool properties
        if (shapeColorPicker) shapeColorPicker.value = this.toolProperties.shape.color;
        if (shapeLineWidthSlider) shapeLineWidthSlider.value = this.toolProperties.shape.lineWidth;
        if (shapeFillToggle) shapeFillToggle.checked = this.toolProperties.shape.fill;
        if (shapeOpacitySlider) shapeOpacitySlider.value = this.toolProperties.shape.opacity * 100;
        if (shapeOpacityValue) shapeOpacityValue.textContent = `${this.toolProperties.shape.opacity * 100}%`;
        
        // Setup shape type buttons
        document.querySelectorAll('.shape-btn').forEach(btn => {
            // Set active state for the currently selected shape
            if (btn.dataset.shape === this.toolProperties.shape.type) {
                btn.classList.add('active');
            }
            
            btn.addEventListener('click', (e) => {
                const shapeType = e.currentTarget.dataset.shape;
                this.selectedShape = shapeType;
                this.toolProperties.shape.type = shapeType;
                
                // Update active state
                document.querySelectorAll('.shape-btn').forEach(b => b.classList.remove('active'));
                e.currentTarget.classList.add('active');
                
                // Update selected element if it's a shape
                if (this.selectedElement && this.selectedElement.classList.contains('shape')) {
                    this.selectedElement.dataset.shapeType = shapeType;
                    
                    // Re-render the shape with the new type
                    const width = parseInt(this.selectedElement.style.width);
                    const height = parseInt(this.selectedElement.style.height);
                    const color = this.selectedElement.style.borderColor;
                    const lineWidth = parseInt(this.selectedElement.style.borderWidth);
                    const fill = this.selectedElement.style.backgroundColor !== '';
                    
                    // Apply shape-specific styling
                    if (shapeType === 'rectangle') {
                        this.selectedElement.style.borderRadius = '0';
                    } else if (shapeType === 'circle') {
                        this.selectedElement.style.borderRadius = '50%';
                    } else if (shapeType === 'line' || shapeType === 'arrow') {
                        // Specialized rendering for lines and arrows...
                    }
                }
            });
        });
        
        shapeColorPicker.addEventListener('input', (e) => {
            this.toolProperties.shape.color = e.target.value;
            
            // Update selected element if it's a shape
            if (this.selectedElement && this.selectedElement.classList.contains('shape')) {
                this.selectedElement.style.borderColor = e.target.value;
                if (this.toolProperties.shape.fill) {
                    this.selectedElement.style.backgroundColor = e.target.value;
                }
            }
            
            // If drawing a shape, update the preview
            if (this.isDrawingShape) {
                this.redrawCanvas();
            }
        });
        
        shapeLineWidthSlider.addEventListener('input', (e) => {
            this.toolProperties.shape.lineWidth = parseInt(e.target.value);
            
            // Update selected element if it's a shape
            if (this.selectedElement && this.selectedElement.classList.contains('shape')) {
                this.selectedElement.style.borderWidth = `${e.target.value}px`;
            }
            
            // If drawing a shape, update the preview
            if (this.isDrawingShape) {
                this.redrawCanvas();
            }
        });
        
        shapeFillToggle.addEventListener('change', (e) => {
            this.toolProperties.shape.fill = e.target.checked;
            
            // Update selected element if it's a shape
            if (this.selectedElement && this.selectedElement.classList.contains('shape')) {
                if (e.target.checked) {
                    this.selectedElement.style.backgroundColor = this.toolProperties.shape.color;
                } else {
                    this.selectedElement.style.backgroundColor = '';
                }
            }
            
            // If drawing a shape, update the preview
            if (this.isDrawingShape) {
                this.redrawCanvas();
            }
        });
        
        if (shapeOpacitySlider && shapeOpacityValue) {
            shapeOpacitySlider.addEventListener('input', (e) => {
                const opacity = e.target.value / 100;
                this.toolProperties.shape.opacity = opacity;
                shapeOpacityValue.textContent = `${e.target.value}%`;
                
                // Update selected element if it's a shape
                if (this.selectedElement && this.selectedElement.classList.contains('shape')) {
                    this.selectedElement.style.opacity = opacity;
                }
                
                // If drawing a shape, update the preview
                if (this.isDrawingShape) {
                    this.redrawCanvas();
                }
            });
        }

        // Sticker and Emoji panels
        ['sticker', 'emoji'].forEach(tool => {
            // Set initial values based on tool properties
            const sizeSlider = document.getElementById(`${tool}SizeSlider`);
            const sizeValue = document.getElementById(`${tool}SizeValue`);
            const opacitySlider = document.getElementById(`${tool}OpacitySlider`);
            const opacityValue = document.getElementById(`${tool}OpacityValue`);
            
            if (sizeSlider) sizeSlider.value = this.toolProperties[tool].size;
            if (sizeValue) sizeValue.textContent = `${this.toolProperties[tool].size}px`;
            if (opacitySlider) opacitySlider.value = this.toolProperties[tool].opacity * 100;
            if (opacityValue) opacityValue.textContent = `${this.toolProperties[tool].opacity * 100}%`;
            
            // Setup sticker/emoji type buttons
            document.querySelectorAll(`.${tool}-btn`).forEach(btn => {
                // Set active state for the currently selected sticker/emoji
                if (btn.dataset[tool] === this.toolProperties[tool].type) {
                    btn.classList.add('active');
                }
                
                btn.addEventListener('click', (e) => {
                    const type = e.currentTarget.dataset[tool];
                    this.toolProperties[tool].type = type;
                    this.toolProperties[tool].isCustom = false;
                    
                    // Update active state
                    document.querySelectorAll(`.${tool}-btn, .custom-${tool}-btn`).forEach(b => b.classList.remove('active'));
                    e.currentTarget.classList.add('active');
                    
                    // Update selected element if it's a sticker/emoji
                    if (this.selectedElement && this.selectedElement.classList.contains('emoji-sticker')) {
                        // Only update if we're in the correct tool mode
                        const content = this.selectedElement.textContent;
                        const isSticker = ['⭐', '❤️', '✅', '❌', '🏠', '🔑', '📍', '💵'].includes(content);
                        
                        if ((tool === 'sticker' && isSticker) || (tool === 'emoji' && !isSticker)) {
                            this.selectedElement.textContent = type;
                            this.selectedElement.style.backgroundImage = '';
                        }
                    }
                });
            });
            
            if (sizeSlider && sizeValue) {
                sizeSlider.addEventListener('input', (e) => {
                    const size = parseInt(e.target.value);
                    this.toolProperties[tool].size = size;
                    sizeValue.textContent = `${size}px`;
                    
                    // Update selected element if it's a sticker/emoji
                    if (this.selectedElement && this.selectedElement.classList.contains('emoji-sticker')) {
                        // Only update if we're in the correct tool mode
                        const content = this.selectedElement.textContent;
                        const isSticker = ['⭐', '❤️', '✅', '❌', '🏠', '🔑', '📍', '💵'].includes(content);
                        const isCustom = this.selectedElement.style.backgroundImage !== '';
                        
                        if ((tool === 'sticker' && (isSticker || isCustom)) || 
                            (tool === 'emoji' && (!isSticker || isCustom))) {
                            this.selectedElement.style.fontSize = `${size}px`;
                            this.selectedElement.style.width = `${size}px`;
                            this.selectedElement.style.height = `${size}px`;
                        }
                    }
                });
            }
            
            if (opacitySlider && opacityValue) {
                opacitySlider.addEventListener('input', (e) => {
                    const opacity = e.target.value / 100;
                    this.toolProperties[tool].opacity = opacity;
                    opacityValue.textContent = `${e.target.value}%`;
                    
                    // Update selected element if it's a sticker/emoji
                    if (this.selectedElement && this.selectedElement.classList.contains('emoji-sticker')) {
                        // Only update if we're in the correct tool mode
                        const content = this.selectedElement.textContent;
                        const isSticker = ['⭐', '❤️', '✅', '❌', '🏠', '🔑', '📍', '💵'].includes(content);
                        const isCustom = this.selectedElement.style.backgroundImage !== '';
                        
                        if ((tool === 'sticker' && (isSticker || isCustom)) || 
                            (tool === 'emoji' && (!isSticker || isCustom))) {
                            this.selectedElement.style.opacity = opacity;
                        }
                    }
                });
            }
        });

        // Grid Properties Panel
        const gridSizeSlider = document.getElementById('gridSizeSlider');
        const gridOpacitySlider = document.getElementById('gridOpacitySlider');
        const gridSizeValue = document.getElementById('gridSizeValue');
        const gridOpacityValue = document.getElementById('gridOpacityValue');
        
        if (gridSizeSlider) {
            gridSizeSlider.value = this.toolProperties.grid.size;
            if (gridSizeValue) gridSizeValue.textContent = `${this.toolProperties.grid.size}px`;
            
            gridSizeSlider.addEventListener('input', (e) => {
                const size = parseInt(e.target.value);
                this.toolProperties.grid.size = size;
                if (gridSizeValue) gridSizeValue.textContent = `${size}px`;
                this.updateGridSize();
            });
        }
        
        if (gridOpacitySlider) {
            gridOpacitySlider.value = this.toolProperties.grid.opacity * 100;
            if (gridOpacityValue) gridOpacityValue.textContent = `${Math.round(this.toolProperties.grid.opacity * 100)}%`;
            
            gridOpacitySlider.addEventListener('input', (e) => {
                const opacity = e.target.value / 100;
                this.toolProperties.grid.opacity = opacity;
                if (gridOpacityValue) gridOpacityValue.textContent = `${e.target.value}%`;
                
                // Apply opacity directly to the grid-overlay element
                const gridOverlay = document.querySelector('.grid-overlay');
                if (gridOverlay && this.gridVisible) {
                    gridOverlay.style.opacity = opacity;
                }
                
                // Also set CSS variable for future use
                document.documentElement.style.setProperty('--grid-opacity', opacity);
            });
        }
    }

    setTool(tool) {
        console.log('Setting tool:', tool);
        
        // Special handling for utility tools
        if (tool === 'grid') {
            this.toggleGrid();
            return;
        } else if (tool === 'undo') {
            this.undo();
            return;
        } else if (tool === 'redo') {
            this.redo();
            return;
        } else if (tool === 'clear') {
            if (confirm('Are you sure you want to clear the canvas?')) {
                this.clearCanvas();
            }
            return;
        }
        
        // Hide all property panels first
        document.querySelectorAll('.property-panel').forEach(panel => {
            panel.classList.remove('active');
        });
        
        // Deactivate all tool buttons
        document.querySelectorAll('.tool-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        
        // Activate the selected tool button
        const toolBtn = document.querySelector(`.tool-btn[data-tool="${tool}"]`);
        if (toolBtn) {
            toolBtn.classList.add('active');
        }
        
        // Show the corresponding property panel
        const propertyPanel = document.getElementById(`${tool}-panel`);
        if (propertyPanel) {
            propertyPanel.classList.add('active');
            this.activePropertyPanel = propertyPanel;
            
            // Update property panel values based on current tool properties
            if (tool === 'pen') {
                console.log('Initializing pen properties panel');
                const penColorSwatches = document.getElementById('penColorSwatches');
                const penCustomColorPicker = document.getElementById('penCustomColorPicker');
                const penSizeSlider = document.getElementById('penSizeSlider');
                const penSizeValue = document.getElementById('penSizeValue');
                const penOpacitySlider = document.getElementById('penOpacitySlider');
                const penOpacityValue = document.getElementById('penOpacityValue');
                const penTipStyle = document.getElementById('penTipStyle');
                
                // Update color swatches
                if (penColorSwatches) {
                    const swatches = penColorSwatches.querySelectorAll('.color-swatch');
                    let foundMatch = false;
                    
                    swatches.forEach(swatch => {
                        if (swatch.dataset.color === this.toolProperties.pen.color) {
                            swatch.classList.add('active');
                            foundMatch = true;
        } else {
                            swatch.classList.remove('active');
                        }
                    });
                    
                    // If no matching swatch, activate custom and set its value
                    if (!foundMatch && penCustomColorPicker) {
                        const customSwatch = penColorSwatches.querySelector('.color-swatch.custom');
                        if (customSwatch) {
                            customSwatch.classList.add('active');
                            penCustomColorPicker.value = this.toolProperties.pen.color;
                        }
                    }
                }
                
                if (penCustomColorPicker) penCustomColorPicker.value = this.toolProperties.pen.color;
                if (penSizeSlider) penSizeSlider.value = this.toolProperties.pen.size;
                if (penSizeValue) penSizeValue.textContent = `${this.toolProperties.pen.size}px`;
                if (penOpacitySlider) penOpacitySlider.value = this.toolProperties.pen.opacity * 100;
                if (penOpacityValue) penOpacityValue.textContent = `${Math.round(this.toolProperties.pen.opacity * 100)}%`;
                if (penTipStyle) {
                    penTipStyle.value = this.toolProperties.pen.tipStyle || 'round';
                    console.log('Set pen tip style to:', penTipStyle.value);
                }
            } else if (tool === 'highlighter') {
                const highlighterColorPicker = document.getElementById('highlighterColorPicker');
                const highlighterSizeSlider = document.getElementById('highlighterSizeSlider');
                const highlighterOpacitySlider = document.getElementById('highlighterOpacitySlider');
                const highlighterOpacityValue = document.getElementById('highlighterOpacityValue');
                
                if (highlighterColorPicker) highlighterColorPicker.value = this.toolProperties.highlighter.color;
                if (highlighterSizeSlider) highlighterSizeSlider.value = this.toolProperties.highlighter.size;
                if (highlighterOpacitySlider) highlighterOpacitySlider.value = this.toolProperties.highlighter.opacity * 100;
                if (highlighterOpacityValue) highlighterOpacityValue.textContent = `${Math.round(this.toolProperties.highlighter.opacity * 100)}%`;
            }
            // ... other tools ...
        }
        
        // Set the current tool
        this.currentTool = tool;
        
        // Set appropriate cursor based on the tool
        switch (tool) {
            case 'pen':
            case 'highlighter':
                this.canvas.style.cursor = 'crosshair';
                break;
            case 'eraser':
                this.canvas.style.cursor = `url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="${this.toolProperties.eraser.size}" height="${this.toolProperties.eraser.size}" viewBox="0 0 ${this.toolProperties.eraser.size} ${this.toolProperties.eraser.size}"><circle cx="${this.toolProperties.eraser.size/2}" cy="${this.toolProperties.eraser.size/2}" r="${this.toolProperties.eraser.size/2}" fill="rgba(255,0,0,0.3)"/></svg>') ${this.toolProperties.eraser.size/2} ${this.toolProperties.eraser.size/2}, auto`;
                break;
            case 'text':
                this.canvas.style.cursor = 'text';
                break;
            case 'shape':
                this.canvas.style.cursor = 'crosshair';
                break;
            case 'sticker':
            case 'emoji':
                this.canvas.style.cursor = 'pointer';
                break;
            default:
                this.canvas.style.cursor = 'default';
        }
    }

    updatePropertyPanelValues(tool) {
        if (!this.selectedElement) return;

        switch (tool) {
            case 'text':
                const textColorPicker = document.getElementById('textColorPicker');
                const textBgColorPicker = document.getElementById('textBgColorPicker');
                const fontFamily = document.getElementById('fontFamily');
                const fontSizeSlider = document.getElementById('fontSizeSlider');
                const fontWeightBtns = document.querySelectorAll('.font-weight-btn');
                const textOpacitySlider = document.getElementById('textOpacitySlider');
                const textBackgroundToggle = document.getElementById('textBackgroundToggle');

                if (textColorPicker) {
                    const color = this.selectedElement.style.color || this.toolProperties.text.color;
                    textColorPicker.value = color;
                    this.toolProperties.text.color = color;
                    this.color = color;
                }
                
                if (textBgColorPicker) {
                    const bgColor = this.selectedElement.style.background || this.toolProperties.text.backgroundColor || '#ffffff';
                    textBgColorPicker.value = bgColor;
                    this.toolProperties.text.backgroundColor = bgColor;
                }
                
                if (fontFamily) {
                    const family = this.selectedElement.style.fontFamily || this.toolProperties.text.fontFamily;
                    fontFamily.value = family.replace(/['"]/g, '');
                    this.toolProperties.text.fontFamily = family;
                    this.fontFamily = family;
                }
                
                if (fontSizeSlider) {
                    const fontSize = parseInt(this.selectedElement.style.fontSize) || parseInt(this.toolProperties.text.fontSize);
                    fontSizeSlider.value = fontSize;
                    document.getElementById('fontSizeValue').textContent = `${fontSize}px`;
                    this.toolProperties.text.fontSize = `${fontSize}px`;
                    this.fontSize = `${fontSize}px`;
                }
                
                if (textOpacitySlider) {
                    const opacity = Math.round((parseFloat(this.selectedElement.style.opacity) || this.toolProperties.text.opacity) * 100);
                    textOpacitySlider.value = opacity;
                    document.getElementById('textOpacityValue').textContent = `${opacity}%`;
                    this.toolProperties.text.opacity = opacity / 100;
                }
                
                if (textBackgroundToggle) {
                    const isTransparent = this.selectedElement.style.background === 'transparent';
                    textBackgroundToggle.checked = isTransparent;
                    this.toolProperties.text.transparent = isTransparent;
                }
                
                if (fontWeightBtns && fontWeightBtns.length > 0) {
                    const weight = this.selectedElement.style.fontWeight || this.toolProperties.text.fontWeight || 'normal';
                    this.toolProperties.text.fontWeight = weight;
                    
                    // Update active state
                    fontWeightBtns.forEach(btn => {
                        if (btn.dataset.weight === weight) {
                            btn.classList.add('active');
                        } else {
                            btn.classList.remove('active');
                        }
                    });
                }
                break;

            case 'shape':
                const shapeColorPicker = document.getElementById('shapeColorPicker');
                const shapeLineWidthSlider = document.getElementById('shapeLineWidthSlider');
                const shapeFillToggle = document.getElementById('shapeFillToggle');
                const shapeOpacitySlider = document.getElementById('shapeOpacitySlider');

                if (shapeColorPicker) {
                    const color = this.selectedElement.style.borderColor || this.toolProperties.shape.color;
                    shapeColorPicker.value = color;
                    this.toolProperties.shape.color = color;
                    this.color = color;
                }
                
                if (shapeLineWidthSlider) {
                    const lineWidth = parseInt(this.selectedElement.style.borderWidth) || this.toolProperties.shape.lineWidth;
                    shapeLineWidthSlider.value = lineWidth;
                    this.toolProperties.shape.lineWidth = lineWidth;
                    this.size = lineWidth;
                }
                
                if (shapeFillToggle) {
                    const isFilled = this.selectedElement.style.backgroundColor !== 'transparent' && this.selectedElement.style.backgroundColor !== '';
                    shapeFillToggle.checked = isFilled;
                    this.toolProperties.shape.fill = isFilled;
                }
                
                if (shapeOpacitySlider && shapeOpacityValue) {
                    const opacity = Math.round((parseFloat(this.selectedElement.style.opacity) || this.toolProperties.shape.opacity) * 100);
                    shapeOpacitySlider.value = opacity;
                    document.getElementById('shapeOpacityValue').textContent = `${opacity}%`;
                    this.toolProperties.shape.opacity = opacity / 100;
                }
                
                // Update shape type based on the selected element
                if (this.selectedElement.dataset.shapeType) {
                    this.selectedShape = this.selectedElement.dataset.shapeType;
                    this.toolProperties.shape.type = this.selectedElement.dataset.shapeType;
                    
                    // Update shape buttons
                    document.querySelectorAll('.shape-btn').forEach(btn => {
                        btn.classList.remove('active');
                    });
                    const shapeBtn = document.querySelector(`.shape-btn[data-shape="${this.selectedShape}"]`);
                    if (shapeBtn) {
                        shapeBtn.classList.add('active');
                    }
                }
                break;

            case 'sticker':
            case 'emoji':
                const sizeSlider = document.getElementById(`${tool}SizeSlider`);
                const opacitySlider = document.getElementById(`${tool}OpacitySlider`);

                if (sizeSlider) {
                    const size = parseInt(this.selectedElement.style.fontSize) || this.toolProperties[tool].size;
                    sizeSlider.value = size;
                    this.toolProperties[tool].size = size;
                    this.size = size;
                }
                
                if (opacitySlider && opacityValue) {
                    const opacity = Math.round((parseFloat(this.selectedElement.style.opacity) || this.toolProperties[tool].opacity) * 100);
                    opacitySlider.value = opacity;
                    document.getElementById(`${tool}OpacityValue`).textContent = `${opacity}%`;
                    this.toolProperties[tool].opacity = opacity / 100;
                }
                
                // Update the selected sticker/emoji type based on content
                const content = this.selectedElement.textContent;
                this.toolProperties[tool].type = content;
                
                // Update sticker/emoji buttons
                document.querySelectorAll(`.${tool}-btn`).forEach(btn => {
                    btn.classList.remove('active');
                });
                const btn = document.querySelector(`.${tool}-btn[data-${tool}="${content}"]`);
                if (btn) {
                    btn.classList.add('active');
                }
                break;
            
            case 'pen':
                const penColorPicker = document.getElementById('penColorPicker');
                const penSizeSlider = document.getElementById('penSizeSlider');
                const penOpacitySlider = document.getElementById('penOpacitySlider');
                const penTipStyle = document.getElementById('penTipStyle');
                
                if (this.selectedElement.classList.contains('pen-stroke')) {
                    if (penColorPicker) {
                        // For pen strokes, we get the color from the background image
                        // Since we can't easily extract it, we'll use the stored property
                        const color = this.toolProperties.pen.color;
                        penColorPicker.value = color;
                        this.toolProperties.pen.color = color;
                        this.color = color;
                    }
                    
                    if (penSizeSlider) {
                        // For pen strokes, we get the size from the stored property
                        const size = this.toolProperties.pen.size;
                        penSizeSlider.value = size;
                        this.toolProperties.pen.size = size;
                        this.size = size;
                    }
                    
                    if (penOpacitySlider) {
                        const opacity = Math.round((parseFloat(this.selectedElement.style.opacity) || this.toolProperties.pen.opacity) * 100);
                        penOpacitySlider.value = opacity;
                        document.getElementById('penOpacityValue').textContent = `${opacity}%`;
                        this.toolProperties.pen.opacity = opacity / 100;
                    }
                    
                    if (penTipStyle && this.selectedElement.dataset.tipStyle) {
                        penTipStyle.value = this.selectedElement.dataset.tipStyle;
                        this.toolProperties.pen.tipStyle = this.selectedElement.dataset.tipStyle;
                    }
                }
                break;
            
            case 'highlighter':
                const highlighterColorPicker = document.getElementById('highlighterColorPicker');
                const highlighterSizeSlider = document.getElementById('highlighterSizeSlider');
                const highlighterOpacitySlider = document.getElementById('highlighterOpacitySlider');
                
                if (this.selectedElement.classList.contains('highlighter-stroke')) {
                    if (highlighterColorPicker) {
                        // For highlighter strokes, we get the color from the stored property
                        const color = this.toolProperties.highlighter.color;
                        highlighterColorPicker.value = color;
                        this.toolProperties.highlighter.color = color;
                        this.color = color;
                    }
                    
                    if (highlighterSizeSlider) {
                        // For highlighter strokes, we get the size from the stored property
                        const size = this.toolProperties.highlighter.size;
                        highlighterSizeSlider.value = size;
                        this.toolProperties.highlighter.size = size;
                        this.size = size;
                    }
                    
                    if (highlighterOpacitySlider) {
                        const opacity = Math.round((parseFloat(this.selectedElement.style.opacity) || this.toolProperties.highlighter.opacity) * 100);
                        highlighterOpacitySlider.value = opacity;
                        document.getElementById('highlighterOpacityValue').textContent = `${opacity}%`;
                        this.toolProperties.highlighter.opacity = opacity / 100;
                    }
                }
                break;
        }
    }

    getCenterPosition() {
        return {
            x: this.canvas.width / 2,
            y: this.canvas.height / 2
        };
    }

    startDrawing(e) {
        if (!this.drawingEnabled) return;
        
        const pos = this.getPosition(e);
        this.isDrawing = true;
        this.lastX = pos.x;
        this.lastY = pos.y;
        
        if (this.currentTool === 'pen' || this.currentTool === 'highlighter') {
            // Create a new stroke object
            this.currentStroke = {
                tool: this.currentTool,
                points: [{x: pos.x, y: pos.y}],
                properties: this.currentTool === 'pen' 
                    ? {...this.toolProperties.pen} 
                    : {...this.toolProperties.highlighter}
            };
            
            // Save current canvas state before drawing
            this.saveState();
            
            // Start drawing on the canvas directly
            this.ctx.save();
            
            if (this.currentTool === 'pen') {
                // Set pen properties
                this.ctx.strokeStyle = this.toolProperties.pen.color;
                this.ctx.lineWidth = this.toolProperties.pen.size;
                this.ctx.globalAlpha = this.toolProperties.pen.opacity;
                
                console.log('Starting pen drawing with:', {
                    color: this.toolProperties.pen.color,
                    size: this.toolProperties.pen.size,
                    opacity: this.toolProperties.pen.opacity,
                    tipStyle: this.toolProperties.pen.tipStyle
                });
                
                // Set the line style based on tip style
                switch (this.toolProperties.pen.tipStyle) {
                    case 'calligraphy':
                        this.ctx.lineCap = 'butt';
                        this.ctx.lineJoin = 'miter';
                        this.ctx.setLineDash([]);
                        break;
                    case 'square':
                        this.ctx.lineCap = 'square';
                        this.ctx.lineJoin = 'miter';
                        this.ctx.setLineDash([]);
                        break;
                    case 'brush':
                        this.ctx.lineCap = 'round';
                        this.ctx.lineJoin = 'round';
                        this.ctx.setLineDash([]);
                        break;
                    case 'dotted':
                        this.ctx.setLineDash([this.toolProperties.pen.size, this.toolProperties.pen.size * 2]);
                        this.ctx.lineCap = 'round';
                        break;
                    default: // round
                        this.ctx.lineCap = 'round';
                        this.ctx.lineJoin = 'round';
                        this.ctx.setLineDash([]);
                }
                
                // Draw the initial point
                this.ctx.beginPath();
                this.ctx.moveTo(pos.x, pos.y);
                
            } else if (this.currentTool === 'highlighter') {
                this.ctx.strokeStyle = this.toolProperties.highlighter.color;
                this.ctx.lineWidth = this.toolProperties.highlighter.size;
                this.ctx.lineCap = 'square';
                this.ctx.lineJoin = 'round';
                this.ctx.globalAlpha = this.toolProperties.highlighter.opacity;
                
                // Draw the initial point
                this.ctx.beginPath();
                this.ctx.moveTo(pos.x, pos.y);
            }
        } else if (this.currentTool === 'eraser') {
            // Save current canvas state before erasing
            this.saveState();
            
            // Create eraser cursor visual
            this.showEraserCursor(pos);
            
            // Start erasing
            this.eraseAtPoint(pos);
        } else if (this.currentTool === 'shape') {
            // Start drawing a shape
            this.isDrawingShape = true;
            this.shapeStartPos = { x: pos.x, y: pos.y };
        } else if (this.currentTool === 'text') {
            // Start text input
            this.startTextInput(pos);
        } else if (this.currentTool === 'sticker') {
            // Add a sticker
            this.addSticker(pos);
        } else if (this.currentTool === 'emoji') {
            // Add an emoji
            this.addEmoji(pos);
        }
    }

    draw(e) {
        if (!this.isDrawing || !this.drawingEnabled) return;

        const pos = this.getPosition(e);
        
        if (this.currentTool === 'pen' && this.currentStroke) {
            // Use quadratic curves for smoother lines
            this.ctx.beginPath();
            
            // Set the line style based on current properties
            this.ctx.strokeStyle = this.toolProperties.pen.color;
            this.ctx.globalAlpha = this.toolProperties.pen.opacity;
            this.ctx.lineWidth = this.toolProperties.pen.size;
            
            // Apply tip style
            switch (this.toolProperties.pen.tipStyle) {
                case 'calligraphy':
                    this.ctx.lineCap = 'butt';
                    this.ctx.lineJoin = 'miter';
                    // Apply transform for calligraphy effect
                    this.ctx.save();
                    this.ctx.setTransform(1, 0.5, -0.5, 1, pos.x, pos.y);
                    break;
                case 'square':
                    this.ctx.lineCap = 'square';
                    this.ctx.lineJoin = 'miter';
                    this.ctx.setLineDash([]);
                    break;
                case 'brush':
                    this.ctx.lineCap = 'round';
                    this.ctx.lineJoin = 'round';
                    this.ctx.setLineDash([]);
                    // Vary line width for brush effect
                    const pressure = Math.random() * 0.5 + 0.5;
                    this.ctx.lineWidth = this.toolProperties.pen.size * pressure;
                    break;
                case 'dotted':
                    this.ctx.lineCap = 'round';
                    this.ctx.lineJoin = 'round';
                    this.ctx.setLineDash([this.toolProperties.pen.size, this.toolProperties.pen.size * 2]);
                    break;
                default: // round
                    this.ctx.lineCap = 'round';
                    this.ctx.lineJoin = 'round';
                    this.ctx.setLineDash([]);
            }
            
            // Calculate midpoint for smoother curves
            const midPoint = this.calculateMidPoint(this.lastX, this.lastY, pos.x, pos.y);
            
            // Draw a smooth curve instead of a straight line
            if (this.currentStroke.points.length > 1) {
                const lastMidPoint = this.calculateMidPoint(
                    this.currentStroke.points[this.currentStroke.points.length - 2].x,
                    this.currentStroke.points[this.currentStroke.points.length - 2].y,
                    this.lastX,
                    this.lastY
                );
                
                // Use quadratic curve for smoother lines
                this.ctx.moveTo(lastMidPoint.x, lastMidPoint.y);
                this.ctx.quadraticCurveTo(this.lastX, this.lastY, midPoint.x, midPoint.y);
            } else {
                // For the first point, just draw a line
                this.ctx.moveTo(this.lastX, this.lastY);
            this.ctx.lineTo(pos.x, pos.y);
            }
            
            this.ctx.stroke();
            
            // Record the point for stroke history
            this.currentStroke.points.push({x: pos.x, y: pos.y});
            this.currentStroke.properties.tipStyle = this.toolProperties.pen.tipStyle;
            this.currentStroke.properties.color = this.toolProperties.pen.color;
            this.currentStroke.properties.size = this.toolProperties.pen.size;
            this.currentStroke.properties.opacity = this.toolProperties.pen.opacity;

            // Restore context if transformed
            if (this.toolProperties.pen.tipStyle === 'calligraphy') {
                this.ctx.restore();
            }
        } else if (this.currentTool === 'highlighter' && this.currentStroke) {
            this.ctx.beginPath();
            this.ctx.strokeStyle = this.toolProperties.highlighter.color;
            this.ctx.lineWidth = this.toolProperties.highlighter.size;
            this.ctx.lineCap = 'square';
            this.ctx.lineJoin = 'round';
            this.ctx.globalAlpha = this.toolProperties.highlighter.opacity;
            
            this.ctx.moveTo(this.lastX, this.lastY);
            this.ctx.lineTo(pos.x, pos.y);
            this.ctx.stroke();
            
            this.currentStroke.points.push({x: pos.x, y: pos.y});
        } else if (this.currentTool === 'eraser') {
            // Update eraser cursor position
            this.showEraserCursor(pos);
            
            // Calculate movement distance to determine if we should erase
            // This prevents excessive redraws for performance
            const movementDistance = Math.sqrt(
                Math.pow(pos.x - this.lastX, 2) + 
                Math.pow(pos.y - this.lastY, 2)
            );
            
            // Only erase if moved enough distance or on first draw
            // Smaller distance for precision mode
            const minDistance = this.toolProperties.eraser.mode === 'precision' ? 2 : 5;
            
            if (movementDistance >= minDistance || !this.lastErasePos) {
                // Store last erase position
                this.lastErasePos = { x: pos.x, y: pos.y };
                
                // Erase at the current position with optimizations
                const wasModified = this.eraseAtPoint(pos);
                
                // For stroke mode, we want less frequent updates for better performance
                if (this.toolProperties.eraser.mode === 'stroke' && !wasModified) {
                    // No changes made, so we can skip some frames
                    this.skipFrames = (this.skipFrames || 0) + 1;
                } else {
                    this.skipFrames = 0;
                }
                
                // Interpolate between points for smoother erasing
                if (this.toolProperties.eraser.mode === 'pixel' && this.lastX && this.lastY) {
                    // Calculate how many points to interpolate based on movement distance
                    const interpolationPoints = Math.min(Math.ceil(movementDistance / 5), 5);
                    
                    for (let i = 1; i < interpolationPoints; i++) {
                        const ratio = i / interpolationPoints;
                        const interpX = this.lastX + (pos.x - this.lastX) * ratio;
                        const interpY = this.lastY + (pos.y - this.lastY) * ratio;
                        
                        this.eraseAtPoint({ x: interpX, y: interpY });
                    }
                }
            }
        }
        
        this.lastX = pos.x;
        this.lastY = pos.y;
    }

    stopDrawing() {
        if (this.isDrawing) {
            this.isDrawing = false;
            
            if (this.currentTool === 'pen' || this.currentTool === 'highlighter') {
                if (this.currentStroke && this.currentStroke.points.length > 1) {
                    // Add the completed stroke to the canvas strokes array
                    this.canvasStrokes.push(this.currentStroke);
                }
                this.currentStroke = null;
            } else if (this.currentTool === 'eraser') {
                // Remove the eraser cursor
                this.removeEraserCursor();
                
                // Save state after erasing is complete
                this.saveState();
            }
        }
    }

    drawArrow(fromX, fromY, toX, toY) {
        const headLength = 20;
        const angle = Math.atan2(toY - fromY, toX - fromX);
        
        // Draw the line
        this.ctx.beginPath();
        this.ctx.moveTo(fromX, fromY);
        this.ctx.lineTo(toX, toY);
        this.ctx.stroke();
        
        // Draw the arrow head
        this.ctx.beginPath();
        this.ctx.moveTo(toX, toY);
        this.ctx.lineTo(
            toX - headLength * Math.cos(angle - Math.PI/6),
            toY - headLength * Math.sin(angle - Math.PI/6)
        );
        this.ctx.moveTo(toX, toY);
        this.ctx.lineTo(
            toX - headLength * Math.cos(angle + Math.PI/6),
            toY - headLength * Math.sin(angle + Math.PI/6)
        );
        this.ctx.stroke();
    }

    addSticker(pos) {
        if (!pos) {
            pos = this.getCenterPosition();
        }
        
        const sticker = document.createElement('div');
        sticker.classList.add('draggable', 'emoji-sticker');
        
        if (this.toolProperties.sticker.isCustom) {
            const imageUrl = this.customStickers.get(this.toolProperties.sticker.type);
            sticker.style.backgroundImage = `url(${imageUrl})`;
            sticker.style.backgroundSize = 'contain'; // Ensure the image is contained
            sticker.textContent = '';
            
            // Set fixed dimensions for custom stickers
            const size = this.toolProperties.sticker.size;
            sticker.style.width = `${size}px`;
            sticker.style.height = `${size}px`;
        } else {
            sticker.textContent = this.toolProperties.sticker.type;
            sticker.style.fontSize = `${this.toolProperties.sticker.size}px`;
            sticker.style.width = `${this.toolProperties.sticker.size}px`;
            sticker.style.height = `${this.toolProperties.sticker.size}px`;
        }
        
        sticker.style.opacity = this.toolProperties.sticker.opacity;
        
        this.addDraggableElement(sticker, pos.x, pos.y);
    }

    addEmoji(pos) {
        if (!pos) {
            pos = this.getCenterPosition();
        }
        
        const emoji = document.createElement('div');
        emoji.classList.add('draggable', 'emoji-sticker');
        
        if (this.toolProperties.emoji.isCustom) {
            const imageUrl = this.customEmojis.get(this.toolProperties.emoji.type);
            emoji.style.backgroundImage = `url(${imageUrl})`;
            emoji.style.backgroundSize = 'contain'; // Ensure the image is contained
            emoji.textContent = '';
            
            // Set fixed dimensions for custom emojis
            const size = this.toolProperties.emoji.size;
            emoji.style.width = `${size}px`;
            emoji.style.height = `${size}px`;
        } else {
            emoji.textContent = this.toolProperties.emoji.type;
            emoji.style.fontSize = `${this.toolProperties.emoji.size}px`;
            emoji.style.width = `${this.toolProperties.emoji.size}px`;
            emoji.style.height = `${this.toolProperties.emoji.size}px`;
        }
        
        emoji.style.opacity = this.toolProperties.emoji.opacity;
        
        this.addDraggableElement(emoji, pos.x, pos.y);
    }

    startTextInput(pos) {
        console.log('Starting text input at position:', pos);
        
        // Position and show the text input
        this.textInput.style.display = 'block';
        this.textInput.style.left = pos.x + 'px';
        this.textInput.style.top = pos.y + 'px';
        
        // Set initial width based on tool properties
        const fontSize = parseInt(this.toolProperties.text.fontSize) || 16;
        this.textInput.style.minWidth = (fontSize * 10) + 'px';
        
        // Clear any previous text and focus
        this.textInputInput.value = '';
        this.textInputInput.focus();
        
        // Add event listener for Enter key to add the text
        const handleKeyDown = (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.addText();
                // Remove the event listener after adding text
                this.textInputInput.removeEventListener('keydown', handleKeyDown);
            }
        };
        
        this.textInputInput.addEventListener('keydown', handleKeyDown);
    }

    addText() {
        const text = this.textInputInput.value.trim();
        console.log('Adding text:', text);
        
        if (text) {
            // Create text element
            const element = document.createElement('div');
            element.className = 'draggable text';
            
            // Apply text styling from tool properties
            element.style.fontFamily = this.toolProperties.text.fontFamily || 'Arial';
            element.style.fontSize = this.toolProperties.text.fontSize || '16px';
            element.style.fontWeight = this.toolProperties.text.fontWeight || 'normal';
            element.style.color = this.toolProperties.text.color || '#000000';
            element.style.opacity = this.toolProperties.text.opacity || 1;
            
            // Apply background color or transparent
            if (this.toolProperties.text.transparent) {
                element.style.background = 'transparent';
            } else {
                element.style.background = this.toolProperties.text.backgroundColor || '#ffffff';
            }
            
            element.style.padding = '5px';
            element.style.whiteSpace = 'nowrap';
            element.textContent = text;
            
            // Get position from text input
            const x = parseInt(this.textInput.style.left);
            const y = parseInt(this.textInput.style.top);
            
            // Add to canvas
            this.addDraggableElement(element, x, y);
            
            // Save state
            this.saveState();
        }

        // Hide and reset text input
        this.textInput.style.display = 'none';
        this.textInputInput.value = '';
    }

    handleDraggableMouseDown(e) {
        // Prevent default to avoid text selection
        e.preventDefault();
        
        // Find the clicked element
        let clickedElement = null;
        let target = e.target;
        
        // Check if we clicked a control handle
        const isControlHandle = target.classList.contains('control-handle');
        const isRotateHandle = target.classList.contains('rotate-handle');
        
        if (isControlHandle || isRotateHandle) {
            // Get the parent draggable element
            clickedElement = target.closest('.draggable');
            
            if (isControlHandle && clickedElement) {
                // Initialize resizing
                this.isResizing = true;
                
                // Extract handle position (nw, n, ne, etc.) from class name
                const handleClasses = Array.from(target.classList);
                const handleClass = handleClasses.find(cls => cls.startsWith('handle-'));
                this.resizeHandle = handleClass ? handleClass.replace('handle-', '') : 'se';
                
                // Get accurate element dimensions using getBoundingClientRect
                const rect = clickedElement.getBoundingClientRect();
                
                // Store the starting point of the mouse
                this.startPoint = { x: e.clientX, y: e.clientY };
                
                // Store initial size and position
                this.initialSize = { 
                    width: rect.width, 
                    height: rect.height 
                };
                
                // Store initial position (in pixels)
                const computedStyle = window.getComputedStyle(clickedElement);
                this.initialPosition = {
                    left: parseFloat(computedStyle.left),
                    top: parseFloat(computedStyle.top)
                };
                
                // If position values are NaN (e.g., if they're set to 'auto'), calculate them from the rect
                if (isNaN(this.initialPosition.left)) {
                    this.initialPosition.left = rect.left;
                }
                if (isNaN(this.initialPosition.top)) {
                    this.initialPosition.top = rect.top;
                }
                
                // Select the element being resized
                this.selectElement(clickedElement);
            } 
            else if (isRotateHandle && clickedElement) {
                // Initialize rotation
                this.isRotating = true;
                
                // Get the center of the element for rotation
                const rect = clickedElement.getBoundingClientRect();
                this.rotationCenter = {
                    x: rect.left + rect.width / 2,
                    y: rect.top + rect.height / 2
                };
                
                // Calculate initial angle
                this.initialRotation = {
                    angle: Math.atan2(
                        e.clientY - this.rotationCenter.y,
                        e.clientX - this.rotationCenter.x
                    ) * (180 / Math.PI),
                    current: this.getElementRotation(clickedElement)
                };
                
                // Select the element being rotated
                this.selectElement(clickedElement);
            }
        } 
        // Check if we clicked a draggable element
        else {
            // Find the closest draggable element
            clickedElement = target.closest('.draggable');
            
            if (clickedElement) {
                // Initialize dragging
                this.isDragging = true;
                
                // Calculate the offset between mouse position and element position
                const rect = clickedElement.getBoundingClientRect();
                this.dragOffset = {
                    x: e.clientX - rect.left,
                    y: e.clientY - rect.top
                };
                
                // Select the element being dragged
                this.selectElement(clickedElement);
            } else {
                // Clicked outside any draggable element, deselect current element
                if (this.selectedElement) {
                    this.removeHandles(this.selectedElement);
                    this.selectedElement.classList.remove('selected');
                    this.selectedElement = null;
                }
            }
        }
    }

    // Helper method to get the current rotation of an element
    getElementRotation(element) {
        const transform = element.style.transform || '';
        const match = transform.match(/rotate\(([-0-9.]+)deg\)/);
        return match ? parseFloat(match[1]) : 0;
    }

    handleDraggableMouseMove(e) {
        // If no action is in progress, exit
        if (!this.isDragging && !this.isResizing && !this.isRotating) return;

        // Handle dragging
        if (this.isDragging && this.selectedElement) {
            const x = e.clientX - this.dragOffset.x;
            const y = e.clientY - this.dragOffset.y;
            
            this.selectedElement.style.left = `${x}px`;
            this.selectedElement.style.top = `${y}px`;
        } 
        // Handle resizing
        else if (this.isResizing && this.selectedElement && this.initialSize && this.startPoint) {
            // Calculate mouse movement with increased sensitivity for better control
            const deltaX = (e.clientX - this.startPoint.x) * 1.5; // Moderate sensitivity
            const deltaY = (e.clientY - this.startPoint.y) * 1.5; // Moderate sensitivity
            
            // Get original dimensions and position
            const origWidth = this.initialSize.width;
            const origHeight = this.initialSize.height;
            const origLeft = this.initialPosition.left;
            const origTop = this.initialPosition.top;
            
            // Initialize new dimensions and position
            let newWidth = origWidth;
            let newHeight = origHeight;
            let newLeft = origLeft;
            let newTop = origTop;
            
            // Calculate new dimensions based on which handle is being dragged
            switch (this.resizeHandle) {
                case 'nw': // Northwest
                    newWidth = Math.max(origWidth - deltaX, 10);
                    newHeight = Math.max(origHeight - deltaY, 10);
                    newLeft = origLeft + (origWidth - newWidth);
                    newTop = origTop + (origHeight - newHeight);
                    break;
                case 'n': // North
                    newHeight = Math.max(origHeight - deltaY, 10);
                    newTop = origTop + (origHeight - newHeight);
                    break;
                case 'ne': // Northeast
                    newWidth = Math.max(origWidth + deltaX, 10);
                    newHeight = Math.max(origHeight - deltaY, 10);
                    newTop = origTop + (origHeight - newHeight);
                    break;
                case 'e': // East
                    newWidth = Math.max(origWidth + deltaX, 10);
                    break;
                case 'se': // Southeast
                    newWidth = Math.max(origWidth + deltaX, 10);
                    newHeight = Math.max(origHeight + deltaY, 10);
                    break;
                case 's': // South
                    newHeight = Math.max(origHeight + deltaY, 10);
                    break;
                case 'sw': // Southwest
                    newWidth = Math.max(origWidth - deltaX, 10);
                    newHeight = Math.max(origHeight + deltaY, 10);
                    newLeft = origLeft + (origWidth - newWidth);
                    break;
                case 'w': // West
                    newWidth = Math.max(origWidth - deltaX, 10);
                    newLeft = origLeft + (origWidth - newWidth);
                    break;
            }
            
            // Special handling for circles and emoji/stickers to maintain aspect ratio
            if ((this.selectedElement.classList.contains('shape') && 
                this.selectedElement.dataset.shapeType === 'circle') ||
                this.selectedElement.classList.contains('emoji-sticker')) {
                
                // Use the larger dimension to determine size
                const size = Math.max(newWidth, newHeight);
                
                // Adjust position to maintain center
                if (this.resizeHandle.includes('w')) {
                    newLeft = origLeft + (origWidth - size);
                }
                if (this.resizeHandle.includes('n')) {
                    newTop = origTop + (origHeight - size);
                }
                
                // Set new dimensions
                newWidth = size;
                newHeight = size;
            }
            
            // Apply new dimensions and position
            this.selectedElement.style.width = `${newWidth}px`;
            this.selectedElement.style.height = `${newHeight}px`;
            this.selectedElement.style.left = `${newLeft}px`;
            this.selectedElement.style.top = `${newTop}px`;
            
            // Special handling for text elements
            if (this.selectedElement.classList.contains('text')) {
                // Adjust font size proportionally but with better constraints
                const fontSize = Math.max(12, Math.min(newHeight * 0.7, newWidth * 0.15));
                this.selectedElement.style.fontSize = `${fontSize}px`;
                
                // Ensure text remains centered
                this.selectedElement.style.display = 'flex';
                this.selectedElement.style.alignItems = 'center';
                this.selectedElement.style.justifyContent = 'center';
            }
            
            // Update bounding box to match element size
            const boundingBox = this.selectedElement.querySelector('.bounding-box');
            if (boundingBox) {
                boundingBox.style.width = '100%';
                boundingBox.style.height = '100%';
                boundingBox.style.top = '0';
                boundingBox.style.left = '0';
            }
            
            // Ensure control handles are properly positioned
            this.updateHandlePositions(this.selectedElement);
        } 
        // Handle rotation
        else if (this.isRotating && this.selectedElement) {
            const currentAngle = Math.atan2(
                e.clientY - this.rotationCenter.y,
                e.clientX - this.rotationCenter.x
            ) * (180 / Math.PI);
            
            const angleDiff = currentAngle - this.initialRotation.angle;
            const newRotation = this.initialRotation.current + angleDiff;
            
            this.selectedElement.style.transform = `rotate(${newRotation}deg)`;
            this.selectedElement.style.transformOrigin = 'center center';
        }
    }
    
    // Helper method to update handle positions
    updateHandlePositions(element) {
        const handles = element.querySelectorAll('.control-handle');
        handles.forEach(handle => {
            // Ensure handles are properly positioned
            if (handle.classList.contains('handle-nw')) {
                handle.style.top = '-5px';
                handle.style.left = '-5px';
            } else if (handle.classList.contains('handle-n')) {
                handle.style.top = '-5px';
                handle.style.left = 'calc(50% - 5px)';
            } else if (handle.classList.contains('handle-ne')) {
                handle.style.top = '-5px';
                handle.style.right = '-5px';
            } else if (handle.classList.contains('handle-e')) {
                handle.style.top = 'calc(50% - 5px)';
                handle.style.right = '-5px';
            } else if (handle.classList.contains('handle-se')) {
                handle.style.bottom = '-5px';
                handle.style.right = '-5px';
            } else if (handle.classList.contains('handle-s')) {
                handle.style.bottom = '-5px';
                handle.style.left = 'calc(50% - 5px)';
            } else if (handle.classList.contains('handle-sw')) {
                handle.style.bottom = '-5px';
                handle.style.left = '-5px';
            } else if (handle.classList.contains('handle-w')) {
                handle.style.top = 'calc(50% - 5px)';
                handle.style.left = '-5px';
            }
        });
    }

    handleDraggableMouseUp() {
        // Only remove flags, keep the element selected
        this.isDragging = false;
        this.isResizing = false;
        this.isRotating = false;
        
        // Save the state if we were modifying something
        this.saveState();
    }

    handleTouch(e) {
        e.preventDefault();
        const touch = e.touches[0];
        const mouseEvent = new MouseEvent(e.type === 'touchstart' ? 'mousedown' : 'mousemove', {
            clientX: touch.clientX,
            clientY: touch.clientY
        });
        this.canvas.dispatchEvent(mouseEvent);
    }

    getPosition(e) {
        const rect = this.canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        return { x, y };
    }

    saveState() {
        const state = {
            canvasImage: this.canvas.toDataURL(),
            canvasStrokes: [...this.canvasStrokes],
            draggableElements: [...this.draggableElements]
        };
        this.history.push(state);
        this.redoStack = [];
    }

    undo() {
        if (this.history.length > 0) {
            // Save current state to redo stack
            this.redoStack.push(this.captureCurrentState());
            
            // Load previous state
            const prevState = this.history.pop();
            this.restoreState(prevState);
        }
    }

    redo() {
        if (this.redoStack.length > 0) {
            // Save current state to history
            this.saveState();
            
            // Load next state
            const nextState = this.redoStack.pop();
            this.restoreState(nextState);
        }
    }

    loadState(state) {
        if (!state) return;
        
        // Load canvas image
        const img = new Image();
        img.onload = () => {
            this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
            this.ctx.drawImage(img, 0, 0);
        };
        img.src = state.canvasImage;
        
        // Load canvas strokes
        this.canvasStrokes = state.canvasStrokes ? [...state.canvasStrokes] : [];
        
        // Load draggable elements
        this.draggableElements = state.draggableElements ? [...state.draggableElements] : [];
        
        // Redraw all canvas strokes
        this.canvasStrokes.forEach(stroke => {
            if (stroke.points.length > 1) {
                this.ctx.save();
                
                // Set stroke properties
                if (stroke.tool === 'pen') {
                    this.ctx.strokeStyle = stroke.properties.color;
                    this.ctx.lineWidth = stroke.properties.size;
                    this.ctx.lineCap = stroke.properties.tipStyle;
                    this.ctx.lineJoin = 'round';
                    this.ctx.globalAlpha = stroke.properties.opacity;
                } else if (stroke.tool === 'highlighter') {
                    this.ctx.strokeStyle = stroke.properties.color;
                    this.ctx.lineWidth = stroke.properties.size;
                    this.ctx.lineCap = 'square';
                    this.ctx.lineJoin = 'round';
                    this.ctx.globalAlpha = stroke.properties.opacity;
                }
                
                // Draw the stroke
                this.ctx.beginPath();
                this.ctx.moveTo(stroke.points[0].x, stroke.points[0].y);
                for (let i = 1; i < stroke.points.length; i++) {
                    this.ctx.lineTo(stroke.points[i].x, stroke.points[i].y);
                }
                this.ctx.stroke();
                
                this.ctx.restore();
            }
        });
    }

    clearCanvas() {
        // Clear canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Clear strokes data
        this.canvasStrokes = [];
        
        // Remove all draggable elements
        this.draggableElements.forEach(element => element.remove());
        this.draggableElements = [];
        this.selectedElement = null;
        
        // Save the clear state
        this.saveState();
    }

    toggleGrid() {
        // Get the grid overlay element
        const gridOverlay = document.querySelector('.grid-overlay');
        if (!gridOverlay) {
            console.error('Grid overlay element not found');
            return;
        }
        
        // Toggle grid visibility state
        this.gridVisible = !this.gridVisible;
        
        console.log('Toggling grid visibility:', this.gridVisible);
        
        if (this.gridVisible) {
            // Show grid
            gridOverlay.classList.add('visible');
            gridOverlay.style.display = 'block';
            
            // Update grid properties
            const size = this.toolProperties.grid.size || 20;
            const opacity = this.toolProperties.grid.opacity || 0.7;
            
            gridOverlay.style.backgroundSize = `${size}px ${size}px`;
            gridOverlay.style.opacity = opacity;
            
            // Show grid properties panel
            const gridPanel = document.getElementById('grid-panel');
            if (gridPanel) {
                // Hide all other panels
                document.querySelectorAll('.property-panel').forEach(panel => {
                    panel.classList.remove('active');
                });
                
                // Show grid panel
                gridPanel.classList.add('active');
                this.activePropertyPanel = gridPanel;
                
                console.log('Grid panel activated');
            } else {
                console.error('Grid panel not found');
            }
        } else {
            // Hide grid
            gridOverlay.classList.remove('visible');
            gridOverlay.style.display = 'none';
            
            // Hide grid properties panel if it's active
            const gridPanel = document.getElementById('grid-panel');
            if (gridPanel && gridPanel.classList.contains('active')) {
                gridPanel.classList.remove('active');
                
                // If we have a previous tool, show its panel
                if (this.previousTool && this.previousTool !== 'grid') {
                    const prevPanel = document.getElementById(`${this.previousTool}-panel`);
                    if (prevPanel) {
                        prevPanel.classList.add('active');
                        this.activePropertyPanel = prevPanel;
                    }
                } else {
                    this.activePropertyPanel = null;
                }
            }
        }
    }

    updateGridSize() {
        const gridOverlay = document.querySelector('.grid-overlay');
        if (!gridOverlay) return;
        
        // Get grid size from tool properties
        const size = this.toolProperties.grid.size || 20;
        const opacity = this.toolProperties.grid.opacity || 0.7;
        
        console.log('Updating grid size:', size, 'opacity:', opacity);
        
        // Update grid size
        gridOverlay.style.backgroundSize = `${size}px ${size}px`;
        
        // Update grid opacity
        gridOverlay.style.opacity = opacity;
        
        // Update grid background image with darker lines
        gridOverlay.style.backgroundImage = `
            linear-gradient(rgba(0, 0, 0, 0.8) 1px, transparent 1px),
            linear-gradient(90deg, rgba(0, 0, 0, 0.8) 1px, transparent 1px)
        `;
    }

    redrawCanvas() {
        // Clear the canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Apply canvas background if set to solid
        if (this.canvasBackground.solid) {
            this.ctx.fillStyle = this.hexToRGBA(this.canvasBackground.color, this.canvasBackground.opacity);
            this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        }
        
        // Redraw all strokes
        this.canvasStrokes.forEach(stroke => {
            if (stroke.points.length > 1) {
                this.ctx.save();
                
                // Set stroke properties
                if (stroke.tool === 'pen') {
                    this.ctx.strokeStyle = stroke.properties.color;
                    this.ctx.lineWidth = stroke.properties.size;
                    
                    // Apply tip style properties
                    switch (stroke.properties.tipStyle) {
                        case 'calligraphy':
                            this.ctx.lineCap = 'butt';
                            this.ctx.lineJoin = 'miter';
                            break;
                        case 'square':
                            this.ctx.lineCap = 'square';
                            this.ctx.lineJoin = 'miter';
                            this.ctx.setLineDash([]);
                            break;
                        case 'brush':
                            this.ctx.lineCap = 'round';
                    this.ctx.lineJoin = 'round';
                            break;
                        case 'dotted':
                            this.ctx.setLineDash([stroke.properties.size, stroke.properties.size * 2]);
                            this.ctx.lineCap = 'round';
                            break;
                        default: // round
                            this.ctx.lineCap = 'round';
                            this.ctx.lineJoin = 'round';
                            this.ctx.setLineDash([]);
                    }
                    
                    this.ctx.globalAlpha = stroke.properties.opacity;
                } else if (stroke.tool === 'highlighter') {
                    this.ctx.strokeStyle = stroke.properties.color;
                    this.ctx.lineWidth = stroke.properties.size;
                    this.ctx.lineCap = 'square';
                    this.ctx.lineJoin = 'round';
                    this.ctx.globalAlpha = stroke.properties.opacity;
                }
                
                // Draw the stroke
                this.ctx.beginPath();
                this.ctx.moveTo(stroke.points[0].x, stroke.points[0].y);
                for (let i = 1; i < stroke.points.length; i++) {
                    this.ctx.lineTo(stroke.points[i].x, stroke.points[i].y);
                }
                this.ctx.stroke();
                
                this.ctx.restore();
            }
        });
    }

    createBoundingBox(element) {
        // Remove any existing bounding box
        const existingBox = element.querySelector('.bounding-box');
        if (existingBox) {
            element.removeChild(existingBox);
        }
        
        // Create bounding box
        const boundingBox = document.createElement('div');
        boundingBox.className = 'bounding-box';
        element.appendChild(boundingBox);
        
        // Create rotation line
        const rotationLine = document.createElement('div');
        rotationLine.className = 'rotation-line';
        boundingBox.appendChild(rotationLine);
        
        // Create rotation handle
        const rotateHandle = document.createElement('div');
        rotateHandle.className = 'rotate-handle';
        boundingBox.appendChild(rotateHandle);
        
        // Create resize handles
        const handlePositions = ['nw', 'n', 'ne', 'e', 'se', 's', 'sw', 'w'];
        handlePositions.forEach(pos => {
            const handle = document.createElement('div');
            handle.className = `control-handle handle-${pos}`;
            boundingBox.appendChild(handle);
        });
        
        // Add event listeners for the element
        element.addEventListener('mousedown', this.handleDraggableMouseDown.bind(this));
        element.addEventListener('touchstart', this.handleTouch.bind(this), { passive: false });
    }

    addDraggableElement(element, x, y) {
        // Add appropriate class based on content type if not already added
        if (!element.classList.contains('text') && 
            !element.classList.contains('shape') && 
            !element.classList.contains('emoji-sticker') && 
            !element.classList.contains('pen-stroke') && 
            !element.classList.contains('highlighter-stroke')) {
            
            if (element.textContent && element.textContent.length <= 2) {
                element.classList.add('emoji-sticker');
            }
        }
        
        // Ensure draggable class is added
        if (!element.classList.contains('draggable')) {
            element.classList.add('draggable');
        }
        
        // Position element at click position or center if no position provided
        if (x !== undefined && y !== undefined) {
            this.canvas.parentElement.appendChild(element);
            element.style.left = (x - element.offsetWidth / 2) + 'px';
            element.style.top = (y - element.offsetHeight / 2) + 'px';
        } else {
            const center = this.getCenterPosition();
            this.canvas.parentElement.appendChild(element);
            element.style.left = (center.x - element.offsetWidth / 2) + 'px';
            element.style.top = (center.y - element.offsetHeight / 2) + 'px';
        }
        
        // Add bounding box with control handles
        this.createBoundingBox(element);
        
        this.draggableElements.push(element);
        
        // Make the element selected by default
        if (this.selectedElement) {
            this.selectedElement.classList.remove('selected');
        }
        this.selectedElement = element;
        element.classList.add('selected');
        
        // Update property panel based on element type
        let tool = '';
        if (element.classList.contains('text')) {
            tool = 'text';
        } else if (element.classList.contains('shape')) {
            tool = 'shape';
        } else if (element.classList.contains('emoji-sticker')) {
            // Determine if it's a sticker or emoji based on the content
            const content = element.textContent;
            tool = ['⭐', '❤️', '✅', '❌', '🏠', '🔑', '📍', '💵'].includes(content) ? 'sticker' : 'emoji';
        } else if (element.classList.contains('pen-stroke')) {
            tool = 'pen';
        } else if (element.classList.contains('highlighter-stroke')) {
            tool = 'highlighter';
        }
        
        if (tool) {
            this.currentTool = tool;
            this.setTool(tool);
            this.updatePropertyPanelValues(tool);
        }
        
        this.saveState();
    }

    setupBackgroundControls() {
        const bgIcons = document.querySelectorAll('.bg-icon');
        const bgColorPicker = document.getElementById('bgColorPicker');
        
        // Set initial state
        this.updateCanvasBackground();
        
        // Handle background type selection
        bgIcons.forEach(icon => {
            icon.addEventListener('click', () => {
                // Remove active class from all icons
                bgIcons.forEach(i => i.classList.remove('active'));
                
                // Add active class to clicked icon
                icon.classList.add('active');
                
                // Update background state
                const bgType = icon.dataset.bgType;
                this.canvasBackground.solid = (bgType === 'solid');
                
                // Update background color in the picker
                if (bgType === 'solid') {
                    bgColorPicker.parentElement.classList.add('color-active');
                } else {
                    bgColorPicker.parentElement.classList.remove('color-active');
                }
                
                // Apply the background change
                this.updateCanvasBackground();
            });
        });
        
        // Handle color picker changes
        bgColorPicker.addEventListener('input', (e) => {
            this.canvasBackground.color = e.target.value;
            
            // If solid background is active, update immediately
            if (this.canvasBackground.solid) {
                this.updateCanvasBackground();
            }
        });
    }

    updateCanvasBackground() {
        const container = this.canvas.parentElement;
        
        if (this.canvasBackground.solid) {
            const rgba = this.hexToRGBA(this.canvasBackground.color, this.canvasBackground.opacity);
            container.style.background = rgba;
            
            // Update the color picker to match
            const bgColorPicker = document.getElementById('bgColorPicker');
            if (bgColorPicker) {
                bgColorPicker.value = this.canvasBackground.color;
            }
            
            // Update the icons
            const solidIcon = document.querySelector('.bg-icon.solid');
            const transparentIcon = document.querySelector('.bg-icon.transparent');
            
            if (solidIcon && transparentIcon) {
                solidIcon.classList.add('active');
                transparentIcon.classList.remove('active');
            }
        } else {
            container.style.background = 'transparent';
            
            // Update the icons
            const solidIcon = document.querySelector('.bg-icon.solid');
            const transparentIcon = document.querySelector('.bg-icon.transparent');
            
            if (solidIcon && transparentIcon) {
                solidIcon.classList.remove('active');
                transparentIcon.classList.add('active');
            }
        }
    }

    hexToRGBA(hex, alpha) {
        const r = parseInt(hex.slice(1, 3), 16);
        const g = parseInt(hex.slice(3, 5), 16);
        const b = parseInt(hex.slice(5, 7), 16);
        return `rgba(${r}, ${g}, ${b}, ${alpha})`;
    }

    updateStrokeElement(element, type, property, value) {
        // Get the stroke data from the element
        const width = parseInt(element.style.width);
        const height = parseInt(element.style.height);
        
        if (width <= 0 || height <= 0) return;
        
        // Update the dataset with the new property value
        if (property === 'color') {
            element.dataset.color = value;
        } else if (property === 'size') {
            element.dataset.size = value;
        } else if (property === 'tipStyle') {
            element.dataset.tipStyle = value;
        }
        
        // Create a canvas to redraw the stroke
        const strokeCanvas = document.createElement('canvas');
        strokeCanvas.width = width;
        strokeCanvas.height = height;
        const strokeCtx = strokeCanvas.getContext('2d');
        
        // Set the stroke properties
        if (type === 'pen') {
            strokeCtx.strokeStyle = element.dataset.color || this.toolProperties.pen.color;
            strokeCtx.lineWidth = element.dataset.size || this.toolProperties.pen.size;
            strokeCtx.lineCap = element.dataset.tipStyle || this.toolProperties.pen.tipStyle;
            strokeCtx.lineJoin = 'round';
            strokeCtx.globalAlpha = this.toolProperties.pen.opacity;
        } else if (type === 'highlighter') {
            strokeCtx.strokeStyle = element.dataset.color || this.toolProperties.highlighter.color;
            strokeCtx.lineWidth = element.dataset.size || this.toolProperties.highlighter.size;
            strokeCtx.lineCap = 'square';
            strokeCtx.lineJoin = 'round';
            strokeCtx.globalAlpha = this.toolProperties.highlighter.opacity;
        }
        
        // Get the stored points from the element's dataset
        const points = element.dataset.points ? JSON.parse(element.dataset.points) : [];
        
        if (points.length > 0) {
            // Redraw the stroke using the original points
            strokeCtx.beginPath();
            points.forEach(point => {
                strokeCtx.moveTo(point.x - element.dataset.minX, point.y - element.dataset.minY);
                strokeCtx.lineTo(point.x2 - element.dataset.minX, point.y2 - element.dataset.minY);
            });
            strokeCtx.stroke();
        } else {
            // Fallback if no points data (legacy strokes)
            strokeCtx.beginPath();
            strokeCtx.moveTo(0, 0);
            strokeCtx.lineTo(width, height);
            strokeCtx.moveTo(width, 0);
            strokeCtx.lineTo(0, height);
            strokeCtx.stroke();
        }
        
        // Update the element's background image
        element.style.backgroundImage = `url(${strokeCanvas.toDataURL()})`;
    }

    clearTempCanvas() {
        if (this.tempCanvas) {
            this.tempCtx.clearRect(0, 0, this.tempCanvas.width, this.tempCanvas.height);
            this.tempCanvas = null;
            this.tempCtx = null;
        }
    }

    initializeBaker() {
        // Sticker baker
        const stickerUploadBtn = document.getElementById('stickerUploadBtn');
        const stickerUpload = document.getElementById('stickerUpload');
        const customStickersGrid = document.querySelector('.custom-stickers-grid');

        stickerUploadBtn.addEventListener('click', () => {
            stickerUpload.click();
        });

        stickerUpload.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const id = `sticker-${Date.now()}`;
                    this.customStickers.set(id, e.target.result);
                    this.addCustomStickerButton(id, e.target.result, customStickersGrid);
                };
                reader.readAsDataURL(file);
            }
        });

        // Emoji baker
        const emojiUploadBtn = document.getElementById('emojiUploadBtn');
        const emojiUpload = document.getElementById('emojiUpload');
        const customEmojisGrid = document.querySelector('.custom-emojis-grid');

        emojiUploadBtn.addEventListener('click', () => {
            emojiUpload.click();
        });

        emojiUpload.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const id = `emoji-${Date.now()}`;
                    this.customEmojis.set(id, e.target.result);
                    this.addCustomEmojiButton(id, e.target.result, customEmojisGrid);
                };
                reader.readAsDataURL(file);
            }
        });
    }

    addCustomStickerButton(id, imageUrl, container) {
        const button = document.createElement('button');
        button.className = 'custom-sticker-btn';
        button.dataset.sticker = id;
        button.style.backgroundImage = `url(${imageUrl})`;
        button.style.backgroundSize = 'contain'; // Ensure the image is contained within the button
        
        button.addEventListener('click', () => {
            this.toolProperties.sticker.type = id;
            this.toolProperties.sticker.isCustom = true;
            
            document.querySelectorAll('.custom-sticker-btn, .sticker-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            button.classList.add('active');
            
            if (this.currentTool === 'sticker') {
                this.addSticker();
            }
        });
        
        container.appendChild(button);
    }

    addCustomEmojiButton(id, imageUrl, container) {
        const button = document.createElement('button');
        button.className = 'custom-emoji-btn';
        button.dataset.emoji = id;
        button.style.backgroundImage = `url(${imageUrl})`;
        button.style.backgroundSize = 'contain'; // Ensure the image is contained within the button
        
        button.addEventListener('click', () => {
            this.toolProperties.emoji.type = id;
            this.toolProperties.emoji.isCustom = true;
            
            document.querySelectorAll('.custom-emoji-btn, .emoji-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            button.classList.add('active');
            
            if (this.currentTool === 'emoji') {
                this.addEmoji();
            }
        });
        
        container.appendChild(button);
    }

    setupCanvasSettings() {
        const backgroundToggle = document.getElementById('canvasBackgroundToggle');
        const colorPicker = document.getElementById('canvasColorPicker');
        const opacitySlider = document.getElementById('canvasOpacitySlider');
        const opacityValue = document.getElementById('canvasOpacityValue');

        backgroundToggle.addEventListener('change', (e) => {
            this.canvasBackground.solid = e.target.checked;
            this.updateCanvasBackground();
        });

        colorPicker.addEventListener('input', (e) => {
            this.canvasBackground.color = e.target.value;
            if (this.canvasBackground.solid) {
                this.updateCanvasBackground();
            }
        });

        opacitySlider.addEventListener('input', (e) => {
            this.canvasBackground.opacity = e.target.value / 100;
            opacityValue.textContent = `${e.target.value}%`;
            this.updateCanvasBackground();
        });
    }

    // Capture current state including all elements
    captureCurrentState() {
        return {
            canvas: this.canvas.toDataURL(),
            elements: Array.from(document.querySelectorAll('.draggable')).map(el => el.outerHTML)
        };
    }

    // Restore a previous state
    restoreState(state) {
        // Restore canvas
        const img = new Image();
        img.onload = () => {
            this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
            this.ctx.drawImage(img, 0, 0);
        };
        img.src = state.canvas;
        
        // Clear all current elements
        document.querySelectorAll('.draggable').forEach(el => el.remove());
        
        // Restore elements
        const container = document.querySelector('.canvas-container');
        const fragment = document.createDocumentFragment();
        
        state.elements.forEach(htmlString => {
            const div = document.createElement('div');
            div.innerHTML = htmlString;
            
            // Get the element and reattach event listeners
            const element = div.firstChild;
            this.makeElementDraggable(element);
            
            fragment.appendChild(element);
        });
        
        container.appendChild(fragment);
        
        // Clear selection
        this.selectedElement = null;
    }

    selectElement(element) {
        // Deselect previously selected element
        if (this.selectedElement) {
            this.selectedElement.classList.remove('selected');
            this.removeHandles(this.selectedElement);
        }
        
        this.selectedElement = element;
        
        // Select new element if not null
        if (element) {
            element.classList.add('selected');
            this.addHandles(element);
            
            // Add delete button to the bounding box
            const boundingBox = element.querySelector('.bounding-box');
            if (boundingBox && !boundingBox.querySelector('.delete-btn')) {
                const deleteBtn = document.createElement('button');
                deleteBtn.className = 'delete-btn';
                deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
                deleteBtn.style.position = 'absolute';
                deleteBtn.style.top = '-25px';
                deleteBtn.style.right = '-5px';
                deleteBtn.style.background = '#ff4d4d';
                deleteBtn.style.color = 'white';
                deleteBtn.style.border = 'none';
                deleteBtn.style.borderRadius = '50%';
                deleteBtn.style.width = '20px';
                deleteBtn.style.height = '20px';
                deleteBtn.style.cursor = 'pointer';
                deleteBtn.style.zIndex = '10';
                deleteBtn.title = 'Delete element';
                
                deleteBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.deleteSelectedElement();
                });
                
                boundingBox.appendChild(deleteBtn);
            }
            
            // Update property panel based on element type
            if (element.classList.contains('text')) {
                this.showPropertyPanel('text');
            } else if (element.classList.contains('shape')) {
                this.showPropertyPanel('shape');
            } else if (element.classList.contains('emoji-sticker')) {
                if (element.dataset.type === 'sticker') {
                    this.showPropertyPanel('sticker');
                } else if (element.dataset.type === 'emoji') {
                    this.showPropertyPanel('emoji');
                }
            }
        }
    }

    deleteSelectedElement() {
        if (!this.selectedElement) return;
        
        const element = this.selectedElement;
        
        // Save state before deletion for undo
        this.saveState();
        
        // Remove element from DOM
        element.remove();
        this.selectedElement = null;
        
        // Show appropriate property panel for current tool
        this.showPropertyPanel(this.currentTool);
    }

    addHandles(element) {
        // Check if element already has a bounding box
        if (element.querySelector('.bounding-box')) return;
        
        // Create bounding box
        const boundingBox = document.createElement('div');
        boundingBox.className = 'bounding-box';
        element.appendChild(boundingBox);
        
        // Add resize handles
        const handlePositions = ['nw', 'n', 'ne', 'e', 'se', 's', 'sw', 'w'];
        handlePositions.forEach(pos => {
            const handle = document.createElement('div');
            handle.className = `control-handle handle-${pos}`;
            handle.dataset.handle = pos; // Set data-handle attribute
            boundingBox.appendChild(handle);
        });
        
        // Add rotation handle
        const rotateHandle = document.createElement('div');
        rotateHandle.className = 'rotate-handle';
        
        // Add rotation line
        const rotationLine = document.createElement('div');
        rotationLine.className = 'rotation-line';
        rotateHandle.appendChild(rotationLine);
        
        boundingBox.appendChild(rotateHandle);
        
        // Add delete button
        const deleteBtn = document.createElement('button');
        deleteBtn.className = 'delete-btn';
        deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
        deleteBtn.title = 'Delete element';
        
        deleteBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.deleteSelectedElement();
        });
        
        boundingBox.appendChild(deleteBtn);
    }

    removeHandles(element) {
        const boundingBox = element.querySelector('.bounding-box');
        if (boundingBox) {
            boundingBox.remove();
        }
    }

    showPropertyPanel(panel) {
        // Hide all panels first
        document.querySelectorAll('.property-panel').forEach(p => {
            p.classList.remove('active');
        });
        
        // Show the specified panel
        const panelElement = document.getElementById(`${panel}-panel`);
        if (panelElement) {
            panelElement.classList.add('active');
        }
    }

    hidePropertyPanel(panel) {
        const panelElement = document.getElementById(`${panel}-panel`);
        if (panelElement) {
            panelElement.classList.remove('active');
        }
    }

    // Add after handleDraggableMouseDown method

    // Helper method to maintain aspect ratio when resizing
    maintainAspectRatio(width, height, originalWidth, originalHeight) {
        const originalRatio = originalWidth / originalHeight;
        const newRatio = width / height;
        
        if (newRatio > originalRatio) {
            // Width is too large for height
            return { width: height * originalRatio, height };
        } else if (newRatio < originalRatio) {
            // Height is too large for width
            return { width, height: width / originalRatio };
        }
        
        // Ratio is the same
        return { width, height };
    }

    initializePenProperties() {
        // Get pen property elements
        const penColorSwatches = document.getElementById('penColorSwatches');
        const penCustomColorPicker = document.getElementById('penCustomColorPicker');
        const penSizeSlider = document.getElementById('penSizeSlider');
        const penSizeValue = document.getElementById('penSizeValue');
        const penOpacitySlider = document.getElementById('penOpacitySlider');
        const penOpacityValue = document.getElementById('penOpacityValue');
        const penTipStyle = document.getElementById('penTipStyle');
        
        // Set initial values
        if (penCustomColorPicker) penCustomColorPicker.value = this.toolProperties.pen.color;
        if (penSizeSlider) penSizeSlider.value = this.toolProperties.pen.size;
        if (penSizeValue) penSizeValue.textContent = `${this.toolProperties.pen.size}px`;
        if (penOpacitySlider) penOpacitySlider.value = this.toolProperties.pen.opacity * 100;
        if (penOpacityValue) penOpacityValue.textContent = `${Math.round(this.toolProperties.pen.opacity * 100)}%`;
        if (penTipStyle) {
            penTipStyle.value = this.toolProperties.pen.tipStyle || 'round';
            console.log('Initial pen tip style:', penTipStyle.value);
        }
        
        // Set active color swatch
        if (penColorSwatches) {
            const swatches = penColorSwatches.querySelectorAll('.color-swatch');
            swatches.forEach(swatch => {
                // Remove any existing event listeners
                swatch.removeEventListener('click', this._penColorSwatchHandler);
                
                // Set active state for the current color
                if (swatch.dataset.color === this.toolProperties.pen.color) {
                    swatch.classList.add('active');
                } else {
                    swatch.classList.remove('active');
                }
                
                // Add click event listener
                this._penColorSwatchHandler = () => {
                    const color = swatch.dataset.color;
                    
                    // If it's the custom color picker, use the input value
                    if (color === 'custom') {
                        return; // The input event will handle this
                    }
                    
                    console.log('Pen color changed to:', color);
                    this.toolProperties.pen.color = color;
                    
                    // Update active state
                    swatches.forEach(s => s.classList.remove('active'));
                    swatch.classList.add('active');
                };
                
                swatch.addEventListener('click', this._penColorSwatchHandler);
            });
        }
        
        // Handle custom color picker
        if (penCustomColorPicker) {
            penCustomColorPicker.removeEventListener('input', this._penCustomColorChangeHandler);
            this._penCustomColorChangeHandler = (e) => {
                const color = e.target.value;
                console.log('Pen custom color changed:', color);
                this.toolProperties.pen.color = color;
                
                // Update active state
                if (penColorSwatches) {
                    const swatches = penColorSwatches.querySelectorAll('.color-swatch');
                    swatches.forEach(s => s.classList.remove('active'));
                    const customSwatch = penColorSwatches.querySelector('.color-swatch.custom');
                    if (customSwatch) customSwatch.classList.add('active');
                }
            };
            penCustomColorPicker.addEventListener('input', this._penCustomColorChangeHandler);
        }
        
        // Handle size slider
        if (penSizeSlider) {
            penSizeSlider.removeEventListener('input', this._penSizeChangeHandler);
            this._penSizeChangeHandler = (e) => {
                const size = parseInt(e.target.value);
                console.log('Pen size changed:', size);
                this.toolProperties.pen.size = size;
                if (penSizeValue) penSizeValue.textContent = `${size}px`;
            };
            penSizeSlider.addEventListener('input', this._penSizeChangeHandler);
        }
        
        // Handle opacity slider
        if (penOpacitySlider) {
            penOpacitySlider.removeEventListener('input', this._penOpacityChangeHandler);
            this._penOpacityChangeHandler = (e) => {
                const opacityPercent = parseInt(e.target.value);
                const opacity = opacityPercent / 100;
                console.log('Pen opacity changed:', opacity);
                this.toolProperties.pen.opacity = opacity;
                if (penOpacityValue) penOpacityValue.textContent = `${opacityPercent}%`;
            };
            penOpacitySlider.addEventListener('input', this._penOpacityChangeHandler);
        }
        
        // Handle tip style dropdown
        if (penTipStyle) {
            penTipStyle.removeEventListener('change', this._penTipStyleChangeHandler);
            this._penTipStyleChangeHandler = (e) => {
                const style = e.target.value;
                console.log('Pen tip style changed:', style);
                this.toolProperties.pen.tipStyle = style;
            };
            penTipStyle.addEventListener('change', this._penTipStyleChangeHandler);
        }
    }

    calculateMidPoint(x1, y1, x2, y2) {
        return {
            x: (x1 + x2) / 2,
            y: (y1 + y2) / 2
        };
    }

    // Helper method to set up high-resolution canvas
    setupHighResolutionCanvas() {
        // Get the device pixel ratio
        const dpr = window.devicePixelRatio || 1;
        
        // Get the canvas size in CSS pixels
        const rect = this.canvas.getBoundingClientRect();
        
        // Set the canvas size in actual pixels
        this.canvas.width = rect.width * dpr;
        this.canvas.height = rect.height * dpr;
        
        // Scale the context to ensure correct drawing operations
        this.ctx.scale(dpr, dpr);
        
        // Set the canvas CSS size
        this.canvas.style.width = `${rect.width}px`;
        this.canvas.style.height = `${rect.height}px`;
        
        // Enable anti-aliasing
        this.ctx.imageSmoothingEnabled = true;
        this.ctx.imageSmoothingQuality = 'high';
    }

    // Enhanced eraser methods
    showEraserCursor(pos) {
        // Remove any existing eraser cursor
        this.removeEraserCursor();
        
        // Create eraser cursor element
        const cursor = document.createElement('div');
        cursor.className = 'eraser-cursor';
        cursor.dataset.mode = this.toolProperties.eraser.mode;
        
        // Add mode-specific class
        cursor.classList.add(`${this.toolProperties.eraser.mode}-mode`);
        
        cursor.style.position = 'absolute';
        cursor.style.width = `${this.toolProperties.eraser.size}px`;
        cursor.style.height = `${this.toolProperties.eraser.size}px`;
        cursor.style.borderRadius = this.toolProperties.eraser.mode === 'precision' ? '50%' : 
                                    this.toolProperties.eraser.mode === 'stroke' ? '5px' : '50%';
        cursor.style.border = '2px solid rgba(0, 0, 0, 0.5)';
        cursor.style.backgroundColor = 'rgba(255, 255, 255, 0.3)';
        cursor.style.transform = 'translate(-50%, -50%)';
        cursor.style.pointerEvents = 'none';
        cursor.style.zIndex = '9999';
        cursor.style.left = `${pos.x}px`;
        cursor.style.top = `${pos.y}px`;
        
        // Add inner indicator for stroke mode
        if (this.toolProperties.eraser.mode === 'stroke') {
            const indicator = document.createElement('div');
            indicator.className = 'eraser-indicator';
            indicator.style.position = 'absolute';
            indicator.style.top = '50%';
            indicator.style.left = '50%';
            indicator.style.transform = 'translate(-50%, -50%)';
            indicator.style.width = '70%';
            indicator.style.height = '70%';
            indicator.style.backgroundColor = 'rgba(255, 0, 0, 0.2)';
            indicator.style.borderRadius = '3px';
            cursor.appendChild(indicator);
        }
        
        // Add precision indicator
        if (this.toolProperties.eraser.mode === 'precision') {
            const indicator = document.createElement('div');
            indicator.className = 'eraser-indicator';
            indicator.style.position = 'absolute';
            indicator.style.top = '50%';
            indicator.style.left = '50%';
            indicator.style.transform = 'translate(-50%, -50%)';
            indicator.style.width = '20%';
            indicator.style.height = '20%';
            indicator.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
            indicator.style.borderRadius = '50%';
            cursor.appendChild(indicator);
        }
        
        // Add cursor to document
        document.body.appendChild(cursor);
    }
    
    removeEraserCursor() {
        const cursor = document.querySelector('.eraser-cursor');
        if (cursor) {
            cursor.remove();
        }
    }
    
    eraseAtPoint(pos) {
        const eraserSize = this.toolProperties.eraser.size;
        const eraserRadius = eraserSize / 2;
        const eraserMode = this.toolProperties.eraser.mode;
        
        // Find strokes that intersect with the eraser
        let modified = false;
        
        // Create a spatial index for performance if many strokes
        const strokesToCheck = this.canvasStrokes.length > 100 ? 
            this.canvasStrokes.filter(stroke => {
                // Quick bounding box check to reduce checks
                if (!stroke.points || stroke.points.length < 2) return false;
                
                // Find bounding box of the stroke
                let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
                for (const point of stroke.points) {
                    minX = Math.min(minX, point.x);
                    minY = Math.min(minY, point.y);
                    maxX = Math.max(maxX, point.x);
                    maxY = Math.max(maxY, point.y);
                }
                
                // Check if eraser overlaps with bounding box
                return (
                    pos.x + eraserRadius >= minX &&
                    pos.x - eraserRadius <= maxX &&
                    pos.y + eraserRadius >= minY &&
                    pos.y - eraserRadius <= maxY
                );
            }) : this.canvasStrokes;
        
        // Process each stroke
        for (let i = 0; i < strokesToCheck.length; i++) {
            const strokeIndex = this.canvasStrokes.indexOf(strokesToCheck[i]);
            if (strokeIndex === -1) continue; // Stroke was already removed
            
            const stroke = this.canvasStrokes[strokeIndex];
            
            // Skip if the stroke has no points
            if (!stroke.points || stroke.points.length < 2) continue;
            
            // Handle different eraser modes
            switch (eraserMode) {
                case 'stroke':
                    // In stroke mode, erase entire stroke if any point is within range
                    for (let j = 0; j < stroke.points.length; j++) {
                        const point = stroke.points[j];
                        const distance = Math.sqrt(
                            Math.pow(point.x - pos.x, 2) + 
                            Math.pow(point.y - pos.y, 2)
                        );
                        
                        if (distance <= eraserRadius) {
                            // Remove the entire stroke
                            this.canvasStrokes.splice(strokeIndex, 1);
                            modified = true;
                            i--; // Adjust index
                            break;
                        }
                    }
                    break;
                    
                case 'precision':
                    // In precision mode, only erase points that are very close
                    const preciseRadius = eraserRadius * 0.3; // More precise
                    let pointsToRemove = [];
                    
                    for (let j = 0; j < stroke.points.length; j++) {
                        const point = stroke.points[j];
                        const distance = Math.sqrt(
                            Math.pow(point.x - pos.x, 2) + 
                            Math.pow(point.y - pos.y, 2)
                        );
                        
                        if (distance <= preciseRadius) {
                            pointsToRemove.push(j);
                            modified = true;
                        }
                    }
                    
                    // Process removals and splits
                    if (pointsToRemove.length > 0) {
                        this.processPointRemovals(stroke, pointsToRemove, strokeIndex);
                    }
                    break;
                    
                default: // 'pixel' mode (standard eraser)
                    // Find points to remove
                    let pixelPointsToRemove = [];
                    
                    for (let j = 0; j < stroke.points.length; j++) {
                        const point = stroke.points[j];
                        const distance = Math.sqrt(
                            Math.pow(point.x - pos.x, 2) + 
                            Math.pow(point.y - pos.y, 2)
                        );
                        
                        if (distance <= eraserRadius) {
                            pixelPointsToRemove.push(j);
                            modified = true;
                        }
                    }
                    
                    // Process removals and splits
                    if (pixelPointsToRemove.length > 0) {
                        this.processPointRemovals(stroke, pixelPointsToRemove, strokeIndex);
                    }
            }
        }
        
        // Redraw the canvas if any changes were made
        if (modified) {
            this.redrawCanvas();
        }
        
        return modified;
    }
    
    // Helper method to process point removals and handle stroke splitting
    processPointRemovals(stroke, pointsToRemove, strokeIndex) {
        // Sort points in descending order to remove from the end first
        pointsToRemove.sort((a, b) => b - a);
        
        // If we're removing all points or almost all, just remove the stroke
        if (pointsToRemove.length >= stroke.points.length - 1) {
            this.canvasStrokes.splice(strokeIndex, 1);
            return;
        }
        
        // Identify continuous segments to be removed
        let segments = [];
        let currentSegment = [];
        let removeSequence = new Set(pointsToRemove);
        
        // Build valid segments
        for (let i = 0; i < stroke.points.length; i++) {
            if (removeSequence.has(i)) {
                if (currentSegment.length > 0) {
                    segments.push([...currentSegment]);
                    currentSegment = [];
                }
            } else {
                currentSegment.push(stroke.points[i]);
            }
        }
        
        // Add the last segment if not empty
        if (currentSegment.length > 0) {
            segments.push(currentSegment);
        }
        
        // Handle segmentation
        if (segments.length === 0) {
            // No valid segments left
            this.canvasStrokes.splice(strokeIndex, 1);
        } else if (segments.length === 1) {
            // Only one segment, replace the current stroke
            stroke.points = segments[0];
        } else {
            // Multiple segments, create new strokes for each one
            stroke.points = segments[0];
            
            // Create new strokes for additional segments
            for (let i = 1; i < segments.length; i++) {
                if (segments[i].length >= 2) { // Only create strokes with at least 2 points
                    this.canvasStrokes.push({
                        tool: stroke.tool,
                        points: segments[i],
                        properties: {...stroke.properties}
                    });
                }
            }
        }
    }

    // Update eraser mode description
    updateEraserModeDescription(mode) {
        const pixelDesc = document.getElementById('pixel-desc');
        const strokeDesc = document.getElementById('stroke-desc');
        const precisionDesc = document.getElementById('precision-desc');
        
        if (pixelDesc && strokeDesc && precisionDesc) {
            pixelDesc.style.display = 'none';
            strokeDesc.style.display = 'none';
            precisionDesc.style.display = 'none';
            
            switch (mode) {
                case 'stroke':
                    strokeDesc.style.display = 'block';
                    break;
                case 'precision':
                    precisionDesc.style.display = 'block';
                    break;
                default: // pixel
                    pixelDesc.style.display = 'block';
            }
        }
    }
}

// Initialize the app when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new AnnotationApp();
});