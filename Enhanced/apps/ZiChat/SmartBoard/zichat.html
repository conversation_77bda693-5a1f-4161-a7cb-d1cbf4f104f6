<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Real Estate Annotation App</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="app-container">
        <!-- Main Toolbar - Vertical -->
        <div class="main-toolbar vertical">
            <!-- Toolbar sections -->
            <div class="tool-section bg-control-section">
                <h3>Background</h3>
                <div class="bg-controls">
                    <div class="bg-preview" title="Background Type">
                        <div class="bg-icon transparent active" data-bg-type="transparent">
                            <i class="fas fa-border-none"></i>
                        </div>
                        <div class="bg-icon solid" data-bg-type="solid">
                            <i class="fas fa-square"></i>
                        </div>
                    </div>
                    <input type="color" id="bgColorPicker" value="#ffffff" title="Background Color">
                </div>
            </div>
            
            <div class="tool-section">
                <h3>Draw</h3>
                <button class="tool-btn" data-tool="pen" title="Pen">
                    <i class="fas fa-pen"></i>
                </button>
                <button class="tool-btn" data-tool="highlighter" title="Highlighter">
                    <i class="fas fa-highlighter"></i>
                </button>
                <button class="tool-btn" data-tool="eraser" title="Eraser">
                    <i class="fas fa-eraser"></i>
                </button>
            </div>
            
            <div class="tool-section">
                <h3>Add</h3>
                <button class="tool-btn" data-tool="text" title="Text">
                    <i class="fas fa-font"></i>
                </button>
                <button class="tool-btn" data-tool="shape" title="Shapes">
                    <i class="fas fa-shapes"></i>
                </button>
                <button class="tool-btn" data-tool="sticker" title="Stickers">
                    <i class="fas fa-star"></i>
                </button>
                <button class="tool-btn" data-tool="emoji" title="Emojis">
                    <i class="fas fa-smile"></i>
                </button>
            </div>
            
            <div class="tool-section">
                <h3>View</h3>
                <button class="tool-btn" data-tool="grid" title="Toggle Grid">
                    <i class="fas fa-border-all"></i>
                </button>
            </div>
            
            <div class="tool-section">
                <h3>Edit</h3>
                <button class="tool-btn" data-tool="undo" title="Undo">
                    <i class="fas fa-undo"></i>
                </button>
                <button class="tool-btn" data-tool="redo" title="Redo">
                    <i class="fas fa-redo"></i>
                </button>
                <button class="tool-btn" data-tool="clear" title="Clear All">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
        <button class="toggle-toolbar">
            <i class="fas fa-chevron-left"></i>
        </button>

        <!-- Dynamic Property Panels -->
        <div class="property-panels">
            <button class="toggle-properties">
                <i class="fas fa-chevron-right"></i>
            </button>
            
            <!-- Pen Properties Panel -->
            <div class="property-panel" id="pen-panel">
                <h3>Pen Properties</h3>
                <div class="property-item">
                    <label>Tip Style</label>
                    <select id="penTipStyle">
                        <option value="round">Round</option>
                        <option value="calligraphy">Calligraphy</option>
                        <option value="square">Square</option>
                        <option value="brush">Brush</option>
                        <option value="dotted">Dotted Line</option>
                    </select>
                </div>
                <div class="property-item">
                    <label>Color</label>
                    <div class="color-swatches" id="penColorSwatches">
                        <div class="color-swatch" data-color="#000000" style="background-color: #000000;"></div>
                        <div class="color-swatch" data-color="#FF0000" style="background-color: #FF0000;"></div>
                        <div class="color-swatch" data-color="#0000FF" style="background-color: #0000FF;"></div>
                        <div class="color-swatch" data-color="#008000" style="background-color: #008000;"></div>
                        <div class="color-swatch" data-color="#FFA500" style="background-color: #FFA500;"></div>
                        <div class="color-swatch" data-color="#800080" style="background-color: #800080;"></div>
                        <div class="color-swatch" data-color="#A52A2A" style="background-color: #A52A2A;"></div>
                        <div class="color-swatch" data-color="#FFC0CB" style="background-color: #FFC0CB;"></div>
                        <div class="color-swatch" data-color="#808080" style="background-color: #808080;"></div>
                        <div class="color-swatch custom" data-color="custom">
                            <input type="color" id="penCustomColorPicker" value="#000000">
                        </div>
                    </div>
                </div>
                <div class="property-item">
                    <label>Size</label>
                    <input type="range" id="penSizeSlider" min="1" max="50" value="5">
                    <span id="penSizeValue">5px</span>
                </div>
                <div class="property-item">
                    <label>Opacity</label>
                    <input type="range" id="penOpacitySlider" min="1" max="100" value="100">
                    <span id="penOpacityValue">100%</span>
                </div>
            </div>

            <!-- Highlighter Properties Panel -->
            <div class="property-panel" id="highlighter-panel">
                <h3>Highlighter Properties</h3>
                <div class="property-item">
                    <label>Color</label>
                    <input type="color" id="highlighterColorPicker" value="#FFFF00">
                </div>
                <div class="property-item">
                    <label>Size</label>
                    <input type="range" id="highlighterSizeSlider" min="5" max="50" value="20">
                </div>
                <div class="property-item">
                    <label>Opacity: <span id="highlighterOpacityValue">50%</span></label>
                    <input type="range" id="highlighterOpacitySlider" min="1" max="100" value="50">
                </div>
            </div>

            <!-- Eraser Properties Panel -->
            <div class="property-panel" id="eraser-panel">
                <h3>Eraser Properties</h3>
                <div class="property-item">
                    <label>Mode</label>
                    <select id="eraserModeSelect">
                        <option value="pixel">Pixel Mode</option>
                        <option value="stroke">Stroke Mode</option>
                        <option value="precision">Precision Mode</option>
                    </select>
                    <div class="mode-description">
                        <span id="pixel-desc">Erases all points within range</span>
                        <span id="stroke-desc" style="display: none;">Erases entire strokes when touched</span>
                        <span id="precision-desc" style="display: none;">Erases only exact points</span>
                    </div>
                </div>
                <div class="property-item">
                    <label>Size</label>
                    <input type="range" id="eraserSizeSlider" min="5" max="100" value="20">
                    <span id="eraserSizeValue">20px</span>
                </div>
                <div class="property-item eraser-preview">
                    <div class="eraser-preview-circle"></div>
                    <span>Eraser Preview</span>
                </div>
            </div>

            <!-- Text Properties Panel -->
            <div class="property-panel" id="text-panel">
                <h3>Text Properties</h3>
                <div class="property-item">
                    <label>Font</label>
                    <select id="fontFamily">
                        <option value="Arial">Arial</option>
                        <option value="Times New Roman">Times New Roman</option>
                        <option value="Helvetica">Helvetica</option>
                        <option value="Verdana">Verdana</option>
                        <option value="Georgia">Georgia</option>
                    </select>
                </div>
                <div class="property-item">
                    <label>Size</label>
                    <input type="range" id="fontSizeSlider" min="8" max="72" value="16">
                    <span id="fontSizeValue">16px</span>
                </div>
                <div class="property-item">
                    <label>Font Weight</label>
                    <div class="font-weight-options">
                        <div class="font-weight-btn active" data-weight="normal">Normal</div>
                        <div class="font-weight-btn" data-weight="bold">Bold</div>
                        <div class="font-weight-btn" data-weight="lighter">Light</div>
                    </div>
                </div>
                <div class="property-item">
                    <label>Text Color</label>
                    <input type="color" id="textColorPicker" value="#000000">
                </div>
                <div class="property-item">
                    <label>Background Color</label>
                    <input type="color" id="textBgColorPicker" value="#ffffff">
                </div>
                <div class="property-item">
                    <label>Opacity: <span id="textOpacityValue">100%</span></label>
                    <input type="range" id="textOpacitySlider" min="1" max="100" value="100">
                </div>
                <div class="property-item">
                    <label>Background</label>
                    <div class="toggle-switch">
                        <input type="checkbox" id="textBackgroundToggle">
                        <label for="textBackgroundToggle"></label>
                        <span>Transparent</span>
                    </div>
                </div>
            </div>

            <!-- Shape Properties Panel -->
            <div class="property-panel" id="shape-panel">
                <h3>Shape Properties</h3>
                <div class="property-item">
                    <label>Shape Type</label>
                    <div class="shape-options">
                        <button class="shape-btn" data-shape="rectangle">
                            <i class="fas fa-square"></i>
                        </button>
                        <button class="shape-btn" data-shape="circle">
                            <i class="fas fa-circle"></i>
                        </button>
                        <button class="shape-btn" data-shape="line">
                            <i class="fas fa-minus"></i>
                        </button>
                        <button class="shape-btn" data-shape="arrow">
                            <i class="fas fa-arrow-right"></i>
                        </button>
                    </div>
                </div>
                <div class="property-item">
                    <label>Color</label>
                    <input type="color" id="shapeColorPicker" value="#000000">
                </div>
                <div class="property-item">
                    <label>Line Width</label>
                    <input type="range" id="shapeLineWidthSlider" min="1" max="20" value="3">
                </div>
                <div class="property-item">
                    <label>Opacity: <span id="shapeOpacityValue">100%</span></label>
                    <input type="range" id="shapeOpacitySlider" min="1" max="100" value="100">
                </div>
                <div class="property-item">
                    <label>Fill</label>
                    <div class="toggle-switch">
                        <input type="checkbox" id="shapeFillToggle">
                        <label for="shapeFillToggle"></label>
                        <span>Fill Shape</span>
                    </div>
                </div>
            </div>

            <!-- Sticker Properties Panel -->
            <div class="property-panel" id="sticker-panel">
                <h3>Sticker Properties</h3>
                <div class="property-item">
                    <label>Size</label>
                    <input type="range" id="stickerSizeSlider" min="20" max="200" value="30">
                    <span id="stickerSizeValue">30px</span>
                </div>
                <div class="property-item">
                    <label>Opacity</label>
                    <input type="range" id="stickerOpacitySlider" min="0" max="100" value="100">
                    <span id="stickerOpacityValue">100%</span>
                </div>
                <div class="property-item sticker-baker">
                    <label>Sticker Baker</label>
                    <input type="file" id="stickerUpload" accept=".png,.svg" style="display: none;">
                    <button id="stickerUploadBtn" class="upload-btn">Import PNG/SVG</button>
                    <div class="custom-stickers-grid">
                        <!-- Custom stickers will be added here -->
                    </div>
                </div>
                <div class="sticker-grid">
                    <!-- Default stickers -->
                    <button class="sticker-btn" data-sticker="⭐">⭐</button>
                    <button class="sticker-btn" data-sticker="❤️">❤️</button>
                    <button class="sticker-btn" data-sticker="✅">✅</button>
                    <button class="sticker-btn" data-sticker="❌">❌</button>
                    <button class="sticker-btn" data-sticker="🏠">🏠</button>
                    <button class="sticker-btn" data-sticker="🔑">🔑</button>
                    <button class="sticker-btn" data-sticker="📍">📍</button>
                    <button class="sticker-btn" data-sticker="💵">💵</button>
                </div>
            </div>

            <!-- Emoji Properties Panel -->
            <div class="property-panel" id="emoji-panel">
                <h3>Emoji Properties</h3>
                <div class="property-item">
                    <label>Size</label>
                    <input type="range" id="emojiSizeSlider" min="20" max="200" value="30">
                    <span id="emojiSizeValue">30px</span>
                </div>
                <div class="property-item">
                    <label>Opacity</label>
                    <input type="range" id="emojiOpacitySlider" min="0" max="100" value="100">
                    <span id="emojiOpacityValue">100%</span>
                </div>
                <div class="property-item emoji-baker">
                    <label>Emoji Baker</label>
                    <input type="file" id="emojiUpload" accept=".png,.svg" style="display: none;">
                    <button id="emojiUploadBtn" class="upload-btn">Import PNG/SVG</button>
                    <div class="custom-emojis-grid">
                        <!-- Custom emojis will be added here -->
                    </div>
                </div>
                <div class="emoji-grid">
                    <!-- Default emojis -->
                    <button class="emoji-btn" data-emoji="😊">😊</button>
                    <button class="emoji-btn" data-emoji="😂">😂</button>
                    <button class="emoji-btn" data-emoji="👍">👍</button>
                    <button class="emoji-btn" data-emoji="👎">👎</button>
                    <button class="emoji-btn" data-emoji="❤️">❤️</button>
                    <button class="emoji-btn" data-emoji="🎉">🎉</button>
                    <button class="emoji-btn" data-emoji="🌟">🌟</button>
                    <button class="emoji-btn" data-emoji="💡">💡</button>
                </div>
            </div>
            
            <!-- Grid Properties Panel -->
            <div class="property-panel" id="grid-panel">
                <h3>Grid Properties</h3>
                <div class="property-item">
                    <label>Grid Size</label>
                    <input type="range" id="gridSizeSlider" min="10" max="100" value="20">
                    <span id="gridSizeValue">20px</span>
                </div>
                <div class="property-item">
                    <label>Opacity: <span id="gridOpacityValue">70%</span></label>
                    <input type="range" id="gridOpacitySlider" min="10" max="100" value="70">
                </div>
            </div>
        </div>

        <!-- Canvas Container -->
        <div class="canvas-container">
            <div class="grid-overlay"></div>
            <canvas id="annotationCanvas"></canvas>
            <div id="textInput" class="text-input" style="display: none;">
                <input type="text" placeholder="Type here...">
            </div>
        </div>
    </div>
    <script src="script.js"></script>
</body>
</html> 