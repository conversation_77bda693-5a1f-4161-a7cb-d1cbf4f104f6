/* Grid Overlay */
.grid-overlay {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background-image: linear-gradient(rgba(0, 0, 0, 0.8) 1px, transparent 1px),
                      linear-gradient(90deg, rgba(0, 0, 0, 0.8) 1px, transparent 1px) !important;
    background-size: 20px 20px !important;
    pointer-events: none !important;
    display: none !important;
    z-index: 0 !important;
    opacity: 0.7 !important;
}

.grid-overlay.visible {
    display: block !important;
}

/* Bounding Box */
.bounding-box {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    border: 2px dashed #3498db !important;
    box-sizing: border-box !important;
    pointer-events: none !important;
    z-index: 1 !important;
    display: none !important;
    opacity: 0.8 !important;
}

.draggable.selected .bounding-box {
    display: block !important;
}

.draggable:hover .bounding-box {
    display: block !important;
}

/* Control Handles */
.control-handle {
    position: absolute !important;
    width: 10px !important;
    height: 10px !important;
    background: #fff !important;
    border: 2px solid #3498db !important;
    border-radius: 50% !important;
    z-index: 2 !important;
    cursor: pointer !important;
}

.handle-nw { top: -5px !important; left: -5px !important; cursor: nwse-resize !important; }
.handle-n { top: -5px !important; left: calc(50% - 5px) !important; cursor: ns-resize !important; }
.handle-ne { top: -5px !important; right: -5px !important; cursor: nesw-resize !important; }
.handle-e { top: calc(50% - 5px) !important; right: -5px !important; cursor: ew-resize !important; }
.handle-se { bottom: -5px !important; right: -5px !important; cursor: nwse-resize !important; }
.handle-s { bottom: -5px !important; left: calc(50% - 5px) !important; cursor: ns-resize !important; }
.handle-sw { bottom: -5px !important; left: -5px !important; cursor: nesw-resize !important; }
.handle-w { top: calc(50% - 5px) !important; left: -5px !important; cursor: ew-resize !important; } 