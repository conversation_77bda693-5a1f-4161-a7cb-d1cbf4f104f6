var edGradients = {
    1: {
        type: 'linear',
        x1: -80,
        y1: 0,
        x2: 80,
        y2: 0,
        colorStops: {
            0: '#ffe47b',
            1: 'rgb(111,154,211)'
        }
    },

    2:  {
        type: 'linear',
        x1: 0,
        y1: -120,
        x2: 0,
        y2: 120,
        colorStops: {
            0: '#ff4040',
            1: '#e6399b'
        }
    },

    3: {
        type: 'linear',
        x1: -90,
        y1: -90,
        x2: 90,
        y2: 90,
        colorStops: {
            0: 'rgb(166,111,213)',
            0.5: 'rgba(106, 72, 215, 0.5)',
            1: '#200772'
        }
    },

    4: {
        type: 'radial',
        r1: 100,
        r2: 10,
        x1: 0,
        y1: 0,
        x2: 0,
        y2: 0,
        colorStops: {
            0: '#FF4F4F',
            1: 'rgb(255, 239, 64)'
        }
    },

    5: {
        type: 'radial',
        r1: 100,
        r2: 10,
        x1: 0,
        y1: 0,
        x2: 20,
        y2: 20,
        colorStops: {
            0: '#ffe47b',
            0.5: 'rgb(111,154,211)',
            1: 'rgb(166,111,213)'
        }
    },

    6: {
        type: 'radial',
        r1: 50,
        r2: 80,
        x1: 45,
        y1: 45,
        x2: 52,
        y2: 50,
        colorStops: {
            0: 'rgb(155, 237, 0)',
            1: 'rgba(0, 164, 128,0.4)'
        }
    },

    7: {
        type: 'linear',
        x1: -90,
        y1: -90,
        x2: 90,
        y2: 90,
        colorStops: {
            0: '#9ecb2d',
            0.5: '#72aa00',
            1: '#bfd255'
        }
    },

    8: {
        type: 'radial',
        r1: 100,
        r2: 50,
        x1: 30,
        y1: 0,
        x2: 0,
        y2: 0,
        colorStops: {
            0: '#aebcbf',
            1: '#0a0809'
        }
    },

    9: {
        type: 'linear',
        x1: -80,
        y1: 0,
        x2: 80,
        y2: 0,
        colorStops: {
            0: '#ffffff',
            1: '#f6f6f6'
        }
    },
    10:  {
        type: 'linear',
        x1: 0,
        y1: -120,
        x2: 0,
        y2: 120,
        colorStops: {
            0: '#fefcea',
            1: '#f1da36'
        }
    },
    11: {
        type: 'linear',
        x1: -90,
        y1: -90,
        x2: 90,
        y2: 90,
        colorStops: {
            0: 'rgb(166,111,213)',
            0.5: 'rgba(106, 72, 215, 0.5)',
            1: '#ff1a00'
        }
    },
    12:  {
        type: 'linear',
        x1: 0,
        y1: -120,
        x2: 0,
        y2: 120,
        colorStops: {
            0: '#b7deed',
            1: '#21b4e2'
        }
    },
    13: {
        type: 'linear',
        x1: -80,
        y1: 100,
        x2: 80,
        y2: -100,
        colorStops: {
            0: '#ffe47b',
            1: 'rgb(111,154,211)'
        }
    },
};