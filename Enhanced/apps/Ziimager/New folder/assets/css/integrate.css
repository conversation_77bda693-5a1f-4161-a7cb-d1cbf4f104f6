#pixie-editor-container {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 9999999;
}
#pixie-editor-container #pixie-frame-container {
  position: fixed;
  width: 80%;
  height: 80%;
  top: 10%;
  left: 10%;
  box-shadow: inset 0 -2px 5px rgba(61, 61, 61, 0.5), 0 6px 44px rgba(0, 0, 0, 0.7);
  border-radius: 5px;
  border-right: 5px solid #263238;
  border-bottom: 5px solid #263238;
  border-left: 1px solid #263238;
  border-top: 1px solid #263238;
}
#pixie-editor-container #pixie-editor-header {
  height: 3%;
  height: 28px;
  overflow: hidden;
  background-color: #263238;
  color: #fff;
  text-align: center;
  box-sizing: border-box;
}
#pixie-editor-container #pixie-editor-header .pixie-close {
  line-height: 1.2;
  position: absolute;
  right: -18px;
  top: -13px;
  cursor: pointer;
  font-size: 35px;
  font-weight: bold;
  background-color: #3c96db;
  border-radius: 50%;
  padding: 0 12px 0 13px;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.26);
  transition: box-shadow 0.4s cubic-bezier(0.25, 0.8, 0.25, 1), transform 0.4s cubic-bezier(0.25, 0.8, 0.25, 1), color 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}
#pixie-editor-container #pixie-editor-header .pixie-close:hover {
  transform: translate3d(0, -1px, 0);
  background-color: #3c96db;
}
#pixie-editor-container iframe {
  width: 100%;
  height: 97%;
  height: calc(100% - 28px);
  border: none;
  border-bottom: 1px solid #263238;
}
body.noscroll {
  overflow: hidden !important;
}
