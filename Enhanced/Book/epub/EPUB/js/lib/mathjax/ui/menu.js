!function(o){var n={};function i(t){if(n[t])return n[t].exports;var e=n[t]={i:t,l:!1,exports:{}};return o[t].call(e.exports,e,e.exports,i),e.l=!0,e.exports}i.m=o,i.c=n,i.d=function(t,e,o){i.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:o})},i.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},i.t=function(e,t){if(1&t&&(e=i(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var o=Object.create(null);if(i.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var n in e)i.d(o,n,function(t){return e[t]}.bind(null,n));return o},i.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return i.d(e,"a",e),e},i.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},i.p="",i(i.s=8)}([function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.APPEND=MathJax._.util.Options.APPEND,e.REMOVE=MathJax._.util.Options.REMOVE,e.Expandable=MathJax._.util.Options.Expandable,e.expandable=MathJax._.util.Options.expandable,e.makeArray=MathJax._.util.Options.makeArray,e.keys=MathJax._.util.Options.keys,e.copy=MathJax._.util.Options.copy,e.insert=MathJax._.util.Options.insert,e.defaultOptions=MathJax._.util.Options.defaultOptions,e.userOptions=MathJax._.util.Options.userOptions,e.selectOptions=MathJax._.util.Options.selectOptions,e.selectOptionsFromKeys=MathJax._.util.Options.selectOptionsFromKeys,e.separateOptions=MathJax._.util.Options.separateOptions},function(t,e,o){"use strict";var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),l=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,o=e&&t[e],n=0;if(o)return o.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},c=this&&this.__read||function(t,e){var o="function"==typeof Symbol&&t[Symbol.iterator];if(!o)return t;var n,i,r=o.call(t),s=[];try{for(;(void 0===e||0<e--)&&!(n=r.next()).done;)s.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(o=r.return)&&o.call(r)}finally{if(i)throw i.error}}return s};Object.defineProperty(e,"__esModule",{value:!0});var r,s=(r=ContextMenu.ContextMenu,i(p,r),p.prototype.post=function(t,e){if(this.mathItem){if(void 0!==e){var o=this.mathItem.inputJax.name,n=this.findID("Show","Original");n.content="MathML"===o?"Original MathML":o+" Commands",this.findID("Copy","Original").content=n.content;var i=this.findID("Settings","semantics");"MathML"===o?i.disable():i.enable(),this.getAnnotationMenu(),this.dynamicSubmenus()}r.prototype.post.call(this,t,e)}},p.prototype.unpost=function(){r.prototype.unpost.call(this),this.mathItem=null},p.prototype.findID=function(){for(var e,t,o=[],n=0;n<arguments.length;n++)o[n]=arguments[n];var i=this,r=null;try{for(var s=l(o),a=s.next();!a.done;a=s.next()){var u=a.value;i?i=(r=i.find(u))instanceof ContextMenu.Submenu?r.getSubmenu():null:r=null}}catch(t){e={error:t}}finally{try{a&&!a.done&&(t=s.return)&&t.call(s)}finally{if(e)throw e.error}}return r},p.prototype.getAnnotationMenu=function(){var t=this,e=this.getAnnotations(this.getSemanticNode());this.createAnnotationMenu("Show",e,function(){return t.showAnnotation.post()}),this.createAnnotationMenu("Copy",e,function(){return t.copyAnnotation()})},p.prototype.getSemanticNode=function(){for(var t=this.mathItem.root;t&&!t.isKind("semantics");){if(t.isToken||1!==t.childNodes.length)return;t=t.childNodes[0]}return t},p.prototype.getAnnotations=function(t){var e,o,n=[];if(!t)return n;try{for(var i=l(t.childNodes),r=i.next();!r.done;r=i.next()){var s=r.value;if(s.isKind("annotation")){var a=this.annotationMatch(s);if(a){var u=s.childNodes.reduce(function(t,e){return t+e.toString()},"");n.push([a,u])}}}}catch(t){e={error:t}}finally{try{r&&!r.done&&(o=i.return)&&o.call(i)}finally{if(e)throw e.error}}return n},p.prototype.annotationMatch=function(t){var e,o,n=t.attributes.get("encoding");try{for(var i=l(Object.keys(this.annotationTypes)),r=i.next();!r.done;r=i.next()){var s=r.value;if(0<=this.annotationTypes[s].indexOf(n))return s}}catch(t){e={error:t}}finally{try{r&&!r.done&&(o=i.return)&&o.call(i)}finally{if(e)throw e.error}}return null},p.prototype.createAnnotationMenu=function(t,e,i){var r=this,o=this.findID(t,"Annotation");o.setSubmenu(ContextMenu.SubMenu.parse({items:e.map(function(t){var e=c(t,2),o=e[0],n=e[1];return{type:"command",id:o,content:o,action:function(){r.annotation=n,i()}}}),id:"annotations"},o)),e.length?o.enable():o.disable()},p.prototype.dynamicSubmenus=function(){var e,t;try{for(var o=l(p.DynamicSubmenus),n=o.next();!n.done;n=o.next()){var i=c(n.value,2),r=i[0],s=i[1],a=this.find(r);if(a){var u=s(this,a);a.setSubmenu(u),u.getItems().length?a.enable():a.disable()}}}catch(t){e={error:t}}finally{try{n&&!n.done&&(t=o.return)&&t.call(o)}finally{if(e)throw e.error}}},p.DynamicSubmenus=new Map,p);function p(){var t=null!==r&&r.apply(this,arguments)||this;return t.mathItem=null,t.annotation="",t.annotationTypes={},t}e.MJContextMenu=s},function(t,e,o){"use strict";var c=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,o=e&&t[e],n=0;if(o)return o.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0});var r=o(3),l=o(4),n=o(0),i=o(1),s=o(5),a=o(6),u="undefined"!=typeof window&&window.navigator&&"Mac"===window.navigator.platform.substr(0,3),p=(Object.defineProperty(h.prototype,"isLoading",{get:function(){return 0<h.loading},enumerable:!0,configurable:!0}),Object.defineProperty(h.prototype,"loadingPromise",{get:function(){return this.isLoading?(h._loadingPromise||(h._loadingPromise=new Promise(function(t,e){h._loadingOK=t,h._loadingFailed=e})),h._loadingPromise):Promise.resolve()},enumerable:!0,configurable:!0}),h.prototype.initSettings=function(){this.settings=this.options.settings,this.jax=this.options.jax;var t=this.document.outputJax;this.jax[t.name]=t,this.settings.renderer=t.name,window.MathJax._.a11y&&window.MathJax._.a11y.explorer&&Object.assign(this.settings,this.document.options.a11y),this.settings.scale=t.options.scale,this.defaultSettings=Object.assign({},this.settings)},h.prototype.initMenu=function(){var e=this;this.menu=i.MJContextMenu.parse({menu:{id:"MathJax_Menu",pool:[this.variable("texHints"),this.variable("semantics"),this.variable("zoom"),this.variable("zscale"),this.variable("renderer",function(t){return e.setRenderer(t)}),this.variable("alt"),this.variable("cmd"),this.variable("ctrl"),this.variable("shift"),this.variable("scale",function(t){return e.setScale(t)}),this.variable("explorer",function(t){return e.setExplorer(t)}),this.a11yVar("highlight"),this.a11yVar("backgroundColor"),this.a11yVar("foregroundColor"),this.a11yVar("speech"),this.a11yVar("subtitles"),this.a11yVar("braille"),this.a11yVar("viewBraille"),this.a11yVar("speechRules"),this.a11yVar("magnification"),this.a11yVar("magnify"),this.a11yVar("treeColoring"),this.a11yVar("infoType"),this.a11yVar("infoRole"),this.a11yVar("infoPrefix"),this.variable("autocollapse"),this.variable("collapsible",function(t){return e.setCollapsible(t)}),this.variable("inTabOrder",function(t){return e.setTabOrder(t)})],items:[this.submenu("Show","Show Math As",[this.command("MathMLcode","MathML Code",function(){return e.mathmlCode.post()}),this.command("Original","Original Form",function(){return e.originalText.post()}),this.submenu("Annotation","Annotation")]),this.submenu("Copy","Copy to Clipboard",[this.command("MathMLcode","MathML Code",function(){return e.copyMathML()}),this.command("Original","Original Form",function(){return e.copyOriginal()}),this.submenu("Annotation","Annotation")]),this.rule(),this.submenu("Settings","Math Settings",[this.submenu("Renderer","Math Renderer",this.radioGroup("renderer",[["CHTML"],["SVG"]])),this.rule(),this.submenu("ZoomTrigger","Zoom Trigger",[this.command("ZoomNow","Zoom Once Now",function(){return e.zoom(null,"",e.menu.mathItem)}),this.rule(),this.radioGroup("zoom",[["Click"],["DoubleClick","Double-Click"],["NoZoom","No Zoom"]]),this.rule(),this.label("TriggerRequires","Trigger Requires:"),this.checkbox(u?"Option":"Alt",u?"Option":"Alt","alt"),this.checkbox("Command","Command","cmd",{hidden:!u}),this.checkbox("Control","Control","ctrl",{hiddne:u}),this.checkbox("Shift","Shift","shift")]),this.submenu("ZoomFactor","Zoom Factor",this.radioGroup("zscale",[["150%"],["175%"],["200%"],["250%"],["300%"],["400%"]])),this.rule(),this.command("Scale","Scale All Math...",function(){return e.scaleAllMath()}),this.rule(),this.checkbox("texHints","Add TeX hints to MathML","texHints"),this.checkbox("semantics","Add original as annotation","semantics"),this.rule(),this.command("Reset","Reset to defaults",function(){return e.resetDefaults()})]),this.submenu("Accessibility","Accessibility",[this.checkbox("Activate","Activate","explorer"),this.submenu("Speech","Speech",[this.checkbox("Speech","Speech Output","speech"),this.checkbox("Subtitles","Speech Subtities","subtitles"),this.checkbox("Braille","Braille Output","braille"),this.checkbox("View Braille","Braille Subtitles","viewBraille"),this.rule(),this.submenu("Mathspeak","Mathspeak Rules",this.radioGroup("speechRules",[["mathspeak-default","Verbose"],["mathspeak-brief","Brief"],["mathspeak-sbrief","Superbrief"]])),this.submenu("Clearspeak","Clearspeak Rules",this.radioGroup("speechRules",[["clearspeak-default","Auto"]])),this.submenu("ChromeVox","ChromeVox Rules",this.radioGroup("speechRules",[["default-default","Verbose"],["default-short","Short"],["default-alternative","Alternative"]]))]),this.submenu("Highlight","Highlight",[this.submenu("Background","Background",this.radioGroup("backgroundColor",[["Blue"],["Red"],["Green"],["Yellow"],["Cyan"],["Magenta"],["White"],["Black"]])),this.submenu("Foreground","Foreground",this.radioGroup("foregroundColor",[["Black"],["White"],["Magenta"],["Cyan"],["Yellow"],["Green"],["Red"],["Blue"]])),this.rule(),this.radioGroup("highlight",[["None"],["Hover"],["Flame"]]),this.rule(),this.checkbox("TreeColoring","Tree Coloring","treeColoring")]),this.submenu("Magnification","Magnification",[this.radioGroup("magnification",[["None"],["Keyboard"],["Mouse"]]),this.rule(),this.radioGroup("magnify",[["200%"],["300%"],["400%"],["500%"]])]),this.submenu("Semantic Info","Semantic Info",[this.checkbox("Type","Type","infoType"),this.checkbox("Role","Role","infoRole"),this.checkbox("Prefix","Prefix","infoPrefix")],!0),this.rule(),this.checkbox("Collapsible","Collapsible Math","collapsible"),this.checkbox("AutoCollapse","Auto Collapse","autocollapse",{disabled:!0}),this.rule(),this.checkbox("InTabOrder","Include in Tab Order","inTabOrder")]),this.submenu("Language","Language"),this.rule(),this.command("About","About MathJax",function(){return e.about.post()}),this.command("Help","MathJax Help",function(){return e.help.post()})]}});var t=this.menu;this.about.attachMenu(t),this.help.attachMenu(t),this.originalText.attachMenu(t),this.annotationText.attachMenu(t),this.mathmlCode.attachMenu(t),this.zoomBox.attachMenu(t),this.checkLoadableItems(),this.enableExplorerItems(this.settings.explorer),t.showAnnotation=this.annotationText,t.copyAnnotation=this.copyAnnotation.bind(this),t.annotationTypes=this.options.annotationTypes,ContextMenu.CssStyles.addInfoStyles(this.document.document),ContextMenu.CssStyles.addMenuStyles(this.document.document)},h.prototype.checkLoadableItems=function(){var e,t,o=window.MathJax;if(o&&o._&&o.loader&&o.startup)!this.settings.collapsible||o._.a11y&&o._.a11y.complexity||this.loadA11y("complexity"),!this.settings.explorer||o._.a11y&&o._.a11y.explorer||this.loadA11y("explorer");else{var n=this.menu;try{for(var i=c(Object.keys(this.jax)),r=i.next();!r.done;r=i.next()){var s=r.value;this.jax[s]||n.findID("Settings","Renderer",s).disable()}}catch(t){e={error:t}}finally{try{r&&!r.done&&(t=i.return)&&t.call(i)}finally{if(e)throw e.error}}n.findID("Accessibility","Explorer").disable(),n.findID("Accessibility","AutoCollapse").disable(),n.findID("Accessibility","Collapsible").disable()}},h.prototype.enableExplorerItems=function(t){var e,o,n=this.menu.findID("Accessibility","Activate").getMenu();try{for(var i=c(n.getItems().slice(1)),r=i.next();!r.done;r=i.next()){var s=r.value;if(s instanceof ContextMenu.Rule)break;t?s.enable():s.disable()}}catch(t){e={error:t}}finally{try{r&&!r.done&&(o=i.return)&&o.call(i)}finally{if(e)throw e.error}}},h.prototype.mergeUserSettings=function(){try{var t=localStorage.getItem(h.MENU_STORAGE);if(!t)return;Object.assign(this.settings,JSON.parse(t)),this.setA11y(this.settings)}catch(t){console.log("MathJax localStorage error: "+t.message)}},h.prototype.saveUserSettings=function(){var e,t,o={};try{for(var n=c(Object.keys(this.settings)),i=n.next();!i.done;i=n.next()){var r=i.value;this.settings[r]!==this.defaultSettings[r]&&(o[r]=this.settings[r])}}catch(t){e={error:t}}finally{try{i&&!i.done&&(t=n.return)&&t.call(n)}finally{if(e)throw e.error}}try{Object.keys(o).length?localStorage.setItem(h.MENU_STORAGE,JSON.stringify(o)):localStorage.removeItem(h.MENU_STORAGE)}catch(t){console.log("MathJax localStorage error: "+t.message)}},h.prototype.setA11y=function(t){window.MathJax._.a11y&&window.MathJax._.a11y.explorer&&window.MathJax._.a11y.explorer_ts.setA11yOptions(this.document,t)},h.prototype.getA11y=function(t){if(window.MathJax._.a11y&&window.MathJax._.a11y.explorer)return this.document.options.a11y[t]},h.prototype.setScale=function(t){this.document.outputJax.options.scale=parseFloat(t),this.document.rerender()},h.prototype.setRenderer=function(e){var o=this;if(this.jax[e])this.setOutputJax(e);else{var n=e.toLowerCase();this.loadComponent("output/"+n,function(){var t=window.MathJax.startup;n in t.constructors&&(t.useOutput(n,!0),t.output=t.getOutputJax(),o.jax[e]=t.output,o.setOutputJax(e))})}},h.prototype.setOutputJax=function(t){this.jax[t].setAdaptor(this.document.adaptor),this.document.outputJax=this.jax[t],this.rerender()},h.prototype.setTabOrder=function(t){this.menu.getStore().inTaborder(t)},h.prototype.setExplorer=function(t){this.enableExplorerItems(t),!t||window.MathJax._.a11y&&window.MathJax._.a11y.explorer?this.rerender(this.settings.collapsible?l.STATE.RERENDER:l.STATE.COMPILED):this.loadA11y("explorer")},h.prototype.setCollapsible=function(t){!t||window.MathJax._.a11y&&window.MathJax._.a11y.complexity?this.rerender(l.STATE.COMPILED):this.loadA11y("complexity")},h.prototype.scaleAllMath=function(){var t=(100*parseFloat(this.settings.scale)).toFixed(1).replace(/.0$/,""),e=prompt("Scale all mathematics (compared to surrounding text) by",t+"%");if(e)if(e.match(/^\s*\d+(\.\d*)?\s*%?\s*$/)){var o=parseFloat(e)/100;o?this.setScale(String(o)):alert("The scale should not be zero")}else alert("The scale should be a percentage (e.g., 120%)")},h.prototype.resetDefaults=function(){var e,t;h.loading++;var o=this.menu.getPool(),n=this.defaultSettings;try{for(var i=c(Object.keys(this.settings)),r=i.next();!r.done;r=i.next()){var s=r.value,a=o.lookup(s);if(a){a.setValue(n[s]);var u=a.items[0];u&&u.executeCallbacks_()}else this.settings[s]=n[s]}}catch(t){e={error:t}}finally{try{r&&!r.done&&(t=i.return)&&t.call(i)}finally{if(e)throw e.error}}h.loading--,this.rerender(l.STATE.COMPILED)},h.prototype.checkComponent=function(t){var e=h.loadingPromises.get(t);e&&r.mathjax.retryAfter(e)},h.prototype.loadComponent=function(t,e){if(!h.loadingPromises.has(t)){var o=window.MathJax.loader;if(o){h.loading++;var n=o.load(t).then(function(){h.loading--,h.loadingPromises.delete(t),e(),0===h.loading&&h._loadingPromise&&(h._loadingPromise=null,h._loadingOK())}).catch(function(t){h._loadingPromise?(h._loadingPromise=null,h._loadingFailed(t)):console.log(t)});h.loadingPromises.set(t,n)}}},h.prototype.loadA11y=function(o){var n=this,i=!l.STATE.ENRICHED;this.loadComponent("a11y/"+o,function(){var t=window.MathJax.startup;r.mathjax.handlers.unregister(t.handler),t.handler=t.getHandler(),r.mathjax.handlers.register(t.handler);var e=n.document;n.document=t.document=t.getDocument(),(n.document.menu=n).transferMathList(e),h._loadingPromise||n.rerender("complexity"===o||i?l.STATE.COMPILED:l.STATE.TYPESET)})},h.prototype.transferMathList=function(t){var e,o,n=this.document.options.MathItem;try{for(var i=c(t.math),r=i.next();!r.done;r=i.next()){var s=r.value,a=new n;Object.assign(a,s),this.document.math.push(a)}}catch(t){e={error:t}}finally{try{r&&!r.done&&(o=i.return)&&o.call(i)}finally{if(e)throw e.error}}},h.prototype.formatSource=function(t){return t.trim().replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;")},h.prototype.toMML=function(t){return this.MmlVisitor.visitTree(t.root,t,{texHints:this.settings.texHints,semantics:this.settings.semantics&&"MathML"!==t.inputJax.name})},h.prototype.zoom=function(t,e,o){t&&!this.isZoomEvent(t,e)||(this.menu.mathItem=o,t&&this.menu.post(t),this.zoomBox.post())},h.prototype.isZoomEvent=function(t,e){return this.settings.zoom===e&&(!this.settings.alt||t.altKey)&&(!this.settings.ctrl||t.ctrlKey)&&(!this.settings.cmd||t.metaKey)&&(!this.settings.shift||t.shiftKey)},h.prototype.rerender=function(t){void 0===t&&(t=l.STATE.TYPESET),this.rerenderStart=Math.min(t,this.rerenderStart),h.loading||(this.document.rerender(this.rerenderStart),this.rerenderStart=l.STATE.LAST)},h.prototype.copyMathML=function(){this.copyToClipboard(this.toMML(this.menu.mathItem))},h.prototype.copyOriginal=function(){this.copyToClipboard(this.menu.mathItem.math)},h.prototype.copyAnnotation=function(){this.copyToClipboard(this.menu.annotation)},h.prototype.copyToClipboard=function(t){var e=document.createElement("textarea");e.value=t,e.setAttribute("readonly",""),e.style.cssText="height: 1px; width: 1px; padding: 1px; position: absolute; left: -10px",document.body.appendChild(e),e.select();try{document.execCommand("copy")}catch(t){alert("Can't copy to clipboard: "+t.message)}document.body.removeChild(e)},h.prototype.addMenu=function(e){var o=this,t=e.typesetRoot;t.addEventListener("contextmenu",function(){return o.menu.mathItem=e},!0),t.addEventListener("keydown",function(){return o.menu.mathItem=e},!0),t.addEventListener("click",function(t){return o.zoom(t,"Click",e)},!0),t.addEventListener("dblclick",function(t){return o.zoom(t,"DoubleClick",e)},!0),this.menu.getStore().insert(t)},h.prototype.clear=function(){this.menu.getStore().clear()},h.prototype.variable=function(e,o){var n=this;return{name:e,getter:function(){return n.settings[e]},setter:function(t){n.settings[e]=t,o&&o(t),n.saveUserSettings()}}},h.prototype.a11yVar=function(o){var n=this;return{name:o,getter:function(){return n.getA11y(o)},setter:function(t){n.settings[o]=t;var e={};e[o]=t,n.setA11y(e),n.saveUserSettings()}}},h.prototype.submenu=function(t,e,o,n){var i,r;void 0===o&&(o=[]),void 0===n&&(n=!1);var s=[];try{for(var a=c(o),u=a.next();!u.done;u=a.next()){var l=u.value;Array.isArray(l)?s=s.concat(l):s.push(l)}}catch(t){i={error:t}}finally{try{u&&!u.done&&(r=a.return)&&r.call(a)}finally{if(i)throw i.error}}return{type:"submenu",id:t,content:e,menu:{items:s},disabled:0===s.length||n}},h.prototype.command=function(t,e,o,n){return void 0===n&&(n={}),Object.assign({type:"command",id:t,content:e,action:o},n)},h.prototype.checkbox=function(t,e,o,n){return void 0===n&&(n={}),Object.assign({type:"checkbox",id:t,content:e,variable:o},n)},h.prototype.radioGroup=function(e,t){var o=this;return t.map(function(t){return o.radio(t[0],t[1]||t[0],e)})},h.prototype.radio=function(t,e,o,n){return void 0===n&&(n={}),Object.assign({type:"radio",id:t,content:e,variable:o},n)},h.prototype.label=function(t,e){return{type:"label",id:t,content:e}},h.prototype.rule=function(){return{type:"rule"}},h.MENU_STORAGE="MathJax-Menu-Settings",h.OPTIONS={settings:{texHints:!0,semantics:!1,zoom:"NoZoom",zscale:"200%",renderer:"CHTML",alt:!1,cmd:!1,ctrl:!1,shift:!1,scale:1,autocollapse:!1,collapsible:!1,inTabOrder:!0,explorer:!1},jax:{CHTML:null,SVG:null},annotationTypes:n.expandable({TeX:["TeX","LaTeX","application/x-tex"],StarMath:["StarMath 5.0"],Maple:["Maple"],ContentMathML:["MathML-Content","application/mathml-content+xml"],OpenMath:["OpenMath"]})},h.loading=0,h.loadingPromises=new Map,h._loadingPromise=null,h._loadingOK=null,h._loadingFailed=null,h);function h(t,e){var o=this;void 0===e&&(e={}),this.settings=null,this.defaultSettings=null,this.menu=null,this.MmlVisitor=new s.MmlVisitor,this.jax={CHTML:null,SVG:null},this.rerenderStart=l.STATE.LAST,this.about=new ContextMenu.Info('<b style="font-size:120%;">MathJax</b> v'+r.mathjax.version,function(){var t=[];return t.push("Input Jax: "+o.document.inputJax.map(function(t){return t.name}).join(", ")),t.push("Output Jax: "+o.document.outputJax.name),t.push("Document Type: "+o.document.kind),t.join("<br/>")},'<a href="https://www.mathjax.org">www.mathjax.org</a>'),this.help=new ContextMenu.Info("<b>MathJax Help</b>",function(){return["<p><b>MathJax</b> is a JavaScript library that allows page"," authors to include mathematics within their web pages."," As a reader, you don't need to do anything to make that happen.</p>","<p><b>Browsers</b>: MathJax works with all modern browsers including"," Edge, Firefox, Chrome, Safari, Opera, and most mobile browsers.</p>","<p><b>Math Menu</b>: MathJax adds a contextual menu to equations."," Right-click or CTRL-click on any mathematics to access the menu.</p>",'<div style="margin-left: 1em;">',"<p><b>Show Math As:</b> These options allow you to view the formula's"," source markup (as MathML or in its original format).</p>","<p><b>Copy to Clipboard:</b> These options copy the formula's source markup,"," as MathML or in its original format, to the clipboard"," (in browsers that support that).</p>","<p><b>Math Settings:</b> These give you control over features of MathJax,"," such the size of the mathematics, and the mechanism used"," to display equations.</p>","<p><b>Accessibility</b>: MathJax can work with screen"," readers to make mathematics accessible to the visually impaired."," Turn on the explorer to enable generation of speech strings"," and the ability to investigate expressions interactively.</p>","<p><b>Language</b>: This menu lets you select the language used by MathJax"," for its menus and warning messages. (Not yet implemented in version 3.)</p>","</div>","<p><b>Math Zoom</b>: If you are having difficulty reading an"," equation, MathJax can enlarge it to help you see it better, or"," you can scall all the math on the page to make it larger."," Turn these features on in the <b>Math Settings</b> menu.</p>","<p><b>Preferences</b>: MathJax uses your browser's localStorage database"," to save the preferences set via this menu locally in your browser.  These"," are not used to track you, and are not transferred or used remotely by"," MathJax in any way.</p>"].join("\n")},'<a href="https://www.mathjax.org">www.mathjax.org</a>'),this.mathmlCode=new a.SelectableInfo("MathJax MathML Expression",function(){if(!o.menu.mathItem)return"";var t=o.toMML(o.menu.mathItem);return"<pre>"+o.formatSource(t)+"</pre>"},""),this.originalText=new a.SelectableInfo("MathJax Original Source",function(){if(!o.menu.mathItem)return"";var t=o.menu.mathItem.math;return'<pre style="font-size:125%; margin:0">'+o.formatSource(t)+"</pre>"},""),this.annotationText=new a.SelectableInfo("MathJax Annotation Text",function(){if(!o.menu.mathItem)return"";var t=o.menu.annotation;return'<pre style="font-size:125%; margin:0">'+o.formatSource(t)+"</pre>"},""),this.zoomBox=new ContextMenu.Info("MathJax Zoomed Expression",function(){if(!o.menu.mathItem)return"";var t=o.menu.mathItem.typesetRoot.cloneNode(!0);return t.style.margin="0",'<div style="font-size: '******parseFloat(o.settings.zscale)+'%">'+t.outerHTML+"</div>"},""),this.document=t,this.options=n.userOptions(n.defaultOptions({},this.constructor.OPTIONS),e),this.initSettings(),this.mergeUserSettings(),this.initMenu()}e.Menu=p},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.mathjax=MathJax._.mathjax.mathjax},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.protoItem=MathJax._.core.MathItem.protoItem,e.AbstractMathItem=MathJax._.core.MathItem.AbstractMathItem,e.STATE=MathJax._.core.MathItem.STATE,e.newState=MathJax._.core.MathItem.newState},function(t,e,o){"use strict";var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});Object.defineProperty(e,"__esModule",{value:!0});var r,s=o(14),a=o(0),u=(r=s.SerializedMmlVisitor,i(l,r),l.prototype.visitTree=function(t,e,o){return void 0===e&&(e=null),void 0===o&&(o={}),this.mathItem=e,a.userOptions(this.options,o),this.visitNode(t,"")},l.prototype.visitTeXAtomNode=function(t,e){return this.options.texHints?r.prototype.visitTeXAtomNode.call(this,t,e):t.childNodes[0]&&1===t.childNodes[0].childNodes.length?this.visitNode(t.childNodes[0],e):e+"<mrow"+this.getAttributes(t)+">\n"+this.childNodeMml(t,e+"  ","\n")+e+"</mrow>"},l.prototype.visitMathNode=function(t,e){if(!this.options.semantics||"TeX"!==this.mathItem.inputJax.name)return r.prototype.visitDefault.call(this,t,e);var o=t.childNodes.length&&1<t.childNodes[0].childNodes.length;return e+"<math"+this.getAttributes(t)+">\n"+e+"  <semantics>\n"+(o?e+"    <mrow>\n":"")+this.childNodeMml(t,e+(o?"      ":"    "),"\n")+(o?e+"    </mrow>\n":"")+e+'    <annotation encoding="application/x-tex">'+this.mathItem.math+"</annotation>\n"+e+"  </semantics>\n"+e+"</math>"},l);function l(){var t=null!==r&&r.apply(this,arguments)||this;return t.options={texHints:!0,semantics:!1},t.mathItem=null,t}e.MmlVisitor=u},function(t,e,o){"use strict";var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});Object.defineProperty(e,"__esModule",{value:!0});var r,s=(r=ContextMenu.Info,i(a,r),a.prototype.addEvents=function(t){var e=this;t.addEventListener("keypress",function(t){"a"===t.key&&(t.ctrlKey||t.metaKey)&&(e.selectAll(),e.stop(t))})},a.prototype.selectAll=function(){document.getSelection().selectAllChildren(this.getHtml().querySelector("pre"))},a.prototype.copyToClipboard=function(){this.selectAll();try{document.execCommand("copy")}catch(t){alert("Can't copy to clipboard: "+t.message)}document.getSelection().removeAllRanges()},a.prototype.generateHtml=function(){var e=this;r.prototype.generateHtml.call(this);var t=this.getHtml().querySelector("span."+ContextMenu.HtmlClasses.INFOSIGNATURE).appendChild(document.createElement("input"));t.type="button",t.value="Copy to Clipboard",t.addEventListener("click",function(t){return e.copyToClipboard()})},a);function a(){return null!==r&&r.apply(this,arguments)||this}e.SelectableInfo=s},function(t,e,o){"use strict";var n,r=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),s=this&&this.__assign||function(){return(s=Object.assign||function(t){for(var e,o=1,n=arguments.length;o<n;o++)for(var i in e=arguments[o])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)},i=this&&this.__read||function(t,e){var o="function"==typeof Symbol&&t[Symbol.iterator];if(!o)return t;var n,i,r=o.call(t),s=[];try{for(;(void 0===e||0<e--)&&!(n=r.next()).done;)s.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(o=r.return)&&o.call(r)}finally{if(i)throw i.error}}return s},a=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(i(arguments[e]));return t},u=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,o=e&&t[e],n=0;if(o)return o.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0});var l=o(3),c=o(4),p=o(0),h=o(2);function d(t){return r(e,n=t),e.prototype.addMenu=function(t){this.state()<c.STATE.CONTEXT_MENU&&(t.menu.addMenu(this),this.state(c.STATE.CONTEXT_MENU))},e.prototype.checkLoading=function(t){t.menu.isLoading&&l.mathjax.retryAfter(t.menu.loadingPromise.catch(function(t){return console.log(t)}))},e.prototype.enrich=function(t,e){void 0===e&&(e=!1);var o=t.menu.settings;(o.collapsible||o.explorer||e)&&(o.collapsible&&t.menu.checkComponent("a11y/complexity"),o.explorer&&t.menu.checkComponent("a11y/explorer"),n.prototype.enrich.call(this,t))},e.prototype.complexity=function(t,e){void 0===e&&(e=!1),(t.menu.settings.collapsible||e)&&(t.menu.checkComponent("a11y/complexity"),n.prototype.complexity.call(this,t))},e.prototype.explorable=function(t,e){void 0===e&&(e=!1),(t.menu.settings.explorer||e)&&(t.menu.checkComponent("a11y/explorer"),n.prototype.explorable.call(this,t))},e;function e(){return null!==n&&n.apply(this,arguments)||this}var n}function m(t){var e,i;return r(o,i=t),o.prototype.addMenu=function(){var e,t;if(!this.processed.isSet("context-menu")){try{for(var o=u(this.math),n=o.next();!n.done;n=o.next())n.value.addMenu(this)}catch(t){e={error:t}}finally{try{n&&!n.done&&(t=o.return)&&t.call(o)}finally{if(e)throw e.error}}this.processed.set("context-menu")}return this},o.prototype.checkLoading=function(){return this.menu.isLoading&&l.mathjax.retryAfter(this.menu.loadingPromise.catch(function(t){return console.log(t)})),this},o.prototype.state=function(t,e){return void 0===e&&(e=!1),i.prototype.state.call(this,t,e),t<c.STATE.CONTEXT_MENU&&this.processed.clear("context-menu"),this},o.prototype.updateDocument=function(){return i.prototype.updateDocument.call(this),this.menu.menu.getStore().sort(),this},o.prototype.enrich=function(t){var e,o;void 0===t&&(t=!1);var n=this.menu.settings;if(!this.processed.isSet("enriched")&&(n.collapsible||n.explorer||t)){n.collapsible&&this.menu.checkComponent("a11y/complexity"),n.explorer&&this.menu.checkComponent("a11y/explorer");try{for(var i=u(this.math),r=i.next();!r.done;r=i.next())r.value.enrich(this,t)}catch(t){e={error:t}}finally{try{r&&!r.done&&(o=i.return)&&o.call(i)}finally{if(e)throw e.error}}this.processed.set("enriched")}return this},o.prototype.complexity=function(t){var e,o;if(void 0===t&&(t=!1),!this.processed.isSet("complexity")&&(this.menu.settings.collapsible||t)){this.menu.checkComponent("a11y/complexity");try{for(var n=u(this.math),i=n.next();!i.done;i=n.next())i.value.complexity(this,t)}catch(t){e={error:t}}finally{try{i&&!i.done&&(o=n.return)&&o.call(n)}finally{if(e)throw e.error}}this.processed.set("complexity")}return this},o.prototype.explorable=function(t){var e,o;if(void 0===t&&(t=!1),!this.processed.isSet("explorer")&&(this.menu.settings.explorer||t)){this.menu.checkComponent("a11y/explorer");try{for(var n=u(this.math),i=n.next();!i.done;i=n.next())i.value.explorable(this,t)}catch(t){e={error:t}}finally{try{i&&!i.done&&(o=n.return)&&o.call(n)}finally{if(e)throw e.error}}this.processed.set("explorer")}return this},(e=o).OPTIONS=s(s({},t.OPTIONS),{MenuClass:h.Menu,menuOptions:h.Menu.OPTIONS,a11y:t.OPTIONS.a11y||p.expandable({}),renderActions:p.expandable(s(s({},t.OPTIONS.renderActions),{addMenu:[c.STATE.CONTEXT_MENU],checkLoading:[c.STATE.UNPROCESSED+1]}))}),e;function o(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var o=i.apply(this,a(t))||this;o.menu=new o.options.MenuClass(o,o.options.menuOptions);var n=o.constructor.ProcessBits;return n.has("context-menu")||n.allocate("context-menu"),o.options.MathItem=d(o.options.MathItem),o}}c.newState("CONTEXT_MENU",170),e.MenuMathItemMixin=d,e.MenuMathDocumentMixin=m,e.MenuHandler=function(t){return t.documentClass=m(t.documentClass),t}},function(t,e,o){"use strict";o(9),o(12);var n=o(7);MathJax.startup&&"undefined"!=typeof window&&MathJax.startup.extendHandler(function(t){return(0,n.MenuHandler)(t)},20)},function(t,e,o){"use strict";(function(t){function e(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}o(11),"undefined"==typeof ContextMenu&&(t.ContextMenu={ContextMenu:function t(){e(this,t)},Info:function t(){e(this,t)}})}).call(this,o(10))},function(sh,th){var uh;uh=function(){return this}();try{uh=uh||Function("return this")()||eval("this")}catch(t){"object"==typeof window&&(uh=window)}sh.exports=uh},function(t,e){var n,o,i,r,s,a,u,l,c,p,h,d,m,f,y,b,g,v,x,M,E,C,w,O,A,S,T,N,_,I,k,H,L,U,P,R,J,D,j,F,V,B,K,z,W,X,G,Y,q,Z,$,Q,tt,et,ot,nt,it,rt,st,at,ut,lt,ct,pt,ht,dt,mt,ft,yt,bt,gt=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});function vt(t,e,o){this.name=t,this.getter=e,this.setter=o,this.items=[]}function xt(){this.pool={}}function Mt(){this.bubble=!1}function Et(t){return h+"_"+t}function Ct(t){return Et(t)}function wt(t){return Et(t)}function Ot(){return null!==m&&m.apply(this,arguments)||this}function At(t,e){var o=b.call(this)||this;return o.className=y.HtmlClasses.MENUITEM,o.role="menuitem",o.type="entry",o.hidden=!1,o.menu=t,o.type=e,o}function St(){var t=null!==x&&x.apply(this,arguments)||this;return t.posted=!1,t}function Tt(){var t=null!==C&&C.apply(this,arguments)||this;return t.className=E.HtmlClasses.CONTEXTMENU,t.role="menu",t.items=[],t}function Nt(t){this.store=[],this.active=null,this.counter=0,this.attachedClass=O.HtmlClasses.ATTACHED+"_"+O.MenuUtil.counter(),this.taborder=!0,this.attrMap={},this.menu=t}function _t(){var t=T.call(this)||this;return t.moving=!1,t.store_=new S.MenuStore(t),t.widgets=[],t.variablePool=new S.VariablePool,t}function It(t){var e=I.call(this)||this;return e.anchor=t,e.variablePool=e.anchor.getMenu().getPool(),e.setBaseMenu(),e}function kt(t,e,o,n){var i=U.call(this,t,e)||this;return i._content=o,i.disabled=!1,i.callbacks=[],i.id=n||o,i}function Ht(){return null!==J&&J.apply(this,arguments)||this}function Lt(t){var e=V.call(this)||this;return e.className=F.HtmlClasses.MENUCLOSE,e.role="button",e.element=t,e}function Ut(t,e,o){var n=z.call(this)||this;return n.className=K.HtmlClasses.INFO,n.role="dialog",n.title="",n.signature="",n.contentDiv=n.generateContent(),n.close=n.generateClose(),n.title=t,n.content=e||function(){return""},n.signature=o,n}function Pt(t,e,o,n){var i=G.call(this,t,"checkbox",e,n)||this;return i.role="menuitemcheckbox",i.variable=t.getPool().lookup(o),i.register(),i}function Rt(t,e,o,n){var i=Z.call(this,t,"radio",e,n)||this;return i.role="combobox",i.inputEvent=!1,i.variable=t.getPool().lookup(o),i.register(),i}function Jt(t,e,o,n){var i=tt.call(this,t,"command",e,n)||this;return i.command=null,i.command=o,i}function Dt(t,e,o){return nt.call(this,t,"label",e,o)||this}function jt(t,e,o,n){var i=st.call(this,t,"radio",e,n)||this;return i.role="menuitemradio",i.variable=t.getPool().lookup(o),i.register(),i}function Ft(t){var e=lt.call(this,t,"rule")||this;return e.className=ut.HtmlClasses.MENUITEM,e.role="separator",e}function Vt(t,e,o){var n=ht.call(this,t,"submenu",e,o)||this;return n.submenu=null,n}function Bt(t,e){var o=ft.call(this)||this;return o.title="",o.window=null,o.localSettings={left:Math.round((screen.width-400)/2),top:Math.round((screen.height-300)/3)},o.windowList=[],o.mobileFlag=!1,o.active=null,o.title=t,o.content=e||function(){return""},o}o=bt=bt||{},vt.prototype.getName=function(){return this.name},vt.prototype.getValue=function(t){try{return this.getter(t)}catch(t){o.MenuUtil.error(t,"Command of variable "+this.name+" failed.")}},vt.prototype.setValue=function(t,e){try{this.setter(t,e)}catch(t){o.MenuUtil.error(t,"Command of variable "+this.name+" failed.")}this.update()},vt.prototype.register=function(t){-1===this.items.indexOf(t)&&this.items.push(t)},vt.prototype.unregister=function(t){var e=this.items.indexOf(t);-1!==e&&this.items.splice(e,1)},vt.prototype.update=function(){this.items.forEach(function(t){return t.update()})},vt.prototype.registerCallback=function(e){this.items.forEach(function(t){return t.registerCallback(e)})},vt.prototype.unregisterCallback=function(e){this.items.forEach(function(t){return t.unregisterCallback(e)})},i=vt,o.Variable=i,r=bt=bt||{},xt.prototype.insert=function(t){this.pool[t.getName()]=t},xt.prototype.lookup=function(t){return this.pool[t]},xt.prototype.remove=function(t){delete this.pool[t]},xt.prototype.update=function(){for(var t in this.pool)this.pool[t].update()},s=xt,r.VariablePool=s,a=bt=bt||{},(u=a.KEY||(a.KEY={}))[u.RETURN=13]="RETURN",u[u.ESCAPE=27]="ESCAPE",u[u.SPACE=32]="SPACE",u[u.LEFT=37]="LEFT",u[u.UP=38]="UP",u[u.RIGHT=39]="RIGHT",u[u.DOWN=40]="DOWN",(bt=bt||{}).MOUSE={CLICK:"click",DBLCLICK:"dblclick",DOWN:"mousedown",UP:"mouseup",OVER:"mouseover",OUT:"mouseout",MOVE:"mousemove",SELECTSTART:"selectstart",SELECTEND:"selectend"},l=bt=bt||{},Mt.prototype.bubbleKey=function(){this.bubble=!0},Mt.prototype.keydown=function(t){switch(t.keyCode){case l.KEY.ESCAPE:this.escape(t);break;case l.KEY.RIGHT:this.right(t);break;case l.KEY.LEFT:this.left(t);break;case l.KEY.UP:this.up(t);break;case l.KEY.DOWN:this.down(t);break;case l.KEY.RETURN:case l.KEY.SPACE:this.space(t);break;default:return}this.bubble?this.bubble=!1:this.stop(t)},Mt.prototype.escape=function(t){},Mt.prototype.space=function(t){},Mt.prototype.left=function(t){},Mt.prototype.right=function(t){},Mt.prototype.up=function(t){},Mt.prototype.down=function(t){},Mt.prototype.stop=function(t){t&&(t.stopPropagation(),t.preventDefault(),t.cancelBubble=!0)},Mt.prototype.mousedown=function(t){return this.stop(t)},Mt.prototype.mouseup=function(t){return this.stop(t)},Mt.prototype.mouseover=function(t){return this.stop(t)},Mt.prototype.mouseout=function(t){return this.stop(t)},Mt.prototype.click=function(t){return this.stop(t)},Mt.prototype.addEvents=function(t){t.addEventListener(l.MOUSE.DOWN,this.mousedown.bind(this)),t.addEventListener(l.MOUSE.UP,this.mouseup.bind(this)),t.addEventListener(l.MOUSE.OVER,this.mouseover.bind(this)),t.addEventListener(l.MOUSE.OUT,this.mouseout.bind(this)),t.addEventListener(l.MOUSE.CLICK,this.click.bind(this)),t.addEventListener("keydown",this.keydown.bind(this)),t.addEventListener("dragstart",this.stop.bind(this)),t.addEventListener("selectstart",this.stop.bind(this)),t.addEventListener("contextmenu",this.stop.bind(this)),t.addEventListener("dblclick",this.stop.bind(this))},c=Mt,l.AbstractNavigatable=c,p=bt=bt||{},h="CtxtMenu",p.HtmlClasses={ATTACHED:Ct("Attached"),CONTEXTMENU:Ct("ContextMenu"),MENU:Ct("Menu"),MENUARROW:Ct("MenuArrow"),MENUACTIVE:Ct("MenuActive"),MENUCHECK:Ct("MenuCheck"),MENUCLOSE:Ct("MenuClose"),MENUCOMBOBOX:Ct("MenuComboBox"),MENUDISABLED:Ct("MenuDisabled"),MENUFRAME:Ct("MenuFrame"),MENUITEM:Ct("MenuItem"),MENULABEL:Ct("MenuLabel"),MENURADIOCHECK:Ct("MenuRadioCheck"),MENUINPUTBOX:Ct("MenuInputBox"),MENURULE:Ct("MenuRule"),MOUSEPOST:Ct("MousePost"),RTL:Ct("RTL"),INFO:Ct("Info"),INFOCLOSE:Ct("InfoClose"),INFOCONTENT:Ct("InfoContent"),INFOSIGNATURE:Ct("InfoSignature"),INFOTITLE:Ct("InfoTitle")},p.HtmlAttrs={COUNTER:wt("Counter"),KEYDOWNFUNC:wt("keydownFunc"),CONTEXTMENUFUNC:wt("contextmenuFunc"),OLDTAB:wt("Oldtabindex"),TOUCHFUNC:wt("TouchFunc")},d=bt=bt||{},m=d.AbstractNavigatable,gt(Ot,m),Ot.prototype.addAttributes=function(t){for(var e in t)this.html.setAttribute(e,t[e])},Ot.prototype.getHtml=function(){return this.html||this.generateHtml(),this.html},Ot.prototype.setHtml=function(t){this.html=t,this.addEvents(t)},Ot.prototype.generateHtml=function(){var t=document.createElement("div");t.classList.add(this.className),t.setAttribute("role",this.role),this.setHtml(t)},Ot.prototype.focus=function(){var t=this.getHtml();t.setAttribute("tabindex","0"),t.focus()},Ot.prototype.unfocus=function(){var t=this.getHtml();t.hasAttribute("tabindex")&&t.setAttribute("tabindex","-1"),t.blur()},f=Ot,d.MenuElement=f,y=bt=bt||{},b=y.MenuElement,gt(At,b),At.prototype.getMenu=function(){return this.menu},At.prototype.setMenu=function(t){this.menu=t},At.prototype.getType=function(){return this.type},At.prototype.hide=function(){this.hidden=!0,this.menu.generateMenu()},At.prototype.show=function(){this.hidden=!1,this.menu.generateMenu()},At.prototype.isHidden=function(){return this.hidden},g=At,y.AbstractEntry=g,v=bt=bt||{},x=v.MenuElement,gt(St,x),St.prototype.isPosted=function(){return this.posted},St.prototype.post=function(t,e){this.posted||(void 0!==t&&void 0!==e&&this.getHtml().setAttribute("style","left: "+t+"px; top: "+e+"px;"),this.display(),this.posted=!0)},St.prototype.unpost=function(){if(this.posted){var t=this.getHtml();t.parentNode&&t.parentNode.removeChild(t),this.posted=!1}},M=St,v.AbstractPostable=M,E=bt=bt||{},C=E.AbstractPostable,gt(Tt,C),Tt.prototype.getItems=function(){return this.items},Tt.prototype.getPool=function(){return this.variablePool},Tt.prototype.getFocused=function(){return this.focused},Tt.prototype.setFocused=function(t){if(this.focused!==t){this.focused||this.unfocus();var e=this.focused;this.focused=t,e&&e.unfocus()}},Tt.prototype.up=function(t){var e=this.getItems().filter(function(t){return t instanceof E.AbstractItem&&!t.isHidden()});if(0!==e.length)if(this.focused){var o=e.indexOf(this.focused);-1!==o&&e[o=o?--o:e.length-1].focus()}else e[e.length-1].focus()},Tt.prototype.down=function(t){var e=this.getItems().filter(function(t){return t instanceof E.AbstractItem&&!t.isHidden()});if(0!==e.length)if(this.focused){var o=e.indexOf(this.focused);-1!==o&&e[o=++o===e.length?0:o].focus()}else e[0].focus()},Tt.prototype.generateHtml=function(){C.prototype.generateHtml.call(this),this.generateMenu()},Tt.prototype.generateMenu=function(){var t=this.getHtml();t.classList.add(E.HtmlClasses.MENU);for(var e=0,o=this.items;e<o.length;e++){var n=o[e];if(n.isHidden()){var i=n.getHtml();i.parentNode&&i.parentNode.removeChild(i)}else t.appendChild(n.getHtml())}},Tt.prototype.post=function(t,e){this.variablePool.update(),C.prototype.post.call(this,t,e)},Tt.prototype.unpostSubmenus=function(){for(var t=0,e=this.items.filter(function(t){return t instanceof E.Submenu});t<e.length;t++){var o=e[t];o.getSubmenu().unpost(),o!==this.getFocused()&&o.unfocus()}},Tt.prototype.unpost=function(){C.prototype.unpost.call(this),this.unpostSubmenus(),this.setFocused(null)},Tt.prototype.find=function(t){for(var e=0,o=this.getItems();e<o.length;e++){var n=o[e];if("rule"!==n.getType()){if(n.getId()===t)return n;if("submenu"===n.getType()){var i=n.getSubmenu().find(t);if(i)return i}}}return null},Tt.prototype.parseItems=function(t){var e=this;t.map(function(t){return[e.parseItem.bind(e)(t),t.hidden]}).forEach(function(t){return t[1]&&t[0].hide()})},Tt.prototype.parseItem=function(t){var e={checkbox:E.Checkbox.parse,combo:E.Combo.parse,command:E.Command.parse,label:E.Label.parse,radio:E.Radio.parse,rule:E.Rule.parse,submenu:E.Submenu.parse}[t.type];if(e){var o=e(t,this);return this.getItems().push(o),t.disabled&&o.disable(),o}},w=Tt,E.AbstractMenu=w,O=bt=bt||{},Nt.prototype.setActive=function(t){do{if(-1!==this.store.indexOf(t)){this.active=t;break}t=t.parentNode}while(t)},Nt.prototype.getActive=function(){return this.active},Nt.prototype.next=function(){var t=this.store.length;if(0===t)return this.active=null;var e=this.store.indexOf(this.active);return e=-1===e?0:e<t-1?e+1:0,this.active=this.store[e],this.active},Nt.prototype.previous=function(){var t=this.store.length;if(0===t)return this.active=null;var e=t-1,o=this.store.indexOf(this.active);return o=-1===o?e:0===o?e:o-1,this.active=this.store[o],this.active},Nt.prototype.clear=function(){this.remove(this.store)},Nt.prototype.insert=function(t){for(var e=0,o=t instanceof HTMLElement?[t]:t;e<o.length;e++){var n=o[e];this.insertElement(n)}this.sort()},Nt.prototype.remove=function(t){for(var e=0,o=t instanceof HTMLElement?[t]:t;e<o.length;e++){var n=o[e];this.removeElement(n)}this.sort()},Nt.prototype.inTaborder=function(t){this.taborder&&!t&&this.removeTaborder(),!this.taborder&&t&&this.insertTaborder(),this.taborder=t},Nt.prototype.insertTaborder=function(){this.taborder&&this.insertTaborder_()},Nt.prototype.removeTaborder=function(){this.taborder&&this.removeTaborder_()},Nt.prototype.insertElement=function(t){t.classList.contains(this.attachedClass)||(t.classList.add(this.attachedClass),this.taborder&&this.addTabindex(t),this.addEvents(t))},Nt.prototype.removeElement=function(t){t.classList.contains(this.attachedClass)&&(t.classList.remove(this.attachedClass),this.taborder&&this.removeTabindex(t),this.removeEvents(t))},Nt.prototype.sort=function(){var t=document.getElementsByClassName(this.attachedClass);this.store=[].slice.call(t)},Nt.prototype.insertTaborder_=function(){this.store.forEach(function(t){return t.setAttribute("tabindex","0")})},Nt.prototype.removeTaborder_=function(){this.store.forEach(function(t){return t.setAttribute("tabindex","-1")})},Nt.prototype.addTabindex=function(t){t.hasAttribute("tabindex")&&t.setAttribute(O.HtmlAttrs.OLDTAB,t.getAttribute("tabindex")),t.setAttribute("tabindex","0")},Nt.prototype.removeTabindex=function(t){t.hasAttribute(O.HtmlAttrs.OLDTAB)?(t.setAttribute("tabindex",t.getAttribute(O.HtmlAttrs.OLDTAB)),t.removeAttribute(O.HtmlAttrs.OLDTAB)):t.removeAttribute("tabindex")},Nt.prototype.addEvents=function(t){t.hasAttribute(O.HtmlAttrs.COUNTER)||(this.addEvent(t,"contextmenu",this.menu.post.bind(this.menu)),this.addEvent(t,"keydown",this.keydown.bind(this)),t.setAttribute(O.HtmlAttrs.COUNTER,this.counter.toString()),this.counter++)},Nt.prototype.addEvent=function(t,e,o){var n=O.HtmlAttrs[e.toUpperCase()+"FUNC"];this.attrMap[n+this.counter]=o,t.addEventListener(e,o)},Nt.prototype.removeEvents=function(t){if(t.hasAttribute(O.HtmlAttrs.COUNTER)){var e=t.getAttribute(O.HtmlAttrs.COUNTER);this.removeEvent(t,"contextmenu",e),this.removeEvent(t,"keydown",e),t.removeAttribute(O.HtmlAttrs.COUNTER)}},Nt.prototype.removeEvent=function(t,e,o){var n=O.HtmlAttrs[e.toUpperCase()+"FUNC"],i=this.attrMap[n+o];t.removeEventListener(e,i)},Nt.prototype.keydown=function(t){t.keyCode===O.KEY.SPACE&&(this.menu.post(t),t.preventDefault(),t.stopImmediatePropagation())},A=Nt,O.MenuStore=A,S=bt=bt||{},T=S.AbstractMenu,gt(_t,T),_t.parse=function(t){var e=t.menu;if(e){var o=e.pool,n=e.items,i=(e.id,new this);return o.forEach(i.parseVariable.bind(i)),i.parseItems(n),i}S.MenuUtil.error(null,"Wrong JSON format for menu.")},_t.prototype.generateHtml=function(){this.isPosted()&&this.unpost(),T.prototype.generateHtml.call(this),this.frame=document.createElement("div"),this.frame.classList.add(S.HtmlClasses.MENUFRAME);var t="left: 0px; top: 0px; z-index: 200; width: 100%; height: 100%; border: 0px; padding: 0px; margin: 0px;";this.frame.setAttribute("style","position: absolute; "+t);var e=document.createElement("div");e.setAttribute("style","position: fixed; "+t),this.frame.appendChild(e),e.addEventListener("mousedown",function(t){this.unpost(),this.unpostWidgets(),this.stop(t)}.bind(this))},_t.prototype.display=function(){document.body.appendChild(this.frame),this.frame.appendChild(this.getHtml()),this.focus()},_t.prototype.escape=function(t){this.unpost(),this.unpostWidgets()},_t.prototype.unpost=function(){if(T.prototype.unpost.call(this),!(0<this.widgets.length)){this.frame.parentNode.removeChild(this.frame);var t=this.getStore();this.moving||t.insertTaborder(),t.getActive().focus()}},_t.prototype.left=function(t){this.move_(this.store_.previous())},_t.prototype.right=function(t){this.move_(this.store_.next())},_t.prototype.getFrame=function(){return this.frame},_t.prototype.getStore=function(){return this.store_},_t.prototype.post=function(t,e){if(void 0!==e)return this.moving||this.getStore().removeTaborder(),void T.prototype.post.call(this,t,e);var o,n,i,r=t;if(r instanceof Event?(o=r.target,this.stop(r)):o=r,r instanceof MouseEvent&&(n=r.pageX,i=r.pageY,n||i||!r.clientX||(n=r.clientX+document.body.scrollLeft+document.documentElement.scrollLeft,i=r.clientY+document.body.scrollTop+document.documentElement.scrollTop)),!n&&!i&&o){var s=window.pageXOffset||document.documentElement.scrollLeft,a=window.pageYOffset||document.documentElement.scrollTop,u=o.getBoundingClientRect();n=(u.right+u.left)/2+s,i=(u.bottom+u.top)/2+a}this.getStore().setActive(o),this.anchor=this.getStore().getActive();var l=this.getHtml();n+l.offsetWidth>document.body.offsetWidth-5&&(n=document.body.offsetWidth-l.offsetWidth-5),this.post(n,i)},_t.prototype.registerWidget=function(t){this.widgets.push(t)},_t.prototype.unregisterWidget=function(t){var e=this.widgets.indexOf(t);-1<e&&this.widgets.splice(e,1),0===this.widgets.length&&this.unpost()},_t.prototype.unpostWidgets=function(){this.widgets.forEach(function(t){return t.unpost()})},_t.prototype.move_=function(t){this.anchor&&t!==this.anchor&&(this.moving=!0,this.unpost(),this.post(t),this.moving=!1)},_t.prototype.parseVariable=function(t){var e=t.name,o=t.getter,n=t.setter;this.getPool().insert(new S.Variable(e,o,n))},N=_t,S.ContextMenu=N,"undefined"!=typeof window&&(window.ContextMenu=bt),_=bt=bt||{},I=_.AbstractMenu,gt(It,I),It.parse=function(t,e){var o=t.items,n=(t.id,new It(e));return n.parseItems(o),n},It.prototype.getAnchor=function(){return this.anchor},It.prototype.post=function(){if(this.anchor.getMenu().isPosted()){for(var t=this.anchor.getHtml(),e=this.getHtml(),o=this.baseMenu.getFrame(),n=t.offsetWidth,i=n-2,r=0;t&&t!==o;)i+=t.offsetLeft,r+=t.offsetTop,t=t.parentNode;i+e.offsetWidth>document.body.offsetWidth-5&&(i=Math.max(5,i-n-e.offsetWidth+6)),I.prototype.post.call(this,i,r)}},It.prototype.display=function(){this.baseMenu.getFrame().appendChild(this.getHtml())},It.prototype.setBaseMenu=function(){for(var t=this;(t=t.anchor.getMenu())instanceof It;);this.baseMenu=t},k=It,_.SubMenu=k,function(t){t.close=function(t){var e=t.getMenu();e instanceof H.SubMenu?e.baseMenu.unpost():e.unpost()},t.getActiveElement=function(t){var e=t.getMenu();return(e instanceof H.SubMenu?e.baseMenu:e).getStore().getActive()},t.error=function(t,e){console.log("ContextMenu Error: "+e)},t.counter=function(){return e++};var e=0}((H=bt=bt||{}).MenuUtil||(H.MenuUtil={})),L=bt=bt||{},U=L.AbstractEntry,gt(kt,U),Object.defineProperty(kt.prototype,"content",{get:function(){return this._content},set:function(t){this._content=t,this.generateHtml(),this.getMenu()&&this.getMenu().generateHtml()},enumerable:!0,configurable:!0}),kt.prototype.getId=function(){return this.id},kt.prototype.press=function(){this.disabled||(this.executeAction(),this.executeCallbacks_())},kt.prototype.executeAction=function(){},kt.prototype.registerCallback=function(t){-1===this.callbacks.indexOf(t)&&this.callbacks.push(t)},kt.prototype.unregisterCallback=function(t){var e=this.callbacks.indexOf(t);-1!==e&&this.callbacks.splice(e,1)},kt.prototype.mousedown=function(t){this.press(),this.stop(t)},kt.prototype.mouseover=function(t){this.focus(),this.stop(t)},kt.prototype.mouseout=function(t){this.deactivate(),this.stop(t)},kt.prototype.generateHtml=function(){U.prototype.generateHtml.call(this);var t=this.getHtml();t.setAttribute("aria-disabled","false"),t.textContent=this.content},kt.prototype.activate=function(){this.disabled||this.getHtml().classList.add(L.HtmlClasses.MENUACTIVE)},kt.prototype.deactivate=function(){this.getHtml().classList.remove(L.HtmlClasses.MENUACTIVE)},kt.prototype.focus=function(){this.getMenu().setFocused(this),U.prototype.focus.call(this),this.activate()},kt.prototype.unfocus=function(){this.deactivate(),U.prototype.unfocus.call(this)},kt.prototype.escape=function(t){L.MenuUtil.close(this)},kt.prototype.up=function(t){this.getMenu().up(t)},kt.prototype.down=function(t){this.getMenu().down(t)},kt.prototype.left=function(t){if(this.getMenu()instanceof L.ContextMenu)this.getMenu().left(t);else{var e=this.getMenu();e.setFocused(null),e.getAnchor().focus()}},kt.prototype.right=function(t){this.getMenu().right(t)},kt.prototype.space=function(t){this.press()},kt.prototype.disable=function(){this.disabled=!0;var t=this.getHtml();t.classList.add(L.HtmlClasses.MENUDISABLED),t.setAttribute("aria-disabled","true")},kt.prototype.enable=function(){this.disabled=!1;var t=this.getHtml();t.classList.remove(L.HtmlClasses.MENUDISABLED),t.removeAttribute("aria-disabled")},kt.prototype.executeCallbacks_=function(){L.MenuUtil.getActiveElement(this);for(var t=0,e=this.callbacks;t<e.length;t++){var o=e[t];try{o(this)}catch(t){L.MenuUtil.error(t,"Callback for menu entry "+this.getId()+" failed.")}}},P=kt,L.AbstractItem=P,R=bt=bt||{},J=R.AbstractItem,gt(Ht,J),Ht.prototype.generateHtml=function(){J.prototype.generateHtml.call(this);var t=this.getHtml();this.span||this.generateSpan(),t.appendChild(this.span),this.update()},Ht.prototype.register=function(){this.variable.register(this)},Ht.prototype.unregister=function(){this.variable.unregister(this)},Ht.prototype.update=function(){this.updateAria(),this.span&&this.updateSpan()},D=Ht,R.AbstractVariableItem=D,function(t){function e(t){return"."+(j.HtmlClasses[t]||t)}var o={};o[e("INFOCLOSE")]="{  top:.2em; right:.2em;}",o[e("INFOCONTENT")]="{  overflow:auto; text-align:left; font-size:80%;  padding:.4em .6em; border:1px inset; margin:1em 0px;  max-height:20em; max-width:30em; background-color:#EEEEEE;  white-space:normal;}",o[e("INFO")+e("MOUSEPOST")]="{outline:none;}",o[e("INFO")]='{  position:fixed; left:50%; width:auto; text-align:center;  border:3px outset; padding:1em 2em; background-color:#DDDDDD;  color:black;  cursor:default; font-family:message-box; font-size:120%;  font-style:normal; text-indent:0; text-transform:none;  line-height:normal; letter-spacing:normal; word-spacing:normal;  word-wrap:normal; white-space:nowrap; float:none; z-index:201;  border-radius: 15px;                     /* Opera 10.5 and IE9 */  -webkit-border-radius:15px;               /* Safari and Chrome */  -moz-border-radius:15px;                  /* Firefox */  -khtml-border-radius:15px;                /* Konqueror */  box-shadow:0px 10px 20px #808080;         /* Opera 10.5 and IE9 */  -webkit-box-shadow:0px 10px 20px #808080; /* Safari 3 & Chrome */  -moz-box-shadow:0px 10px 20px #808080;    /* Forefox 3.5 */  -khtml-box-shadow:0px 10px 20px #808080;  /* Konqueror */  filter:progid:DXImageTransform.Microsoft.dropshadow(OffX=2, OffY=2, Color="gray", Positive="true"); /* IE */}';var n={};n[e("MENU")]="{  position:absolute;  background-color:white;  color:black;  width:auto; padding:5px 0px;  border:1px solid #CCCCCC; margin:0; cursor:default;  font: menu; text-align:left; text-indent:0; text-transform:none;  line-height:normal; letter-spacing:normal; word-spacing:normal;  word-wrap:normal; white-space:nowrap; float:none; z-index:201;  border-radius: 5px;                     /* Opera 10.5 and IE9 */  -webkit-border-radius: 5px;             /* Safari and Chrome */  -moz-border-radius: 5px;                /* Firefox */  -khtml-border-radius: 5px;              /* Konqueror */  box-shadow:0px 10px 20px #808080;         /* Opera 10.5 and IE9 */  -webkit-box-shadow:0px 10px 20px #808080; /* Safari 3 & Chrome */  -moz-box-shadow:0px 10px 20px #808080;    /* Forefox 3.5 */  -khtml-box-shadow:0px 10px 20px #808080;  /* Konqueror */}",n[e("MENUITEM")]="{  padding: 1px 2em;  background:transparent;}",n[e("MENUARROW")]="{  position:absolute; right:.5em; padding-top:.25em; color:#666666;  font-family: null; font-size: .75em}",n[e("MENUACTIVE")+" "+e("MENUARROW")]="{color:white}",n[e("MENUARROW")+e("RTL")]="{left:.5em; right:auto}",n[e("MENUCHECK")]="{  position:absolute; left:.7em;  font-family: null}",n[e("MENUCHECK")+e("RTL")]="{ right:.7em; left:auto }",n[e("MENURADIOCHECK")]="{  position:absolute; left: .7em;}",n[e("MENURADIOCHECK")+e("RTL")]="{  right: .7em; left:auto}",n[e("MENUINPUTBOX")]="{  padding-left: 1em; right:.5em; color:#666666;  font-family: null;}",n[e("MENUINPUTBOX")+e("RTL")]="{  left: .1em;}",n[e("MENUCOMBOBOX")]="{  left:.1em; padding-bottom:.5em;}",n[e("MENULABEL")]="{  padding: 1px 2em 3px 1.33em;  font-style:italic}",n[e("MENURULE")]="{  border-top: 1px solid #DDDDDD;  margin: 4px 3px;}",n[e("MENUDISABLED")]="{  color:GrayText}",n[e("MENUACTIVE")]="{  background-color: #606872;  color: white;}",n[e("MENUDISABLED")+":focus"]="{  background-color: #E8E8E8}",n[e("MENULABEL")+":focus"]="{  background-color: #E8E8E8}",n[e("CONTEXTMENU")+":focus"]="{  outline:none}",n[e("CONTEXTMENU")+" "+e("MENUITEM")+":focus"]="{  outline:none}",n[e("MENU")+" "+e("MENUCLOSE")]="{  top:-10px; left:-10px}";var i={};i[e("MENUCLOSE")]='{  position:absolute;  cursor:pointer;  display:inline-block;  border:2px solid #AAA;  border-radius:18px;  -webkit-border-radius: 18px;             /* Safari and Chrome */  -moz-border-radius: 18px;                /* Firefox */  -khtml-border-radius: 18px;              /* Konqueror */  font-family: "Courier New", Courier;  font-size:24px;  color:#F0F0F0}',i[e("MENUCLOSE")+" span"]="{  display:block; background-color:#AAA; border:1.5px solid;  border-radius:18px;  -webkit-border-radius: 18px;             /* Safari and Chrome */  -moz-border-radius: 18px;                /* Firefox */  -khtml-border-radius: 18px;              /* Konqueror */  line-height:0;  padding:8px 0 6px     /* may need to be browser-specific */}",i[e("MENUCLOSE")+":hover"]="{  color:white!important;  border:2px solid #CCC!important}",i[e("MENUCLOSE")+":hover span"]="{  background-color:#CCC!important}";var r=!(i[e("MENUCLOSE")+":hover:focus"]="{  outline:none}"),s=!1,a=!1;function u(t){a||(l(i,t),a=!0)}function l(t,e){var o=e||document,n=o.createElement("style");n.type="text/css";var i="";for(var r in t)i+=r,i+=" ",i+=t[r],i+="\n";n.innerHTML=i,o.head.appendChild(n)}t.addMenuStyles=function(t){s||(l(n,t),s=!0,u(t))},t.addInfoStyles=function(t){r||(l(o,t),r=!0,u(t))}}((j=bt=bt||{}).CssStyles||(j.CssStyles={})),F=bt=bt||{},V=F.AbstractPostable,gt(Lt,V),Lt.prototype.generateHtml=function(){var t=document.createElement("span");t.classList.add(this.className),t.setAttribute("role",this.role),t.setAttribute("tabindex","0");var e=document.createElement("span");e.textContent="\xd7",t.appendChild(e),this.setHtml(t)},Lt.prototype.display=function(){},Lt.prototype.unpost=function(){V.prototype.unpost.call(this),this.element.unpost()},Lt.prototype.keydown=function(t){this.bubbleKey(),V.prototype.keydown.call(this,t)},Lt.prototype.space=function(t){this.unpost(),this.stop(t)},Lt.prototype.mousedown=function(t){this.unpost(),this.stop(t)},B=Lt,F.CloseButton=B,K=bt=bt||{},z=K.AbstractPostable,gt(Ut,z),Ut.prototype.attachMenu=function(t){this.menu=t},Ut.prototype.getHtml=function(){return z.prototype.getHtml.call(this)},Ut.prototype.generateHtml=function(){z.prototype.generateHtml.call(this);var t=this.getHtml();t.appendChild(this.generateTitle()),t.appendChild(this.contentDiv),t.appendChild(this.generateSignature()),t.appendChild(this.close.getHtml()),t.setAttribute("tabindex","0")},Ut.prototype.post=function(){z.prototype.post.call(this);var t=document.documentElement,e=this.getHtml(),o=window.innerHeight||t.clientHeight||t.scrollHeight||0,n=Math.floor(-e.offsetWidth/2),i=Math.floor((o-e.offsetHeight)/3);e.setAttribute("style","margin-left: "+n+"px; top: "+i+"px;"),window.event instanceof MouseEvent&&e.classList.add(K.HtmlClasses.MOUSEPOST),e.focus()},Ut.prototype.display=function(){this.menu.registerWidget(this),this.contentDiv.innerHTML=this.content();var t=this.menu.getHtml();t.parentNode.removeChild(t),this.menu.getFrame().appendChild(this.getHtml())},Ut.prototype.click=function(t){},Ut.prototype.keydown=function(t){this.bubbleKey(),z.prototype.keydown.call(this,t)},Ut.prototype.escape=function(t){this.unpost()},Ut.prototype.unpost=function(){z.prototype.unpost.call(this),this.getHtml().classList.remove(K.HtmlClasses.MOUSEPOST),this.menu.unregisterWidget(this)},Ut.prototype.generateClose=function(){var t=new K.CloseButton(this),e=t.getHtml();return e.classList.add(K.HtmlClasses.INFOCLOSE),e.setAttribute("aria-label","Close Dialog Box"),t},Ut.prototype.generateTitle=function(){var t=document.createElement("span");return t.innerHTML=this.title,t.classList.add(K.HtmlClasses.INFOTITLE),t},Ut.prototype.generateContent=function(){var t=document.createElement("div");return t.classList.add(K.HtmlClasses.INFOCONTENT),t.setAttribute("tabindex","0"),t},Ut.prototype.generateSignature=function(){var t=document.createElement("span");return t.innerHTML=this.signature,t.classList.add(K.HtmlClasses.INFOSIGNATURE),t},W=Ut,K.Info=W,X=bt=bt||{},G=X.AbstractVariableItem,gt(Pt,G),Pt.parse=function(t,e){return new Pt(e,t.content,t.variable,t.id)},Pt.prototype.executeAction=function(){this.variable.setValue(!this.variable.getValue()),X.MenuUtil.close(this)},Pt.prototype.generateSpan=function(){this.span=document.createElement("span"),this.span.textContent="\u2713",this.span.classList.add(X.HtmlClasses.MENUCHECK)},Pt.prototype.updateAria=function(){this.getHtml().setAttribute("aria-checked",this.variable.getValue()?"true":"false")},Pt.prototype.updateSpan=function(){this.span.style.display=this.variable.getValue()?"":"none"},Y=Pt,X.Checkbox=Y,q=bt=bt||{},Z=q.AbstractVariableItem,gt(Rt,Z),Rt.parse=function(t,e){return new Rt(e,t.content,t.variable,t.id)},Rt.prototype.executeAction=function(){this.variable.setValue(this.input.value,q.MenuUtil.getActiveElement(this))},Rt.prototype.space=function(t){Z.prototype.space.call(this,t),this.down(null)},Rt.prototype.focus=function(){Z.prototype.focus.call(this),this.input.focus()},Rt.prototype.generateHtml=function(){Z.prototype.generateHtml.call(this),this.getHtml().classList.add(q.HtmlClasses.MENUCOMBOBOX)},Rt.prototype.generateSpan=function(){this.span=document.createElement("span"),this.span.classList.add(q.HtmlClasses.MENUINPUTBOX),this.input=document.createElement("input"),this.input.addEventListener("keydown",this.inputKey.bind(this)),this.input.setAttribute("size","10em"),this.input.setAttribute("type","text"),this.input.setAttribute("tabindex","-1"),this.span.appendChild(this.input)},Rt.prototype.inputKey=function(t){this.bubbleKey(),this.inputEvent=!0},Rt.prototype.keydown=function(t){if(this.inputEvent&&t.keyCode!==q.KEY.ESCAPE&&t.keyCode!==q.KEY.RETURN)return this.inputEvent=!1,void t.stopPropagation();Z.prototype.keydown.call(this,t),t.stopPropagation()},Rt.prototype.updateAria=function(){},Rt.prototype.updateSpan=function(){var e;try{e=this.variable.getValue(q.MenuUtil.getActiveElement(this))}catch(t){e=""}this.input.value=e},$=Rt,q.Combo=$,Q=bt=bt||{},tt=Q.AbstractItem,gt(Jt,tt),Jt.parse=function(t,e){return new Jt(e,t.content,t.action,t.id)},Jt.prototype.executeAction=function(){try{this.command(Q.MenuUtil.getActiveElement(this))}catch(t){Q.MenuUtil.error(t,"Illegal command callback.")}Q.MenuUtil.close(this)},et=Jt,Q.Command=et,ot=bt=bt||{},nt=ot.AbstractItem,gt(Dt,nt),Dt.parse=function(t,e){return new Dt(e,t.content,t.id)},Dt.prototype.generateHtml=function(){nt.prototype.generateHtml.call(this),this.getHtml().classList.add(ot.HtmlClasses.MENULABEL)},it=Dt,ot.Label=it,rt=bt=bt||{},st=rt.AbstractVariableItem,gt(jt,st),jt.parse=function(t,e){return new jt(e,t.content,t.variable,t.id)},jt.prototype.executeAction=function(){this.variable.setValue(this.getId()),rt.MenuUtil.close(this)},jt.prototype.generateSpan=function(){this.span=document.createElement("span"),this.span.textContent="\u2713",this.span.classList.add(rt.HtmlClasses.MENURADIOCHECK)},jt.prototype.updateAria=function(){this.getHtml().setAttribute("aria-checked",this.variable.getValue()===this.getId()?"true":"false")},jt.prototype.updateSpan=function(){this.span.style.display=this.variable.getValue()===this.getId()?"":"none"},at=jt,rt.Radio=at,ut=bt=bt||{},lt=ut.AbstractEntry,gt(Ft,lt),Ft.parse=function(t,e){return new Ft(e)},Ft.prototype.generateHtml=function(){lt.prototype.generateHtml.call(this);var t=this.getHtml();t.classList.add(ut.HtmlClasses.MENURULE),t.setAttribute("aria-orientation","vertical")},Ft.prototype.addEvents=function(t){},ct=Ft,ut.Rule=ct,pt=bt=bt||{},ht=pt.AbstractItem,gt(Vt,ht),Vt.parse=function(t,e){var o=t.content,n=t.menu,i=new Vt(e,o,t.id);return i.setSubmenu(pt.SubMenu.parse(n,i)),i},Vt.prototype.setSubmenu=function(t){this.submenu=t},Vt.prototype.getSubmenu=function(){return this.submenu},Vt.prototype.mouseover=function(t){this.focus(),this.stop(t)},Vt.prototype.mouseout=function(t){this.stop(t)},Vt.prototype.unfocus=function(){if(this.submenu.isPosted()){if(this.getMenu().getFocused()!==this)return ht.prototype.unfocus.call(this),void this.getMenu().unpostSubmenus();this.getHtml().setAttribute("tabindex","-1"),this.getHtml().blur()}else ht.prototype.unfocus.call(this)},Vt.prototype.focus=function(){ht.prototype.focus.call(this),this.submenu.isPosted()||this.disabled||this.submenu.post()},Vt.prototype.executeAction=function(){this.submenu.isPosted()?this.submenu.unpost():this.submenu.post()},Vt.prototype.generateHtml=function(){ht.prototype.generateHtml.call(this);var t=this.getHtml();this.span=document.createElement("span"),this.span.textContent="\u25ba",this.span.classList.add(pt.HtmlClasses.MENUARROW),t.appendChild(this.span),t.setAttribute("aria-haspopup","true")},Vt.prototype.left=function(t){this.getSubmenu().isPosted()?this.getSubmenu().unpost():ht.prototype.left.call(this,t)},Vt.prototype.right=function(t){this.getSubmenu().isPosted()?this.getSubmenu().down(t):this.getSubmenu().post()},dt=Vt,pt.Submenu=dt,mt=bt=bt||{},ft=mt.AbstractPostable,gt(Bt,ft),Bt.prototype.attachMenu=function(t){this.menu=t},Bt.prototype.post=function(){this.display()},Bt.prototype.display=function(){this.active=this.menu.getStore().getActive();var t=[];for(var e in Bt.popupSettings)t.push(e+"="+Bt.popupSettings[e]);for(var e in this.localSettings)t.push(e+"="+this.localSettings[e]);this.window=window.open("","_blank",t.join(",")),this.windowList.push(this.window);var o=this.window.document;this.mobileFlag?(o.open(),o.write('<html><head><meta name="viewport" content="width=device-width, initial-scale=1.0" /><title>'+this.title+'</title></head><body style="font-size:85%">'),o.write("<pre>"+this.generateContent()+"</pre>"),o.write('<hr><input type="button" value="Close" onclick="window.close()" />'),o.write("</body></html>"),o.close()):(o.open(),o.write("<html><head><title>"+this.title+'</title></head><body style="font-size:85%">'),o.write("<table><tr><td><pre>"+this.generateContent()+"</pre></td></tr></table>"),o.write("</body></html>"),o.close(),setTimeout(this.resize.bind(this),50))},Bt.prototype.unpost=function(){this.windowList.forEach(function(t){return t.close()}),this.window=null},Bt.prototype.generateContent=function(){return this.content(this.active)},Bt.prototype.resize=function(){var t=this.window.document.body.firstChild,e=this.window.outerHeight-this.window.innerHeight||30,o=this.window.outerWidth-this.window.innerWidth||30;o=Math.max(140,Math.min(Math.floor(.5*this.window.screen.width),t.offsetWidth+o+25)),e=Math.max(40,Math.min(Math.floor(.5*this.window.screen.height),t.offsetHeight+e+25)),this.window.resizeTo(o,e);var n=this.active.getBoundingClientRect();if(n){var i=Math.max(0,Math.min(n.right-Math.floor(o/2),this.window.screen.width-o-20)),r=Math.max(0,Math.min(n.bottom-Math.floor(e/2),this.window.screen.height-e-20));this.window.moveTo(i,r)}this.active=null},Bt.popupSettings={status:"no",toolbar:"no",locationbar:"no",menubar:"no",directories:"no",personalbar:"no",resizable:"yes",scrollbars:"yes",width:400,height:300},yt=Bt,mt.Popup=yt,(bt=bt||{}).TOUCH={START:"touchstart",MOVE:"touchmove",END:"touchend",CANCEL:"touchcancel"}},function(t,e,o){"use strict";var n=o(13),i=l(o(1)),r=l(o(2)),s=l(o(7)),a=l(o(5)),u=l(o(6));function l(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e.default=t,e}(0,n.combineWithMathJax)({_:{ui:{menu:{MJContextMenu:i,Menu:r,MenuHandler:s,MmlVisitor:a,SelectableInfo:u}}}})},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.combineConfig=MathJax._.components.global.combineConfig,e.combineDefaults=MathJax._.components.global.combineDefaults,e.combineWithMathJax=MathJax._.components.global.combineWithMathJax,e.MathJax=MathJax._.components.global.MathJax},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.SerializedMmlVisitor=MathJax._.core.MmlTree.SerializedMmlVisitor.SerializedMmlVisitor}]);