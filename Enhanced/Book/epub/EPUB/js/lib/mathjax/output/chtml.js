!function(r){var o={};function i(t){if(o[t])return o[t].exports;var e=o[t]={i:t,l:!1,exports:{}};return r[t].call(e.exports,e,e.exports,i),e.l=!0,e.exports}i.m=r,i.c=o,i.d=function(t,e,r){i.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},i.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},i.t=function(e,t){if(1&t&&(e=i(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(i.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)i.d(r,o,function(t){return e[t]}.bind(null,o));return r},i.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return i.d(e,"a",e),e},i.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},i.p="",i(i.s=72)}([function(t,c,e){"use strict";var o,r,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),u=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},p=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a};Object.defineProperty(c,"__esModule",{value:!0});var n=e(2),a=e(23),s=e(24);c.FONTSIZE={"70.7%":"s","70%":"s","50%":"ss","60%":"Tn","85%":"sm","120%":"lg","144%":"Lg","173%":"LG","207%":"hg","249%":"HG"},c.SPACE=((r={})[n.em(2/18)]="1",r[n.em(3/18)]="2",r[n.em(4/18)]="3",r[n.em(5/18)]="4",r[n.em(6/18)]="5",r);var l,h=(l=a.CommonWrapper,i(d,l),d.prototype.toCHTML=function(t){var e,r,o=this.standardCHTMLnode(t);try{for(var i=u(this.childNodes),n=i.next();!n.done;n=i.next())n.value.toCHTML(o)}catch(t){e={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(e)throw e.error}}},d.prototype.standardCHTMLnode=function(t){this.markUsed();var e=this.createCHTMLnode(t);return this.handleStyles(),this.handleVariant(),this.handleScale(),this.handleColor(),this.handleSpace(),this.handleAttributes(),this.handlePWidth(),e},d.prototype.markUsed=function(){this.constructor.used=!0},d.prototype.createCHTMLnode=function(t){var e=this.node.attributes.get("href");return e&&(t=this.adaptor.append(t,this.html("a",{href:e}))),this.chtml=this.adaptor.append(t,this.html("mjx-"+this.node.kind)),this.chtml},d.prototype.handleStyles=function(){if(this.styles){var t=this.styles.cssText;if(t){this.adaptor.setAttribute(this.chtml,"style",t);var e=this.styles.get("font-family");e&&this.adaptor.setStyle(this.chtml,"font-family","MJXZERO, "+e)}}},d.prototype.handleVariant=function(){this.node.isToken&&"-explicitFont"!==this.variant&&this.adaptor.setAttribute(this.chtml,"class",(this.font.getVariant(this.variant)||this.font.getVariant("normal")).classes)},d.prototype.handleScale=function(){this.setScale(this.chtml,this.bbox.rscale)},d.prototype.setScale=function(t,e){var r=Math.abs(e-1)<.001?1:e;if(t&&1!==r){var o=this.percent(r);c.FONTSIZE[o]?this.adaptor.setAttribute(t,"size",c.FONTSIZE[o]):this.adaptor.setStyle(t,"fontSize",o)}return t},d.prototype.handleSpace=function(){var e,t;try{for(var r=u([[this.bbox.L,"space","marginLeft"],[this.bbox.R,"rspace","marginRight"]]),o=r.next();!o.done;o=r.next()){var i=o.value,n=p(i,3),a=n[0],s=n[1],l=n[2];if(a){var h=this.em(a);c.SPACE[h]?this.adaptor.setAttribute(this.chtml,s,c.SPACE[h]):this.adaptor.setStyle(this.chtml,l,h)}}}catch(t){e={error:t}}finally{try{o&&!o.done&&(t=r.return)&&t.call(r)}finally{if(e)throw e.error}}},d.prototype.handleColor=function(){var t=this.node.attributes,e=t.getExplicit("mathcolor"),r=t.getExplicit("color"),o=t.getExplicit("mathbackground"),i=t.getExplicit("background");(e||r)&&this.adaptor.setStyle(this.chtml,"color",e||r),(o||i)&&this.adaptor.setStyle(this.chtml,"backgroundColor",o||i)},d.prototype.handleAttributes=function(){var e,t,r=this.node.attributes,o=r.getAllDefaults(),i=d.skipAttributes;try{for(var n=u(r.getExplicitNames()),a=n.next();!a.done;a=n.next()){var s=a.value;!1!==i[s]&&(s in o||i[s]||this.adaptor.hasAttribute(this.chtml,s))||this.adaptor.setAttribute(this.chtml,s,r.getExplicit(s))}}catch(t){e={error:t}}finally{try{a&&!a.done&&(t=n.return)&&t.call(n)}finally{if(e)throw e.error}}r.get("class")&&this.adaptor.addClass(this.chtml,r.get("class"))},d.prototype.handlePWidth=function(){this.bbox.pwidth&&(this.bbox.pwidth===s.BBox.fullWidth?this.adaptor.setAttribute(this.chtml,"width","full"):this.adaptor.setStyle(this.chtml,"width",this.bbox.pwidth))},d.prototype.setIndent=function(t,e,r){var o=this.adaptor;if("center"===e||"left"===e){var i=this.getBBox().L;o.setStyle(t,"margin-left",this.em(r+i))}if("center"===e||"right"===e){var n=this.getBBox().R;o.setStyle(t,"margin-right",this.em(-r+n))}},d.prototype.drawBBox=function(){var t=this.getBBox(),e=t.w,r=t.h,o=t.d,i=t.R,n=this.html("mjx-box",{style:{opacity:.25,"margin-left":this.em(-e-i)}},[this.html("mjx-box",{style:{height:this.em(r),width:this.em(e),"background-color":"red"}}),this.html("mjx-box",{style:{height:this.em(o),width:this.em(e),"margin-left":this.em(-e),"vertical-align":this.em(-o),"background-color":"green"}})]),a=this.chtml||this.parent.chtml,s=this.adaptor.getAttribute(a,"size");s&&this.adaptor.setAttribute(n,"size",s);var l=this.adaptor.getStyle(a,"fontSize");l&&this.adaptor.setStyle(n,"fontSize",l),this.adaptor.append(this.adaptor.parent(a),n),this.adaptor.setStyle(a,"backgroundColor","#FFEE00")},d.prototype.html=function(t,e,r){return void 0===e&&(e={}),void 0===r&&(r=[]),this.jax.html(t,e,r)},d.prototype.text=function(t){return this.jax.text(t)},d.prototype.createMo=function(t){return l.prototype.createMo.call(this,t)},d.prototype.coreMO=function(){return l.prototype.coreMO.call(this)},d.prototype.char=function(t){return this.font.charSelector(t).substr(1)},d.kind="unknown",d.autoStyle=!0,d.used=!1,d);function d(){var t=null!==l&&l.apply(this,arguments)||this;return t.chtml=null,t}c.CHTMLWrapper=h},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var o=r(2);e.BBoxStyleAdjust=[["borderTopWidth","h"],["borderRightWidth","w"],["borderBottomWidth","d"],["borderLeftWidth","w",0],["paddingTop","h"],["paddingRight","w"],["paddingBottom","d"],["paddingLeft","w",0]];var i=(n.zero=function(){return new n({h:0,d:0,w:0})},n.empty=function(){return new n},n.prototype.empty=function(){return this.w=0,this.h=this.d=-o.BIGDIMEN,this},n.prototype.clean=function(){this.w===-o.BIGDIMEN&&(this.w=0),this.h===-o.BIGDIMEN&&(this.h=0),this.d===-o.BIGDIMEN&&(this.d=0)},n.prototype.rescale=function(t){this.w*=t,this.h*=t,this.d*=t},n.prototype.combine=function(t,e,r){void 0===e&&(e=0),void 0===r&&(r=0);var o=t.rscale,i=e+o*(t.w+t.L+t.R),n=r+o*t.h,a=o*t.d-r;i>this.w&&(this.w=i),n>this.h&&(this.h=n),a>this.d&&(this.d=a)},n.prototype.append=function(t){var e=t.rscale;this.w+=e*(t.w+t.L+t.R),e*t.h>this.h&&(this.h=e*t.h),e*t.d>this.d&&(this.d=e*t.d)},n.prototype.updateFrom=function(t){this.h=t.h,this.d=t.d,this.w=t.w,t.pwidth&&(this.pwidth=t.pwidth)},n.fullWidth="100%",n);function n(t){void 0===t&&(t={w:0,h:-o.BIGDIMEN,d:-o.BIGDIMEN}),this.w=t.w||0,this.h="h"in t?t.h:-o.BIGDIMEN,this.d="d"in t?t.d:-o.BIGDIMEN,this.L=this.R=this.ic=this.sk=0,this.scale=this.rscale=1,this.pwidth=""}e.BBox=i},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.BIGDIMEN=MathJax._.util.lengths.BIGDIMEN,e.UNITS=MathJax._.util.lengths.UNITS,e.RELUNITS=MathJax._.util.lengths.RELUNITS,e.MATHSPACE=MathJax._.util.lengths.MATHSPACE,e.length2em=MathJax._.util.lengths.length2em,e.percent=MathJax._.util.lengths.percent,e.em=MathJax._.util.lengths.em,e.emRounded=MathJax._.util.lengths.emRounded,e.px=MathJax._.util.lengths.px},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.TEXCLASS=MathJax._.core.MmlTree.MmlNode.TEXCLASS,e.TEXCLASSNAMES=MathJax._.core.MmlTree.MmlNode.TEXCLASSNAMES,e.indentAttributes=MathJax._.core.MmlTree.MmlNode.indentAttributes,e.AbstractMmlNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlNode,e.AbstractMmlTokenNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlTokenNode,e.AbstractMmlLayoutNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlLayoutNode,e.AbstractMmlBaseNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlBaseNode,e.AbstractMmlEmptyNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlEmptyNode,e.TextNode=MathJax._.core.MmlTree.MmlNode.TextNode,e.XMLNode=MathJax._.core.MmlTree.MmlNode.XMLNode},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.sortLength=MathJax._.util.string.sortLength,e.quotePattern=MathJax._.util.string.quotePattern,e.unicodeChars=MathJax._.util.string.unicodeChars,e.isPercent=MathJax._.util.string.isPercent,e.split=MathJax._.util.string.split},function(t,e,r){"use strict";var a=this&&this.__assign||function(){return(a=Object.assign||function(t){for(var e,r=1,o=arguments.length;r<o;r++)for(var i in e=arguments[r])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)},o=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a},s=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(o(arguments[e]));return t},l=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.V=1,e.H=2,e.NOSTRETCH={dir:0};var i=(n.charOptions=function(t,e){var r=t[e];return 3===r.length&&(r[3]={}),r[3]},n.prototype.createVariant=function(t,e,r){void 0===e&&(e=null),void 0===r&&(r=null);var o={linked:[],chars:e?Object.create(this.variant[e].chars):{}};r&&this.variant[r]&&(Object.assign(o.chars,this.variant[r].chars),this.variant[r].linked.push(o.chars),o.chars=Object.create(o.chars)),this.variant[t]=o},n.prototype.createVariants=function(t){var e,r;try{for(var o=l(t),i=o.next();!i.done;i=o.next()){var n=i.value;this.createVariant(n[0],n[1],n[2])}}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}},n.prototype.defineChars=function(t,e){var r,o,i=this.variant[t];Object.assign(i.chars,e);try{for(var n=l(i.linked),a=n.next();!a.done;a=n.next()){var s=a.value;Object.assign(s,e)}}catch(t){r={error:t}}finally{try{a&&!a.done&&(o=n.return)&&o.call(n)}finally{if(r)throw r.error}}},n.prototype.defineDelimiters=function(t){Object.assign(this.delimiters,t)},n.prototype.defineRemap=function(t,e){this.remapChars.hasOwnProperty(t)||(this.remapChars[t]={}),Object.assign(this.remapChars[t],e)},n.prototype.getDelimiter=function(t){return this.delimiters[t]},n.prototype.getSizeVariant=function(t,e){return this.delimiters[t].variants&&(e=this.delimiters[t].variants[e]),this.sizeVariants[e]},n.prototype.getChar=function(t,e){return this.variant[t].chars[e]},n.prototype.getVariant=function(t){return this.variant[t]},n.prototype.getCssFont=function(t){return this.cssFontMap[t]||["serif",!1,!1]},n.prototype.getRemappedChar=function(t,e){return(this.remapChars[t]||{})[e]},n.OPTIONS={},n.defaultVariants=[["normal"],["bold","normal"],["italic","normal"],["bold-italic","italic","bold"],["double-struck","bold"],["fraktur","normal"],["bold-fraktur","bold","fraktur"],["script","normal"],["bold-script","bold","script"],["sans-serif","normal"],["bold-sans-serif","bold","sans-serif"],["sans-serif-italic","italic","sans-serif"],["bold-sans-serif-italic","bold-italic","sans-serif"],["monospace","normal"]],n.defaultCssFonts={normal:["serif",!1,!1],bold:["serif",!1,!0],italic:["serif",!0,!1],"bold-italic":["serif",!0,!0],"double-struck":["serif",!1,!0],fraktur:["serif",!1,!1],"bold-fraktur":["serif",!1,!0],script:["cursive",!1,!1],"bold-script":["cursive",!1,!0],"sans-serif":["sans-serif",!1,!1],"bold-sans-serif":["sans-serif",!1,!0],"sans-serif-italic":["sans-serif",!0,!1],"bold-sans-serif-italic":["sans-serif",!0,!0],monospace:["monospace",!1,!1]},n.defaultAccentMap={768:"\u02cb",769:"\u02ca",770:"\u02c6",771:"\u02dc",772:"\u02c9",774:"\u02d8",775:"\u02d9",776:"\xa8",778:"\u02da",780:"\u02c7",8594:"\u20d7",8242:"'",8243:"''",8244:"'''",8245:"`",8246:"``",8247:"```",8279:"''''",8400:"\u21bc",8401:"\u21c0",8406:"\u2190",8417:"\u2194",8432:"*",8411:"...",8412:"....",8428:"\u21c1",8429:"\u21bd",8430:"\u2190",8431:"\u2192"},n.defaultMoMap={45:"\u2212"},n.defaultMnMap={45:"\u2212"},n.defaultParams={x_height:.442,quad:1,num1:.676,num2:.394,num3:.444,denom1:.686,denom2:.345,sup1:.413,sup2:.363,sup3:.289,sub1:.15,sub2:.247,sup_drop:.386,sub_drop:.05,delim1:2.39,delim2:1,axis_height:.25,rule_thickness:.06,big_op_spacing1:.111,big_op_spacing2:.167,big_op_spacing3:.2,big_op_spacing4:.6,big_op_spacing5:.1,surd_height:.075,scriptspace:.05,nulldelimiterspace:.12,delimiterfactor:901,delimitershortfall:.3,min_rule_thickness:1.25},n.defaultDelimiters={},n.defaultChars={},n.defaultSizeVariants=[],n);function n(){var e,t;this.variant={},this.delimiters={},this.cssFontMap={},this.remapChars={};var r=this.constructor;this.params=a({},r.defaultParams),this.sizeVariants=s(r.defaultSizeVariants),this.cssFontMap=a({},r.defaultCssFonts),this.createVariants(r.defaultVariants),this.defineDelimiters(r.defaultDelimiters);try{for(var o=l(Object.keys(r.defaultChars)),i=o.next();!i.done;i=o.next()){var n=i.value;this.defineChars(n,r.defaultChars[n])}}catch(t){e={error:t}}finally{try{i&&!i.done&&(t=o.return)&&t.call(o)}finally{if(e)throw e.error}}this.defineRemap("accent",r.defaultAccentMap),this.defineRemap("mo",r.defaultMoMap),this.defineRemap("mn",r.defaultMnMap)}e.FontData=i},function(t,d,e){"use strict";var f=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a};Object.defineProperty(d,"__esModule",{value:!0}),d.ARROWX=4,d.ARROWDX=1,d.ARROWY=2,d.THICKNESS=.067,d.PADDING=.2,d.SOLID=d.THICKNESS+"em solid",d.sideIndex={top:0,right:1,bottom:2,left:3},d.sideNames=Object.keys(d.sideIndex),d.fullBBox=function(t){return new Array(4).fill(t.thickness+t.padding)},d.fullPadding=function(t){return new Array(4).fill(t.padding)},d.fullBorder=function(t){return new Array(4).fill(t.thickness)},d.arrowHead=function(t){return Math.max(t.padding,t.thickness*(t.arrowhead.x+t.arrowhead.dx+1))},d.arrowBBoxHD=function(t,e){if(t.childNodes[0]){var r=t.childNodes[0].getBBox(),o=r.h,i=r.d;e[0]=e[2]=Math.max(0,t.thickness*t.arrowhead.y-(o+i)/2)}return e},d.arrowBBoxW=function(t,e){if(t.childNodes[0]){var r=t.childNodes[0].getBBox().w;e[1]=e[3]=Math.max(0,t.thickness*t.arrowhead.y-r/2)}return e},d.arrowDef={up:[-Math.PI/2,!1,!0,"verticalstrike"],down:[Math.PI/2,!1,!0,"verticakstrike"],right:[0,!1,!1,"horizontalstrike"],left:[Math.PI,!1,!1,"horizontalstrike"],updown:[Math.PI/2,!0,!0,"verticalstrike uparrow downarrow"],leftright:[0,!0,!1,"horizontalstrike leftarrow rightarrow"]},d.diagonalArrowDef={updiagonal:[-1,0,!1,"updiagonalstrike northeastarrow"],northeast:[-1,0,!1,"updiagonalstrike updiagonalarrow"],southeast:[1,0,!1,"downdiagonalstrike"],northwest:[1,Math.PI,!1,"downdiagonalstrike"],southwest:[-1,Math.PI,!1,"updiagonalstrike"],northeastsouthwest:[-1,0,!0,"updiagonalstrike northeastarrow updiagonalarrow southwestarrow"],northwestsoutheast:[1,0,!0,"downdiagonalstrike northwestarrow southeastarrow"]},d.arrowBBox={up:function(t){return d.arrowBBoxW(t,[d.arrowHead(t),0,t.padding,0])},down:function(t){return d.arrowBBoxW(t,[t.padding,0,d.arrowHead(t),0])},right:function(t){return d.arrowBBoxHD(t,[0,d.arrowHead(t),0,t.padding])},left:function(t){return d.arrowBBoxHD(t,[0,t.padding,0,d.arrowHead(t)])},updown:function(t){return d.arrowBBoxW(t,[d.arrowHead(t),0,d.arrowHead(t),0])},leftright:function(t){return d.arrowBBoxHD(t,[0,d.arrowHead(t),0,d.arrowHead(t)])}},d.CommonBorder=function(e){return function(t){var r=d.sideIndex[t];return[t,{renderer:e,bbox:function(t){var e=[0,0,0,0];return e[r]=t.thickness+t.padding,e},border:function(t){var e=[0,0,0,0];return e[r]=t.thickness,e}}]}},d.CommonBorder2=function(n){return function(t,e,r){var o=d.sideIndex[e],i=d.sideIndex[r];return[t,{renderer:n,bbox:function(t){var e=t.thickness+t.padding,r=[0,0,0,0];return r[o]=r[i]=e,r},border:function(t){var e=[0,0,0,0];return e[o]=e[i]=t.thickness,e},remove:e+" "+r}]}},d.CommonDiagonalStrike=function(r){return function(t){var e="mjx-"+t.charAt(0)+"strike";return[t+"diagonalstrike",{renderer:r(e),bbox:d.fullBBox}]}},d.CommonDiagonalArrow=function(h){return function(t){var e=f(d.diagonalArrowDef[t],4),a=e[0],s=e[1],l=e[2];return[t+"arrow",{renderer:function(t,e){var r=t.arrowData(),o=r.a,i=r.W,n=t.arrow(i,a*(o-s),l);h(t,n)},bbox:function(t){var e=t.arrowData(),r=e.a,o=e.x,i=e.y,n=f([t.arrowhead.x,t.arrowhead.y,t.arrowhead.dx],3),a=n[0],s=n[1],l=n[2],h=f(t.getArgMod(a+l,s),2),c=h[0],u=h[1],p=i+(r<c?t.thickness*u*Math.sin(c-r):0),d=o+(c>Math.PI/2-r?t.thickness*u*Math.sin(c+r-Math.PI/2):0);return[p,d,p,d]},remove:e[3]}]}},d.CommonArrow=function(p){return function(t){var e=f(d.arrowDef[t],4),h=e[0],c=e[1],u=e[2],r=e[3];return[t+"arrow",{renderer:function(t,e){var r=t.getBBox(),o=r.w,i=r.h,n=r.d,a=f(u?[i+n,o]:[o,i+n],2),s=a[0],l=(a[1],t.arrow(s,h,c));p(t,l)},bbox:d.arrowBBox[t],remove:r}]}}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),u=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a};Object.defineProperty(e,"__esModule",{value:!0});var n,a=r(52),s=r(8),l=r(8),h=r(8),c=r(91),p=(n=s.CommonMsubMixin(a.CHTMLscriptbase),i(d,n),d.kind=c.MmlMsub.prototype.kind,d.useIC=!1,d);function d(){return null!==n&&n.apply(this,arguments)||this}e.CHTMLmsub=p;var f,m=(f=l.CommonMsupMixin(a.CHTMLscriptbase),i(y,f),y.kind=c.MmlMsup.prototype.kind,y.useIC=!0,y);function y(){return null!==f&&f.apply(this,arguments)||this}e.CHTMLmsup=m;var v,b=(v=h.CommonMsubsupMixin(a.CHTMLscriptbase),i(x,v),x.prototype.toCHTML=function(t){var e=this.standardCHTMLnode(t),r=u([this.baseChild,this.supChild,this.subChild],3),o=r[0],i=r[1],n=r[2],a=u(this.getUVQ(o.getBBox(),n.getBBox(),i.getBBox()),3),s=(a[0],a[1]),l=a[2],h={"vertical-align":this.em(s)};o.toCHTML(e);var c=this.adaptor.append(e,this.html("mjx-script",{style:h}));i.toCHTML(c),this.adaptor.append(c,this.html("mjx-spacer",{style:{"margin-top":this.em(l)}})),n.toCHTML(c),this.baseCore.bbox.ic&&this.adaptor.setStyle(i.chtml,"marginLeft",this.em(this.coreIC()/i.bbox.rscale))},x.kind=c.MmlMsubsup.prototype.kind,x.styles={"mjx-script":{display:"inline-block","padding-right":".05em"},"mjx-script > *":{display:"block"}},x.useIC=!1,x);function x(){return null!==v&&v.apply(this,arguments)||this}e.CHTMLmsubsup=b},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),p=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMsubMixin=function(t){return i(e,r=t),Object.defineProperty(e.prototype,"script",{get:function(){return this.childNodes[this.node.sub]},enumerable:!0,configurable:!0}),e.prototype.getOffset=function(t,e){return[0,-this.getV(t,e)]},e;function e(){return null!==r&&r.apply(this,arguments)||this}var r},e.CommonMsupMixin=function(t){return i(e,r=t),Object.defineProperty(e.prototype,"script",{get:function(){return this.childNodes[this.node.sup]},enumerable:!0,configurable:!0}),e.prototype.getOffset=function(t,e){return[this.baseCore.bbox.ic?.2*this.baseCore.bbox.ic+.05:0,this.getU(t,e)]},e;function e(){return null!==r&&r.apply(this,arguments)||this}var r},e.CommonMsubsupMixin=function(t){return i(e,r=t),Object.defineProperty(e.prototype,"subChild",{get:function(){return this.childNodes[this.node.sub]},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"supChild",{get:function(){return this.childNodes[this.node.sup]},enumerable:!0,configurable:!0}),e.prototype.computeBBox=function(t,e){void 0===e&&(e=!1);var r=this.baseChild.getBBox(),o=this.subChild.getBBox(),i=this.supChild.getBBox();t.empty(),t.append(r);var n=t.w,a=p(this.getUVQ(r,o,i),3),s=a[0],l=a[1];a[2],t.combine(o,n,l),t.combine(i,n+this.coreIC(),s),t.w+=this.font.params.scriptspace,t.clean(),this.setChildPWidths(e)},e.prototype.getUVQ=function(t,e,r){if(this.UVQ)return this.UVQ;var o=this.font.params,i=3*o.rule_thickness,n=this.length2em(this.node.attributes.get("subscriptshift"),o.sub2),a=this.isCharBase()?0:t.d+o.sub_drop*e.rscale,s=p([this.getU(t,r),Math.max(a,n)],2),l=s[0],h=s[1],c=l-r.d*r.rscale-(e.h*e.rscale-h);if(c<i){h+=i-c;var u=.8*o.x_height-(l-r.d*r.rscale);0<u&&(l+=u,h-=u)}return l=Math.max(this.length2em(this.node.attributes.get("superscriptshift"),l),l),h=Math.max(this.length2em(this.node.attributes.get("subscriptshift"),h),h),c=l-r.d*r.rscale-(e.h*e.rscale-h),this.UVQ=[l,-h,c],this.UVQ},e;function e(){var t=null!==r&&r.apply(this,arguments)||this;return t.UVQ=null,t}var r}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),m=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a},n=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(m(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMunderMixin=function(t){return i(e,c=t),Object.defineProperty(e.prototype,"script",{get:function(){return this.childNodes[this.node.under]},enumerable:!0,configurable:!0}),e.prototype.computeBBox=function(t,e){if(void 0===e&&(e=!1),this.hasMovableLimits())c.prototype.computeBBox.call(this,t,e);else{t.empty();var r=this.baseChild.getBBox(),o=this.script.getBBox(),i=m(this.getUnderKV(r,o),2),n=(i[0],i[1]),a=this.getDelta(!0),s=m(this.getDeltaW([r,o],[0,-a]),2),l=s[0],h=s[1];t.combine(r,l,0),t.combine(o,h,n),t.d+=this.font.params.big_op_spacing5,t.ic=-this.baseCore.bbox.ic,t.clean(),this.setChildPWidths(e)}},e;function e(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var r=c.apply(this,n(t))||this;return r.stretchChildren(),r}var c},e.CommonMoverMixin=function(t){return i(e,h=t),Object.defineProperty(e.prototype,"script",{get:function(){return this.childNodes[this.node.over]},enumerable:!0,configurable:!0}),e.prototype.computeBBox=function(t){if(this.hasMovableLimits())h.prototype.computeBBox.call(this,t);else{t.empty();var e=this.baseChild.getBBox(),r=this.script.getBBox(),o=m(this.getOverKU(e,r),2),i=(o[0],o[1]),n=this.getDelta(),a=m(this.getDeltaW([e,r],[0,n]),2),s=a[0],l=a[1];t.combine(e,s,0),t.combine(r,l,i),t.h+=this.font.params.big_op_spacing5,t.ic=-this.baseCore.bbox.ic,t.clean()}},e;function e(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var r=h.apply(this,n(t))||this;return r.stretchChildren(),r}var h},e.CommonMunderoverMixin=function(t){return i(e,f=t),Object.defineProperty(e.prototype,"underChild",{get:function(){return this.childNodes[this.node.under]},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"overChild",{get:function(){return this.childNodes[this.node.over]},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"subChild",{get:function(){return this.underChild},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"supChild",{get:function(){return this.overChild},enumerable:!0,configurable:!0}),e.prototype.computeBBox=function(t){if(this.hasMovableLimits())f.prototype.computeBBox.call(this,t);else{t.empty();var e=this.overChild.getBBox(),r=this.baseChild.getBBox(),o=this.underChild.getBBox(),i=m(this.getOverKU(r,e),2),n=(i[0],i[1]),a=m(this.getUnderKV(r,o),2),s=(a[0],a[1]),l=this.getDelta(),h=m(this.getDeltaW([r,o,e],[0,-l,l]),3),c=h[0],u=h[1],p=h[2];t.combine(r,c,0),t.combine(e,p,n),t.combine(o,u,s);var d=this.font.params.big_op_spacing5;t.h+=d,t.d+=d,t.ic=-this.baseCore.bbox.ic,t.clean()}},e;function e(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var r=f.apply(this,n(t))||this;return r.stretchChildren(),r}var f}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a},l=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(n(arguments[e]));return t},w=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0});var h=r(1);e.CommonMrowMixin=function(t){return i(e,s=t),Object.defineProperty(e.prototype,"fixesPWidth",{get:function(){return!1},enumerable:!0,configurable:!0}),e.prototype.stretchChildren=function(){var e,t,r,o,i,n,a=[];try{for(var s=w(this.childNodes),l=s.next();!l.done;l=s.next())(M=l.value).canStretch(1)&&a.push(M)}catch(t){e={error:t}}finally{try{l&&!l.done&&(t=s.return)&&t.call(s)}finally{if(e)throw e.error}}var h=a.length,c=this.childNodes.length;if(h&&1<c){var u=0,p=0,d=1<h&&h===c;try{for(var f=w(this.childNodes),m=f.next();!m.done;m=f.next()){var y=0===(M=m.value).stretch.dir;if(d||y){var v=M.getBBox(y),b=v.h,x=v.d;u<b&&(u=b),p<x&&(p=x)}}}catch(t){r={error:t}}finally{try{m&&!m.done&&(o=f.return)&&o.call(f)}finally{if(r)throw r.error}}try{for(var g=w(a),_=g.next();!_.done;_=g.next()){var M;(M=_.value).coreMO().getStretchedVariant([u,p])}}catch(t){i={error:t}}finally{try{_&&!_.done&&(n=g.return)&&n.call(g)}finally{if(i)throw i.error}}}},e;function e(){for(var e,t,r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];var i=s.apply(this,l(r))||this;i.stretchChildren();try{for(var n=w(i.childNodes),a=n.next();!a.done;a=n.next()){if(a.value.bbox.pwidth){i.bbox.pwidth=h.BBox.fullWidth;break}}}catch(t){e={error:t}}finally{try{a&&!a.done&&(t=n.return)&&t.call(n)}finally{if(e)throw e.error}}return i}var s},e.CommonInferredMrowMixin=function(t){return i(e,r=t),e.prototype.getScale=function(){this.bbox.scale=this.parent.bbox.scale,this.bbox.rscale=1},e;function e(){return null!==r&&r.apply(this,arguments)||this}var r}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),p=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a};Object.defineProperty(e,"__esModule",{value:!0});var n,a=r(0),s=r(49),l=r(89),h=(n=s.CommonMsqrtMixin(a.CHTMLWrapper),i(c,n),c.prototype.toCHTML=function(t){var e,r,o,i,n=this.childNodes[this.surd],a=this.childNodes[this.base],s=n.getBBox(),l=(a.getBBox(),p(this.getPQ(s),2)),h=(l[0],l[1]),c=this.standardCHTMLnode(t);null!=this.root&&(o=this.adaptor.append(c,this.html("mjx-root")),i=this.childNodes[this.root]);var u=this.adaptor.append(c,this.html("mjx-sqrt",{},[e=this.html("mjx-surd"),r=this.html("mjx-box",{style:{paddingTop:this.em(h)}})]));this.addRoot(o,i,s),n.toCHTML(e),a.toCHTML(r),n.size<0&&this.adaptor.addClass(u,"mjx-tall")},c.prototype.addRoot=function(t,e,r){},c.kind=l.MmlMsqrt.prototype.kind,c.styles={"mjx-root":{display:"inline-block","white-space":"nowrap"},"mjx-surd":{display:"inline-block","vertical-align":"top"},"mjx-sqrt":{display:"inline-block","padding-top":".07em"},"mjx-sqrt > mjx-box":{"border-top":".07em solid"},"mjx-sqrt.mjx-tall > mjx-box":{"padding-left":".3em","margin-left":"-.3em"}},c);function c(){return null!==n&&n.apply(this,arguments)||this}e.CHTMLmsqrt=h},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),C=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMtrMixin=function(t){return i(e,r=t),Object.defineProperty(e.prototype,"fixesPWidth",{get:function(){return!1},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"numCells",{get:function(){return this.childNodes.length},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"labeled",{get:function(){return!1},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"tableCells",{get:function(){return this.childNodes},enumerable:!0,configurable:!0}),e.prototype.getChild=function(t){return this.childNodes[t]},e.prototype.getChildBBoxes=function(){return this.childNodes.map(function(t){return t.getBBox()})},e.prototype.stretchChildren=function(t){var e,r,o,i,n,a;void 0===t&&(t=null);var s=[],l=this.labeled?this.childNodes.slice(1):this.childNodes;try{for(var h=C(l),c=h.next();!c.done;c=h.next())(j=c.value.childNodes[0]).canStretch(1)&&s.push(j)}catch(t){e={error:t}}finally{try{c&&!c.done&&(r=h.return)&&r.call(h)}finally{if(e)throw e.error}}var u=s.length,p=this.childNodes.length;if(u&&1<p){if(null===t){var d=0,f=0,m=1<u&&u===p;try{for(var y=C(l),v=y.next();!v.done;v=y.next()){var b=0===(j=v.value.childNodes[0]).stretch.dir;if(m||b){var x=j.getBBox(b),g=x.h,_=x.d;d<g&&(d=g),f<_&&(f=_)}}}catch(t){o={error:t}}finally{try{v&&!v.done&&(i=y.return)&&i.call(y)}finally{if(o)throw o.error}}t=[d,f]}try{for(var M=C(s),w=M.next();!w.done;w=M.next()){var j;(j=w.value).coreMO().getStretchedVariant(t)}}catch(t){n={error:t}}finally{try{w&&!w.done&&(a=M.return)&&a.call(M)}finally{if(n)throw n.error}}}},e;function e(){return null!==r&&r.apply(this,arguments)||this}var r},e.CommonMlabeledtrMixin=function(t){return i(e,r=t),Object.defineProperty(e.prototype,"numCells",{get:function(){return Math.max(0,this.childNodes.length-1)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"labeled",{get:function(){return!0},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"tableCells",{get:function(){return this.childNodes.slice(1)},enumerable:!0,configurable:!0}),e.prototype.getChild=function(t){return this.childNodes[t+1]},e.prototype.getChildBBoxes=function(){return this.childNodes.slice(1).map(function(t){return t.getBBox()})},e;function e(){return null!==r&&r.apply(this,arguments)||this}var r}},function(t,i,e){"use strict";var o,r=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),h=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a},c=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(h(arguments[e]));return t};Object.defineProperty(i,"__esModule",{value:!0});var n=e(4);i.TooltipData={dx:".2em",dy:".1em",postDelay:600,clearDelay:100,hoverTimer:new Map,clearTimer:new Map,stopTimers:function(t,e){e.clearTimer.has(t)&&(clearTimeout(e.clearTimer.get(t)),e.clearTimer.delete(t)),e.hoverTimer.has(t)&&(clearTimeout(e.hoverTimer.get(t)),e.hoverTimer.delete(t))}},i.CommonMactionMixin=function(t){return r(e,l=t),Object.defineProperty(e.prototype,"selected",{get:function(){var t=this.node.attributes.get("selection"),e=Math.max(1,Math.min(this.childNodes.length,t))-1;return this.childNodes[e]||this.wrap(this.node.selected)},enumerable:!0,configurable:!0}),e.prototype.getParameters=function(){var t=this.node.attributes.get("data-offsets"),e=h(n.split(t||""),2),r=e[0],o=e[1];this.dx=this.length2em(r||i.TooltipData.dx),this.dy=this.length2em(o||i.TooltipData.dy)},e.prototype.computeBBox=function(t,e){void 0===e&&(e=!1),t.updateFrom(this.selected.getBBox()),this.selected.setChildPWidths(e)},e;function e(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var r=l.apply(this,c(t))||this,o=r.constructor.actions,i=r.node.attributes.get("actiontype"),n=h(o.get(i)||[function(t,e){},{}],2),a=n[0],s=n[1];return r.action=a,r.data=s,r.getParameters(),r}var l}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.combineConfig=MathJax._.components.global.combineConfig,e.combineDefaults=MathJax._.components.global.combineDefaults,e.combineWithMathJax=MathJax._.components.global.combineWithMathJax,e.MathJax=MathJax._.components.global.MathJax},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__assign||function(){return(n=Object.assign||function(t){for(var e,r=1,o=arguments.length;r<o;r++)for(var i in e=arguments[r])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)},a=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0});var s,l=r(16),h=r(20),c=r(102),u=(s=l.CommonOutputJax,i(p,s),p.prototype.escaped=function(t,e){return this.setDocument(e),this.html("span",{},[this.text(t.math)])},p.prototype.styleSheet=function(t){var e=s.prototype.styleSheet.call(this,t);return this.adaptor.setAttribute(e,"id",p.STYLESHEETID),e},p.prototype.addClassStyles=function(t){var e;this.options.adaptiveCSS&&!t.used||(t.autoStyle&&"unknown"!==t.kind&&this.cssStyles.addStyles(((e={})["mjx-"+t.kind]={display:"inline-block","text-align":"left"},e)),s.prototype.addClassStyles.call(this,t))},p.prototype.processMath=function(t,e){this.factory.wrap(t).toCHTML(e)},p.prototype.clearCache=function(){var e,t;this.cssStyles.clear(),this.font.clearCache();try{for(var r=a(this.factory.getKinds()),o=r.next();!o.done;o=r.next()){var i=o.value;this.factory.getNodeClass(i).used=!1}}catch(t){e={error:t}}finally{try{o&&!o.done&&(t=r.return)&&t.call(r)}finally{if(e)throw e.error}}},p.prototype.unknownText=function(t,e){var r={},o=100/this.math.metrics.scale;return 100!=o&&(r["font-size"]=this.fixed(o,1)+"%"),"-explicitFont"!==e&&this.cssFontStyles(this.font.getCssFont(e),r),this.html("mjx-utext",{variant:e,style:r},[this.text(t)])},p.prototype.measureTextNode=function(t){var e=this.adaptor;t=e.clone(t);var r=this.html("mjx-measure-text",{},[t]);e.append(e.parent(this.math.start.node),this.container),e.append(this.container,r);var o=e.nodeSize(t,this.math.metrics.em)[0]/this.math.metrics.scale;return e.remove(this.container),e.remove(r),{w:o,h:.75,d:.25}},p.prototype.getFontData=function(t){var e=s.prototype.getFontData.call(this,t);return e[0]="MJXZERO, "+e[0],e},p.NAME="CHTML",p.OPTIONS=n(n({},l.CommonOutputJax.OPTIONS),{adaptiveCSS:!0}),p.commonStyles={'mjx-container [space="1"]':{"margin-left":".111em"},'mjx-container [space="2"]':{"margin-left":".167em"},'mjx-container [space="3"]':{"margin-left":".222em"},'mjx-container [space="4"]':{"margin-left":".278em"},'mjx-container [space="5"]':{"margin-left":".333em"},'mjx-container [rspace="1"]':{"margin-right":".111em"},'mjx-container [rspace="2"]':{"margin-right":".167em"},'mjx-container [rspace="3"]':{"margin-right":".222em"},'mjx-container [rspace="4"]':{"margin-right":".278em"},'mjx-container [rspace="5"]':{"margin-right":".333em"},'mjx-container [size="s"]':{"font-size":"70.7%"},'mjx-container [size="ss"]':{"font-size":"50%"},'mjx-container [size="Tn"]':{"font-size":"60%"},'mjx-container [size="sm"]':{"font-size":"85%"},'mjx-container [size="lg"]':{"font-size":"120%"},'mjx-container [size="Lg"]':{"font-size":"144%"},'mjx-container [size="LG"]':{"font-size":"173%"},'mjx-container [size="hg"]':{"font-size":"207%"},'mjx-container [size="HG"]':{"font-size":"249%"},'mjx-container [width="full"]':{width:"100%"},"mjx-box":{display:"inline-block"},"mjx-block":{display:"block"},"mjx-itable":{display:"inline-table"},"mjx-row":{display:"table-row"},"mjx-row > *":{display:"table-cell"},"mjx-mtext":{display:"inline-block"},"mjx-mstyle":{display:"inline-block"},"mjx-merror":{display:"inline-block",color:"red","background-color":"yellow"},"mjx-mphantom":{visibility:"hidden"}},p.STYLESHEETID="MJX-CHTML-styles",p);function p(t){void 0===t&&(t=null);var e=s.call(this,t,h.CHTMLWrapperFactory,c.TeXFont)||this;return e.font.adaptiveCSS(e.options.adaptiveCSS),e}e.CHTML=u},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__assign||function(){return(n=Object.assign||function(t){for(var e,r=1,o=arguments.length;r<o;r++)for(var i in e=arguments[r])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)},s=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a},T=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0});var l,a=r(74),h=r(17),c=r(18),u=r(2),p=r(19),d=(l=a.AbstractOutputJax,i(f,l),f.prototype.typeset=function(t,e){this.setDocument(e);var r=this.createNode();return this.toDOM(t,r,e),r},f.prototype.createNode=function(){var t=this.constructor.NAME;return this.html("mjx-container",{class:"MathJax",jax:t})},f.prototype.setScale=function(t){var e=this.math.metrics.scale*this.options.scale;1!=e&&this.adaptor.setStyle(t,"fontSize",u.percent(e))},f.prototype.toDOM=function(t,e,r){void 0===r&&(r=null),this.setDocument(r),this.math=t,this.pxPerEm=t.metrics.ex/this.font.params.x_height,t.root.setTeXclass(null),this.setScale(e),this.nodeMap=new Map,this.container=e,this.processMath(t.root,e),this.nodeMap=null,this.executeFilters(this.postFilters,t,r,e)},f.prototype.getBBox=function(t,e){this.setDocument(e),(this.math=t).root.setTeXclass(null),this.nodeMap=new Map;var r=this.factory.wrap(t.root).getBBox();return this.nodeMap=null,r},f.prototype.getMetrics=function(t){var e,r;this.setDocument(t);var o=this.adaptor,i=this.getMetricMaps(t);try{for(var n=T(t.math),a=n.next();!a.done;a=n.next()){var s=a.value,l=i[s.display?1:0].get(o.parent(s.start.node)),h=l.em,c=l.ex,u=l.containerWidth,p=l.lineWidth,d=l.scale;s.setMetrics(h,c,u,p,d)}}catch(t){e={error:t}}finally{try{a&&!a.done&&(r=n.return)&&r.call(n)}finally{if(e)throw e.error}}},f.prototype.getMetricsFor=function(t,e){var r=this.getTestElement(t,e),o=this.measureMetrics(r);return this.adaptor.remove(r),o},f.prototype.getMetricMaps=function(t){var e,r,o,i,n,a,s,l,h,c,u=this.adaptor,p=[new Map,new Map];try{for(var d=T(t.math),f=d.next();!f.done;f=d.next()){var m=f.value,y=u.parent(m.start.node),v=p[m.display?1:0];v.has(y)||v.set(y,this.getTestElement(y,m.display))}}catch(t){e={error:t}}finally{try{f&&!f.done&&(r=d.return)&&r.call(d)}finally{if(e)throw e.error}}var b=[new Map,new Map];try{for(var x=T(b.keys()),g=x.next();!g.done;g=x.next()){var _=g.value;try{for(var M=(n=void 0,T(p[_].keys())),w=M.next();!w.done;w=M.next())y=w.value,b[_].set(y,this.measureMetrics(p[_].get(y)))}catch(t){n={error:t}}finally{try{w&&!w.done&&(a=M.return)&&a.call(M)}finally{if(n)throw n.error}}}}catch(t){o={error:t}}finally{try{g&&!g.done&&(i=x.return)&&i.call(x)}finally{if(o)throw o.error}}try{for(var j=T(b.keys()),C=j.next();!C.done;C=j.next()){_=C.value;try{for(var S=(h=void 0,T(p[_].values())),O=S.next();!O.done;O=S.next())y=O.value,u.remove(y)}catch(t){h={error:t}}finally{try{O&&!O.done&&(c=S.return)&&c.call(S)}finally{if(h)throw h.error}}}}catch(t){s={error:t}}finally{try{C&&!C.done&&(l=j.return)&&l.call(j)}finally{if(s)throw s.error}}return b},f.prototype.getTestElement=function(t,e){var r=this.adaptor;if(!this.testInline){this.testInline=this.html("mjx-test",{style:{display:"inline-block",width:"100%","font-style":"normal","font-weight":"normal","font-size":"100%","font-size-adjust":"none","text-indent":0,"text-transform":"none","letter-spacing":"normal","word-spacing":"normal",overflow:"hidden",height:"1px","margin-right":"-1px"}},[this.html("mjx-left-box",{style:{display:"inline-block",width:0,float:"left"}}),this.html("mjx-ex-box",{style:{position:"absolute",overflow:"hidden",width:"1px",height:"60ex"}}),this.html("mjx-right-box",{style:{display:"inline-block",width:0,float:"right"}})]),this.testDisplay=r.clone(this.testInline),r.setStyle(this.testDisplay,"display","table"),r.setStyle(this.testDisplay,"margin-right",""),r.setStyle(r.firstChild(this.testDisplay),"display","none");var o=r.lastChild(this.testDisplay);r.setStyle(o,"display","table-cell"),r.setStyle(o,"width","10000em"),r.setStyle(o,"float","")}return r.append(t,r.clone(e?this.testDisplay:this.testInline))},f.prototype.measureMetrics=function(t){var e=this.adaptor,r=e.fontSize(t),o=e.nodeSize(e.childNode(t,1))[1]/60||r*this.options.exFactor;return{em:r,ex:o,containerWidth:"table"===e.getStyle(t,"display")?e.nodeSize(e.lastChild(t))[0]-1:e.nodeBBox(e.lastChild(t)).left-e.nodeBBox(e.firstChild(t)).left-2,lineWidth:1e6,scale:Math.max(this.options.minScale,this.options.matchFontHeight?o/this.font.params.x_height/r:1)}},f.prototype.styleSheet=function(t){var e,r;this.setDocument(t),this.cssStyles.clear(),this.cssStyles.addStyles(this.constructor.commonStyles);try{for(var o=T(this.factory.getKinds()),i=o.next();!i.done;i=o.next()){var n=i.value;this.addClassStyles(this.factory.getNodeClass(n))}}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}return this.cssStyles.addStyles(this.font.styles),this.html("style",{id:"MJX-styles"},[this.text("\n"+this.cssStyles.cssText+"\n")])},f.prototype.addClassStyles=function(t){this.cssStyles.addStyles(t.styles)},f.prototype.setDocument=function(t){t&&(this.document=t,this.adaptor.document=t.document)},f.prototype.html=function(t,e,r,o){return void 0===e&&(e={}),void 0===r&&(r=[]),this.adaptor.node(t,e,r,o)},f.prototype.text=function(t){return this.adaptor.text(t)},f.prototype.fixed=function(t,e){return void 0===e&&(e=3),Math.abs(t)<6e-4?"0":t.toFixed(e).replace(/\.?0+$/,"")},f.prototype.measureText=function(t,e,r){void 0===r&&(r=["",!1,!1]);var o=this.unknownText(t,e);if("-explicitFont"===e){var i=this.cssFontStyles(r);this.adaptor.setAttributes(o,{style:i})}return this.measureTextNodeWithCache(o,t,e,r)},f.prototype.measureTextNodeWithCache=function(t,e,r,o){void 0===o&&(o=["",!1,!1]),"-explicitFont"===r&&(r=[o[0],o[1]?"T":"F",o[2]?"T":"F",""].join("-")),this.unknownCache.has(r)||this.unknownCache.set(r,new Map);var i=this.unknownCache.get(r),n=i.get(e);if(n)return n;var a=this.measureTextNode(t);return i.set(e,a),a},f.prototype.cssFontStyles=function(t,e){void 0===e&&(e={});var r=s(t,3),o=r[0],i=r[1],n=r[2];return e["font-family"]=o,i&&(e["font-style"]="italic"),n&&(e["font-weight"]="bold"),e},f.prototype.getFontData=function(t){return[(t=t||new p.Styles).get("font-family"),"italic"===t.get("font-style"),"bold"===t.get("font-weight")]},f.NAME="Common",f.OPTIONS=n(n({},a.AbstractOutputJax.OPTIONS),{scale:1,minScale:.5,matchFontHeight:!0,mtextInheritFont:!1,merrorInheritFont:!0,mathmlSpacing:!1,skipAttributes:{},exFactor:.5,displayAlign:"center",displayIndent:"0",wrapperFactory:null,font:null,cssStyles:null}),f.commonStyles={},f);function f(t,e,r){void 0===t&&(t=null),void 0===e&&(e=null),void 0===r&&(r=null);var o=this,i=s(h.separateOptions(t,r.OPTIONS),2),n=i[0],a=i[1];return(o=l.call(this,n)||this).factory=o.options.wrapperFactory||new e,(o.factory.jax=o).cssStyles=o.options.cssStyles||new c.CssStyles,o.font=o.options.font||new r(a),o.unknownCache=new Map,o}e.CommonOutputJax=d},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.APPEND=MathJax._.util.Options.APPEND,e.REMOVE=MathJax._.util.Options.REMOVE,e.Expandable=MathJax._.util.Options.Expandable,e.expandable=MathJax._.util.Options.expandable,e.makeArray=MathJax._.util.Options.makeArray,e.keys=MathJax._.util.Options.keys,e.copy=MathJax._.util.Options.copy,e.insert=MathJax._.util.Options.insert,e.defaultOptions=MathJax._.util.Options.defaultOptions,e.userOptions=MathJax._.util.Options.userOptions,e.selectOptions=MathJax._.util.Options.selectOptions,e.selectOptionsFromKeys=MathJax._.util.Options.selectOptionsFromKeys,e.separateOptions=MathJax._.util.Options.separateOptions},function(t,e,r){"use strict";var h=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0});var o=(Object.defineProperty(i.prototype,"cssText",{get:function(){return this.getStyleString()},enumerable:!0,configurable:!0}),i.prototype.addStyles=function(t){var e,r;if(t)try{for(var o=h(Object.keys(t)),i=o.next();!i.done;i=o.next()){var n=i.value;this.styles[n]||(this.styles[n]={}),Object.assign(this.styles[n],t[n])}}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}},i.prototype.removeStyles=function(){for(var e,t,r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];try{for(var i=h(r),n=i.next();!n.done;n=i.next()){var a=n.value;delete this.styles[a]}}catch(t){e={error:t}}finally{try{n&&!n.done&&(t=i.return)&&t.call(i)}finally{if(e)throw e.error}}},i.prototype.clear=function(){this.styles={}},i.prototype.getStyleString=function(){var e,t,r=Object.keys(this.styles),o=new Array(r.length),i=0;try{for(var n=h(r),a=n.next();!a.done;a=n.next()){var s=a.value;o[i++]=s+" {\n"+this.getStyleDefString(this.styles[s])+"\n}"}}catch(t){e={error:t}}finally{try{a&&!a.done&&(t=n.return)&&t.call(n)}finally{if(e)throw e.error}}return o.join("\n\n")},i.prototype.getStyleDefString=function(t){var e,r,o=Object.keys(t),i=new Array(o.length),n=0;try{for(var a=h(o),s=a.next();!s.done;s=a.next()){var l=s.value;i[n++]="  "+l+": "+t[l]+";"}}catch(t){e={error:t}}finally{try{s&&!s.done&&(r=a.return)&&r.call(a)}finally{if(e)throw e.error}}return i.join("\n")},i);function i(t){void 0===t&&(t=null),this.styles={},this.addStyles(t)}e.CssStyles=o},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Styles=MathJax._.util.Styles.Styles},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0});var n,a=r(21),s=r(22),l=(n=a.CommonWrapperFactory,i(h,n),h.defaultNodes=s.CHTMLWrappers,h);function h(){return null!==n&&n.apply(this,arguments)||this}e.CHTMLWrapperFactory=l},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0});var n,a=r(75),s=(n=a.AbstractWrapperFactory,i(l,n),Object.defineProperty(l.prototype,"Wrappers",{get:function(){return this.node},enumerable:!0,configurable:!0}),l.defaultNodes={},l);function l(){var t=null!==n&&n.apply(this,arguments)||this;return t.jax=null,t}e.CommonWrapperFactory=s},function(t,e,r){"use strict";var o;Object.defineProperty(e,"__esModule",{value:!0});var i=r(0),n=r(25),a=r(27),s=r(29),l=r(31),h=r(33),c=r(35),u=r(37),p=r(39),d=r(41),f=r(44),m=r(45),y=r(47),v=r(11),b=r(50),x=r(7),g=r(54),_=r(55),M=r(57),w=r(59),j=r(60),C=r(62),S=r(63),O=r(65),T=r(67),B=r(69);e.CHTMLWrappers=((o={})[n.CHTMLmath.kind]=n.CHTMLmath,o[f.CHTMLmrow.kind]=f.CHTMLmrow,o[f.CHTMLinferredMrow.kind]=f.CHTMLinferredMrow,o[a.CHTMLmi.kind]=a.CHTMLmi,o[s.CHTMLmo.kind]=s.CHTMLmo,o[l.CHTMLmn.kind]=l.CHTMLmn,o[h.CHTMLms.kind]=h.CHTMLms,o[c.CHTMLmtext.kind]=c.CHTMLmtext,o[u.CHTMLmspace.kind]=u.CHTMLmspace,o[p.CHTMLmpadded.kind]=p.CHTMLmpadded,o[d.CHTMLmenclose.kind]=d.CHTMLmenclose,o[y.CHTMLmfrac.kind]=y.CHTMLmfrac,o[v.CHTMLmsqrt.kind]=v.CHTMLmsqrt,o[b.CHTMLmroot.kind]=b.CHTMLmroot,o[x.CHTMLmsub.kind]=x.CHTMLmsub,o[x.CHTMLmsup.kind]=x.CHTMLmsup,o[x.CHTMLmsubsup.kind]=x.CHTMLmsubsup,o[g.CHTMLmunder.kind]=g.CHTMLmunder,o[g.CHTMLmover.kind]=g.CHTMLmover,o[g.CHTMLmunderover.kind]=g.CHTMLmunderover,o[_.CHTMLmmultiscripts.kind]=_.CHTMLmmultiscripts,o[m.CHTMLmfenced.kind]=m.CHTMLmfenced,o[M.CHTMLmtable.kind]=M.CHTMLmtable,o[w.CHTMLmtr.kind]=w.CHTMLmtr,o[w.CHTMLmlabeledtr.kind]=w.CHTMLmlabeledtr,o[j.CHTMLmtd.kind]=j.CHTMLmtd,o[C.CHTMLmaction.kind]=C.CHTMLmaction,o[S.CHTMLmglyph.kind]=S.CHTMLmglyph,o[O.CHTMLsemantics.kind]=O.CHTMLsemantics,o[O.CHTMLannotation.kind]=O.CHTMLannotation,o[O.CHTMLannotationXML.kind]=O.CHTMLannotationXML,o[O.CHTMLxml.kind]=O.CHTMLxml,o[T.CHTMLTeXAtom.kind]=T.CHTMLTeXAtom,o[B.CHTMLTextNode.kind]=B.CHTMLTextNode,o[i.CHTMLWrapper.kind]=i.CHTMLWrapper,o)},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),c=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a},a=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(n(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0});var s=r(76),l=r(3),h=r(4),u=r(2),p=r(19),d=r(1),f=r(5),m=2/18;function y(t,e){return t?e<m?0:m:e}var v,b=(v=s.AbstractWrapper,i(x,v),Object.defineProperty(x.prototype,"jax",{get:function(){return this.factory.jax},enumerable:!0,configurable:!0}),Object.defineProperty(x.prototype,"adaptor",{get:function(){return this.factory.jax.adaptor},enumerable:!0,configurable:!0}),Object.defineProperty(x.prototype,"metrics",{get:function(){return this.factory.jax.math.metrics},enumerable:!0,configurable:!0}),Object.defineProperty(x.prototype,"fixesPWidth",{get:function(){return!this.node.notParent&&!this.node.isToken},enumerable:!0,configurable:!0}),x.prototype.wrap=function(t,e){void 0===e&&(e=null);var r=this.factory.wrap(t,e||this);return e&&e.childNodes.push(r),this.jax.nodeMap.set(t,r),r},x.prototype.getBBox=function(t){if(void 0===t&&(t=!0),this.bboxComputed)return this.bbox;var e=t?this.bbox:d.BBox.zero();return this.computeBBox(e),this.bboxComputed=t,e},x.prototype.computeBBox=function(t,e){var r,o;void 0===e&&(e=!1),t.empty();try{for(var i=c(this.childNodes),n=i.next();!n.done;n=i.next()){var a=n.value;t.append(a.getBBox())}}catch(t){r={error:t}}finally{try{n&&!n.done&&(o=i.return)&&o.call(i)}finally{if(r)throw r.error}}t.clean(),this.fixesPWidth&&this.setChildPWidths(e)&&this.computeBBox(t,!0)},x.prototype.setChildPWidths=function(t,e,r){var o,i;if(void 0===e&&(e=null),void 0===r&&(r=!0),t)return!1;r&&(this.bbox.pwidth="");var n=!1;try{for(var a=c(this.childNodes),s=a.next();!s.done;s=a.next()){var l=s.value,h=l.getBBox();h.pwidth&&l.setChildPWidths(t,null===e?h.w:e,r)&&(n=!0)}}catch(t){o={error:t}}finally{try{s&&!s.done&&(i=a.return)&&i.call(a)}finally{if(o)throw o.error}}return n},x.prototype.invalidateBBox=function(){this.bboxComputed&&(this.bboxComputed=!1,this.parent&&this.parent.invalidateBBox())},x.prototype.copySkewIC=function(t){var e=this.childNodes[0];e&&e.bbox.sk&&(t.sk=e.bbox.sk);var r=this.childNodes[this.childNodes.length-1];r&&r.bbox.ic&&(t.ic=r.bbox.ic,t.w+=t.ic)},x.prototype.getStyles=function(){var t=this.node.attributes.getExplicit("style");if(t)for(var e=this.styles=new p.Styles(t),r=0,o=x.removeStyles.length;r<o;r++){var i=x.removeStyles[r];e.get(i)&&(this.removedStyles||(this.removedStyles={}),this.removedStyles[i]=e.get(i),e.set(i,""))}},x.prototype.getVariant=function(){if(this.node.isToken){var t=this.node.attributes,e=t.get("mathvariant");if(!t.getExplicit("mathvariant")){var r=t.getList("fontfamily","fontweight","fontstyle");if(this.removedStyles){var o=this.removedStyles;o.fontFamily&&(r.family=o.fontFamily),o.fontWeight&&(r.weight=o.fontWeight),o.fontStyle&&(r.style=o.fontStyle)}r.fontfamily&&(r.family=r.fontfamily),r.fontweight&&(r.weight=r.fontweight),r.fontstyle&&(r.style=r.fontstyle),r.weight&&r.weight.match(/^\d+$/)&&(r.weight=600<parseInt(r.weight)?"bold":"normal"),e=r.family?this.explicitVariant(r.family,r.weight,r.style):(this.node.getProperty("variantForm")&&(e="-tex-variant"),e=(x.BOLDVARIANTS[r.weight]||{})[e]||e,(x.ITALICVARIANTS[r.style]||{})[e]||e)}this.variant=e}},x.prototype.explicitVariant=function(t,e,r){var o=this.styles;return(o=o||(this.styles=new p.Styles)).set("fontFamily",t),e&&o.set("fontWeight",e),r&&o.set("fontStyle",r),"-explicitFont"},x.prototype.getScale=function(){var t=1,e=this.parent,r=e?e.bbox.scale:1,o=this.node.attributes,i=Math.min(o.get("scriptlevel"),2),n=o.get("fontsize"),a=this.node.isToken||this.node.isKind("mstyle")?o.get("mathsize"):o.getInherited("mathsize");if(0!==i){t=Math.pow(o.get("scriptsizemultiplier"),i);var s=this.length2em(o.get("scriptminsize"),.8,1);t<s&&(t=s)}this.removedStyles&&this.removedStyles.fontSize&&!n&&(n=this.removedStyles.fontSize),n&&!a&&(a=n),"1"!==a&&(t*=this.length2em(a,1,1)),this.bbox.scale=t,this.bbox.rscale=t/r},x.prototype.getSpace=function(){var t=this.isTopEmbellished(),e=this.node.hasSpacingAttributes();this.jax.options.mathmlSpacing||e?t&&this.getMathMLSpacing():this.getTeXSpacing(t,e)},x.prototype.getMathMLSpacing=function(){var t=this.node.coreMO(),e=t.attributes,r=(this.jax.nodeMap.get(t.coreParent()),0<e.get("scriptlevel"));this.bbox.L=e.isSet("lspace")?Math.max(0,this.length2em(e.get("lspace"))):y(r,t.lspace),this.bbox.R=e.isSet("rspace")?Math.max(0,this.length2em(e.get("rspace"))):y(r,t.rspace)},x.prototype.getTeXSpacing=function(t,e){if(!e){var r=this.node.texSpacing();r&&(this.bbox.L=this.length2em(r))}if(t||e){var o=this.node.coreMO().attributes;o.isSet("lspace")&&(this.bbox.L=Math.max(0,this.length2em(o.get("lspace")))),o.isSet("rspace")&&(this.bbox.R=Math.max(0,this.length2em(o.get("rspace"))))}},x.prototype.isTopEmbellished=function(){return this.node.isEmbellished&&!(this.node.Parent&&this.node.Parent.isEmbellished)},x.prototype.core=function(){return this.jax.nodeMap.get(this.node.core())},x.prototype.coreMO=function(){return this.jax.nodeMap.get(this.node.coreMO())},x.prototype.getText=function(){var e,t,r="";if(this.node.isToken)try{for(var o=c(this.node.childNodes),i=o.next();!i.done;i=o.next()){var n=i.value;n instanceof l.TextNode&&(r+=n.getText())}}catch(t){e={error:t}}finally{try{i&&!i.done&&(t=o.return)&&t.call(o)}finally{if(e)throw e.error}}return r},x.prototype.canStretch=function(t){if(this.stretch=f.NOSTRETCH,this.node.isEmbellished){var e=this.core();e&&e.node!==this.node&&e.canStretch(t)&&(this.stretch=e.stretch)}return 0!==this.stretch.dir},x.prototype.getAlignShift=function(){var t,e=(t=this.node.attributes).getList.apply(t,a(l.indentAttributes)),r=e.indentalign,o=e.indentshift,i=e.indentalignfirst,n=e.indentshiftfirst;return"indentalign"!==i&&(r=i),"auto"===r&&(r=this.jax.options.displayAlign),"indentshift"!==n&&(o=n),"auto"===o&&(o=this.jax.options.displayIndent,"right"!==r||o.match(/^\s*0[a-z]*\s*$/)||(o=("-"+o.trim()).replace(/^--/,""))),[r,this.length2em(o,this.metrics.containerWidth)]},x.prototype.getAlignX=function(t,e,r){return"right"===r?t-(e.w+e.R)*e.rscale:"left"===r?e.L*e.rscale:(t-e.w*e.rscale)/2},x.prototype.getAlignY=function(t,e,r,o,i){return"top"===i?t-r:"bottom"===i?o-e:"middle"===i?(t-r-(e-o))/2:0},x.prototype.getWrapWidth=function(t){return this.childNodes[t].getBBox().w},x.prototype.getChildAlign=function(t){return"left"},x.prototype.percent=function(t){return u.percent(t)},x.prototype.em=function(t){return u.em(t)},x.prototype.px=function(t,e){return void 0===e&&(e=-u.BIGDIMEN),u.px(t,e,this.metrics.em)},x.prototype.length2em=function(t,e,r){return void 0===e&&(e=1),void 0===r&&(r=null),null===r&&(r=this.bbox.scale),u.length2em(t,e,r,this.jax.pxPerEm)},x.prototype.unicodeChars=function(t){return h.unicodeChars(t)},x.prototype.remapChars=function(t){return t},x.prototype.mmlText=function(t){return this.node.factory.create("text").setText(t)},x.prototype.mmlNode=function(t,e,r){return void 0===e&&(e={}),void 0===r&&(r=[]),this.node.factory.create(t,e,r)},x.prototype.createMo=function(t){var e=this.node.factory,r=e.create("text").setText(t),o=e.create("mo",{stretchy:!0},[r]);o.inheritAttributesFrom(this.node);var i=this.wrap(o);return i.parent=this,i},x.prototype.getVariantChar=function(t,e){var r=this.font.getChar(t,e)||[0,0,0,{unknown:!0}];return 3===r.length&&(r[3]={}),r},x.kind="unknown",x.styles={},x.removeStyles=["fontSize","fontFamily","fontWeight","fontStyle","fontVariant","font"],x.skipAttributes={fontfamily:!0,fontsize:!0,fontweight:!0,fontstyle:!0,color:!0,background:!0,class:!0,href:!0,style:!0,xmlns:!0},x.BOLDVARIANTS={bold:{normal:"bold",italic:"bold-italic",fraktur:"bold-fraktur",script:"bold-script","sans-serif":"bold-sans-serif","sans-serif-italic":"sans-serif-bold-italic"},normal:{bold:"normal","bold-italic":"italic","bold-fraktur":"fraktur","bold-script":"script","bold-sans-serif":"sans-serif","sans-serif-bold-italic":"sans-serif-italic"}},x.ITALICVARIANTS={italic:{normal:"italic",bold:"bold-italic","sans-serif":"sans-serif-italic","bold-sans-serif":"sans-serif-bold-italic"},normal:{italic:"normal","bold-italic":"bold","sans-serif-italic":"sans-serif","sans-serif-bold-italic":"bold-sans-serif"}},x);function x(t,r,e){void 0===e&&(e=null);var o=v.call(this,t,r)||this;return o.parent=null,o.removedStyles=null,o.styles=null,o.variant="",o.bboxComputed=!1,o.stretch=f.NOSTRETCH,o.font=null,o.parent=e,o.font=t.jax.font,o.bbox=d.BBox.zero(),o.getStyles(),o.getVariant(),o.getScale(),o.getSpace(),o.childNodes=r.childNodes.map(function(t){var e=o.wrap(t);return e.bbox.pwidth&&(r.notParent||r.isKind("math"))&&(o.bbox.pwidth=d.BBox.fullWidth),e}),o}e.CommonWrapper=b},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var o=r(1);e.BBox=o.BBox,e.BBoxStyleAdjust=o.BBoxStyleAdjust},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),l=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a};Object.defineProperty(e,"__esModule",{value:!0});var h,n=r(0),a=r(26),s=r(77),c=(h=a.CommonMathMixin(n.CHTMLWrapper),i(u,h),u.prototype.toCHTML=function(t){h.prototype.toCHTML.call(this,t);var e=this.chtml,r=this.adaptor,o="block"===this.node.attributes.get("display");if(o)r.setAttribute(e,"display","true"),r.setAttribute(t,"display","true");else{var i=r.getStyle(e,"margin-right");i&&(r.setStyle(e,"margin-right",""),r.setStyle(t,"margin-right",i),r.setStyle(t,"width","0"))}r.addClass(e,"MJX-TEX");var n=l(this.getAlignShift(),2),a=n[0],s=n[1];"center"!==a&&r.setAttribute(t,"justify",a),o&&s&&!r.hasAttribute(e,"width")&&this.setIndent(e,a,s)},u.prototype.setChildPWidths=function(t,e,r){return void 0===e&&(e=null),void 0===r&&(r=!0),!!this.parent&&h.prototype.setChildPWidths.call(this,t,e)},u.kind=s.MmlMath.prototype.kind,u.styles={"mjx-math":{"line-height":0,"text-align":"left","text-indent":0,"font-style":"normal","font-weight":"normal","font-size":"100%","font-size-adjust":"none","letter-spacing":"normal","word-wrap":"normal","word-spacing":"normal","white-space":"nowrap",direction:"ltr",padding:"1px 0"},'mjx-container[jax="CHTML"][display="true"]':{display:"block","text-align":"center",margin:"1em 0"},'mjx-container[jax="CHTML"][display="true"] mjx-math':{padding:0},'mjx-container[jax="CHTML"][justify="left"]':{"text-align":"left"},'mjx-container[jax="CHTML"][justify="right"]':{"text-align":"right"}},u);function u(){return null!==h&&h.apply(this,arguments)||this}e.CHTMLmath=c},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMathMixin=function(t){return i(e,r=t),e.prototype.getWrapWidth=function(t){return this.parent?this.getBBox().w:this.metrics.containerWidth/this.jax.pxPerEm},e;function e(){return null!==r&&r.apply(this,arguments)||this}var r}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0});var n,a=r(0),s=r(28),l=r(78),h=(n=s.CommonMiMixin(a.CHTMLWrapper),i(c,n),c.prototype.toCHTML=function(t){n.prototype.toCHTML.call(this,t),this.noIC&&this.adaptor.setAttribute(this.chtml,"noIC","true")},c.kind=l.MmlMi.prototype.kind,c);function c(){return null!==n&&n.apply(this,arguments)||this}e.CHTMLmi=h},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMiMixin=function(t){return i(e,r=t),e.prototype.computeBBox=function(t,e){void 0===e&&(e=!1),r.prototype.computeBBox.call(this,t),this.copySkewIC(t),this.noIC&&(t.w-=t.ic)},e;function e(){var t=null!==r&&r.apply(this,arguments)||this;return t.noIC=!1,t}var r}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),u=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0});var p,n=r(0),f=r(30),a=r(79),d=r(24),s=(p=f.CommonMoMixin(n.CHTMLWrapper),i(l,p),l.prototype.toCHTML=function(t){var e,r,o=this.node.attributes,i=o.get("symmetric")&&2!==this.stretch.dir,n=0!==this.stretch.dir;n&&null===this.size&&this.getStretchedVariant([]);var a=this.standardCHTMLnode(t);if(this.noIC&&this.adaptor.setAttribute(a,"noIC","true"),n&&this.size<0)this.stretchHTML(a,i);else{if(i||o.get("largeop")){var s=d.BBox.empty();p.prototype.computeBBox.call(this,s);var l=this.em((s.d-s.h)/2+this.font.params.axis_height);"0"!==l&&this.adaptor.setStyle(a,"verticalAlign",l)}try{for(var h=u(this.childNodes),c=h.next();!c.done;c=h.next())c.value.toCHTML(a)}catch(t){e={error:t}}finally{try{c&&!c.done&&(r=h.return)&&r.call(h)}finally{if(e)throw e.error}}}},l.prototype.stretchHTML=function(t,e){var r=this.getText().charCodeAt(0),o=this.stretch;o.used=!0;var i=o.stretch,n=[];i[0]&&n.push(this.html("mjx-beg",{},[this.html("mjx-c")])),n.push(this.html("mjx-ext",{},[this.html("mjx-c")])),4===i.length&&n.push(this.html("mjx-mid",{},[this.html("mjx-c")]),this.html("mjx-ext",{},[this.html("mjx-c")])),i[2]&&n.push(this.html("mjx-end",{},[this.html("mjx-c")]));var a={},s=this.bbox,l=s.h,h=s.d,c=s.w;1===o.dir?(n.push(this.html("mjx-mark")),a.height=this.em(l+h),a.verticalAlign=this.em(-h)):a.width=this.em(c);var u=f.DirectionVH[o.dir],p={class:this.char(o.c||r),style:a},d=this.html("mjx-stretchy-"+u,p,n);this.adaptor.append(t,d)},l.kind=a.MmlMo.prototype.kind,l.styles={"mjx-stretchy-h":{display:"inline-table",width:"100%"},"mjx-stretchy-h > *":{display:"table-cell",width:0},"mjx-stretchy-h > * > mjx-c":{display:"inline-block"},"mjx-stretchy-h > * > mjx-c::before":{padding:".001em 0",width:"initial"},"mjx-stretchy-h > mjx-ext":{overflow:"hidden",width:"100%"},"mjx-stretchy-h > mjx-ext > mjx-c::before":{transform:"scalex(500)"},"mjx-stretchy-h > mjx-ext > mjx-c":{width:0},"mjx-stretchy-h > mjx-beg > mjx-c":{"margin-right":"-.1em"},"mjx-stretchy-h > mjx-end > mjx-c":{"margin-left":"-.1em"},"mjx-stretchy-v":{display:"inline-block"},"mjx-stretchy-v > *":{display:"block"},"mjx-stretchy-v > mjx-beg":{height:0},"mjx-stretchy-v > mjx-end > mjx-c":{display:"block"},"mjx-stretchy-v > * > mjx-c":{transform:"scale(1)","transform-origin":"left center",overflow:"hidden"},"mjx-stretchy-v > mjx-ext":{display:"block",height:"100%","box-sizing":"border-box",border:"0px solid transparent",overflow:"hidden"},"mjx-stretchy-v > mjx-ext > mjx-c::before":{width:"initial"},"mjx-stretchy-v > mjx-ext > mjx-c":{transform:"scaleY(500) translateY(.1em)",overflow:"visible"},"mjx-mark":{display:"inline-block",height:"0px"}},l);function l(){return null!==p&&p.apply(this,arguments)||this}e.CHTMLmo=s},function(t,e,r){"use strict";var o,i,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),m=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a},a=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(m(arguments[e]));return t},d=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0});var s=r(5);e.DirectionVH=((i={})[1]="v",i[2]="h",i),e.CommonMoMixin=function(t){return n(e,i=t),e.prototype.computeBBox=function(t,e){void 0===e&&(e=!1);var r=0!==this.stretch.dir;if(r&&null===this.size&&this.getStretchedVariant([0]),!(r&&this.size<0)&&(i.prototype.computeBBox.call(this,t),this.copySkewIC(t),this.noIC&&(t.w-=t.ic),this.node.attributes.get("symmetric")&&2!==this.stretch.dir)){var o=(t.h+t.d)/2+this.font.params.axis_height-t.h;t.h+=o,t.d-=o}},e.prototype.getVariant=function(){this.node.attributes.get("largeop")?this.variant=this.node.attributes.get("displaystyle")?"-largeop":"-smallop":i.prototype.getVariant.call(this)},e.prototype.canStretch=function(t){if(0!==this.stretch.dir)return this.stretch.dir===t;if(!this.node.attributes.get("stretchy"))return!1;var e=this.getText();if(1!==e.length)return!1;var r=this.font.getDelimiter(e.charCodeAt(0));return this.stretch=r&&r.dir===t?r:s.NOSTRETCH,0!==this.stretch.dir},e.prototype.getStretchedVariant=function(t,e){var r,o;if(void 0===e&&(e=!1),0!==this.stretch.dir){var i=this.getWH(t),n=this.getSize("minsize",0),a=this.getSize("maxsize",1/0);i=Math.max(n,Math.min(a,i));var s=n||e?i:Math.max(i*this.font.params.delimiterfactor/1e3,i-this.font.params.delimitershortfall),l=this.stretch,h=l.c||this.getText().charCodeAt(0),c=0;if(l.sizes)try{for(var u=d(l.sizes),p=u.next();!p.done;p=u.next()){if(s<=p.value)return this.variant=this.font.getSizeVariant(h,c),void(this.size=c);c++}}catch(t){r={error:t}}finally{try{p&&!p.done&&(o=u.return)&&o.call(u)}finally{if(r)throw r.error}}l.stretch?(this.size=-1,this.invalidateBBox(),this.getStretchBBox(t,i,l)):(this.variant=this.font.getSizeVariant(h,c-1),this.size=c-1)}},e.prototype.getSize=function(t,e){var r=this.node.attributes;return r.isSet(t)&&(e=this.length2em(r.get(t),1,1)),e},e.prototype.getWH=function(t){if(0===t.length)return 0;if(1===t.length)return t[0];var e=m(t,2),r=e[0],o=e[1],i=this.font.params.axis_height;return this.node.attributes.get("symmetric")?2*Math.max(r-i,o+i):r+o},e.prototype.getStretchBBox=function(t,e,r){var o;r.hasOwnProperty("min")&&r.min>e&&(e=r.min);var i=m(r.HDW,3),n=i[0],a=i[1],s=i[2];1===this.stretch.dir?(n=(o=m(this.getBaseline(t,e,r),2))[0],a=o[1]):s=e,this.bbox.h=n,this.bbox.d=a,this.bbox.w=s},e.prototype.getBaseline=function(t,e,r){var o=2===t.length&&t[0]+t[1]===e,i=this.node.attributes.get("symmetric"),n=m(o?t:[e,0],2),a=n[0],s=n[1],l=m([a+s,0],2),h=l[0],c=l[1];if(i){var u=this.font.params.axis_height;o&&(h=2*Math.max(a-u,s+u)),c=h/2-u}else if(o)c=s;else{var p=m(r.HDW||[.75,.25],2),d=p[0],f=p[1];c=f*(h/(d+f))}return[h-c,c]},e.prototype.remapChars=function(t){if(1==t.length){var e=this.node.parent,r=this.isAccent&&(e===this.node.coreParent()||e.isEmbellished)?"accent":"mo",o=this.font.getRemappedChar(r,t[0]);o&&(t=this.unicodeChars(o))}return t},e;function e(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var r=i.apply(this,a(t))||this;return r.noIC=!1,r.size=null,r.isAccent=r.node.isAccent,r}var i}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0});var n,a=r(0),s=r(32),l=r(80),h=(n=s.CommonMnMixin(a.CHTMLWrapper),i(c,n),c.kind=l.MmlMn.prototype.kind,c);function c(){return null!==n&&n.apply(this,arguments)||this}e.CHTMLmn=h},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMnMixin=function(t){return i(e,r=t),e.prototype.remapChars=function(t){if(t.length){var e=this.font.getRemappedChar("mn",t[0]);if(e){var r=this.unicodeChars(e);1===r.length?t[0]=r[0]:t=r.concat(t.slice(1))}}return t},e;function e(){return null!==r&&r.apply(this,arguments)||this}var r}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0});var n,a=r(0),s=r(34),l=r(81),h=(n=s.CommonMsMixin(a.CHTMLWrapper),i(c,n),c.kind=l.MmlMs.prototype.kind,c);function c(){return null!==n&&n.apply(this,arguments)||this}e.CHTMLms=h},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a},a=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(n(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMsMixin=function(t){return i(e,n=t),e.prototype.createText=function(t){var e=this.wrap(this.mmlText(t));return e.parent=this,e},e;function e(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var r=n.apply(this,a(t))||this,o=r.node.attributes,i=o.getList("lquote","rquote");return"monospace"!==r.variant&&(o.isSet("lquote")||'"'!==i.lquote||(i.lquote="\u201c"),o.isSet("rquote")||'"'!==i.rquote||(i.rquote="\u201d")),r.childNodes.unshift(r.createText(i.lquote)),r.childNodes.push(r.createText(i.rquote)),r}var n}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0});var n,a=r(0),s=r(36),l=r(82),h=(n=s.CommonMtextMixin(a.CHTMLWrapper),i(c,n),c.kind=l.MmlMtext.prototype.kind,c);function c(){return null!==n&&n.apply(this,arguments)||this}e.CHTMLmtext=h},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a},a=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(n(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMtextMixin=function(t){var e,o;return i(r,o=t),r.prototype.getVariant=function(){var t=this.jax.options;if(t.mtextInheritFont||t.merrorInheritFont&&this.node.Parent.isKind("merror")){var e=this.node.attributes.get("mathvariant"),r=this.constructor.INHERITFONTS[e];if(r)return void(this.variant=this.explicitVariant.apply(this,a(r)))}o.prototype.getVariant.call(this)},(e=r).INHERITFONTS={normal:["","",""],bold:["","bold",""],italic:["","","italic"],"bold-italic":["","bold","italic"],script:["cursive","",""],"bold-script":["cursive","bold",""],"sans-serif":["sans-serif","",""],"bold-sans-serif":["sans-serif","bold",""],"sans-serif-italic":["sans-serif","","italic"],"bold-sans-serif-italic":["sans-serif","bold","italic"],monospace:["monospace","",""]},e;function r(){return null!==o&&o.apply(this,arguments)||this}}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0});var n,a=r(0),s=r(38),l=r(83),h=(n=s.CommonMspaceMixin(a.CHTMLWrapper),i(c,n),c.prototype.toCHTML=function(t){var e=this.standardCHTMLnode(t),r=this.getBBox(),o=r.w,i=r.h,n=r.d;o<0&&(this.adaptor.setStyle(e,"marginRight",this.em(o)),o=0),o&&this.adaptor.setStyle(e,"width",this.em(o)),(i=Math.max(0,i+n))&&this.adaptor.setStyle(e,"height",this.em(Math.max(0,i))),n&&this.adaptor.setStyle(e,"verticalAlign",this.em(-n))},c.kind=l.MmlMspace.prototype.kind,c);function c(){return null!==n&&n.apply(this,arguments)||this}e.CHTMLmspace=h},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMspaceMixin=function(t){return i(e,r=t),e.prototype.computeBBox=function(t,e){void 0===e&&(e=!1);var r=this.node.attributes;t.w=this.length2em(r.get("width"),0),t.h=this.length2em(r.get("height"),0),t.d=this.length2em(r.get("depth"),0)},e.prototype.handleVariant=function(){},e;function e(){return null!==r&&r.apply(this,arguments)||this}var r}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),v=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a},b=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0});var n,a=r(0),s=r(40),l=r(84),h=(n=s.CommonMpaddedMixin(a.CHTMLWrapper),i(c,n),c.prototype.toCHTML=function(t){var e,r,o=this.standardCHTMLnode(t),i=[],n={},a=v(this.getDimens(),9),s=(a[0],a[1],a[2]),l=a[3],h=a[4],c=a[5],u=a[6],p=a[7],d=a[8];if(c&&(n.width=this.em(s+c)),(l||h)&&(n.margin=this.em(l)+" 0 "+this.em(h)),u+d||p){n.position="relative";var f=this.html("mjx-rbox",{style:{left:this.em(u+d),top:this.em(-p)}});u+d&&this.childNodes[0].getBBox().pwidth&&(this.adaptor.setAttribute(f,"width","full"),this.adaptor.setStyle(f,"left",this.em(u))),i.push(f)}o=this.adaptor.append(o,this.html("mjx-block",{style:n},i));try{for(var m=b(this.childNodes),y=m.next();!y.done;y=m.next())y.value.toCHTML(i[0]||o)}catch(t){e={error:t}}finally{try{y&&!y.done&&(r=m.return)&&r.call(m)}finally{if(e)throw e.error}}},c.kind=l.MmlMpadded.prototype.kind,c.styles={"mjx-mpadded":{display:"inline-block"},"mjx-rbox":{display:"inline-block",position:"relative"}},c);function c(){return null!==n&&n.apply(this,arguments)||this}e.CHTMLmpadded=h},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),h=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMpaddedMixin=function(t){return i(e,r=t),e.prototype.getDimens=function(){var t=this.node.attributes.getList("width","height","depth","lspace","voffset"),e=this.childNodes[0].getBBox(),r=e.w,o=e.h,i=e.d,n=r,a=o,s=i,l=0,h=0,c=0;""!==t.width&&(r=this.dimen(t.width,e,"w",0)),""!==t.height&&(o=this.dimen(t.height,e,"h",0)),""!==t.depth&&(i=this.dimen(t.depth,e,"d",0)),""!==t.voffset&&(h=this.dimen(t.voffset,e)),""!==t.lspace&&(l=this.dimen(t.lspace,e));var u=this.node.attributes.get("data-align");return u&&(c=this.getAlignX(r,e,u)),[a,s,n,o-a,i-s,r-n,l,h,c]},e.prototype.dimen=function(t,e,r,o){void 0===r&&(r=""),void 0===o&&(o=null);var i=(t=String(t)).match(/width|height|depth/),n=i?e[i[0].charAt(0)]:r?e[r]:0,a=this.length2em(t,n)||0;return t.match(/^[-+]/)&&r&&(a+=n),null!=o&&(a=Math.max(o,a)),a},e.prototype.computeBBox=function(t,e){void 0===e&&(e=!1);var r=h(this.getDimens(),8),o=r[0],i=r[1],n=r[2],a=r[3],s=r[4],l=r[5];r[6],r[7],t.w=n+l,t.h=o+a,t.d=i+s,this.setChildPWidths(e,t.w)},e.prototype.getWrapWidth=function(t){return this.getBBox().w},e.prototype.getChildAlign=function(t){return this.node.attributes.get("data-align")||"left"},e;function e(){return null!==r&&r.apply(this,arguments)||this}var r}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),v=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},m=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a};Object.defineProperty(e,"__esModule",{value:!0});var n=r(0),a=r(42),b=r(43),s=r(85),l=r(2);function y(t,e){return Math.atan2(t,e).toFixed(3).replace(/\.?0+$/,"")}var h,c=y(b.ARROWDX,b.ARROWY),u=(h=a.CommonMencloseMixin(n.CHTMLWrapper),i(p,h),p.prototype.toCHTML=function(t){var e,r,o,i,n=this.adaptor,a=this.standardCHTMLnode(t),s=n.append(a,this.html("mjx-box"));this.renderChild?this.renderChild(this,s):this.childNodes[0].toCHTML(s);try{for(var l=v(Object.keys(this.notations)),h=l.next();!h.done;h=l.next()){var c=h.value,u=this.notations[c];u.renderChild||u.renderer(this,s)}}catch(t){e={error:t}}finally{try{h&&!h.done&&(r=l.return)&&r.call(l)}finally{if(e)throw e.error}}var p=this.getPadding();try{for(var d=v(b.sideNames),f=d.next();!f.done;f=d.next()){var m=f.value,y=b.sideIndex[m];0<p[y]&&n.setStyle(s,"padding-"+m,this.em(p[y]))}}catch(t){o={error:t}}finally{try{f&&!f.done&&(i=d.return)&&i.call(d)}finally{if(o)throw o.error}}},p.prototype.arrow=function(t,e,r){void 0===r&&(r=!1);var o=this.getBBox().w,i={width:this.em(t)};o!==t&&(i.left=this.em((o-t)/2)),e&&(i.transform="rotate("+this.fixed(e)+"rad)");var n=this.html("mjx-arrow",{style:i},[this.html("mjx-aline"),this.html("mjx-rthead"),this.html("mjx-rbhead")]);return r&&(this.adaptor.append(n,this.html("mjx-lthead")),this.adaptor.append(n,this.html("mjx-lbhead")),this.adaptor.setAttribute(n,"double","true")),this.adjustArrow(n,r),n},p.prototype.adjustArrow=function(t,e){var r=this,o=this.thickness,i=this.arrowhead;if(i.x!==b.ARROWX||i.y!==b.ARROWY||i.dx!==b.ARROWDX||o!==b.THICKNESS){var n=m([o*i.x,o*i.y,o*i.dx].map(function(t){return r.em(t)}),3),a=n[0],s=n[1],l=(n[2],y(i.dx,i.y)),h=m(this.adaptor.childNodes(t),5),c=h[0],u=h[1],p=h[2],d=h[3],f=h[4];this.adjustHead(u,[s,"0","1px",a],l),this.adjustHead(p,["1px","0",s,a],"-"+l),this.adjustHead(d,[s,a,"1px","0"],"-"+l),this.adjustHead(f,["1px",a,s,"0"],l),this.adjustLine(c,o,i.x,e)}},p.prototype.adjustHead=function(t,e,r){t&&(this.adaptor.setStyle(t,"border-width",e.join(" ")),this.adaptor.setStyle(t,"transform","skewX("+r+"rad)"))},p.prototype.adjustLine=function(t,e,r,o){this.adaptor.setStyle(t,"borderTop",this.em(e)+" solid"),this.adaptor.setStyle(t,"top",this.em(-e/2)),this.adaptor.setStyle(t,"right",this.em(e*(r-1))),o&&this.adaptor.setStyle(t,"left",this.em(e*(r-1)))},p.prototype.adjustBorder=function(t){return this.thickness!==b.THICKNESS&&this.adaptor.setStyle(t,"borderWidth",this.em(this.thickness)),t},p.prototype.adjustThickness=function(t){return this.thickness!==b.THICKNESS&&this.adaptor.setStyle(t,"strokeWidth",this.fixed(this.thickness)),t},p.prototype.fixed=function(t,e){return void 0===e&&(e=3),Math.abs(t)<6e-4?"0":t.toFixed(e).replace(/\.?0+$/,"")},p.prototype.em=function(t){return h.prototype.em.call(this,t)},p.kind=s.MmlMenclose.prototype.kind,p.styles={"mjx-menclose":{position:"relative"},"mjx-menclose > mjx-dstrike":{display:"inline-block",left:0,top:0,position:"absolute","border-top":b.SOLID,"transform-origin":"top left"},"mjx-menclose > mjx-ustrike":{display:"inline-block",left:0,bottom:0,position:"absolute","border-top":b.SOLID,"transform-origin":"bottom left"},"mjx-menclose > mjx-hstrike":{"border-top":b.SOLID,position:"absolute",left:0,right:0,bottom:"50%",transform:"translateY("+l.em(b.THICKNESS/2)+")"},"mjx-menclose > mjx-vstrike":{"border-left":b.SOLID,position:"absolute",top:0,bottom:0,right:"50%",transform:"translateX("+l.em(b.THICKNESS/2)+")"},"mjx-menclose > mjx-rbox":{position:"absolute",top:0,bottom:0,right:0,left:0,border:b.SOLID,"border-radius":l.em(b.THICKNESS+b.PADDING)},"mjx-menclose > mjx-cbox":{position:"absolute",top:0,bottom:0,right:0,left:0,border:b.SOLID,"border-radius":"50%"},"mjx-menclose > mjx-arrow":{position:"absolute",left:0,bottom:"50%",height:0,width:0},"mjx-menclose > mjx-arrow > *":{display:"block",position:"absolute","transform-origin":"bottom","border-left":l.em(b.THICKNESS*b.ARROWX)+" solid","border-right":0,"box-sizing":"border-box"},"mjx-menclose > mjx-arrow > mjx-aline":{left:0,top:l.em(-b.THICKNESS/2),right:l.em(b.THICKNESS*(b.ARROWX-1)),height:0,"border-top":l.em(b.THICKNESS)+" solid","border-left":0},"mjx-menclose > mjx-arrow[double] > mjx-aline":{left:l.em(b.THICKNESS*(b.ARROWX-1)),height:0},"mjx-menclose > mjx-arrow > mjx-rthead":{transform:"skewX("+c+"rad)",right:0,bottom:"-1px","border-bottom":"1px solid transparent","border-top":l.em(b.THICKNESS*b.ARROWY)+" solid transparent"},"mjx-menclose > mjx-arrow > mjx-rbhead":{transform:"skewX(-"+c+"rad)","transform-origin":"top",right:0,top:"-1px","border-top":"1px solid transparent","border-bottom":l.em(b.THICKNESS*b.ARROWY)+" solid transparent"},"mjx-menclose > mjx-arrow > mjx-lthead":{transform:"skewX(-"+c+"rad)",left:0,bottom:"-1px","border-left":0,"border-right":l.em(b.THICKNESS*b.ARROWX)+" solid","border-bottom":"1px solid transparent","border-top":l.em(b.THICKNESS*b.ARROWY)+" solid transparent"},"mjx-menclose > mjx-arrow > mjx-lbhead":{transform:"skewX("+c+"rad)","transform-origin":"top",left:0,top:"-1px","border-left":0,"border-right":l.em(b.THICKNESS*b.ARROWX)+" solid","border-top":"1px solid transparent","border-bottom":l.em(b.THICKNESS*b.ARROWY)+" solid transparent"},"mjx-menclose > dbox":{position:"absolute",top:0,bottom:0,left:l.em(-1.5*b.PADDING),width:l.em(3*b.PADDING),border:l.em(b.THICKNESS)+" solid","border-radius":"50%","clip-path":"inset(0 0 0 "+l.em(1.5*b.PADDING)+")","box-sizing":"border-box"}},p.notations=new Map([b.Border("top"),b.Border("right"),b.Border("bottom"),b.Border("left"),b.Border2("actuarial","top","right"),b.Border2("madruwb","bottom","right"),b.DiagonalStrike("up",1),b.DiagonalStrike("down",-1),["horizontalstrike",{renderer:b.RenderElement("hstrike","Y"),bbox:function(t){return[0,t.padding,0,t.padding]}}],["verticalstrike",{renderer:b.RenderElement("vstrike","X"),bbox:function(t){return[t.padding,0,t.padding,0]}}],["box",{renderer:function(t,e){t.adaptor.setStyle(e,"border",t.em(t.thickness)+" solid")},bbox:b.fullBBox,border:b.fullBorder,remove:"left right top bottom"}],["roundedbox",{renderer:b.RenderElement("rbox"),bbox:b.fullBBox}],["circle",{renderer:b.RenderElement("cbox"),bbox:b.fullBBox}],["phasorangle",{renderer:function(t,e){var r=t.getBBox(),o=(r.w,r.h),i=r.d,n=m(t.getArgMod(1.75*t.padding,o+i),2),a=n[0],s=n[1],l=t.thickness*Math.sin(a)*.9;t.adaptor.setStyle(e,"border-bottom",t.em(t.thickness)+" solid");var h=t.adjustBorder(t.html("mjx-ustrike",{style:{width:t.em(s),transform:"translateX("+t.em(l)+") rotate("+t.fixed(-a)+"rad)"}}));t.adaptor.append(t.chtml,h)},bbox:function(t){var e=t.padding/2,r=t.thickness;return[2*e,e,e+r,3*e+r]},border:function(t){return[0,0,t.thickness,0]},remove:"bottom"}],b.Arrow("up"),b.Arrow("down"),b.Arrow("left"),b.Arrow("right"),b.Arrow("updown"),b.Arrow("leftright"),b.DiagonalArrow("updiagonal"),b.DiagonalArrow("northeast"),b.DiagonalArrow("southeast"),b.DiagonalArrow("northwest"),b.DiagonalArrow("southwest"),b.DiagonalArrow("northeastsouthwest"),b.DiagonalArrow("northwestsoutheast"),["longdiv",{renderer:function(t,e){var r=t.adaptor;r.setStyle(e,"border-top",t.em(t.thickness)+" solid");var o=r.append(t.chtml,t.html("dbox")),i=t.thickness,n=t.padding;i!==b.THICKNESS&&r.setStyle(o,"border-width",t.em(i)),n!==b.PADDING&&(r.setStyle(o,"left",t.em(-1.5*n)),r.setStyle(o,"width",t.em(3*n)),r.setStyle(o,"clip-path","inset(0 0 0 "+t.em(1.5*n)+")"))},bbox:function(t){var e=t.padding,r=t.thickness;return[e+r,e,e,2*e+r/2]}}],["radical",{renderer:function(e,t){e.msqrt.toCHTML(t);var r=e.sqrtTRBL();e.adaptor.setStyle(e.msqrt.chtml,"margin",r.map(function(t){return e.em(-t)}).join(" "))},init:function(t){t.msqrt=t.createMsqrt(t.childNodes[0])},bbox:function(t){return t.sqrtTRBL()},renderChild:!0}]]),p);function p(){return null!==h&&h.apply(this,arguments)||this}e.CHTMLmenclose=u},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),p=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a},n=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(p(arguments[e]));return t},u=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0});var l=r(6),h=r(4);e.CommonMencloseMixin=function(t){return i(e,o=t),e.prototype.getParameters=function(){var t=this.node.attributes,e=t.get("data-padding");void 0!==e&&(this.padding=this.length2em(e,l.PADDING));var r=t.get("data-thickness");void 0!==r&&(this.thickness=this.length2em(r,l.THICKNESS));var o=t.get("data-arrowhead");if(void 0!==o){var i=p(h.split(o),3),n=i[0],a=i[1],s=i[2];this.arrowhead={x:n?parseFloat(n):l.ARROWX,y:a?parseFloat(a):l.ARROWY,dx:s?parseFloat(s):l.ARROWDX}}},e.prototype.getNotations=function(){var e,t,r=this.constructor.notations;try{for(var o=u(h.split(this.node.attributes.get("notation"))),i=o.next();!i.done;i=o.next()){var n=i.value,a=r.get(n);a&&(this.notations[n]=a).renderChild&&(this.renderChild=a.renderer)}}catch(t){e={error:t}}finally{try{i&&!i.done&&(t=o.return)&&t.call(o)}finally{if(e)throw e.error}}},e.prototype.removeRedundantNotations=function(){var e,t,r,o;try{for(var i=u(Object.keys(this.notations)),n=i.next();!n.done;n=i.next()){var a=n.value;if(this.notations[a]){var s=this.notations[a].remove||"";try{for(var l=(r=void 0,u(s.split(/ /))),h=l.next();!h.done;h=l.next()){var c=h.value;delete this.notations[c]}}catch(t){r={error:t}}finally{try{h&&!h.done&&(o=l.return)&&o.call(l)}finally{if(r)throw r.error}}}}}catch(t){e={error:t}}finally{try{n&&!n.done&&(t=i.return)&&t.call(i)}finally{if(e)throw e.error}}},e.prototype.initializeNotations=function(){var e,t;try{for(var r=u(Object.keys(this.notations)),o=r.next();!o.done;o=r.next()){var i=o.value,n=this.notations[i].init;n&&n(this)}}catch(t){e={error:t}}finally{try{o&&!o.done&&(t=r.return)&&t.call(r)}finally{if(e)throw e.error}}},e.prototype.computeBBox=function(t,e){void 0===e&&(e=!1);var r=p(this.getBBoxExtenders(),4),o=r[0],i=r[1],n=r[2],a=r[3],s=this.childNodes[0].getBBox();t.combine(s,a,0),t.h+=o,t.d+=n,t.w+=i,this.setChildPWidths(e)},e.prototype.getBBoxExtenders=function(){var e,t,r=[0,0,0,0];try{for(var o=u(Object.keys(this.notations)),i=o.next();!i.done;i=o.next()){var n=i.value;this.maximizeEntries(r,this.notations[n].bbox(this))}}catch(t){e={error:t}}finally{try{i&&!i.done&&(t=o.return)&&t.call(o)}finally{if(e)throw e.error}}return r},e.prototype.getPadding=function(){var e,t,r=[0,0,0,0],o=[0,0,0,0];try{for(var i=u(Object.keys(this.notations)),n=i.next();!n.done;n=i.next()){var a=n.value;this.maximizeEntries(r,this.notations[a].bbox(this));var s=this.notations[a].border;s&&this.maximizeEntries(o,s(this))}}catch(t){e={error:t}}finally{try{n&&!n.done&&(t=i.return)&&t.call(i)}finally{if(e)throw e.error}}return[0,1,2,3].map(function(t){return r[t]-o[t]})},e.prototype.maximizeEntries=function(t,e){for(var r=0;r<t.length;r++)t[r]<e[r]&&(t[r]=e[r])},e.prototype.getArgMod=function(t,e){return[Math.atan2(e,t),Math.sqrt(t*t+e*e)]},e.prototype.arrow=function(t,e,r){return void 0===r&&(r=!1),null},e.prototype.arrowData=function(){var t=p([this.padding,this.thickness],2),e=t[0],r=t[1]*(this.arrowhead.x+Math.max(1,this.arrowhead.dx)),o=this.childNodes[0].getBBox(),i=o.h,n=o.d,a=o.w,s=i+n,l=Math.sqrt(s*s+a*a),h=Math.max(e,r*a/l),c=Math.max(e,r*s/l),u=p(this.getArgMod(a+2*h,s+2*c),2);return{a:u[0],W:u[1],x:h,y:c}},e.prototype.createMsqrt=function(t){var e=this.node.factory.create("msqrt");e.inheritAttributesFrom(this.node),e.childNodes[0]=t.node;var r=this.wrap(e);return r.parent=this,r},e.prototype.sqrtTRBL=function(){var t=this.msqrt.getBBox(),e=this.msqrt.childNodes[0].getBBox();return[t.h-e.h,0,t.d-e.d,t.w-e.w]},e;function e(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var r=o.apply(this,n(t))||this;return r.notations={},r.renderChild=null,r.msqrt=null,r.padding=l.PADDING,r.thickness=l.THICKNESS,r.arrowhead={x:l.ARROWX,y:l.ARROWY,dx:l.ARROWDX},r.getParameters(),r.getNotations(),r.removeRedundantNotations(),r.initializeNotations(),r}var o}},function(t,r,e){"use strict";var d=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a};Object.defineProperty(r,"__esModule",{value:!0});var a=e(6);!function(t){for(var e in t)r.hasOwnProperty(e)||(r[e]=t[e])}(e(6)),r.RenderElement=function(i,n){return void 0===n&&(n=""),function(t,e){var r=t.adjustBorder(t.html("mjx-"+i));if(n&&t.thickness!==a.THICKNESS){var o="translate"+n+"("+t.em(t.thickness/2)+")";t.adaptor.setStyle(r,"transform",o)}t.adaptor.append(t.chtml,r)}},r.Border=function(r){return a.CommonBorder(function(t,e){t.adaptor.setStyle(e,"border-"+r,t.em(t.thickness)+" solid")})(r)},r.Border2=function(t,o,i){return a.CommonBorder2(function(t,e){var r=t.em(t.thickness)+" solid";t.adaptor.setStyle(e,"border-"+o,r),t.adaptor.setStyle(e,"border-"+i,r)})(t,o,i)},r.DiagonalStrike=function(t,p){return a.CommonDiagonalStrike(function(u){return function(t,e){var r=t.getBBox(),o=r.w,i=r.h,n=r.d,a=d(t.getArgMod(o,i+n),2),s=a[0],l=a[1],h=p*t.thickness/2,c=t.adjustBorder(t.html(u,{style:{width:t.em(l),transform:"rotate("+t.fixed(-p*s)+"rad) translateY("+h+"em)"}}));t.adaptor.append(t.chtml,c)}})(t)},r.DiagonalArrow=function(t){return a.CommonDiagonalArrow(function(t,e){t.adaptor.append(t.chtml,e)})(t)},r.Arrow=function(t){return a.CommonArrow(function(t,e){t.adaptor.append(t.chtml,e)})(t)}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),h=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0});var n,a=r(0),s=r(10),l=r(10),c=r(86),u=(n=s.CommonMrowMixin(a.CHTMLWrapper),i(p,n),p.prototype.toCHTML=function(t){var e,r,o=this.node.isInferred?this.chtml=t:this.standardCHTMLnode(t),i=!1;try{for(var n=h(this.childNodes),a=n.next();!a.done;a=n.next()){var s=a.value;s.toCHTML(o),s.bbox.w<0&&(i=!0)}}catch(t){e={error:t}}finally{try{a&&!a.done&&(r=n.return)&&r.call(n)}finally{if(e)throw e.error}}if(i){var l=this.getBBox().w;l&&(this.adaptor.setStyle(o,"width",this.em(Math.max(0,l))),l<0&&this.adaptor.setStyle(o,"marginRight",this.em(l)))}},p.kind=c.MmlMrow.prototype.kind,p);function p(){return null!==n&&n.apply(this,arguments)||this}e.CHTMLmrow=u;var d,f=(d=l.CommonInferredMrowMixin(u),i(m,d),m.kind=c.MmlInferredMrow.prototype.kind,m);function m(){return null!==d&&d.apply(this,arguments)||this}e.CHTMLinferredMrow=f},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0});var n,a=r(0),s=r(46),l=r(87),h=(n=s.CommonMfencedMixin(a.CHTMLWrapper),i(c,n),c.prototype.toCHTML=function(t){var e=this.standardCHTMLnode(t);this.mrow.toCHTML(e)},c.kind=l.MmlMfenced.prototype.kind,c);function c(){return null!==n&&n.apply(this,arguments)||this}e.CHTMLmfenced=h},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a},a=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(n(arguments[e]));return t},l=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMfencedMixin=function(t){return i(e,o=t),e.prototype.createMrow=function(){var t=this.node.factory.create("inferredMrow");t.inheritAttributesFrom(this.node),this.mrow=this.wrap(t),this.mrow.parent=this},e.prototype.addMrowChildren=function(){var e,t,r=this.node,o=this.mrow;this.addMo(r.open),this.childNodes.length&&o.childNodes.push(this.childNodes[0]);var i=0;try{for(var n=l(this.childNodes.slice(1)),a=n.next();!a.done;a=n.next()){var s=a.value;this.addMo(r.separators[i++]),o.childNodes.push(s)}}catch(t){e={error:t}}finally{try{a&&!a.done&&(t=n.return)&&t.call(n)}finally{if(e)throw e.error}}this.addMo(r.close),o.stretchChildren()},e.prototype.addMo=function(t){if(t){var e=this.wrap(t);this.mrow.childNodes.push(e),e.parent=this.mrow}},e.prototype.computeBBox=function(t,e){void 0===e&&(e=!1),t.updateFrom(this.mrow.getBBox()),this.setChildPWidths(e)},e;function e(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var r=o.apply(this,a(t))||this;return r.mrow=null,r.createMrow(),r.addMrowChildren(),r}var o}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),M=this&&this.__assign||function(){return(M=Object.assign||function(t){for(var e,r=1,o=arguments.length;r<o;r++)for(var i in e=arguments[r])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0});var n,a=r(0),s=r(48),l=r(88),h=(n=s.CommonMfracMixin(a.CHTMLWrapper),i(c,n),c.prototype.toCHTML=function(t){this.standardCHTMLnode(t);var e=this.node.attributes.getList("linethickness","bevelled"),r=e.linethickness,o=e.bevelled,i=this.isDisplay();if(o)this.makeBevelled(i);else{var n=this.length2em(String(r));0===n?this.makeAtop(i):this.makeFraction(i,n)}},c.prototype.makeFraction=function(t,e){var r,o,i=this.node.attributes.getList("numalign","denomalign"),n=i.numalign,a=i.denomalign,s=t?{type:"d"}:{},l=this.node.getProperty("withDelims")?M(M({},s),{delims:"true"}):M({},s),h="center"!==n?{align:n}:{},c="center"!==a?{align:a}:{},u=M({},s),p=M({},s),d=this.font.params;if(.06!==e){var f=d.axis_height,m=this.em(e),y=this.getTUV(t,e),v=y.T,b=y.u,x=y.v,g=(t?this.em(3*e):m)+" -.1em";s.style={height:m,"border-top":m+" solid",margin:g};var _=this.em(Math.max(0,b));p.style={height:_,"vertical-align":"-"+_},u.style={height:this.em(Math.max(0,x))},l.style={"vertical-align":this.em(f-v)}}this.adaptor.append(this.chtml,this.html("mjx-frac",l,[r=this.html("mjx-num",h,[this.html("mjx-nstrut",p)]),this.html("mjx-dbox",{},[this.html("mjx-dtable",{},[this.html("mjx-line",s),this.html("mjx-row",{},[o=this.html("mjx-den",c,[this.html("mjx-dstrut",u)])])])])])),this.childNodes[0].toCHTML(r),this.childNodes[1].toCHTML(o)},c.prototype.makeAtop=function(t){var e,r,o=this.node.attributes.getList("numalign","denomalign"),i=o.numalign,n=o.denomalign,a=t?{type:"d",atop:!0}:{atop:!0},s=this.node.getProperty("withDelims")?M(M({},a),{delims:!0}):M({},a),l="center"!==i?{align:i}:{},h="center"!==n?{align:n}:{},c=this.getUVQ(t),u=c.v,p=c.q;l.style={"padding-bottom":this.em(p)},s.style={"vertical-align":this.em(-u)},this.adaptor.append(this.chtml,this.html("mjx-frac",s,[e=this.html("mjx-num",l),r=this.html("mjx-den",h)])),this.childNodes[0].toCHTML(e),this.childNodes[1].toCHTML(r)},c.prototype.makeBevelled=function(t){var e=this.adaptor;e.setAttribute(this.chtml,"bevelled","ture");var r=e.append(this.chtml,this.html("mjx-num"));this.childNodes[0].toCHTML(r),this.bevel.toCHTML(this.chtml);var o=e.append(this.chtml,this.html("mjx-den"));this.childNodes[1].toCHTML(o);var i=this.getBevelData(t),n=i.u,a=i.v,s=i.delta,l=i.nbox,h=i.dbox;n&&e.setStyle(r,"verticalAlign",this.em(n/l.scale)),a&&e.setStyle(o,"verticalAlign",this.em(a/h.scale));var c=this.em(-s/2);e.setStyle(this.bevel.chtml,"marginLeft",c),e.setStyle(this.bevel.chtml,"marginRight",c)},c.kind=l.MmlMfrac.prototype.kind,c.styles={"mjx-frac":{display:"inline-block","vertical-align":"0.17em",padding:"0 .22em"},'mjx-frac[type="d"]':{"vertical-align":".04em"},"mjx-frac[delims]":{padding:"0 .1em"},"mjx-frac[atop]":{padding:"0 .12em"},"mjx-frac[atop][delims]":{padding:"0"},"mjx-dtable":{display:"inline-table",width:"100%"},"mjx-dtable > *":{"font-size":"2000%"},"mjx-dbox":{display:"block","font-size":"5%"},"mjx-num":{display:"block","text-align":"center"},"mjx-den":{display:"block","text-align":"center"},"mjx-mfrac[bevelled] > mjx-num":{display:"inline-block"},"mjx-mfrac[bevelled] > mjx-den":{display:"inline-block"},'mjx-den[align="right"], mjx-num[align="right"]':{"text-align":"right"},'mjx-den[align="left"], mjx-num[align="left"]':{"text-align":"left"},"mjx-nstrut":{display:"inline-block",height:".054em",width:0,"vertical-align":"-.054em"},'mjx-nstrut[type="d"]':{height:".217em","vertical-align":"-.217em"},"mjx-dstrut":{display:"inline-block",height:".505em",width:0},'mjx-dstrut[type="d"]':{height:".726em"},"mjx-line":{display:"block","box-sizing":"border-box","min-height":"1px",height:".06em","border-top":".06em solid",margin:".06em -.1em",overflow:"hidden"},'mjx-line[type="d"]':{margin:".18em -.1em"}},c);function c(){return null!==n&&n.apply(this,arguments)||this}e.CHTMLmfrac=h},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),h=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a},a=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(h(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMfracMixin=function(t){return i(e,n=t),e.prototype.computeBBox=function(t,e){void 0===e&&(e=!1),t.empty();var r=this.node.attributes.getList("linethickness","bevelled"),o=r.linethickness,i=r.bevelled,n=this.isDisplay(),a=null;if(i)this.getBevelledBBox(t,n);else{var s=this.length2em(String(o));a=-2*this.pad,0===s?this.getAtopBBox(t,n):(this.getFractionBBox(t,n,s),a-=.2),a+=t.w}t.clean(),this.setChildPWidths(e,a)},e.prototype.getFractionBBox=function(t,e,r){var o=this.childNodes[0].getBBox(),i=this.childNodes[1].getBBox(),n=this.font.params.axis_height,a=this.getTUV(e,r),s=a.T,l=a.u,h=a.v;t.combine(o,0,n+s+Math.max(o.d*o.rscale,l)),t.combine(i,0,n-s-Math.max(i.h*i.rscale,h)),t.w+=2*this.pad+.2},e.prototype.getTUV=function(t,e){var r=this.font.params,o=r.axis_height,i=(t?3.5:1.5)*e;return{T:(t?3.5:1.5)*e,u:(t?r.num1:r.num2)-o-i,v:(t?r.denom1:r.denom2)+o-i}},e.prototype.getAtopBBox=function(t,e){this.font.params;var r=this.getUVQ(e),o=r.u,i=r.v,n=r.nbox,a=r.dbox;t.combine(n,0,o),t.combine(a,0,-i),t.w+=2*this.pad},e.prototype.getUVQ=function(t){var e=this.childNodes[0].getBBox(),r=this.childNodes[1].getBBox(),o=this.font.params,i=h(t?[o.num1,o.denom1]:[o.num3,o.denom2],2),n=i[0],a=i[1],s=(t?7:3)*o.rule_thickness,l=n-e.d*e.scale-(r.h*r.scale-a);return l<s&&(n+=(s-l)/2,a+=(s-l)/2,l=s),{u:n,v:a,q:l,nbox:e,dbox:r}},e.prototype.getBevelledBBox=function(t,e){var r=this.getBevelData(e),o=r.u,i=r.v,n=r.delta,a=r.nbox,s=r.dbox,l=this.bevel.getBBox();t.combine(a,0,o),t.combine(l,t.w-n/2,0),t.combine(s,t.w-n/2,i)},e.prototype.getBevelData=function(t){var e=this.childNodes[0].getBBox(),r=this.childNodes[1].getBBox(),o=t?.4:.15,i=Math.max(e.scale*(e.h+e.d),r.scale*(r.h+r.d))+2*o,n=this.font.params.axis_height;return{H:i,delta:o,u:e.scale*(e.d-e.h)/2+n+o,v:r.scale*(r.d-r.h)/2+n-o,nbox:e,dbox:r}},e.prototype.canStretch=function(t){return!1},e.prototype.isDisplay=function(){var t=this.node.attributes.getList("displaystyle","scriptlevel"),e=t.displaystyle,r=t.scriptlevel;return e&&0===r},e.prototype.getWrapWidth=function(t){var e=this.node.attributes;return e.get("bevelled")?this.childNodes[t].getBBox().w:this.getBBox().w-(this.length2em(e.get("linethickness"))?.2:0)-2*this.pad},e.prototype.getChildAlign=function(t){var e=this.node.attributes;return e.get("bevelled")?"left":e.get(["numalign","denomalign"][t])},e;function e(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var r=n.apply(this,a(t))||this;if(r.bevel=null,r.pad=r.node.getProperty("withDelims")?0:r.font.params.nulldelimiterspace,r.node.attributes.get("bevelled")){var o=r.getBevelData(r.isDisplay()).H,i=r.bevel=r.createMo("/");i.canStretch(1),i.getStretchedVariant([o],!0)}return r}var n}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),c=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a},u=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(c(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0});var p=r(1);e.CommonMsqrtMixin=function(t){return i(e,h=t),Object.defineProperty(e.prototype,"base",{get:function(){return 0},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"surd",{get:function(){return 1},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"root",{get:function(){return null},enumerable:!0,configurable:!0}),e.prototype.createMo=function(t){var e=h.prototype.createMo.call(this,t);return this.childNodes.push(e),e},e.prototype.computeBBox=function(t,e){void 0===e&&(e=!1);var r=this.childNodes[this.surd].getBBox(),o=new p.BBox(this.childNodes[this.base].getBBox()),i=c(this.getPQ(r),2),n=(i[0],i[1]),a=c(this.getRootDimens(r),1)[0],s=this.font.params.rule_thickness,l=o.h+n+s;t.h=l+s,this.combineRootBBox(t,r),t.combine(r,a,l-r.h),t.combine(o,a+r.w,0),t.clean(),this.setChildPWidths(e)},e.prototype.combineRootBBox=function(t,e){},e.prototype.getPQ=function(t){var e=this.font.params.rule_thickness,r=this.node.attributes.get("displaystyle")?this.font.params.x_height:e;return[r,t.h+t.d>this.surdH?(t.h+t.d-(this.surdH-e))/2:e+r/4]},e.prototype.getRootDimens=function(t){return[0,0,0,0]},e;function e(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var r=h.apply(this,u(t))||this,o=r.createMo("\u221a");o.canStretch(1);var i=r.childNodes[r.base].getBBox(),n=i.h,a=i.d,s=r.font.params.rule_thickness,l=r.node.attributes.get("displaystyle")?r.font.params.x_height:s;return r.surdH=n+a+2*s+l/4,o.getStretchedVariant([r.surdH-a,a],!0),r}var h}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),s=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a};Object.defineProperty(e,"__esModule",{value:!0});var n,a=r(11),l=r(51),h=r(90),c=(n=l.CommonMrootMixin(a.CHTMLmsqrt),i(u,n),u.prototype.addRoot=function(t,e,r){e.toCHTML(t);var o=s(this.getRootDimens(r),3),i=o[0],n=o[1],a=o[2];e.getBBox(),this.adaptor.setStyle(t,"verticalAlign",this.em(n)),this.adaptor.setStyle(t,"width",this.em(i)),a&&this.adaptor.setStyle(this.adaptor.firstChild(t),"paddingLeft",this.em(a))},u.kind=h.MmlMroot.prototype.kind,u);function u(){return null!==n&&n.apply(this,arguments)||this}e.CHTMLmroot=c},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMrootMixin=function(t){return i(e,r=t),Object.defineProperty(e.prototype,"surd",{get:function(){return 2},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"root",{get:function(){return 1},enumerable:!0,configurable:!0}),e.prototype.combineRootBBox=function(t,e){var r=this.childNodes[this.root].getBBox(),o=n(this.getRootDimens(e),2),i=(o[0],o[1]);t.combine(r,0,i)},e.prototype.getRootDimens=function(t){var e=this.childNodes[this.surd],r=this.childNodes[this.root].getBBox(),o=(e.size<0?.5:.6)*t.w,i=r.w,n=r.rscale,a=Math.max(i,o/n),s=Math.max(0,a-i);return[a*n-o,this.rootHeight(r,t,e.size),s]},e.prototype.rootHeight=function(t,e,r){var o=e.h+e.d;return(r<0?2+.3*(o-4):.55*o)-e.d+Math.max(0,t.d*t.rscale)},e;function e(){return null!==r&&r.apply(this,arguments)||this}var r}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a},c=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0});var a,s=r(0),l=r(53),h=(a=l.CommonScriptbaseMixin(s.CHTMLWrapper),i(u,a),u.prototype.toCHTML=function(t){this.chtml=this.standardCHTMLnode(t);var e=n(this.getOffset(this.baseChild.getBBox(),this.script.getBBox()),2),r=e[0],o=e[1],i={"vertical-align":this.em(o)};r&&(i["margin-left"]=this.em(r)),this.baseChild.toCHTML(this.chtml),this.script.toCHTML(this.adaptor.append(this.chtml,this.html("mjx-script",{style:i})))},u.prototype.setDeltaW=function(t,e){for(var r=0;r<e.length;r++)e[r]&&this.adaptor.setStyle(t[r],"paddingLeft",this.em(e[r]))},u.prototype.adjustOverDepth=function(t,e){0<=e.d||this.adaptor.setStyle(t,"marginBottom",this.em(e.d*e.rscale))},u.prototype.adjustUnderDepth=function(t,e){var r,o;if(!(0<=e.d)){var i=this.adaptor,n=(i.firstChild(i.firstChild(t)),this.em(e.d)),a=this.html("mjx-box",{style:{"margin-bottom":n,"vertical-align":n}});try{for(var s=c(i.childNodes(i.firstChild(t))),l=s.next();!l.done;l=s.next()){var h=l.value;i.append(a,h)}}catch(t){r={error:t}}finally{try{l&&!l.done&&(o=s.return)&&o.call(s)}finally{if(r)throw r.error}}i.append(i.firstChild(t),a)}},u.kind="scriptbase",u.useIC=!1,u);function u(){return null!==a&&a.apply(this,arguments)||this}e.CHTMLscriptbase=h},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),s=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a},y=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(s(arguments[e]));return t},M=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0});var a=1.5;e.CommonScriptbaseMixin=function(t){var e,i;return n(r,i=t),Object.defineProperty(r.prototype,"baseChild",{get:function(){return this.childNodes[this.node.base]},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"script",{get:function(){return this.childNodes[1]},enumerable:!0,configurable:!0}),r.prototype.computeBBox=function(t,e){void 0===e&&(e=!1);var r=this.baseChild.getBBox(),o=this.script.getBBox(),i=s(this.getOffset(r,o),2),n=i[0],a=i[1];t.append(r),t.combine(o,t.w+n,a),t.w+=this.font.params.scriptspace,t.clean(),this.setChildPWidths(e)},r.prototype.coreIC=function(){var t=this.baseCore.getBBox();return t.ic?1.2*t.ic+.05:0},r.prototype.isCharBase=function(){var t=this.baseChild;return(t.node.isKind("mstyle")||t.node.isKind("mrow"))&&1===t.childNodes.length&&(t=t.childNodes[0]),(t.node.isKind("mo")||t.node.isKind("mi")||t.node.isKind("mn"))&&1===t.bbox.rscale&&1===t.getText().length&&!t.node.attributes.get("largeop")},r.prototype.getOffset=function(t,e){return[0,0]},r.prototype.getV=function(t,e){var r=this.font.params,o=this.length2em(this.node.attributes.get("subscriptshift"),r.sub1);return Math.max(this.isCharBase()?0:t.d+r.sub_drop*e.rscale,o,e.h*e.rscale-.8*r.x_height)},r.prototype.getU=function(t,e){var r=this.font.params,o=this.node.attributes.getList("displaystyle","texprimestyle","superscriptshift"),i=o.displaystyle?r.sup1:o.texprimestyle?r.sup3:r.sup2,n=this.length2em(o.superscriptshift,i);return Math.max(this.isCharBase()?0:t.h-r.sup_drop*e.rscale,n,e.d*e.rscale+.25*r.x_height)},r.prototype.hasMovableLimits=function(){return!this.node.attributes.get("displaystyle")&&(this.node.getProperty("movablelimits")||this.node.attributes.get("movablelimits")||this.baseChild.coreMO().node.attributes.get("movablelimits"))},r.prototype.getOverKU=function(t,e){var r=this.node.attributes.get("accent"),o=this.font.params,i=e.d*e.rscale,n=(r?o.rule_thickness:Math.max(o.big_op_spacing1,o.big_op_spacing3-Math.max(0,i)))-(this.baseChild.node.isKind("munderover")?.1:0);return[n,t.h*t.rscale+n+i]},r.prototype.getUnderKV=function(t,e){var r=this.node.attributes.get("accentunder"),o=this.font.params,i=e.h*e.rscale,n=(r?o.rule_thickness:Math.max(o.big_op_spacing2,o.big_op_spacing4-i))-(this.baseChild.node.isKind("munderover")?.1:0);return[n,-(t.d*t.rscale+n+i)]},r.prototype.getDeltaW=function(t,e){var r,o,i,n;void 0===e&&(e=[0,0,0]);var a=this.node.attributes.get("align"),s=t.map(function(t){return t.w*t.rscale}),l=Math.max.apply(Math,y(s)),h=[],c=0;try{for(var u=M(s.keys()),p=u.next();!p.done;p=u.next())h[m=p.value]=("center"===a?(l-s[m])/2:"right"===a?l-s[m]:0)+e[m],h[m]<c&&(c=-h[m])}catch(t){r={error:t}}finally{try{p&&!p.done&&(o=u.return)&&o.call(u)}finally{if(r)throw r.error}}if(c)try{for(var d=M(h.keys()),f=d.next();!f.done;f=d.next()){var m;h[m=f.value]+=c}}catch(t){i={error:t}}finally{try{f&&!f.done&&(n=d.return)&&n.call(d)}finally{if(i)throw i.error}}return h},r.prototype.getDelta=function(t){void 0===t&&(t=!1);var e=this.node.attributes.get("accent")&&!t?this.baseChild.coreMO().bbox.sk:0;return a*this.baseCore.bbox.ic/2+e},r.prototype.stretchChildren=function(){var e,t,r,o,i,n,a=[];try{for(var s=M(this.childNodes),l=s.next();!l.done;l=s.next())(_=l.value).canStretch(2)&&a.push(_)}catch(t){e={error:t}}finally{try{l&&!l.done&&(t=s.return)&&t.call(s)}finally{if(e)throw e.error}}var h=a.length,c=this.childNodes.length;if(h&&1<c){var u=0,p=1<h&&h===c;try{for(var d=M(this.childNodes),f=d.next();!f.done;f=d.next()){var m=0===(_=f.value).stretch.dir;if(p||m){var y=_.getBBox(m),v=y.w,b=y.rscale;u<v*b&&(u=v*b)}}}catch(t){r={error:t}}finally{try{f&&!f.done&&(o=d.return)&&o.call(d)}finally{if(r)throw r.error}}try{for(var x=M(a),g=x.next();!g.done;g=x.next()){var _;(_=g.value).coreMO().getStretchedVariant([u/_.bbox.rscale])}}catch(t){i={error:t}}finally{try{g&&!g.done&&(n=x.return)&&n.call(x)}finally{if(i)throw i.error}}}},(e=r).useIC=!1,e;function r(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var r=i.apply(this,y(t))||this,o=r.baseCore=r.childNodes[0];if(!o)return r;for(;1===o.childNodes.length&&(o.node.isKind("mrow")||o.node.isKind("TeXAtom")||o.node.isKind("mstyle")||o.node.isKind("mpadded")||o.node.isKind("mphantom")||o.node.isKind("semantics"));)if(!(o=o.childNodes[0]))return r;return"noIC"in o&&(r.baseCore=o,r.constructor.useIC||(o.noIC=!0)),r}}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),d=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a};Object.defineProperty(e,"__esModule",{value:!0});var l,n=r(7),a=r(9),s=r(9),h=r(9),c=r(92),u=(l=a.CommonMunderMixin(n.CHTMLmsub),i(p,l),p.prototype.toCHTML=function(t){if(this.hasMovableLimits())return l.prototype.toCHTML.call(this,t),void this.adaptor.setAttribute(this.chtml,"limits","false");this.chtml=this.standardCHTMLnode(t);var e=this.adaptor.append(this.adaptor.append(this.chtml,this.html("mjx-row")),this.html("mjx-base")),r=this.adaptor.append(this.adaptor.append(this.chtml,this.html("mjx-row")),this.html("mjx-under"));this.baseChild.toCHTML(e),this.script.toCHTML(r);var o=this.baseChild.getBBox(),i=this.script.getBBox(),n=d(this.getUnderKV(o,i),2),a=n[0],s=(n[1],this.getDelta(!0));this.adaptor.setStyle(r,"paddingTop",this.em(a)),this.setDeltaW([e,r],this.getDeltaW([o,i],[0,-s])),this.adjustUnderDepth(r,i)},p.kind=c.MmlMunder.prototype.kind,p.useIC=!0,p.styles={"mjx-over":{"text-align":"left"},'mjx-munder:not([limits="false"])':{display:"inline-table"},"mjx-munder > mjx-row":{"text-align":"left"},"mjx-under":{"padding-bottom":".1em"}},p);function p(){return null!==l&&l.apply(this,arguments)||this}e.CHTMLmunder=u;var f,m=(f=s.CommonMoverMixin(n.CHTMLmsup),i(y,f),y.prototype.toCHTML=function(t){if(this.hasMovableLimits())return f.prototype.toCHTML.call(this,t),void this.adaptor.setAttribute(this.chtml,"limits","false");this.chtml=this.standardCHTMLnode(t);var e=this.adaptor.append(this.chtml,this.html("mjx-over")),r=this.adaptor.append(this.chtml,this.html("mjx-base"));this.script.toCHTML(e),this.baseChild.toCHTML(r);var o=this.script.getBBox(),i=this.baseChild.getBBox(),n=d(this.getOverKU(i,o),2),a=n[0],s=(n[1],this.getDelta());this.adaptor.setStyle(e,"paddingBottom",this.em(a)),this.setDeltaW([r,e],this.getDeltaW([i,o],[0,s])),this.adjustOverDepth(e,o)},y.kind=c.MmlMover.prototype.kind,y.useIC=!0,y.styles={'mjx-mover:not([limits="false"])':{"padding-top":".1em"},'mjx-mover:not([limits="false"]) > *':{display:"block","text-align":"left"}},y);function y(){return null!==f&&f.apply(this,arguments)||this}e.CHTMLmover=m;var v,b=(v=h.CommonMunderoverMixin(n.CHTMLmsubsup),i(x,v),x.prototype.toCHTML=function(t){if(this.hasMovableLimits())return v.prototype.toCHTML.call(this,t),void this.adaptor.setAttribute(this.chtml,"limits","false");this.chtml=this.standardCHTMLnode(t);var e=this.adaptor.append(this.chtml,this.html("mjx-over")),r=this.adaptor.append(this.adaptor.append(this.chtml,this.html("mjx-box")),this.html("mjx-munder")),o=this.adaptor.append(this.adaptor.append(r,this.html("mjx-row")),this.html("mjx-base")),i=this.adaptor.append(this.adaptor.append(r,this.html("mjx-row")),this.html("mjx-under"));this.overChild.toCHTML(e),this.baseChild.toCHTML(o),this.underChild.toCHTML(i);var n=this.overChild.getBBox(),a=this.baseChild.getBBox(),s=this.underChild.getBBox(),l=d(this.getOverKU(a,n),2),h=l[0],c=(l[1],d(this.getUnderKV(a,s),2)),u=c[0],p=(c[1],this.getDelta());this.adaptor.setStyle(e,"paddingBottom",this.em(h)),this.adaptor.setStyle(i,"paddingTop",this.em(u)),this.setDeltaW([o,i,e],this.getDeltaW([a,s,n],[0,-p,p])),this.adjustOverDepth(e,n),this.adjustUnderDepth(i,s)},x.kind=c.MmlMunderover.prototype.kind,x.useIC=!0,x.styles={'mjx-munderover:not([limits="false"])':{"padding-top":".1em"},'mjx-munderover:not([limits="false"]) > *':{display:"block"}},x);function x(){return null!==v&&v.apply(this,arguments)||this}e.CHTMLmunderover=b},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),l=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a};Object.defineProperty(e,"__esModule",{value:!0});var n,a=r(7),s=r(56),h=r(93),c=(n=s.CommonMmultiscriptsMixin(a.CHTMLmsubsup),i(u,n),u.prototype.toCHTML=function(t){var e=this.standardCHTMLnode(t),r=this.getScriptData(),o=this.combinePrePost(r.sub,r.psub),i=this.combinePrePost(r.sup,r.psup),n=l(this.getUVQ(r.base,o,i),3),a=n[0],s=n[1];n[2],r.numPrescripts&&this.addScripts(a,-s,!0,r.psub,r.psup,this.firstPrescript,r.numPrescripts),this.childNodes[0].toCHTML(e),r.numScripts&&this.addScripts(a,-s,!1,r.sub,r.sup,1,r.numScripts)},u.prototype.addScripts=function(t,e,r,o,i,n,a){var s=this.adaptor,l=t-i.d+(e-o.h),h=t<0&&0===e?o.h+t:t,c=0<l?{style:{height:this.em(l)}}:{},u=h?{style:{"vertical-align":this.em(h)}}:{},p=this.html("mjx-row"),d=this.html("mjx-row",c),f=this.html("mjx-row"),m="mjx-"+(r?"pre":"")+"scripts";s.append(this.chtml,this.html(m,u,[p,d,f]));for(var y=n+2*a;n<y;)this.childNodes[n++].toCHTML(s.append(f,this.html("mjx-cell"))),this.childNodes[n++].toCHTML(s.append(p,this.html("mjx-cell")))},u.kind=h.MmlMmultiscripts.prototype.kind,u.styles={"mjx-prescripts":{display:"inline-table","padding-left":".05em"},"mjx-scripts":{display:"inline-table","padding-right":".05em"},"mjx-prescripts > mjx-row > mjx-cell":{"text-align":"right"}},u);function u(){return null!==n&&n.apply(this,arguments)||this}e.CHTMLmmultiscripts=c},function(t,s,e){"use strict";var o,r=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),f=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a},h=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(s,"__esModule",{value:!0});var i=e(1);s.NextScript={base:"subList",subList:"supList",supList:"subList",psubList:"psupList",psupList:"psubList"},s.ScriptNames=["sup","sup","psup","psub"],s.CommonMmultiscriptsMixin=function(t){return r(e,l=t),e.prototype.combinePrePost=function(t,e){var r=new i.BBox(t);return r.combine(e,0,0),r},e.prototype.computeBBox=function(t,e){void 0===e&&(e=!1);var r=this.font.params.scriptspace,o=this.getScriptData(),i=this.combinePrePost(o.sub,o.psub),n=this.combinePrePost(o.sup,o.psup),a=f(this.getUVQ(o.base,i,n),2),s=a[0],l=a[1];if(t.empty(),o.numPrescripts&&(t.combine(o.psup,r,s),t.combine(o.psub,r,l)),t.append(o.base),o.numScripts){var h=t.w;t.combine(o.sup,h,s),t.combine(o.sub,h,l),t.w+=r}t.clean(),this.setChildPWidths(e)},e.prototype.getScriptData=function(){if(this.scriptData)return this.scriptData;var t=this.scriptData={base:null,sub:i.BBox.empty(),sup:i.BBox.empty(),psub:i.BBox.empty(),psup:i.BBox.empty(),numPrescripts:0,numScripts:0},e=this.getScriptBBoxLists();return this.combineBBoxLists(t.sub,t.sup,e.subList,e.supList),this.combineBBoxLists(t.psub,t.psup,e.psubList,e.psupList),this.scriptData.base=e.base[0],this.scriptData.numPrescripts=e.psubList.length,this.scriptData.numScripts=e.subList.length,this.scriptData},e.prototype.getScriptBBoxLists=function(){var e,t,r={base:[],subList:[],supList:[],psubList:[],psupList:[]},o="base";try{for(var i=h(this.childNodes),n=i.next();!n.done;n=i.next()){var a=n.value;o=a.node.isKind("mprescripts")?"psubList":(r[o].push(a.getBBox()),s.NextScript[o])}}catch(t){e={error:t}}finally{try{n&&!n.done&&(t=i.return)&&t.call(i)}finally{if(e)throw e.error}}return this.firstPrescript=r.subList.length+r.supList.length+2,this.padLists(r.subList,r.supList),this.padLists(r.psubList,r.psupList),r},e.prototype.padLists=function(t,e){t.length>e.length&&e.push(i.BBox.empty())},e.prototype.combineBBoxLists=function(t,e,r,o){for(var i=0;i<r.length;i++){var n=f(this.getScaledWHD(r[i]),3),a=n[0],s=n[1],l=n[2],h=f(this.getScaledWHD(o[i]),3),c=h[0],u=h[1],p=h[2],d=Math.max(a,c);t.w+=d,e.w+=d,s>t.h&&(t.h=s),l>t.d&&(t.d=l),u>e.h&&(e.h=u),p>e.d&&(e.d=p)}},e.prototype.getScaledWHD=function(t){var e=t.w,r=t.h,o=t.d,i=t.rscale;return[e*i,r*i,o*i]},e.prototype.getUVQ=function(t,e,r){var o;if(!this.UVQ){var i=f([0,0,0],3),n=i[0],a=i[1],s=i[2];0===e.h&&0===e.d?n=this.getU(t,r):0===r.h&&0===r.d?n=-this.getV(t,e):(n=(o=f(l.prototype.getUVQ.call(this,t,e,r),3))[0],a=o[1],s=o[2]),this.UVQ=[n,a,s]}return this.UVQ},e;function e(){var t=null!==l&&l.apply(this,arguments)||this;return t.scriptData=null,t.firstPrescript=0,t}var l}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),y=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},c=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a};Object.defineProperty(e,"__esModule",{value:!0});var n,a=r(0),s=r(58),l=r(95),u=r(4),h=(n=s.CommonMtableMixin(a.CHTMLWrapper),i(p,n),p.prototype.getAlignShift=function(){var t=n.prototype.getAlignShift.call(this);return this.isTop||(t[1]=0),t},p.prototype.toCHTML=function(t){var e,r,o=this.standardCHTMLnode(t);this.adaptor.append(o,this.html("mjx-table",{},[this.itable]));try{for(var i=y(this.childNodes),n=i.next();!n.done;n=i.next())n.value.toCHTML(this.itable)}catch(t){e={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(e)throw e.error}}this.padRows(),this.handleColumnSpacing(),this.handleColumnLines(),this.handleColumnWidths(),this.handleRowSpacing(),this.handleRowLines(),this.handleEqualRows(),this.handleFrame(),this.handleWidth(),this.handleLabels(),this.handleAlign(),this.handleJustify(),this.shiftColor()},p.prototype.shiftColor=function(){var t=this.adaptor,e=t.getStyle(this.chtml,"backgroundColor");e&&(t.setStyle(this.chtml,"backgroundColor",""),t.setStyle(this.itable,"backgroundColor",e))},p.prototype.padRows=function(){var e,t,r=this.adaptor;try{for(var o=y(r.childNodes(this.itable)),i=o.next();!i.done;i=o.next())for(var n=i.value;r.childNodes(n).length<this.numCols;)r.append(n,this.html("mjx-mtd"))}catch(t){e={error:t}}finally{try{i&&!i.done&&(t=o.return)&&t.call(o)}finally{if(e)throw e.error}}},p.prototype.handleColumnSpacing=function(){var e,t,r,o,i=this.getEmHalfSpacing(this.fSpace[0],this.cSpace),n=this.frame;try{for(var a=y(this.tableRows),s=a.next();!s.done;s=a.next()){var l=s.value,h=0;try{for(var c=(r=void 0,y(l.tableCells)),u=c.next();!u.done;u=c.next()){var p=u.value,d=i[h++],f=i[h],m=p?p.chtml:this.adaptor.childNodes(l.chtml)[h];(1<h&&"0.4em"!==d||n&&1===h)&&this.adaptor.setStyle(m,"paddingLeft",d),(h<this.numCols&&"0.4em"!==f||n&&h===this.numCols)&&this.adaptor.setStyle(m,"paddingRight",f)}}catch(t){r={error:t}}finally{try{u&&!u.done&&(o=c.return)&&o.call(c)}finally{if(r)throw r.error}}}}catch(t){e={error:t}}finally{try{s&&!s.done&&(t=a.return)&&t.call(a)}finally{if(e)throw e.error}}},p.prototype.handleColumnLines=function(){var e,t,r,o;if("none"!==this.node.attributes.get("columnlines")){var i=this.getColumnAttributes("columnlines");try{for(var n=y(this.childNodes),a=n.next();!a.done;a=n.next()){var s=a.value,l=0;try{for(var h=(r=void 0,y(this.adaptor.childNodes(s.chtml).slice(1))),c=h.next();!c.done;c=h.next()){var u=c.value,p=i[l++];"none"!==p&&this.adaptor.setStyle(u,"borderLeft",".07em "+p)}}catch(t){r={error:t}}finally{try{c&&!c.done&&(o=h.return)&&o.call(h)}finally{if(r)throw r.error}}}}catch(t){e={error:t}}finally{try{a&&!a.done&&(t=n.return)&&t.call(n)}finally{if(e)throw e.error}}}},p.prototype.handleColumnWidths=function(){var e,t,r,o;try{for(var i=y(this.childNodes),n=i.next();!n.done;n=i.next()){var a=n.value,s=0;try{for(var l=(r=void 0,y(this.adaptor.childNodes(a.chtml))),h=l.next();!h.done;h=l.next()){var c=h.value,u=this.cWidths[s++];if(null!==u){var p="number"==typeof u?this.em(u):u;this.adaptor.setStyle(c,"width",p),this.adaptor.setStyle(c,"maxWidth",p),this.adaptor.setStyle(c,"minWidth",p)}}}catch(t){r={error:t}}finally{try{h&&!h.done&&(o=l.return)&&o.call(l)}finally{if(r)throw r.error}}}}catch(t){e={error:t}}finally{try{n&&!n.done&&(t=i.return)&&t.call(i)}finally{if(e)throw e.error}}},p.prototype.handleRowSpacing=function(){var e,t,r,o,i=this.getEmHalfSpacing(this.fSpace[1],this.rSpace),n=this.frame,a=0;try{for(var s=y(this.childNodes),l=s.next();!l.done;l=s.next()){var h=l.value,c=i[a++],u=i[a];try{for(var p=(r=void 0,y(h.childNodes)),d=p.next();!d.done;d=p.next()){var f=d.value;(1<a&&"0.215em"!==c||n&&1===a)&&this.adaptor.setStyle(f.chtml,"paddingTop",c),(a<this.numRows&&"0.215em"!==u||n&&a===this.numRows)&&this.adaptor.setStyle(f.chtml,"paddingBottom",u)}}catch(t){r={error:t}}finally{try{d&&!d.done&&(o=p.return)&&o.call(p)}finally{if(r)throw r.error}}}}catch(t){e={error:t}}finally{try{l&&!l.done&&(t=s.return)&&t.call(s)}finally{if(e)throw e.error}}},p.prototype.handleRowLines=function(){var e,t,r,o;if("none"!==this.node.attributes.get("rowlines")){var i=this.getRowAttributes("rowlines"),n=0;try{for(var a=y(this.childNodes.slice(1)),s=a.next();!s.done;s=a.next()){var l=s.value,h=i[n++];if("none"!==h)try{for(var c=(r=void 0,y(this.adaptor.childNodes(l.chtml))),u=c.next();!u.done;u=c.next()){var p=u.value;this.adaptor.setStyle(p,"borderTop",".07em "+h)}}catch(t){r={error:t}}finally{try{u&&!u.done&&(o=c.return)&&o.call(c)}finally{if(r)throw r.error}}}}catch(t){e={error:t}}finally{try{s&&!s.done&&(t=a.return)&&t.call(a)}finally{if(e)throw e.error}}}},p.prototype.handleEqualRows=function(){if(this.node.attributes.get("equalrows"))for(var t=this.getRowHalfSpacing(),e=this.getTableData(),r=e.H,o=e.D,i=e.NH,n=e.ND,a=this.getEqualRowHeight(),s=(this.em(a),0);s<this.numRows;s++){var l=this.childNodes[s];a!==i[s]+n[s]&&this.setRowHeight(l,a,(a-r[s]+o[s])/2,t[s]+t[s+1])}},p.prototype.setRowHeight=function(t,e,r,o){var i,n;this.adaptor.setStyle(t.chtml,"height",this.em(e+o));var a=t.node.attributes.get("rowalign");try{for(var s=y(t.childNodes),l=s.next();!l.done;l=s.next()){var h=l.value;if(this.setCellBaseline(h,a,e,r))break}}catch(t){i={error:t}}finally{try{l&&!l.done&&(n=s.return)&&n.call(s)}finally{if(i)throw i.error}}},p.prototype.setCellBaseline=function(t,e,r,o){var i=t.node.attributes.get("rowalign");if("baseline"===i||"axis"===i){var n=this.adaptor,a=n.lastChild(t.chtml);n.setStyle(a,"height",this.em(r)),n.setStyle(a,"verticalAlign",this.em(-o));var s=t.parent;if(!(s.node.isKind("mlabeledtr")&&t===s.childNodes[0]||"baseline"!==e&&"axis"!==e))return!0}return!1},p.prototype.handleFrame=function(){this.frame&&this.adaptor.setStyle(this.itable,"border",".07em "+this.node.attributes.get("frame"))},p.prototype.handleWidth=function(){var t=this.adaptor,e=this.getBBox(),r=e.w,o=e.L,i=e.R;t.setStyle(this.chtml,"minWidth",this.em(o+r+i));var n=this.node.attributes.get("width");if(u.isPercent(n))t.setStyle(this.chtml,"width",""),t.setAttribute(this.chtml,"width","full");else if(!this.hasLabels){if("auto"===n)return;n=this.em(this.length2em(n)+2*this.fLine)}var a=t.firstChild(this.chtml);t.setStyle(a,"width",n),t.setStyle(a,"minWidth",this.em(r)),(o||i)&&(t.setStyle(this.chtml,"margin",""),o===i?t.setStyle(a,"margin","0 "+this.em(i)):t.setStyle(a,"margin","0 "+this.em(i)+" 0 "+this.em(o))),t.setAttribute(this.itable,"width","full")},p.prototype.handleAlign=function(){var t=c(this.getAlignmentRow(),2),e=t[0],r=t[1];if(null===r)"axis"!==e&&this.adaptor.setAttribute(this.chtml,"align",e);else{var o=this.getVerticalPosition(r,e);this.adaptor.setAttribute(this.chtml,"align","top"),this.adaptor.setStyle(this.chtml,"verticalAlign",this.em(o))}},p.prototype.handleJustify=function(){var t=c(this.getAlignShift(),2),e=t[0];t[1],"center"!==e&&this.adaptor.setAttribute(this.chtml,"justify",e)},p.prototype.handleLabels=function(){if(this.hasLabels){var t=this.labels,e=this.node.attributes,r=this.adaptor,o=e.get("side");r.setAttribute(this.chtml,"side",o),r.setAttribute(t,"align",o),r.setStyle(t,o,"0");var i=c(this.addLabelPadding(o),2),n=i[0],a=i[1];if(a){var s=r.firstChild(this.chtml);this.setIndent(s,n,a)}this.updateRowHeights(),this.addLabelSpacing()}},p.prototype.addLabelPadding=function(t){var e=c(this.getPadAlignShift(t),3),r=(e[0],e[1]),o=e[2],i={};if("right"===t){var n=this.node.attributes.get("width"),a=this.getBBox(),s=a.w,l=a.L,h=a.R;i.style={width:u.isPercent(n)?"calc("+n+" + "+this.em(l+h)+")":this.em(l+s+h),minWidth:"100%"}}return this.adaptor.append(this.chtml,this.html("mjx-labels",i,[this.labels])),[r,o]},p.prototype.updateRowHeights=function(){if(!this.node.attributes.get("equalrows"))for(var t=this.getTableData(),e=t.H,r=t.D,o=t.NH,i=t.ND,n=this.getRowHalfSpacing(),a=0;a<this.numRows;a++){var s=this.childNodes[a];e[a]!==o[a]||r[a]!==i[a]?this.setRowHeight(s,e[a]+r[a],r[a],n[a]+n[a+1]):s.node.isKind("mlabeledtr")&&this.setCellBaseline(s.childNodes[0],"",e[a]+r[a],r[a])}},p.prototype.addLabelSpacing=function(){for(var t=this.adaptor,e=this.node.attributes.get("equalrows"),r=this.getTableData(),o=r.H,i=r.D,n=e?this.getEqualRowHeight():0,a=this.getRowHalfSpacing(),s=this.fLine,l=t.firstChild(this.labels),h=0;h<this.numRows;h++)this.childNodes[h].node.isKind("mlabeledtr")?(s&&t.insert(this.html("mjx-mtr",{style:{height:this.em(s)}}),l),t.setStyle(l,"height",this.em((e?n:o[h]+i[h])+a[h]+a[h+1])),l=t.next(l),s=this.rLines[h]):s+=a[h]+(e?n:o[h]+i[h])+a[h+1]+this.rLines[h]},p.kind=l.MmlMtable.prototype.kind,p.styles={"mjx-mtable":{"vertical-align":".25em","text-align":"center",position:"relative","box-sizing":"border-box"},"mjx-labels":{position:"absolute",left:0,top:0},"mjx-table":{display:"inline-block"},"mjx-table > mjx-itable":{"vertical-align":"middle","text-align":"left","box-sizing":"border-box"},"mjx-labels > mjx-itable":{position:"absolute",top:0},'mjx-mtable[justify="left"]':{"text-align":"left"},'mjx-mtable[justify="right"]':{"text-align":"right"},'mjx-mtable[justify="left"][side="left"]':{"padding-right":"0 ! important"},'mjx-mtable[justify="left"][side="right"]':{"padding-left":"0 ! important"},'mjx-mtable[justify="right"][side="left"]':{"padding-right":"0 ! important"},'mjx-mtable[justify="right"][side="right"]':{"padding-left":"0 ! important"},"mjx-mtable[align]":{"vertical-align":"baseline"},'mjx-mtable[align="top"] > mjx-table':{"vertical-align":"top"},'mjx-mtable[align="bottom"] > mjx-table':{"vertical-align":"bottom"},'mjx-mtable[align="center"] > mjx-table':{"vertical-align":"middle"},'mjx-mtable[align="baseline"] > mjx-table':{"vertical-align":"middle"}},p);function p(t,e,r){void 0===r&&(r=null);var o=n.call(this,t,e,r)||this;return o.itable=o.html("mjx-itable"),o.labels=o.html("mjx-itable"),o}e.CHTMLmtable=h},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),y=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a},a=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(y(arguments[e]));return t},M=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0});var s=r(1),v=r(4),b=r(94);e.CommonMtableMixin=function(t){return n(e,i=t),Object.defineProperty(e.prototype,"tableRows",{get:function(){return this.childNodes},enumerable:!0,configurable:!0}),e.prototype.findContainer=function(){for(var t=this,e=t.parent;e&&(e.node.notParent||e.node.isKind("mrow"));)e=(t=e).parent;this.container=e,this.containerI=t.node.childPosition()},e.prototype.getPercentageWidth=function(){if(this.hasLabels)this.bbox.pwidth=s.BBox.fullWidth;else{var t=this.node.attributes.get("width");v.isPercent(t)&&(this.bbox.pwidth=t)}},e.prototype.stretchRows=function(){for(var t=this.node.attributes.get("equalrows"),e=t?this.getEqualRowHeight():0,r=t?this.getTableData():{H:[0],D:[0]},o=r.H,i=r.D,n=this.tableRows,a=0;a<this.numRows;a++){var s=t?[(e+o[a]-i[a])/2,(e-o[a]+i[a])/2]:null;n[a].stretchChildren(s)}},e.prototype.stretchColumns=function(){for(var t=0;t<this.numCols;t++){var e="number"==typeof this.cWidths[t]?this.cWidths[t]:null;this.stretchColumn(t,e)}},e.prototype.stretchColumn=function(t,e){var r,o,i,n,a,s,l=[];try{for(var h=M(this.tableRows),c=h.next();!c.done;c=h.next())(y=c.value.getChild(t))&&0===(_=y.childNodes[0]).stretch.dir&&_.canStretch(2)&&l.push(_)}catch(t){r={error:t}}finally{try{c&&!c.done&&(o=h.return)&&o.call(h)}finally{if(r)throw r.error}}var u=l.length,p=this.childNodes.length;if(u&&1<p){if(null===e){e=0;var d=1<u&&u===p;try{for(var f=M(this.tableRows),m=f.next();!m.done;m=f.next()){var y;if(y=m.value.getChild(t)){var v=0===(_=y.childNodes[0]).stretch.dir;if(d||v){var b=_.getBBox(v).w;e<b&&(e=b)}}}}catch(t){i={error:t}}finally{try{m&&!m.done&&(n=f.return)&&n.call(f)}finally{if(i)throw i.error}}}try{for(var x=M(l),g=x.next();!g.done;g=x.next()){var _;(_=g.value).coreMO().getStretchedVariant([e])}}catch(t){a={error:t}}finally{try{g&&!g.done&&(s=x.return)&&s.call(x)}finally{if(a)throw a.error}}}},e.prototype.getTableData=function(){if(this.data)return this.data;for(var t=new Array(this.numRows).fill(0),e=new Array(this.numRows).fill(0),r=new Array(this.numCols).fill(0),o=new Array(this.numRows),i=new Array(this.numRows),n=[0],a=this.tableRows,s=0;s<a.length;s++){for(var l=a[s],h=0;h<l.numCells;h++){var c=l.getChild(h);this.updateHDW(c,h,s,t,e,r),this.recordPWidthCell(c,h)}o[s]=t[s],i[s]=e[s],l.labeled&&this.updateHDW(l.childNodes[0],0,s,t,e,n)}this.node.attributes.get("width");var u=n[0];return this.data={H:t,D:e,W:r,NH:o,ND:i,L:u},this.data},e.prototype.updateHDW=function(t,e,r,o,i,n){void 0===n&&(n=null);var a=t.getBBox(),s=a.h,l=a.d,h=a.w;a.pwidth,s<.75&&(s=.75),l<.25&&(l=.25),s>o[r]&&(o[r]=s),l>i[r]&&(i[r]=l),n&&h>n[e]&&(n[e]=h)},e.prototype.recordPWidthCell=function(t,e){t.childNodes[0]&&t.childNodes[0].getBBox().pwidth&&this.pwidthCells.push([t,e])},e.prototype.computeBBox=function(t,e){void 0===e&&(e=!1);var r,o,i=this.getTableData(),n=i.H,a=i.D;if(this.node.attributes.get("equalrows")){var s=this.getEqualRowHeight();r=b.sum([].concat(this.rLines,this.rSpace))+s*this.numRows}else r=b.sum(n.concat(a,this.rLines,this.rSpace));r+=2*(this.fLine+this.fSpace[1]);var l=this.getComputedWidths();o=b.sum(l.concat(this.cLines,this.cSpace))+2*(this.fLine+this.fSpace[0]);var h=this.node.attributes.get("width");"auto"!==h&&(o=Math.max(this.length2em(h,0)+2*this.fLine,o));var c=y(this.getBBoxHD(r),2),u=c[0],p=c[1];t.h=u,t.d=p,t.w=o;var d=y(this.getBBoxLR(),2),f=d[0],m=d[1];t.L=f,t.R=m,v.isPercent(h)||this.setColumnPWidths()},e.prototype.setChildPWidths=function(t,e,r){var o=this.node.attributes.get("width");if(v.isPercent(o)){this.hasLabels||(this.bbox.pwidth="",this.container.bbox.pwidth="");var i=this.bbox,n=i.w,a=i.L,s=i.R,l=Math.max(n,this.length2em(o,Math.max(e,a+n+s))),h=this.node.attributes.get("equalcolumns")?Array(this.numCols).fill(this.percent(1/Math.max(1,this.numCols))):this.getColumnAttributes("columnwidth",0);this.cWidths=this.getColumnWidthsFixed(h,l);var c=this.getComputedWidths();return this.pWidth=b.sum(c.concat(this.cLines,this.cSpace))+2*(this.fLine+this.fSpace[0]),this.isTop&&(this.bbox.w=this.pWidth),this.setColumnPWidths(),this.pWidth!==n&&this.parent.invalidateBBox(),this.pWidth!==n}},e.prototype.setColumnPWidths=function(){var e,t,r=this.cWidths;try{for(var o=M(this.pwidthCells),i=o.next();!i.done;i=o.next()){var n=y(i.value,2),a=n[0],s=n[1];a.setChildPWidths(!1,r[s])&&(a.invalidateBBox(),a.getBBox())}}catch(t){e={error:t}}finally{try{i&&!i.done&&(t=o.return)&&t.call(o)}finally{if(e)throw e.error}}},e.prototype.getBBoxHD=function(t){var e=y(this.getAlignmentRow(),2),r=e[0],o=e[1];if(null===o){var i=this.font.params.axis_height,n=t/2;return{top:[0,t],center:[n,n],bottom:[t,0],baseline:[n,n],axis:[n+i,n-i]}[r]||[n,n]}var a=this.getVerticalPosition(o,r);return[a,t-a]},e.prototype.getBBoxLR=function(){if(this.hasLabels){var t=this.node.attributes.get("side"),e=y(this.getPadAlignShift(t),3),r=e[0],o=e[1];return e[2],"center"===o?[r,r]:"left"===t?[r,0]:[0,r]}return[0,0]},e.prototype.getPadAlignShift=function(t){var e=this.getTableData().L+this.length2em(this.node.attributes.get("minlabelspacing")),r=y(null==this.styles?["",""]:[this.styles.get("padding-left"),this.styles.get("padding-right")],2),o=r[0],i=r[1];(o||i)&&(e=Math.max(e,this.length2em(o||"0"),this.length2em(i||"0")));var n=y(this.getAlignShift(),2),a=n[0],s=n[1];return a===t&&(s="left"===t?Math.max(e,s)-e:Math.min(-e,s)+e),[e,a,s]},e.prototype.getAlignShift=function(){return this.isTop?i.prototype.getAlignShift.call(this):[this.container.getChildAlign(this.containerI),0]},e.prototype.getWidth=function(){return this.pWidth||this.getBBox().w},e.prototype.getEqualRowHeight=function(){var t=this.getTableData(),e=t.H,r=t.D,o=Array.from(e.keys()).map(function(t){return e[t]+r[t]});return Math.max.apply(Math,o)},e.prototype.getComputedWidths=function(){var e=this,r=this.getTableData().W,t=Array.from(r.keys()).map(function(t){return"number"==typeof e.cWidths[t]?e.cWidths[t]:r[t]});return this.node.attributes.get("equalcolumns")&&(t=Array(t.length).fill(b.max(t))),t},e.prototype.getColumnWidths=function(){var t=this.node.attributes.get("width");if(this.node.attributes.get("equalcolumns"))return this.getEqualColumns(t);var e=this.getColumnAttributes("columnwidth",0);return"auto"===t?this.getColumnWidthsAuto(e):v.isPercent(t)?this.getColumnWidthsPercent(e,t):this.getColumnWidthsFixed(e,this.length2em(t))},e.prototype.getEqualColumns=function(t){var e,r=Math.max(1,this.numCols);if("auto"===t){var o=this.getTableData().W;e=b.max(o)}else if(v.isPercent(t))e=this.percent(1/r);else{var i=b.sum([].concat(this.cLines,this.cSpace))+2*this.fSpace[0];e=Math.max(0,this.length2em(t)-i)/r}return Array(this.numCols).fill(e)},e.prototype.getColumnWidthsAuto=function(t){var e=this;return t.map(function(t){return"auto"===t||"fit"===t?null:v.isPercent(t)?t:e.length2em(t)})},e.prototype.getColumnWidthsPercent=function(r,t){var o=this,i=0<=r.indexOf("fit"),n=(i?this.getTableData():{W:null}).W;return Array.from(r.keys()).map(function(t){var e=r[t];return"fit"===e?null:"auto"===e?i?n[t]:null:v.isPercent(e)?e:o.length2em(e)})},e.prototype.getColumnWidthsFixed=function(r,o){var i=this,t=Array.from(r.keys()),n=t.filter(function(t){return"fit"===r[t]}),e=t.filter(function(t){return"auto"===r[t]}),a=n.length||e.length,s=(a?this.getTableData():{W:null}).W,l=o-b.sum([].concat(this.cLines,this.cSpace))-2*this.fSpace[0],h=l;t.forEach(function(t){var e=r[t];h-="fit"===e||"auto"===e?s[t]:i.length2em(e,o)});var c=a&&0<h?h/a:0;return t.map(function(t){var e=r[t];return"fit"===e?s[t]+c:"auto"===e?s[t]+(0===n.length?c:0):i.length2em(e,l)})},e.prototype.getVerticalPosition=function(t,e){for(var r=this.node.attributes.get("equalrows"),o=this.getTableData(),i=o.H,n=o.D,a=r?this.getEqualRowHeight():0,s=this.getRowHalfSpacing(),l=this.fLine,h=0;h<t;h++)l+=s[h]+(r?a:i[h]+n[h])+s[h+1]+this.rLines[h];var c=y(r?[(a+i[t]-n[t])/2,(a-i[t]+n[t])/2]:[i[t],n[t]],2),u=c[0],p=c[1];return l+={top:0,center:s[t]+(u+p)/2,bottom:s[t]+u+p+s[t+1],baseline:s[t]+u,axis:s[t]+u-.25}[e]||0},e.prototype.getEmHalfSpacing=function(t,e){var r=this.em(t),o=this.addEm(e,2);return o.unshift(r),o.push(r),o},e.prototype.getRowHalfSpacing=function(){var t=this.rSpace.map(function(t){return t/2});return t.unshift(this.fSpace[1]),t.push(this.fSpace[1]),t},e.prototype.getColumnHalfSpacing=function(){var t=this.cSpace.map(function(t){return t/2});return t.unshift(this.fSpace[0]),t.push(this.fSpace[0]),t},e.prototype.getAlignmentRow=function(){var t=y(v.split(this.node.attributes.get("align")),2),e=t[0],r=t[1];if(null==r)return[e,null];var o=parseInt(r);return o<0&&(o+=this.numRows),[e,o<1||o>this.numRows?null:o-1]},e.prototype.getColumnAttributes=function(t,e){void 0===e&&(e=1);var r=this.numCols-e,o=this.getAttributeArray(t);if(0!==o.length){for(;o.length<r;)o.push(o[o.length-1]);return o.length>r&&o.splice(r),o}},e.prototype.getRowAttributes=function(t,e){void 0===e&&(e=1);var r=this.numRows-e,o=this.getAttributeArray(t);if(0!==o.length){for(;o.length<r;)o.push(o[o.length-1]);return o.length>r&&o.splice(r),o}},e.prototype.getAttributeArray=function(t){var e=this.node.attributes.get(t);return e?v.split(e):[this.node.attributes.getDefault(t)]},e.prototype.addEm=function(t,e){var r=this;if(void 0===e&&(e=1),t)return t.map(function(t){return r.em(t/e)})},e.prototype.convertLengths=function(t){var e=this;if(t)return t.map(function(t){return e.length2em(t)})},e;function e(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var r=i.apply(this,a(t))||this;r.numCols=0,r.numRows=0,r.data=null,r.pwidthCells=[],r.pWidth=0,r.numCols=b.max(r.tableRows.map(function(t){return t.numCells})),r.numRows=r.childNodes.length,r.hasLabels=r.childNodes.reduce(function(t,e){return t||e.node.isKind("mlabeledtr")},!1),r.findContainer(),r.isTop=!r.container||r.container.node.isKind("math")&&!r.container.parent,r.getPercentageWidth();var o=r.node.attributes;return r.frame="none"!==o.get("frame"),r.fLine=r.frame?.07:0,r.fSpace=r.frame?r.convertLengths(r.getAttributeArray("framespacing")):[0,0],r.cSpace=r.convertLengths(r.getColumnAttributes("columnspacing")),r.rSpace=r.convertLengths(r.getRowAttributes("rowspacing")),r.cLines=r.getColumnAttributes("columnlines").map(function(t){return"none"===t?0:.07}),r.rLines=r.getRowAttributes("rowlines").map(function(t){return"none"===t?0:.07}),r.cWidths=r.getColumnWidths(),r.stretchRows(),r.stretchColumns(),r}var i}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0});var n,a=r(0),s=r(12),l=r(12),h=r(96),c=(n=s.CommonMtrMixin(a.CHTMLWrapper),i(u,n),u.prototype.toCHTML=function(t){n.prototype.toCHTML.call(this,t);var e=this.node.attributes.get("rowalign");"baseline"!==e&&this.adaptor.setAttribute(this.chtml,"rowalign",e)},u.kind=h.MmlMtr.prototype.kind,u.styles={"mjx-mtr":{display:"table-row"},'mjx-mtr[rowalign="top"] > mjx-mtd':{"vertical-align":"top"},'mjx-mtr[rowalign="center"] > mjx-mtd':{"vertical-align":"middle"},'mjx-mtr[rowalign="bottom"] > mjx-mtd':{"vertical-align":"bottom"},'mjx-mtr[rowalign="baseline"] > mjx-mtd':{"vertical-align":"baseline"},'mjx-mtr[rowalign="axis"] > mjx-mtd':{"vertical-align":".25em"}},u);function u(){return null!==n&&n.apply(this,arguments)||this}e.CHTMLmtr=c;var p,d=(p=l.CommonMlabeledtrMixin(c),i(f,p),f.prototype.toCHTML=function(t){p.prototype.toCHTML.call(this,t);var e=this.adaptor.firstChild(this.chtml);if(e){this.adaptor.remove(e);var r=this.node.attributes.get("rowalign"),o="baseline"!==r&&"axis"!==r?{rowalign:r}:{},i=this.html("mjx-mtr",o,[e]);this.adaptor.append(this.parent.labels,i)}},f.kind=h.MmlMlabeledtr.prototype.kind,f.styles={"mjx-mlabeledtr":{display:"table-row"},'mjx-mlabeledtr[rowalign="top"] > mjx-mtd':{"vertical-align":"top"},'mjx-mlabeledtr[rowalign="center"] > mjx-mtd':{"vertical-align":"middle"},'mjx-mlabeledtr[rowalign="bottom"] > mjx-mtd':{"vertical-align":"bottom"},'mjx-mlabeledtr[rowalign="baseline"] > mjx-mtd':{"vertical-align":"baseline"},'mjx-mlabeledtr[rowalign="axis"] > mjx-mtd':{"vertical-align":".25em"}},f);function f(){return null!==p&&p.apply(this,arguments)||this}e.CHTMLmlabeledtr=d},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0});var n,a=r(0),s=r(61),l=r(97),h=(n=s.CommonMtdMixin(a.CHTMLWrapper),i(c,n),c.prototype.toCHTML=function(t){n.prototype.toCHTML.call(this,t);var e=this.node.attributes.get("rowalign"),r=this.node.attributes.get("columnalign");e!==this.parent.node.attributes.get("rowalign")&&this.adaptor.setAttribute(this.chtml,"rowalign",e),"center"===r||"mlabeledtr"===this.parent.kind&&this===this.parent.childNodes[0]&&r===this.parent.parent.node.attributes.get("side")||this.adaptor.setStyle(this.chtml,"textAlign",r),this.adaptor.append(this.chtml,this.html("mjx-tstrut"))},c.kind=l.MmlMtd.prototype.kind,c.styles={"mjx-mtd":{display:"table-cell","text-align":"center",padding:".215em .4em"},"mjx-mtd:first-child":{"padding-left":0},"mjx-mtd:last-child":{"padding-right":0},"mjx-mtable > * > mjx-itable > *:first-child > mjx-mtd":{"padding-top":0},"mjx-mtable > * > mjx-itable > *:last-child > mjx-mtd":{"padding-bottom":0},"mjx-tstrut":{display:"inline-block",height:"1em","vertical-align":"-.25em"},'mjx-labels[align="left"] > mjx-mtr > mjx-mtd':{"text-align":"left"},'mjx-labels[align="right"] > mjx-mtr > mjx-mtd':{"text-align":"right"},'mjx-mtr mjx-mtd[rowalign="top"], mjx-mlabeledtr mjx-mtd[rowalign="top"]':{"vertical-align":"top"},'mjx-mtr mjx-mtd[rowalign="center"], mjx-mlabeledtr mjx-mtd[rowalign="center"]':{"vertical-align":"middle"},'mjx-mtr mjx-mtd[rowalign="bottom"], mjx-mlabeledtr mjx-mtd[rowalign="bottom"]':{"vertical-align":"bottom"},'mjx-mtr mjx-mtd[rowalign="baseline"], mjx-mlabeledtr mjx-mtd[rowalign="baseline"]':{"vertical-align":"baseline"},'mjx-mtr mjx-mtd[rowalign="axis"], mjx-mlabeledtr mjx-mtd[rowalign="axis"]':{"vertical-align":".25em"}},c);function c(){return null!==n&&n.apply(this,arguments)||this}e.CHTMLmtd=h},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMtdMixin=function(t){return i(e,r=t),Object.defineProperty(e.prototype,"fixesPWidth",{get:function(){return!1},enumerable:!0,configurable:!0}),e.prototype.invalidateBBox=function(){this.bboxComputed=!1},e.prototype.getWrapWidth=function(t){var e=this.parent.parent,r=this.parent,o=this.node.childPosition()-(r.labeled?1:0);return"number"==typeof e.cWidths[o]?e.cWidths[o]:e.getTableData().W[o]},e.prototype.getChildAlign=function(t){return this.node.attributes.get("columnalign")},e;function e(){return null!==r&&r.apply(this,arguments)||this}var r}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0});var n,a=r(0),s=r(13),l=r(13),h=r(98),c=(n=s.CommonMactionMixin(a.CHTMLWrapper),i(u,n),u.prototype.toCHTML=function(t){var e=this.standardCHTMLnode(t);this.selected.toCHTML(e),this.action(this,this.data)},u.prototype.setEventHandler=function(t,e){this.chtml.addEventListener(t,e)},u.kind=h.MmlMaction.prototype.kind,u.styles={"mjx-maction":{position:"relative"},"mjx-maction > mjx-tool":{display:"none",position:"absolute",bottom:0,right:0,width:0,height:0,"z-index":500},"mjx-tool > mjx-tip":{display:"inline-block",padding:".2em",border:"1px solid #888","font-size":"70%","background-color":"#F8F8F8",color:"black","box-shadow":"2px 2px 5px #AAAAAA"},"mjx-maction[toggle]":{cursor:"pointer"},"mjx-status":{display:"block",position:"fixed",left:"1em",bottom:"1em","min-width":"25%",padding:".2em .4em",border:"1px solid #888","font-size":"90%","background-color":"#F8F8F8",color:"black"}},u.actions=new Map([["toggle",[function(t,e){t.adaptor.setAttribute(t.chtml,"toggle",t.node.attributes.get("selection"));var r=t.factory.jax.math,o=t.factory.jax.document,i=t.node;t.setEventHandler("click",function(t){r.start.node||(r.start.node=r.end.node=r.typesetRoot,r.start.n=r.end.n=0),i.nextToggleSelection(),r.rerender(o),t.stopPropagation()})},{}]],["tooltip",[function(r,o){var t=r.childNodes[1];if(t)if(t.node.isKind("mtext")){var e=t.node.getText();r.adaptor.setAttribute(r.chtml,"title",e)}else{var i=r.adaptor,n=i.append(r.chtml,r.html("mjx-tool",{style:{bottom:r.em(-r.dy),right:r.em(-r.dx)}},[r.html("mjx-tip")]));t.toCHTML(i.firstChild(n)),r.setEventHandler("mouseover",function(t){o.stopTimers(r,o);var e=setTimeout(function(){return i.setStyle(n,"display","block")},o.postDelay);o.hoverTimer.set(r,e),t.stopPropagation()}),r.setEventHandler("mouseout",function(t){o.stopTimers(r,o);var e=setTimeout(function(){return i.setStyle(n,"display","")},o.clearDelay);o.clearTimer.set(r,e),t.stopPropagation()})}},l.TooltipData]],["statusline",[function(r,o){var t=r.childNodes[1];if(t&&t.node.isKind("mtext")){var i=r.adaptor,n=t.node.getText();i.setAttribute(r.chtml,"statusline",n),r.setEventHandler("mouseover",function(t){if(null===o.status){var e=i.body(i.document);o.status=i.append(e,r.html("mjx-status",{},[r.text(n)]))}t.stopPropagation()}),r.setEventHandler("mouseout",function(t){o.status&&(i.remove(o.status),o.status=null),t.stopPropagation()})}},{status:null}]]]),u);function u(){return null!==n&&n.apply(this,arguments)||this}e.CHTMLmaction=c},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0});var n,a=r(0),s=r(64),l=r(99),h=(n=s.CommonMglyphMixin(a.CHTMLWrapper),i(c,n),c.prototype.toCHTML=function(t){var e=this.standardCHTMLnode(t),r=this.node.attributes.getList("src","alt"),o=r.src,i=r.alt,n={width:this.em(this.width),height:this.em(this.height)};this.voffset&&(n.verticalAlign=this.em(-this.voffset));var a=this.html("img",{src:o,style:n,alt:i,title:i});this.adaptor.append(e,a)},c.kind=l.MmlMglyph.prototype.kind,c.styles={"mjx-mglyph > img":{display:"inline-block",border:0,padding:0}},c);function c(){return null!==n&&n.apply(this,arguments)||this}e.CHTMLmglyph=h},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a},a=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(n(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMglyphMixin=function(t){return i(e,o=t),e.prototype.getParameters=function(){var t=this.node.attributes.getList("width","height","voffset"),e=t.width,r=t.height,o=t.voffset;this.width="auto"===e?1:this.length2em(e),this.height="auto"===r?1:this.length2em(r),this.voffset=this.length2em(o||"0")},e.prototype.computeBBox=function(t,e){void 0===e&&(e=!1),t.w=this.width,t.h=this.height-this.voffset,t.d=this.voffset},e;function e(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var r=o.apply(this,a(t))||this;return r.getParameters(),r}var o}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0});var n,a=r(0),s=r(66),l=r(100),h=r(3),c=(n=s.CommonSemanticsMixin(a.CHTMLWrapper),i(u,n),u.prototype.toCHTML=function(t){var e=this.standardCHTMLnode(t);this.childNodes.length&&this.childNodes[0].toCHTML(e)},u.kind=l.MmlSemantics.prototype.kind,u);function u(){return null!==n&&n.apply(this,arguments)||this}e.CHTMLsemantics=c;var p,d=(p=a.CHTMLWrapper,i(f,p),f.prototype.toCHTML=function(t){p.prototype.toCHTML.call(this,t)},f.prototype.computeBBox=function(){return this.bbox},f.kind=l.MmlAnnotation.prototype.kind,f);function f(){return null!==p&&p.apply(this,arguments)||this}e.CHTMLannotation=d;var m,y=(m=a.CHTMLWrapper,i(v,m),v.kind=l.MmlAnnotationXML.prototype.kind,v);function v(){return null!==m&&m.apply(this,arguments)||this}e.CHTMLannotationXML=y;var b,x=(b=a.CHTMLWrapper,i(g,b),g.prototype.toCHTML=function(t){this.adaptor.append(t,this.adaptor.clone(this.node.getXML()))},g.prototype.computeBBox=function(){return this.bbox},g.prototype.getStyles=function(){},g.prototype.getScale=function(){},g.prototype.getVariant=function(){},g.kind=h.XMLNode.prototype.kind,g.autoStyle=!1,g);function g(){return null!==b&&b.apply(this,arguments)||this}e.CHTMLxml=x},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CommonSemanticsMixin=function(t){return i(e,r=t),e.prototype.computeBBox=function(t,e){if(void 0===e&&(e=!1),this.childNodes.length){var r=this.childNodes[0].getBBox(),o=r.w,i=r.h,n=r.d;t.w=o,t.h=i,t.d=n}},e;function e(){return null!==r&&r.apply(this,arguments)||this}var r}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0});var n,a=r(0),s=r(68),l=r(101),h=r(3),c=(n=s.CommonTeXAtomMixin(a.CHTMLWrapper),i(u,n),u.prototype.toCHTML=function(t){if(n.prototype.toCHTML.call(this,t),this.node.texClass===h.TEXCLASS.VCENTER){var e=this.childNodes[0].getBBox(),r=e.h,o=(r+e.d)/2+this.font.params.axis_height-r;this.adaptor.setStyle(this.chtml,"verticalAlign",this.em(o))}},u.kind=l.TeXAtom.prototype.kind,u);function u(){return null!==n&&n.apply(this,arguments)||this}e.CHTMLTeXAtom=c},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0});var a=r(3);e.CommonTeXAtomMixin=function(t){return n(e,i=t),e.prototype.computeBBox=function(t,e){if(void 0===e&&(e=!1),i.prototype.computeBBox.call(this,t,e),this.childNodes[0]&&this.childNodes[0].bbox.ic&&(t.ic=this.childNodes[0].bbox.ic),this.node.texClass===a.TEXCLASS.VCENTER){var r=t.h,o=(r+t.d)/2+this.font.params.axis_height-r;t.h+=o,t.d-=o}},e;function e(){return null!==i&&i.apply(this,arguments)||this}var i}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),f=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0});var n,a=r(3),s=r(0),l=r(70),h=(n=l.CommonTextNodeMixin(s.CHTMLWrapper),i(c,n),c.prototype.toCHTML=function(t){var e,r;this.markUsed();var o=this.adaptor,i=this.parent.variant,n=this.node.getText();if("-explicitFont"===i){var a=this.jax.getFontData(this.parent.styles);o.append(t,this.jax.unknownText(n,i,a))}else{var s=this.parent.stretch.c,l=this.parent.remapChars(s?[s]:this.unicodeChars(n));try{for(var h=f(l),c=h.next();!c.done;c=h.next()){var u=c.value,p=this.getVariantChar(i,u)[3],d=p.unknown?this.jax.unknownText(String.fromCharCode(u),i):this.html("mjx-c",{class:this.char(u)});o.append(t,d),p.used=!0}}catch(t){e={error:t}}finally{try{c&&!c.done&&(r=h.return)&&r.call(h)}finally{if(e)throw e.error}}}},c.kind=a.TextNode.prototype.kind,c.autoStyle=!1,c.styles={"mjx-c":{display:"inline-block"},"mjx-utext":{display:"inline-block",padding:".75em 0 .25em 0"},"mjx-measure-text":{position:"absolute","font-family":"MJXZERO","white-space":"nowrap",height:"1px",width:"1px",overflow:"hidden"}},c);function c(){return null!==n&&n.apply(this,arguments)||this}e.CHTMLTextNode=h},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),x=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},g=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonTextNodeMixin=function(t){return i(e,r=t),e.prototype.computeBBox=function(t,e){var r,o;void 0===e&&(e=!1);var i=this.parent.variant,n=this.node.getText();if("-explicitFont"===i){var a=this.jax.getFontData(this.parent.styles),s=this.jax.measureText(n,i,a),l=s.w,h=s.h,c=s.d;t.h=h,t.d=c,t.w=l}else{var u=this.parent.stretch.c,p=this.parent.remapChars(u?[u]:this.unicodeChars(n));t.empty();try{for(var d=x(p),f=d.next();!f.done;f=d.next()){var m=f.value,y=g(this.getVariantChar(i,m),4),v=(h=y[0],c=y[1],l=y[2],y[3]);if(v.unknown){var b=this.jax.measureText(String.fromCharCode(m),i);l=b.w,h=b.h,c=b.d}t.w+=l,h>t.h&&(t.h=h),c>t.d&&(t.d=c),t.ic=v.ic||0,t.sk=v.sk||0}}catch(t){r={error:t}}finally{try{f&&!f.done&&(o=d.return)&&o.call(d)}finally{if(r)throw r.error}}1<p.length&&(t.sk=0),t.clean()}},e.prototype.getStyles=function(){},e.prototype.getVariant=function(){},e.prototype.getScale=function(){},e.prototype.getSpace=function(){},e;function e(){return null!==r&&r.apply(this,arguments)||this}var r}},function(t,r,e){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),h=this&&this.__assign||function(){return(h=Object.assign||function(t){for(var e,r=1,o=arguments.length;r<o;r++)for(var i in e=arguments[r])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)},y=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},v=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a};Object.defineProperty(r,"__esModule",{value:!0});var l=e(5),n=e(2),c=e(17);!function(t){for(var e in t)r.hasOwnProperty(e)||(r[e]=t[e])}(e(5));var u,a=(u=l.FontData,i(s,u),s.prototype.adaptiveCSS=function(t){this.options.adaptiveCSS=t},s.prototype.clearCache=function(){var e,t,r,o,i,n;if(this.options.adaptiveCSS){try{for(var a=y(Object.keys(this.delimiters)),s=a.next();!s.done;s=a.next()){var l=s.value;this.delimiters[parseInt(l)].used=!1}}catch(t){e={error:t}}finally{try{s&&!s.done&&(t=a.return)&&t.call(a)}finally{if(e)throw e.error}}try{for(var h=y(Object.keys(this.variant)),c=h.next();!c.done;c=h.next()){var u=c.value,p=this.variant[u].chars;try{for(var d=(i=void 0,y(Object.keys(p))),f=d.next();!f.done;f=d.next()){l=f.value;var m=p[parseInt(l)][3];m&&(m.used=!1)}}catch(t){i={error:t}}finally{try{f&&!f.done&&(n=d.return)&&n.call(d)}finally{if(i)throw i.error}}}}catch(t){r={error:t}}finally{try{c&&!c.done&&(o=h.return)&&o.call(h)}finally{if(r)throw r.error}}}},Object.defineProperty(s.prototype,"styles",{get:function(){var e,t,r=this.constructor,o=h({},r.defaultStyles);this.addFontURLs(o,r.defaultFonts,this.options.fontURL);try{for(var i=y(Object.keys(this.delimiters)),n=i.next();!n.done;n=i.next()){var a=n.value,s=parseInt(a);this.addDelimiterStyles(o,s,this.delimiters[s])}}catch(t){e={error:t}}finally{try{n&&!n.done&&(t=i.return)&&t.call(i)}finally{if(e)throw e.error}}return this.addVariantChars(o),o},enumerable:!0,configurable:!0}),s.prototype.addVariantChars=function(t){var e,r,o,i,n=new Map;try{for(var a=y(Object.keys(this.variant)),s=a.next();!s.done;s=a.next()){var l=s.value,h=this.variant[l],c="normal"===l?"":"."+h.classes.replace(/ /g,".");try{for(var u=(o=void 0,y(Object.keys(h.chars))),p=u.next();!p.done;p=u.next()){var d=p.value,f=parseInt(d);4===h.chars[f].length&&this.addCharStyles(t,c,f,h.chars[f],n)}}catch(t){o={error:t}}finally{try{p&&!p.done&&(i=u.return)&&i.call(u)}finally{if(o)throw o.error}}}}catch(t){e={error:t}}finally{try{s&&!s.done&&(r=a.return)&&r.call(a)}finally{if(e)throw e.error}}},s.prototype.addFontURLs=function(t,e,r){var o,i;try{for(var n=y(Object.keys(e)),a=n.next();!a.done;a=n.next()){var s=a.value,l=h({},e[s]);l.src=l.src.replace(/%%URL%%/,r),t[s]=l}}catch(t){o={error:t}}finally{try{a&&!a.done&&(i=n.return)&&i.call(n)}finally{if(o)throw o.error}}},s.prototype.addDelimiterStyles=function(t,e,r){if(!this.options.adaptiveCSS||r.used){var o=this.charSelector(e);r.c&&r.c!==e&&(t[this.cssRoot+".mjx-stretched mjx-c"+o+"::before"]={content:this.charContent(r.c)}),r.stretch&&(1===r.dir?this.addDelimiterVStyles(t,o,r):this.addDelimiterHStyles(t,o,r))}},s.prototype.addDelimiterVStyles=function(t,e,r){var o=r.HDW[2],i=v(r.stretch,4),n=i[0],a=i[1],s=i[2],l=i[3],h=this.addDelimiterVPart(t,e,o,"beg",n);this.addDelimiterVPart(t,e,o,"ext",a);var c=this.addDelimiterVPart(t,e,o,"end",s),u={},p=this.cssRoot;if(l){var d=this.addDelimiterVPart(t,e,o,"mid",l);u.height="50%",t[p+"mjx-stretchy-v"+e+" > mjx-mid"]={"margin-top":this.em(-d/2),"margin-bottom":this.em(-d/2)}}h&&(u["border-top-width"]=this.em0(h-.03)),c&&(u["border-bottom-width"]=this.em0(c-.03),t[p+"mjx-stretchy-v"+e+" > mjx-end"]={"margin-top":this.em(-c)}),Object.keys(u).length&&(t[p+"mjx-stretchy-v"+e+" > mjx-ext"]=u)},s.prototype.addDelimiterVPart=function(t,e,r,o,i){if(!i)return 0;var n=this.getDelimiterData(i),a=(r-n[2])/2,s={content:this.charContent(i)};return"ext"!==o?s.padding=this.padding(n,a):a&&(s["padding-left"]=this.em0(a)),t[this.cssRoot+"mjx-stretchy-v"+e+" mjx-"+o+" mjx-c::before"]=s,n[0]+n[1]},s.prototype.addDelimiterHStyles=function(t,e,r){var o=v(r.stretch,4),i=o[0],n=o[1],a=o[2],s=o[3];this.addDelimiterHPart(t,e,"beg",i),this.addDelimiterHPart(t,e,"ext",n,!(i||a)),this.addDelimiterHPart(t,e,"end",a),s&&(this.addDelimiterHPart(t,e,"mid",s),t[this.cssRoot+"mjx-stretchy-h"+e+" > mjx-ext"]={width:"50%"})},s.prototype.addDelimiterHPart=function(t,e,r,o,i){if(void 0===i&&(i=!1),!o)return 0;var n=this.getDelimiterData(o),a=n[3],s={content:a&&a.c?'"'+a.c+'"':this.charContent(o)};"ext"===r&&!i||(s.padding=this.padding(n,0,-n[2])),t[this.cssRoot+"mjx-stretchy-h"+e+" mjx-"+r+" mjx-c::before"]=s},s.prototype.addCharStyles=function(t,e,r,o,i){var n=v(o,4),a=(n[0],n[1],n[2]),s=n[3];if(!this.options.adaptiveCSS||s.used){var l={},h="mjx-c"+this.charSelector(r),c=this.cssRoot;l.padding=this.padding(o,0,s.ic||0);var u=s.c?'"'+s.c+'"':this.charContent(r);i.get(r)!==u&&(i.has(r)||s.c?t[c+e+" "+h+"::before"]={content:u}:(t[c+h+"::before"]={content:u},i.set(r,u))),void 0!==s.f&&(l["font-family"]="MJXZERO, MJXTEX"+(s.f?"-"+s.f:""));var p=(e?e+" ":"")+h;if(t[c+p]=l,s.ic){var d=v([c+"mjx-","[noIC]"+p+":last-child"],2),f=d[0],m=d[1];t[f+"mi"+m]=t[f+"mo"+m]={"padding-right":this.em(a)}}}},s.prototype.getDelimiterData=function(t){return this.getChar("-smallop",t)},s.charOptions=function(t,e){return u.charOptions.call(this,t,e)},s.prototype.em=function(t){return n.em(t)},s.prototype.em0=function(t){return n.em(Math.max(0,t))},s.prototype.padding=function(t,e,r){var o=v(t,3),i=o[0],n=o[1];return void 0===e&&(e=0),void 0===r&&(r=0),[i,o[2]+r,n,e].map(this.em0).join(" ")},s.prototype.charContent=function(t){return'"'+(32<=t&&t<=126&&34!==t&&39!==t&&92!==t?String.fromCharCode(t):"\\"+t.toString(16).toUpperCase())+'"'},s.prototype.charSelector=function(t){return".mjx-c"+t.toString(16).toUpperCase()},s.OPTIONS={fontURL:"js/output/chtml/fonts/tex-woff-v2"},s.defaultVariantClasses={},s.defaultStyles={"mjx-c::before":{display:"inline-block",width:0}},s.defaultFonts={"@font-face /* 0 */":{"font-family":"MJXZERO",src:'url("%%URL%%/MathJax_Zero.woff") format("woff")'}},s);function s(t){var e,r;void 0===t&&(t=null);var o=u.call(this)||this;o.cssRoot="";var i=o.constructor;o.options=c.userOptions(c.defaultOptions({},i.OPTIONS),t);try{for(var n=y(Object.keys(i.defaultVariantClasses)),a=n.next();!a.done;a=n.next()){var s=a.value;o.variant[s].classes=i.defaultVariantClasses[s]}}catch(t){e={error:t}}finally{try{a&&!a.done&&(r=n.return)&&r.call(n)}finally{if(e)throw e.error}}return o}r.CHTMLFontData=a,r.AddCSS=function(t,e){var r,o;try{for(var i=y(Object.keys(e)),n=i.next();!n.done;n=i.next()){var a=n.value,s=parseInt(a);Object.assign(l.FontData.charOptions(t,s),e[s])}}catch(t){r={error:t}}finally{try{n&&!n.done&&(o=i.return)&&o.call(i)}finally{if(r)throw r.error}}return t}},function(t,e,r){"use strict";r(73);var o=r(14),i=r(15);MathJax.loader&&(0,o.combineDefaults)(MathJax.config.loader,"output/chtml",{checkReady:function(){return MathJax.loader.load("output/chtml/fonts/tex")}}),MathJax.startup&&(MathJax.startup.registerConstructor("chtml",i.CHTML),MathJax.startup.useOutput("chtml"))},function(t,e,r){"use strict";var o=r(14),i=mt(r(15)),n=mt(r(71)),a=mt(r(43)),s=mt(r(0)),l=mt(r(20)),h=mt(r(22)),c=mt(r(67)),u=mt(r(69)),p=mt(r(62)),d=mt(r(25)),f=mt(r(41)),m=mt(r(45)),y=mt(r(47)),v=mt(r(63)),b=mt(r(27)),x=mt(r(55)),g=mt(r(31)),_=mt(r(29)),M=mt(r(39)),w=mt(r(50)),j=mt(r(44)),C=mt(r(33)),S=mt(r(37)),O=mt(r(11)),T=mt(r(7)),B=mt(r(57)),P=mt(r(60)),L=mt(r(35)),H=mt(r(59)),k=mt(r(54)),A=mt(r(52)),N=mt(r(65)),D=mt(r(1)),W=mt(r(18)),R=mt(r(5)),E=mt(r(6)),I=mt(r(16)),J=mt(r(23)),F=mt(r(21)),V=mt(r(68)),z=mt(r(70)),X=mt(r(13)),q=mt(r(26)),K=mt(r(42)),U=mt(r(46)),G=mt(r(48)),Q=mt(r(64)),Y=mt(r(28)),Z=mt(r(56)),$=mt(r(32)),tt=mt(r(30)),et=mt(r(40)),rt=mt(r(51)),ot=mt(r(10)),it=mt(r(34)),nt=mt(r(38)),at=mt(r(49)),st=mt(r(8)),lt=mt(r(58)),ht=mt(r(61)),ct=mt(r(36)),ut=mt(r(12)),pt=mt(r(9)),dt=mt(r(53)),ft=mt(r(66));function mt(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e.default=t,e}(0,o.combineWithMathJax)({_:{output:{chtml_ts:i,chtml:{FontData:n,Notation:a,Wrapper:s,WrapperFactory:l,Wrappers_ts:h,Wrappers:{TeXAtom:c,TextNode:u,maction:p,math:d,menclose:f,mfenced:m,mfrac:y,mglyph:v,mi:b,mmultiscripts:x,mn:g,mo:_,mpadded:M,mroot:w,mrow:j,ms:C,mspace:S,msqrt:O,msubsup:T,mtable:B,mtd:P,mtext:L,mtr:H,munderover:k,scriptbase:A,semantics:N}},common:{BBox:D,CssStyles:W,FontData:R,Notation:E,OutputJax:I,Wrapper:J,WrapperFactory:F,Wrappers:{TeXAtom:V,TextNode:z,maction:X,math:q,menclose:K,mfenced:U,mfrac:G,mglyph:Q,mi:Y,mmultiscripts:Z,mn:$,mo:tt,mpadded:et,mroot:rt,mrow:ot,ms:it,mspace:nt,msqrt:at,msubsup:st,mtable:lt,mtd:ht,mtext:ct,mtr:ut,munderover:pt,scriptbase:dt,semantics:ft}}}}})},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AbstractOutputJax=MathJax._.core.OutputJax.AbstractOutputJax},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AbstractWrapperFactory=MathJax._.core.Tree.WrapperFactory.AbstractWrapperFactory},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AbstractWrapper=MathJax._.core.Tree.Wrapper.AbstractWrapper},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMath=MathJax._.core.MmlTree.MmlNodes.math.MmlMath},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMi=MathJax._.core.MmlTree.MmlNodes.mi.MmlMi},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMo=MathJax._.core.MmlTree.MmlNodes.mo.MmlMo},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMn=MathJax._.core.MmlTree.MmlNodes.mn.MmlMn},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMs=MathJax._.core.MmlTree.MmlNodes.ms.MmlMs},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMtext=MathJax._.core.MmlTree.MmlNodes.mtext.MmlMtext},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMspace=MathJax._.core.MmlTree.MmlNodes.mspace.MmlMspace},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMpadded=MathJax._.core.MmlTree.MmlNodes.mpadded.MmlMpadded},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMenclose=MathJax._.core.MmlTree.MmlNodes.menclose.MmlMenclose},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMrow=MathJax._.core.MmlTree.MmlNodes.mrow.MmlMrow,e.MmlInferredMrow=MathJax._.core.MmlTree.MmlNodes.mrow.MmlInferredMrow},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMfenced=MathJax._.core.MmlTree.MmlNodes.mfenced.MmlMfenced},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMfrac=MathJax._.core.MmlTree.MmlNodes.mfrac.MmlMfrac},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMsqrt=MathJax._.core.MmlTree.MmlNodes.msqrt.MmlMsqrt},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMroot=MathJax._.core.MmlTree.MmlNodes.mroot.MmlMroot},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMsubsup=MathJax._.core.MmlTree.MmlNodes.msubsup.MmlMsubsup,e.MmlMsub=MathJax._.core.MmlTree.MmlNodes.msubsup.MmlMsub,e.MmlMsup=MathJax._.core.MmlTree.MmlNodes.msubsup.MmlMsup},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMunderover=MathJax._.core.MmlTree.MmlNodes.munderover.MmlMunderover,e.MmlMunder=MathJax._.core.MmlTree.MmlNodes.munderover.MmlMunder,e.MmlMover=MathJax._.core.MmlTree.MmlNodes.munderover.MmlMover},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMmultiscripts=MathJax._.core.MmlTree.MmlNodes.mmultiscripts.MmlMmultiscripts,e.MmlMprescripts=MathJax._.core.MmlTree.MmlNodes.mmultiscripts.MmlMprescripts,e.MmlNone=MathJax._.core.MmlTree.MmlNodes.mmultiscripts.MmlNone},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.sum=MathJax._.util.numeric.sum,e.max=MathJax._.util.numeric.max},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMtable=MathJax._.core.MmlTree.MmlNodes.mtable.MmlMtable},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMtr=MathJax._.core.MmlTree.MmlNodes.mtr.MmlMtr,e.MmlMlabeledtr=MathJax._.core.MmlTree.MmlNodes.mtr.MmlMlabeledtr},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMtd=MathJax._.core.MmlTree.MmlNodes.mtd.MmlMtd},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMaction=MathJax._.core.MmlTree.MmlNodes.maction.MmlMaction},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMglyph=MathJax._.core.MmlTree.MmlNodes.mglyph.MmlMglyph},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlSemantics=MathJax._.core.MmlTree.MmlNodes.semantics.MmlSemantics,e.MmlAnnotationXML=MathJax._.core.MmlTree.MmlNodes.semantics.MmlAnnotationXML,e.MmlAnnotation=MathJax._.core.MmlTree.MmlNodes.semantics.MmlAnnotation},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.TeXAtom=MathJax._.core.MmlTree.MmlNodes.TeXAtom.TeXAtom},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.TeXFont=void 0;var o=r(71);function i(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,i),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(i.__proto__||Object.getPrototypeOf(i)).apply(this,arguments))}(e.TeXFont=(function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(i,o.FontData),i)).OPTIONS={fontURL:"."}}]);