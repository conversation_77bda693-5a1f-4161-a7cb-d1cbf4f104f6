!function(s){var i={};function t(c){if(i[c])return i[c].exports;var f=i[c]={i:c,l:!1,exports:{}};return s[c].call(f.exports,f,f.exports,t),f.l=!0,f.exports}t.m=s,t.c=i,t.d=function(c,f,s){t.o(c,f)||Object.defineProperty(c,f,{enumerable:!0,get:s})},t.r=function(c){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(c,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(c,"__esModule",{value:!0})},t.t=function(f,c){if(1&c&&(f=t(f)),8&c)return f;if(4&c&&"object"==typeof f&&f&&f.__esModule)return f;var s=Object.create(null);if(t.r(s),Object.defineProperty(s,"default",{enumerable:!0,value:f}),2&c&&"string"!=typeof f)for(var i in f)t.d(s,i,function(c){return f[c]}.bind(null,i));return s},t.n=function(c){var f=c&&c.__esModule?function(){return c.default}:function(){return c};return t.d(f,"a",f),f},t.o=function(c,f){return Object.prototype.hasOwnProperty.call(c,f)},t.p="",t(t.s=52)}([function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0}),f.CHTMLFontData=MathJax._.output.chtml.FontData.CHTMLFontData,f.AddCSS=MathJax._.output.chtml.FontData.AddCSS},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0}),f.combineConfig=MathJax._.components.global.combineConfig,f.combineDefaults=MathJax._.components.global.combineDefaults,f.combineWithMathJax=MathJax._.components.global.combineWithMathJax,f.MathJax=MathJax._.components.global.MathJax},function(c,f,s){"use strict";var i,t=this&&this.__extends||(i=function(c,f){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(c,f){c.__proto__=f}||function(c,f){for(var s in f)f.hasOwnProperty(s)&&(c[s]=f[s])})(c,f)},function(c,f){function s(){this.constructor=c}i(c,f),c.prototype=null===f?Object.create(f):(s.prototype=f.prototype,new s)}),e=this&&this.__assign||function(){return(e=Object.assign||function(c){for(var f,s=1,i=arguments.length;s<i;s++)for(var t in f=arguments[s])Object.prototype.hasOwnProperty.call(f,t)&&(c[t]=f[t]);return c}).apply(this,arguments)};Object.defineProperty(f,"__esModule",{value:!0});var k,S=s(0),r=s(54),B=s(3),o=s(5),a=s(7),I=s(9),n=s(11),l=s(13),u=s(15),d=s(17),X=s(19),M=s(21),E=s(23),m=s(25),T=s(27),J=s(29),p=s(31),x=s(33),h=s(35),C=s(37),H=s(39),A=s(41),R=s(43),F=s(45),_=s(47),y=s(49),D=s(51),O=(k=r.CommonTeXFontMixin(S.CHTMLFontData),t(b,k),b.defaultVariantClasses={normal:"mjx-n",bold:"mjx-b",italic:"mjx-i","bold-italic":"mjx-b mjx-i","double-struck":"mjx-ds",fraktur:"mjx-fr","bold-fraktur":"mjx-fr mjx-b",script:"mjx-sc","bold-script":"mjx-sc mjx-b","sans-serif":"mjx-ss","bold-sans-serif":"mjx-ss mjx-b","sans-serif-italic":"mjx-ss mjx-i","bold-sans-serif-italic":"mjx-ss mjx-b mjx-i",monospace:"mjx-ty","-smallop":"mjx-sop","-largeop":"mjx-lop","-size3":"mjx-s3","-size4":"mjx-s4","-tex-calligraphic":"mjx-cal","-tex-bold-calligraphic":"mjx-cal mjx-b","-tex-mathit":"mjx-mit","-tex-oldstyle":"mjx-os","-tex-bold-oldstyle":"mjx-os mjx-b","-tex-variant":"mjx-v"},b.defaultDelimiters=D.delimiters,b.defaultChars={normal:X.normal,bold:o.bold,italic:l.italic,"bold-italic":B.boldItalic,"double-struck":a.doubleStruck,fraktur:n.fraktur,"bold-fraktur":I.frakturBold,script:p.script,"bold-script":J.scriptBold,"sans-serif":T.sansSerif,"bold-sans-serif":E.sansSerifBold,"sans-serif-italic":m.sansSerifItalic,"bold-sans-serif-italic":M.sansSerifBoldItalic,monospace:d.monospace,"-smallop":x.smallop,"-largeop":u.largeop,"-size3":F.texSize3,"-size4":_.texSize4,"-tex-calligraphic":C.texCalligraphic,"-tex-bold-calligraphic":h.texCalligraphicBold,"-tex-mathit":H.texMathit,"-tex-oldstyle":R.texOldstyle,"-tex-bold-oldstyle":A.texOldstyleBold,"-tex-variant":y.texVariant},b.defaultStyles=e(e({},S.CHTMLFontData.defaultStyles),{".mjx-n mjx-c":{"font-family":"MJXZERO, MJXTEX, MJXTEX-I, MJXTEX-S1, MJXTEX-A"},".mjx-i mjx-c":{"font-family":"MJXZERO, MJXTEX-I, MJXTEX, MJXTEX-S1, MJXTEX-A"},".mjx-b mjx-c":{"font-family":"MJXZERO, MJXTEX-B, MJXTEX-BI, MJXTEX, MJXTEX-I, MJXTEX-S1, MJXTEX-A"},".mjx-b.mjx-i mjx-c":{"font-family":"MJXZERO, MJXTEX-BI, MJXTEX-B, MJXTEX-I, MJXTEX, MJXTEX-S1, MJXTEX-A"},".mjx-cal mjx-c":{"font-family":"MJXZERO, MJXTEX-C, MJXTEX-I, MJXTEX, MJXTEX-S1, MJXTEX-A"},".mjx-cal.mjx-b mjx-c":{"font-family":"MJXZERO, MJXTEX-C-B, MJXTEX-C, MJXTEX-B, MJXTEX-BI, MJXTEX, MJXTEX-S1, MJXTEX-A"},".mjx-ds mjx-c":{"font-family":"MJXZERO, MJXTEX-A, MJXTEX-B, MJXTEX-BI, MJXTEX, MJXTEX-I, MJXTEX-S1"},".mjx-fr mjx-c":{"font-family":"MJXZERO, MJXTEX-FR, MJXTEX, MJXTEX-I, MJXTEX-S1, MJXTEX-A"},".mjx-fr.mjx-b mjx-c":{"font-family":"MJXZERO, MJXTEX-FR-B, MJXTEX-FR, MJXTEX-B, MJXTEX-BI, MJXTEX, MJXTEX-I, MJXTEX-S1, MJXTEX-A"},".mjx-sc mjx-c":{"font-family":"MJXZERO, MJXTEX-SC, MJXTEX, MJXTEX-I, MJXTEX-S1, MJXTEX-A"},".mjx-sc.mjx-b mjx-c":{"font-family":"MJXZERO, MJXTEX-SC-B, MJXTEX-SC, MJXTEX-B, MJXTEX-BI, MJXTEX, MJXTEX-I, MJXTEX-S1, MJXTEX-A"},".mjx-ss mjx-c":{"font-family":"MJXZERO, MJXTEX-SS, MJXTEX, MJXTEX-I, MJXTEX-S1, MJXTEX-A"},".mjx-ss.mjx-b mjx-c":{"font-family":"MJXZERO, MJXTEX-SS-B, MJXTEX-SS, MJXTEX-B, MJXTEX-BI, MJXTEX, MJXTEX-I, MJXTEX-S1, MJXTEX-A"},".mjx-ss.mjx-i mjx-c":{"font-family":"MJXZERO, MJXTEX-SS-I, MJXTEX-I, MJXTEX, MJXTEX-S1, MJXTEX-A"},".mjx-ss.mjx-b.mjx-i mjx-c":{"font-family":"MJXZERO, MJXTEX-SS-B, MJXTEX-SS-I, MJXTEX-BI, MJXTEX-B, MJXTEX-I, MJXTEX, MJXTEX-S1, MJXTEX-A"},".mjx-ty mjx-c":{"font-family":"MJXZERO, MJXTEX-T, MJXTEX, MJXTEX-I, MJXTEX-S1, MJXTEX-A"},".mjx-var mjx-c":{"font-family":"MJXZERO, MJXTEX-A, MJXTEX, MJXTEX-I, MJXTEX-S1"},".mjx-os mjx-c":{"font-family":"MJXZERO, MJXTEX-C, MJXTEX, MJXTEX-I, MJXTEX-S1, MJXTEX-A"},".mjx-os.mjx-b mjx-c":{"font-family":"MJXZERO, MJXTEX-C-B, MJXTEX-C, MJXTEX-B, MJXTEX-BI, MJXTEX, MJXTEX-I, MJXTEX-S1, MJXTEX-A"},".mjx-mit mjx-c":{"font-family":"MJXZERO, MJXTEX-MI, MJXTEX-I, MJXTEX, MJXTEX-S1, MJXTEX-A"},".mjx-lop mjx-c":{"font-family":"MJXZERO, MJXTEX-S2, MJXTEX-S1, MJXTEX, MJXTEX-I, MJXTEX-A"},".mjx-sop mjx-c":{"font-family":"MJXZERO, MJXTEX-S1, MJXTEX, MJXTEX-I, MJXTEX-A"},".mjx-s3 mjx-c":{"font-family":"MJXZERO, MJXTEX-S3, MJXTEX, MJXTEX-I, MJXTEX-S1, MJXTEX-A"},".mjx-s4 mjx-c":{"font-family":"MJXZERO, MJXTEX-S4, MJXTEX, MJXTEX-I, MJXTEX-S1, MJXTEX-A"},".MJX-TEX":{"font-family":"MJXZERO"},"mjx-stretchy-v mjx-c, mjx-stretchy-h mjx-c":{"font-family":"MJXZERO, MJXTEX-S1, MJXTEX-S4, MJXTEX, MJXTEX-A ! important"}}),b.defaultFonts=e(e({},S.CHTMLFontData.defaultFonts),{"@font-face /* 1 */":{"font-family":"MJXTEX",src:'url("%%URL%%/MathJax_Main-Regular.woff") format("woff")'},"@font-face /* 2 */":{"font-family":"MJXTEX-B",src:'url("%%URL%%/MathJax_Main-Bold.woff") format("woff")'},"@font-face /* 3 */":{"font-family":"MJXTEX-MI",src:'url("%%URL%%/MathJax_Main-Italic.woff") format("woff")'},"@font-face /* 4 */":{"font-family":"MJXTEX-I",src:'url("%%URL%%/MathJax_Math-Italic.woff") format("woff")'},"@font-face /* 5 */":{"font-family":"MJXTEX-BI",src:'url("%%URL%%/MathJax_Math-BoldItalic.woff") format("woff")'},"@font-face /* 6 */":{"font-family":"MJXTEX-S1",src:'url("%%URL%%/MathJax_Size1-Regular.woff") format("woff")'},"@font-face /* 7 */":{"font-family":"MJXTEX-S2",src:'url("%%URL%%/MathJax_Size2-Regular.woff") format("woff")'},"@font-face /* 8 */":{"font-family":"MJXTEX-S3",src:'url("%%URL%%/MathJax_Size3-Regular.woff") format("woff")'},"@font-face /* 9 */":{"font-family":"MJXTEX-S4",src:'url("%%URL%%/MathJax_Size4-Regular.woff") format("woff")'},"@font-face /* 10 */":{"font-family":"MJXTEX-A",src:'url("%%URL%%/MathJax_AMS-Regular.woff") format("woff")'},"@font-face /* 11 */":{"font-family":"MJXTEX-C",src:'url("%%URL%%/MathJax_Calligraphic-Regular.woff") format("woff")'},"@font-face /* 12 */":{"font-family":"MJXTEX-C-B",src:'url("%%URL%%/MathJax_Calligraphic-Bold.woff") format("woff")'},"@font-face /* 13 */":{"font-family":"MJXTEX-FR",src:'url("%%URL%%/MathJax_Fraktur-Regular.woff") format("woff")'},"@font-face /* 14 */":{"font-family":"MJXTEX-FR-B",src:'url("%%URL%%/MathJax_Fraktur-Bold.woff") format("woff")'},"@font-face /* 15 */":{"font-family":"MJXTEX-SS",src:'url("%%URL%%/MathJax_SansSerif-Regular.woff") format("woff")'},"@font-face /* 16 */":{"font-family":"MJXTEX-SS-B",src:'url("%%URL%%/MathJax_SansSerif-Bold.woff") format("woff")'},"@font-face /* 17 */":{"font-family":"MJXTEX-SS-I",src:'url("%%URL%%/MathJax_SansSerif-Italic.woff") format("woff")'},"@font-face /* 18 */":{"font-family":"MJXTEX-SC",src:'url("%%URL%%/MathJax_Script-Regular.woff") format("woff")'},"@font-face /* 19 */":{"font-family":"MJXTEX-T",src:'url("%%URL%%/MathJax_Typewriter-Regular.woff") format("woff")'},"@font-face /* 20 */":{"font-family":"MJXTEX-V",src:'url("%%URL%%/MathJax_Vector-Regular.woff") format("woff")'},"@font-face /* 21 */":{"font-family":"MJXTEX-VB",src:'url("%%URL%%/MathJax_Vector-Bold.woff") format("woff")'}}),b);function b(){return null!==k&&k.apply(this,arguments)||this}f.TeXFont=O},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0});var i=s(0),t=s(4);f.boldItalic=i.AddCSS(t.boldItalic,{32:{c:" "},47:{c:"/"},65:{c:"A"},66:{c:"B"},67:{c:"C"},68:{c:"D"},69:{c:"E"},70:{c:"F"},71:{c:"G"},72:{c:"H"},73:{c:"I"},74:{c:"J"},75:{c:"K"},76:{c:"L"},77:{c:"M"},78:{c:"N"},79:{c:"O"},80:{c:"P"},81:{c:"Q"},82:{c:"R"},83:{c:"S"},84:{c:"T"},85:{c:"U"},86:{c:"V"},87:{c:"W"},88:{c:"X"},89:{c:"Y"},90:{c:"Z"},97:{c:"a"},98:{c:"b"},99:{c:"c"},100:{c:"d"},101:{c:"e"},102:{c:"f"},103:{c:"g"},104:{c:"h"},105:{c:"i"},106:{c:"j"},107:{c:"k"},108:{c:"l"},109:{c:"m"},110:{c:"n"},111:{c:"o"},112:{c:"p"},113:{c:"q"},114:{c:"r"},115:{c:"s"},116:{c:"t"},117:{c:"u"},118:{c:"v"},119:{c:"w"},120:{c:"x"},121:{c:"y"},122:{c:"z"},913:{c:"A"},914:{c:"B"},917:{c:"E"},918:{c:"Z"},919:{c:"H"},921:{c:"I"},922:{c:"K"},924:{c:"M"},925:{c:"N"},927:{c:"O"},929:{c:"P"},930:{c:"\\398"},932:{c:"T"},935:{c:"X"},978:{c:"\\3A5"},988:{c:"F"},8260:{c:"/"},8710:{c:"\\394"},10744:{c:"/",f:"BI"}})},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0}),f.boldItalic={32:[0,0,.25],47:[.711,.21,.894],65:[.711,0,.869,{sk:.16}],66:[.686,0,.866,{sk:.0958}],67:[.703,.017,.817,{sk:.0958}],68:[.686,0,.938,{sk:.0639}],69:[.68,0,.81,{sk:.0958}],70:[.68,0,.689,{ic:.12,sk:.0958}],71:[.703,.016,.887,{sk:.0958}],72:[.686,0,.982,{sk:.0639}],73:[.686,0,.511,{ic:.062,sk:.128}],74:[.686,.017,.631,{ic:.063,sk:.192}],75:[.686,0,.971,{sk:.0639}],76:[.686,0,.756,{sk:.0319}],77:[.686,0,1.142,{ic:.077,sk:.0958}],78:[.686,0,.95,{ic:.077,sk:.0958}],79:[.703,.017,.837,{sk:.0958}],80:[.686,0,.723,{ic:.124,sk:.0958}],81:[.703,.194,.869,{sk:.0958}],82:[.686,.017,.872,{sk:.0958}],83:[.703,.017,.693,{sk:.0958}],84:[.675,0,.637,{ic:.135,sk:.0958}],85:[.686,.016,.8,{ic:.077,sk:.0319}],86:[.686,.016,.678,{ic:.208}],87:[.686,.017,1.093,{ic:.114}],88:[.686,0,.947,{sk:.0958}],89:[.686,0,.675,{ic:.201}],90:[.686,0,.773,{sk:.0958}],97:[.452,.008,.633],98:[.694,.008,.521],99:[.451,.008,.513,{sk:.0639}],100:[.694,.008,.61,{sk:.192}],101:[.452,.008,.554,{sk:.0639}],102:[.701,.201,.568,{ic:.056,sk:.192}],103:[.452,.202,.545,{sk:.0319}],104:[.694,.008,.668,{sk:-.0319}],105:[.694,.008,.405],106:[.694,.202,.471],107:[.694,.008,.604],108:[.694,.008,.348,{sk:.0958}],109:[.452,.008,1.032],110:[.452,.008,.713],111:[.452,.008,.585,{sk:.0639}],112:[.452,.194,.601,{sk:.0958}],113:[.452,.194,.542,{sk:.0958}],114:[.452,.008,.529,{sk:.0639}],115:[.451,.008,.531,{sk:.0639}],116:[.643,.007,.415,{sk:.0958}],117:[.452,.008,.681,{sk:.0319}],118:[.453,.008,.567,{sk:.0319}],119:[.453,.008,.831,{sk:.0958}],120:[.452,.008,.659,{sk:.0319}],121:[.452,.202,.59,{sk:.0639}],122:[.452,.008,.555,{sk:.0639}],160:[0,0,.25],913:[.711,0,.869,{sk:.16}],914:[.686,0,.866,{sk:.0958}],915:[.68,0,.657,{ic:.12,sk:.0958}],916:[.711,0,.958,{sk:.192}],917:[.68,0,.81,{sk:.0958}],918:[.686,0,.773,{sk:.0958}],919:[.686,0,.982,{sk:.0639}],920:[.702,.017,.867,{sk:.0958}],921:[.686,0,.511,{ic:.062,sk:.128}],922:[.686,0,.971,{sk:.0639}],923:[.711,0,.806,{sk:.192}],924:[.686,0,1.142,{ic:.077,sk:.0958}],925:[.686,0,.95,{ic:.077,sk:.0958}],926:[.675,0,.841,{sk:.0958}],927:[.703,.017,.837,{sk:.0958}],928:[.68,0,.982,{sk:.0639}],929:[.686,0,.723,{ic:.124,sk:.0958}],930:[.702,.017,.867,{sk:.0958}],931:[.686,0,.885,{sk:.0958}],932:[.675,0,.637,{ic:.135,sk:.0958}],933:[.703,0,.671,{ic:.131,sk:.0639}],934:[.686,0,.767,{sk:.0958}],935:[.686,0,.947,{sk:.0958}],936:[.686,0,.714,{ic:.076,sk:.0639}],937:[.703,0,.879,{sk:.0958}],945:[.452,.008,.761,{sk:.0319}],946:[.701,.194,.66,{sk:.0958}],947:[.451,.211,.59],948:[.725,.008,.522,{sk:.0639}],949:[.461,.017,.529,{sk:.0958}],950:[.711,.202,.508,{sk:.0958}],951:[.452,.211,.6,{sk:.0639}],952:[.702,.008,.562,{sk:.0958}],953:[.452,.008,.412,{sk:.0639}],954:[.452,.008,.668],955:[.694,.013,.671],956:[.452,.211,.708,{sk:.0319}],957:[.452,0,.577,{sk:.0319}],958:[.711,.201,.508,{sk:.128}],959:[.452,.008,.585,{sk:.0639}],960:[.444,.008,.682],961:[.451,.211,.612,{sk:.0958}],962:[.451,.105,.424,{sk:.0958}],963:[.444,.008,.686],964:[.444,.013,.521,{ic:.089,sk:.0319}],965:[.453,.008,.631,{sk:.0319}],966:[.452,.216,.747,{sk:.0958}],967:[.452,.201,.718,{sk:.0639}],968:[.694,.202,.758,{sk:.128}],969:[.453,.008,.718],977:[.701,.008,.692,{sk:.0958}],978:[.703,0,.671,{ic:.131,sk:.0639}],981:[.694,.202,.712,{sk:.0958}],982:[.444,.008,.975],988:[.68,0,.689,{ic:.12,sk:.0958}],1009:[.451,.194,.612,{sk:.0958}],1013:[.444,.007,.483,{sk:.0639}],8260:[.711,.21,.894],8710:[.711,0,.958,{sk:.192}],10744:[.711,.21,.894]}},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0});var i=s(0),t=s(6);f.bold=i.AddCSS(t.bold,{32:{c:" "},33:{c:"!"},35:{c:"#"},36:{c:"$"},37:{c:"%"},38:{c:"&"},40:{c:"("},41:{c:")"},42:{c:"*"},43:{c:"+"},44:{c:","},45:{c:"-"},46:{c:"."},47:{c:"/"},48:{c:"0"},49:{c:"1"},50:{c:"2"},51:{c:"3"},52:{c:"4"},53:{c:"5"},54:{c:"6"},55:{c:"7"},56:{c:"8"},57:{c:"9"},58:{c:":"},59:{c:";"},60:{c:"<"},61:{c:"="},62:{c:">"},63:{c:"?"},64:{c:"@"},65:{c:"A"},66:{c:"B"},67:{c:"C"},68:{c:"D"},69:{c:"E"},70:{c:"F"},71:{c:"G"},72:{c:"H"},73:{c:"I"},74:{c:"J"},75:{c:"K"},76:{c:"L"},77:{c:"M"},78:{c:"N"},79:{c:"O"},80:{c:"P"},81:{c:"Q"},82:{c:"R"},83:{c:"S"},84:{c:"T"},85:{c:"U"},86:{c:"V"},87:{c:"W"},88:{c:"X"},89:{c:"Y"},90:{c:"Z"},91:{c:"["},93:{c:"]"},94:{c:"^"},95:{c:"_"},96:{c:"`"},97:{c:"a"},98:{c:"b"},99:{c:"c"},100:{c:"d"},101:{c:"e"},102:{c:"f"},103:{c:"g"},104:{c:"h"},105:{c:"i"},106:{c:"j"},107:{c:"k"},108:{c:"l"},109:{c:"m"},110:{c:"n"},111:{c:"o"},112:{c:"p"},113:{c:"q"},114:{c:"r"},115:{c:"s"},116:{c:"t"},117:{c:"u"},118:{c:"v"},119:{c:"w"},120:{c:"x"},121:{c:"y"},122:{c:"z"},123:{c:"{"},124:{c:"|"},125:{c:"}"},126:{c:"~"},183:{c:"\\22C5"},697:{c:"\\2032"},913:{c:"A"},914:{c:"B"},917:{c:"E"},918:{c:"Z"},919:{c:"H"},921:{c:"I"},922:{c:"K"},924:{c:"M"},925:{c:"N"},927:{c:"O"},929:{c:"P"},930:{c:"\\398"},932:{c:"T"},935:{c:"X"},978:{c:"\\3A5"},988:{c:"F"},8194:{c:""},8195:{c:""},8196:{c:""},8197:{c:""},8198:{c:""},8201:{c:""},8202:{c:""},8213:{c:"\\2014"},8214:{c:"\\2225"},8215:{c:"_"},8226:{c:"\\2219"},8243:{c:"\\2032\\2032"},8244:{c:"\\2032\\2032\\2032"},8254:{c:"\\2C9"},8260:{c:"/"},8279:{c:"\\2032\\2032\\2032\\2032"},8407:{c:"\\2192",f:"VB"},8465:{c:"I",f:"FR"},8476:{c:"R",f:"FR"},8602:{c:"\\2190\\338"},8603:{c:"\\2192\\338"},8622:{c:"\\2194\\338"},8653:{c:"\\21D0\\338"},8654:{c:"\\21D4\\338"},8655:{c:"\\21D2\\338"},8708:{c:"\\2203\\338"},8710:{c:"\\394"},8716:{c:"\\220B\\338"},8740:{c:"\\2223\\338"},8742:{c:"\\2225\\338"},8769:{c:"\\223C\\338"},8772:{c:"\\2243\\338"},8775:{c:"\\2245\\338"},8777:{c:"\\2248\\338"},8802:{c:"\\2261\\338"},8813:{c:"\\224D\\338"},8814:{c:"<\\338"},8815:{c:">\\338"},8816:{c:"\\2264\\338"},8817:{c:"\\2265\\338"},8832:{c:"\\227A\\338"},8833:{c:"\\227B\\338"},8836:{c:"\\2282\\338"},8837:{c:"\\2283\\338"},8840:{c:"\\2286\\338"},8841:{c:"\\2287\\338"},8876:{c:"\\22A2\\338"},8877:{c:"\\22A8\\338"},8930:{c:"\\2291\\338"},8931:{c:"\\2292\\338"},9001:{c:"\\27E8"},9002:{c:"\\27E9"},9653:{c:"\\25B3"},9663:{c:"\\25BD"},10072:{c:"\\2223"},10744:{c:"/",f:"BI"},10799:{c:"\\D7"},12296:{c:"\\27E8"},12297:{c:"\\27E9"}})},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0}),f.bold={32:[0,0,.25],33:[.705,0,.35],34:[.694,-.329,.603],35:[.694,.193,.958],36:[.75,.056,.575],37:[.75,.056,.958],38:[.705,.011,.894],39:[.694,-.329,.319],40:[.75,.249,.447],41:[.75,.249,.447],42:[.75,-.306,.575],43:[.633,.131,.894],44:[.171,.194,.319],45:[.278,-.166,.383],46:[.171,0,.319],47:[.75,.25,.575],48:[.654,.01,.575],49:[.655,0,.575],50:[.654,0,.575],51:[.655,.011,.575],52:[.656,0,.575],53:[.655,.011,.575],54:[.655,.011,.575],55:[.676,.011,.575],56:[.654,.011,.575],57:[.654,.011,.575],58:[.444,0,.319],59:[.444,.194,.319],60:[.587,.085,.894],61:[.393,-.109,.894],62:[.587,.085,.894],63:[.7,0,.543],64:[.699,.006,.894],65:[.698,0,.869],66:[.686,0,.818],67:[.697,.011,.831],68:[.686,0,.882],69:[.68,0,.756],70:[.68,0,.724],71:[.697,.01,.904],72:[.686,0,.9],73:[.686,0,.436],74:[.686,.011,.594],75:[.686,0,.901],76:[.686,0,.692],77:[.686,0,1.092],78:[.686,0,.9],79:[.696,.01,.864],80:[.686,0,.786],81:[.696,.193,.864],82:[.686,.011,.862],83:[.697,.011,.639],84:[.675,0,.8],85:[.686,.011,.885],86:[.686,.007,.869],87:[.686,.007,1.189],88:[.686,0,.869],89:[.686,0,.869],90:[.686,0,.703],91:[.75,.25,.319],92:[.75,.25,.575],93:[.75,.25,.319],94:[.694,-.52,.575],95:[-.01,.061,.575],96:[.706,-.503,.575],97:[.453,.006,.559],98:[.694,.006,.639],99:[.453,.006,.511],100:[.694,.006,.639],101:[.452,.006,.527],102:[.7,0,.351,{ic:.101}],103:[.455,.201,.575],104:[.694,0,.639],105:[.695,0,.319],106:[.695,.2,.351],107:[.694,0,.607],108:[.694,0,.319],109:[.45,0,.958],110:[.45,0,.639],111:[.452,.005,.575],112:[.45,.194,.639],113:[.45,.194,.607],114:[.45,0,.474],115:[.453,.006,.454],116:[.635,.005,.447],117:[.45,.006,.639],118:[.444,0,.607],119:[.444,0,.831],120:[.444,0,.607],121:[.444,.2,.607],122:[.444,0,.511],123:[.75,.25,.575],124:[.75,.249,.319],125:[.75,.25,.575],126:[.344,-.202,.575],160:[0,0,.25],168:[.695,-.535,.575],172:[.371,-.061,.767],175:[.607,-.54,.575],176:[.702,-.536,.575],177:[.728,.035,.894],180:[.706,-.503,.575],183:[.336,-.166,.319],215:[.53,.028,.894],247:[.597,.096,.894],305:[.452,.008,.394,{sk:.0319}],567:[.451,.201,.439,{sk:.0958}],697:[.563,-.033,.344],710:[.694,-.52,.575],711:[.66,-.515,.575],713:[.607,-.54,.575],714:[.706,-.503,.575],715:[.706,-.503,.575],728:[.694,-.5,.575],729:[.695,-.525,.575],730:[.702,-.536,.575],732:[.694,-.552,.575],768:[.706,-.503,0],769:[.706,-.503,0],770:[.694,-.52,0],771:[.694,-.552,0],772:[.607,-.54,0],774:[.694,-.5,0],775:[.695,-.525,0],776:[.695,-.535,0],778:[.702,-.536,0],779:[.714,-.511,0],780:[.66,-.515,0],824:[.711,.21,0],913:[.698,0,.869],914:[.686,0,.818],915:[.68,0,.692],916:[.698,0,.958],917:[.68,0,.756],918:[.686,0,.703],919:[.686,0,.9],920:[.696,.01,.894],921:[.686,0,.436],922:[.686,0,.901],923:[.698,0,.806],924:[.686,0,1.092],925:[.686,0,.9],926:[.675,0,.767],927:[.696,.01,.864],928:[.68,0,.9],929:[.686,0,.786],930:[.696,.01,.894],931:[.686,0,.831],932:[.675,0,.8],933:[.697,0,.894],934:[.686,0,.831],935:[.686,0,.869],936:[.686,0,.894],937:[.696,0,.831],945:[.452,.008,.761,{sk:.0319}],946:[.701,.194,.66,{sk:.0958}],947:[.451,.211,.59],948:[.725,.008,.522,{sk:.0639}],949:[.461,.017,.529,{sk:.0958}],950:[.711,.202,.508,{sk:.0958}],951:[.452,.211,.6,{sk:.0639}],952:[.702,.008,.562,{sk:.0958}],953:[.452,.008,.412,{sk:.0639}],954:[.452,.008,.668],955:[.694,.013,.671],956:[.452,.211,.708,{sk:.0319}],957:[.452,0,.577,{sk:.0319}],958:[.711,.201,.508,{sk:.128}],959:[.452,.008,.585,{sk:.0639}],960:[.444,.008,.682],961:[.451,.211,.612,{sk:.0958}],962:[.451,.105,.424,{sk:.0958}],963:[.444,.008,.686],964:[.444,.013,.521,{ic:.089,sk:.0319}],965:[.453,.008,.631,{sk:.0319}],966:[.452,.216,.747,{sk:.0958}],967:[.452,.201,.718,{sk:.0639}],968:[.694,.202,.758,{sk:.128}],969:[.453,.008,.718],977:[.701,.008,.692,{sk:.0958}],978:[.697,0,.894],981:[.694,.202,.712,{sk:.0958}],982:[.444,.008,.975],988:[.68,0,.724],1009:[.451,.194,.612,{sk:.0958}],1013:[.444,.007,.483,{sk:.0639}],8194:[0,0,.5],8195:[0,0,.999],8196:[0,0,.333],8197:[0,0,.25],8198:[0,0,.167],8201:[0,0,.167],8202:[0,0,.083],8211:[.3,-.249,.575],8212:[.3,-.249,1.15],8213:[.3,-.249,1.15],8214:[.75,.248,.575],8215:[-.01,.061,.575],8216:[.694,-.329,.319],8217:[.694,-.329,.319],8220:[.694,-.329,.603],8221:[.694,-.329,.603],8224:[.702,.211,.511],8225:[.702,.202,.511],8226:[.474,-.028,.575],8230:[.171,0,1.295],8242:[.563,-.033,.344],8243:[.563,0,.688],8244:[.563,0,1.032],8254:[.607,-.54,.575],8260:[.75,.25,.575],8279:[.563,0,1.376],8407:[.723,-.513,.575],8463:[.694,.008,.668,{sk:-.0319}],8465:[.686,.026,.554],8467:[.702,.019,.474,{sk:.128}],8472:[.461,.21,.74],8476:[.686,.026,.828],8501:[.694,0,.703],8592:[.518,.017,1.15],8593:[.694,.193,.575],8594:[.518,.017,1.15],8595:[.694,.194,.575],8596:[.518,.017,1.15],8597:[.767,.267,.575],8598:[.724,.194,1.15],8599:[.724,.193,1.15],8600:[.694,.224,1.15],8601:[.694,.224,1.15],8602:[.711,.21,1.15],8603:[.711,.21,1.15],8614:[.518,.017,1.15],8617:[.518,.017,1.282],8618:[.518,.017,1.282],8622:[.711,.21,1.15],8636:[.518,-.22,1.15],8637:[.281,.017,1.15],8640:[.518,-.22,1.15],8641:[.281,.017,1.15],8652:[.718,.017,1.15],8653:[.711,.21,1.15],8654:[.711,.21,1.15],8655:[.711,.21,1.15],8656:[.547,.046,1.15],8657:[.694,.193,.703],8658:[.547,.046,1.15],8659:[.694,.194,.703],8660:[.547,.046,1.15],8661:[.767,.267,.703],8704:[.694,.016,.639],8706:[.71,.017,.628,{sk:.0958}],8707:[.694,0,.639],8708:[.711,.21,.639],8709:[.767,.073,.575],8710:[.698,0,.958],8711:[.686,.024,.958],8712:[.587,.086,.767],8713:[.711,.21,.767],8715:[.587,.086,.767],8716:[.711,.21,.767],8722:[.281,-.221,.894],8723:[.537,.227,.894],8725:[.75,.25,.575],8726:[.75,.25,.575],8727:[.472,-.028,.575],8728:[.474,-.028,.575],8729:[.474,-.028,.575],8730:[.82,.18,.958],8733:[.451,.008,.894],8734:[.452,.008,1.15],8736:[.714,0,.722],8739:[.75,.249,.319],8740:[.75,.249,.319],8741:[.75,.248,.575],8742:[.75,.248,.575],8743:[.604,.017,.767],8744:[.604,.016,.767],8745:[.603,.016,.767],8746:[.604,.016,.767],8747:[.711,.211,.569,{ic:.063}],8764:[.391,-.109,.894],8768:[.583,.082,.319],8769:[.711,.21,.894],8771:[.502,0,.894],8772:[.711,.21,.894],8773:[.638,.027,.894],8775:[.711,.21,.894],8776:[.524,-.032,.894],8777:[.711,.21,.894],8781:[.533,.032,.894],8784:[.721,-.109,.894],8800:[.711,.21,.894],8801:[.505,0,.894],8802:[.711,.21,.894],8804:[.697,.199,.894],8805:[.697,.199,.894],8810:[.617,.116,1.15],8811:[.618,.116,1.15],8813:[.711,.21,.894],8814:[.711,.21,.894],8815:[.711,.21,.894],8816:[.711,.21,.894],8817:[.711,.21,.894],8826:[.585,.086,.894],8827:[.586,.086,.894],8832:[.711,.21,.894],8833:[.711,.21,.894],8834:[.587,.085,.894],8835:[.587,.086,.894],8836:[.711,.21,.894],8837:[.711,.21,.894],8838:[.697,.199,.894],8839:[.697,.199,.894],8840:[.711,.21,.894],8841:[.711,.21,.894],8846:[.604,.016,.767],8849:[.697,.199,.894],8850:[.697,.199,.894],8851:[.604,0,.767],8852:[.604,0,.767],8853:[.632,.132,.894],8854:[.632,.132,.894],8855:[.632,.132,.894],8856:[.632,.132,.894],8857:[.632,.132,.894],8866:[.693,0,.703],8867:[.693,0,.703],8868:[.694,0,.894],8869:[.693,0,.894],8872:[.75,.249,.974],8876:[.711,.21,.703],8877:[.75,.249,.974],8900:[.523,.021,.575],8901:[.336,-.166,.319],8902:[.502,0,.575],8904:[.54,.039,1],8930:[.711,.21,.894],8931:[.711,.21,.894],8942:[.951,.029,.319],8943:[.336,-.166,1.295],8945:[.871,-.101,1.323],8968:[.75,.248,.511],8969:[.75,.248,.511],8970:[.749,.248,.511],8971:[.749,.248,.511],8994:[.405,-.108,1.15],8995:[.392,-.126,1.15],9001:[.75,.249,.447],9002:[.75,.249,.447],9651:[.711,0,1.022],9653:[.711,0,1.022],9657:[.54,.039,.575],9661:[.5,.21,1.022],9663:[.5,.21,1.022],9667:[.539,.038,.575],9711:[.711,.211,1.15],9824:[.719,.129,.894],9825:[.711,.024,.894],9826:[.719,.154,.894],9827:[.719,.129,.894],9837:[.75,.017,.447],9838:[.741,.223,.447],9839:[.724,.224,.447],10072:[.75,.249,.319],10216:[.75,.249,.447],10217:[.75,.249,.447],10229:[.518,.017,1.805],10230:[.518,.017,1.833],10231:[.518,.017,2.126],10232:[.547,.046,1.868],10233:[.547,.046,1.87],10234:[.547,.046,2.126],10236:[.518,.017,1.833],10744:[.711,.21,.894],10799:[.53,.028,.894],10815:[.686,0,.9],10927:[.696,.199,.894],10928:[.697,.199,.894],12296:[.75,.249,.447],12297:[.75,.249,.447]}},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0});var i=s(0),t=s(8);f.doubleStruck=i.AddCSS(t.doubleStruck,{32:{c:" "},65:{c:"A"},66:{c:"B"},67:{c:"C"},68:{c:"D"},69:{c:"E"},70:{c:"F"},71:{c:"G"},72:{c:"H"},73:{c:"I"},74:{c:"J"},75:{c:"K"},76:{c:"L"},77:{c:"M"},78:{c:"N"},79:{c:"O"},80:{c:"P"},81:{c:"Q"},82:{c:"R"},83:{c:"S"},84:{c:"T"},85:{c:"U"},86:{c:"V"},87:{c:"W"},88:{c:"X"},89:{c:"Y"},90:{c:"Z"},107:{c:"k"},913:{c:"A",f:"B"},914:{c:"B",f:"B"},917:{c:"E",f:"B"},918:{c:"Z",f:"B"},919:{c:"H",f:"B"},921:{c:"I",f:"B"},922:{c:"K",f:"B"},924:{c:"M",f:"B"},925:{c:"N",f:"B"},927:{c:"O",f:"B"},929:{c:"P",f:"B"},930:{c:"\\398",f:"B"},932:{c:"T",f:"B"},935:{c:"X",f:"B"},978:{c:"\\3A5",f:"B"},988:{c:"F",f:"B"},8450:{c:"C",f:"A"},8461:{c:"H",f:"A"},8469:{c:"N",f:"A"},8473:{c:"P",f:"A"},8474:{c:"Q",f:"A"},8477:{c:"R",f:"A"},8484:{c:"Z",f:"A"}})},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0}),f.doubleStruck={32:[0,0,.25],65:[.701,0,.722],66:[.683,0,.667],67:[.702,.019,.722],68:[.683,0,.722],69:[.683,0,.667],70:[.683,0,.611],71:[.702,.019,.778],72:[.683,0,.778],73:[.683,0,.389],74:[.683,.077,.5],75:[.683,0,.778],76:[.683,0,.667],77:[.683,0,.944],78:[.683,.02,.722],79:[.701,.019,.778],80:[.683,0,.611],81:[.701,.181,.778],82:[.683,0,.722],83:[.702,.012,.556],84:[.683,0,.667],85:[.683,.019,.722],86:[.683,.02,.722],87:[.683,.019,1],88:[.683,0,.722],89:[.683,0,.722],90:[.683,0,.667],107:[.683,0,.556],160:[0,0,.25],913:[.698,0,.869],914:[.686,0,.818],917:[.68,0,.756],918:[.686,0,.703],919:[.686,0,.9],921:[.686,0,.436],922:[.686,0,.901],924:[.686,0,1.092],925:[.686,0,.9],927:[.696,.01,.864],929:[.686,0,.786],930:[.696,.01,.894],932:[.675,0,.8],935:[.686,0,.869],978:[.697,0,.894],988:[.68,0,.724],8450:[.702,.019,.722],8461:[.683,0,.778],8469:[.683,.02,.722],8473:[.683,0,.611],8474:[.701,.181,.778],8477:[.683,0,.722],8484:[.683,0,.667]}},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0});var i=s(0),t=s(10);f.frakturBold=i.AddCSS(t.frakturBold,{32:{c:" "},33:{c:"!"},38:{c:"&"},40:{c:"("},41:{c:")"},42:{c:"*"},43:{c:"+"},44:{c:","},45:{c:"-"},46:{c:"."},47:{c:"/"},48:{c:"0"},49:{c:"1"},50:{c:"2"},51:{c:"3"},52:{c:"4"},53:{c:"5"},54:{c:"6"},55:{c:"7"},56:{c:"8"},57:{c:"9"},58:{c:":"},59:{c:";"},61:{c:"="},63:{c:"?"},65:{c:"A"},66:{c:"B"},67:{c:"C"},68:{c:"D"},69:{c:"E"},70:{c:"F"},71:{c:"G"},72:{c:"H"},73:{c:"I"},74:{c:"J"},75:{c:"K"},76:{c:"L"},77:{c:"M"},78:{c:"N"},79:{c:"O"},80:{c:"P"},81:{c:"Q"},82:{c:"R"},83:{c:"S"},84:{c:"T"},85:{c:"U"},86:{c:"V"},87:{c:"W"},88:{c:"X"},89:{c:"Y"},90:{c:"Z"},91:{c:"["},93:{c:"]"},94:{c:"^"},97:{c:"a"},98:{c:"b"},99:{c:"c"},100:{c:"d"},101:{c:"e"},102:{c:"f"},103:{c:"g"},104:{c:"h"},105:{c:"i"},106:{c:"j"},107:{c:"k"},108:{c:"l"},109:{c:"m"},110:{c:"n"},111:{c:"o"},112:{c:"p"},113:{c:"q"},114:{c:"r"},115:{c:"s"},116:{c:"t"},117:{c:"u"},118:{c:"v"},119:{c:"w"},120:{c:"x"},121:{c:"y"},122:{c:"z"},913:{c:"A",f:"B"},914:{c:"B",f:"B"},917:{c:"E",f:"B"},918:{c:"Z",f:"B"},919:{c:"H",f:"B"},921:{c:"I",f:"B"},922:{c:"K",f:"B"},924:{c:"M",f:"B"},925:{c:"N",f:"B"},927:{c:"O",f:"B"},929:{c:"P",f:"B"},930:{c:"\\398",f:"B"},932:{c:"T",f:"B"},935:{c:"X",f:"B"},978:{c:"\\3A5",f:"B"},988:{c:"F",f:"B"},8260:{c:"/"}})},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0}),f.frakturBold={32:[0,0,.25],33:[.689,.012,.349],34:[.695,-.432,.254],38:[.696,.016,.871],39:[.695,-.436,.25],40:[.737,.186,.459],41:[.735,.187,.459],42:[.692,-.449,.328],43:[.598,.082,.893],44:[.107,.191,.328],45:[.275,-.236,.893],46:[.102,.015,.328],47:[.721,.182,.593],48:[.501,.012,.593],49:[.489,0,.593],50:[.491,0,.593],51:[.487,.193,.593],52:[.495,.196,.593],53:[.481,.19,.593],54:[.704,.012,.593],55:[.479,.197,.593],56:[.714,.005,.593],57:[.487,.195,.593],58:[.457,.012,.255],59:[.458,.19,.255],61:[.343,-.168,.582],63:[.697,.014,.428],65:[.686,.031,.847],66:[.684,.031,1.044],67:[.676,.032,.723],68:[.683,.029,.982],69:[.686,.029,.783],70:[.684,.146,.722],71:[.687,.029,.927],72:[.683,.126,.851],73:[.681,.025,.655],74:[.68,.141,.652],75:[.681,.026,.789],76:[.683,.028,.786],77:[.683,.032,1.239],78:[.679,.03,.983],79:[.726,.03,.976],80:[.688,.223,.977],81:[.726,.083,.976],82:[.688,.028,.978],83:[.685,.031,.978],84:[.686,.03,.79],85:[.688,.039,.851],86:[.685,.029,.982],87:[.683,.03,1.235],88:[.681,.035,.849],89:[.688,.214,.984],90:[.677,.148,.711],91:[.74,.13,.257],93:[.738,.132,.257],94:[.734,-.452,.59],97:[.472,.032,.603],98:[.69,.032,.59],99:[.473,.026,.464],100:[.632,.028,.589],101:[.471,.027,.472],102:[.687,.222,.388],103:[.472,.208,.595],104:[.687,.207,.615],105:[.686,.025,.331],106:[.682,.203,.332],107:[.682,.025,.464],108:[.681,.024,.337],109:[.476,.031,.921],110:[.473,.028,.654],111:[.482,.034,.609],112:[.557,.207,.604],113:[.485,.211,.596],114:[.472,.026,.46],115:[.479,.034,.523],116:[.648,.027,.393],117:[.472,.032,.589],118:[.546,.027,.604],119:[.549,.032,.918],120:[.471,.188,.459],121:[.557,.221,.589],122:[.471,.214,.461],160:[0,0,.25],913:[.698,0,.869],914:[.686,0,.818],917:[.68,0,.756],918:[.686,0,.703],919:[.686,0,.9],921:[.686,0,.436],922:[.686,0,.901],924:[.686,0,1.092],925:[.686,0,.9],927:[.696,.01,.864],929:[.686,0,.786],930:[.696,.01,.894],932:[.675,0,.8],935:[.686,0,.869],978:[.697,0,.894],988:[.68,0,.724],8216:[.708,-.411,.254],8217:[.692,-.394,.254],8260:[.721,.182,.593],58113:[.63,.027,.587],58114:[.693,.212,.394],58115:[.681,.219,.387],58116:[.473,.212,.593],58117:[.684,.027,.393],58120:[.679,.22,.981],58121:[.717,.137,.727]}},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0});var i=s(0),t=s(12);f.fraktur=i.AddCSS(t.fraktur,{32:{c:" "},33:{c:"!"},38:{c:"&"},40:{c:"("},41:{c:")"},42:{c:"*"},43:{c:"+"},44:{c:","},45:{c:"-"},46:{c:"."},47:{c:"/"},48:{c:"0"},49:{c:"1"},50:{c:"2"},51:{c:"3"},52:{c:"4"},53:{c:"5"},54:{c:"6"},55:{c:"7"},56:{c:"8"},57:{c:"9"},58:{c:":"},59:{c:";"},61:{c:"="},63:{c:"?"},65:{c:"A"},66:{c:"B"},67:{c:"C"},68:{c:"D"},69:{c:"E"},70:{c:"F"},71:{c:"G"},72:{c:"H"},73:{c:"I"},74:{c:"J"},75:{c:"K"},76:{c:"L"},77:{c:"M"},78:{c:"N"},79:{c:"O"},80:{c:"P"},81:{c:"Q"},82:{c:"R"},83:{c:"S"},84:{c:"T"},85:{c:"U"},86:{c:"V"},87:{c:"W"},88:{c:"X"},89:{c:"Y"},90:{c:"Z"},91:{c:"["},93:{c:"]"},94:{c:"^"},97:{c:"a"},98:{c:"b"},99:{c:"c"},100:{c:"d"},101:{c:"e"},102:{c:"f"},103:{c:"g"},104:{c:"h"},105:{c:"i"},106:{c:"j"},107:{c:"k"},108:{c:"l"},109:{c:"m"},110:{c:"n"},111:{c:"o"},112:{c:"p"},113:{c:"q"},114:{c:"r"},115:{c:"s"},116:{c:"t"},117:{c:"u"},118:{c:"v"},119:{c:"w"},120:{c:"x"},121:{c:"y"},122:{c:"z"},913:{c:"A",f:""},914:{c:"B",f:""},917:{c:"E",f:""},918:{c:"Z",f:""},919:{c:"H",f:""},921:{c:"I",f:""},922:{c:"K",f:""},924:{c:"M",f:""},925:{c:"N",f:""},927:{c:"O",f:""},929:{c:"P",f:""},930:{c:"\\398",f:""},932:{c:"T",f:""},935:{c:"X",f:""},978:{c:"\\3A5",f:""},988:{c:"F",f:""},8260:{c:"/"},8460:{c:"H",f:"FR"},8465:{c:"I",f:"FR"},8476:{c:"R",f:"FR"},8488:{c:"Z",f:"FR"},8493:{c:"C",f:"FR"}})},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0}),f.fraktur={32:[0,0,.25],33:[.689,.012,.296],34:[.695,-.432,.215],38:[.698,.011,.738],39:[.695,-.436,.212],40:[.737,.186,.389],41:[.735,.187,.389],42:[.692,-.449,.278],43:[.598,.082,.756],44:[.107,.191,.278],45:[.275,-.236,.756],46:[.102,.015,.278],47:[.721,.182,.502],48:[.492,.013,.502],49:[.468,0,.502],50:[.474,0,.502],51:[.473,.182,.502],52:[.476,.191,.502],53:[.458,.184,.502],54:[.7,.013,.502],55:[.468,.181,.502],56:[.705,.01,.502],57:[.469,.182,.502],58:[.457,.012,.216],59:[.458,.189,.216],61:[.368,-.132,.756],63:[.693,.011,.362],65:[.696,.026,.718],66:[.691,.027,.884],67:[.685,.024,.613],68:[.685,.027,.832],69:[.685,.024,.663],70:[.686,.153,.611],71:[.69,.026,.785],72:[.666,.133,.72],73:[.686,.026,.554],74:[.686,.139,.552],75:[.68,.027,.668],76:[.686,.026,.666],77:[.692,.027,1.05],78:[.686,.025,.832],79:[.729,.027,.827],80:[.692,.218,.828],81:[.729,.069,.827],82:[.686,.026,.828],83:[.692,.027,.829],84:[.701,.027,.669],85:[.697,.027,.646],86:[.686,.026,.831],87:[.686,.027,1.046],88:[.688,.027,.719],89:[.686,.218,.833],90:[.729,.139,.602],91:[.74,.13,.278],93:[.738,.131,.278],94:[.734,-.452,.5],97:[.47,.035,.5],98:[.685,.031,.513],99:[.466,.029,.389],100:[.609,.033,.499],101:[.467,.03,.401],102:[.681,.221,.326],103:[.47,.209,.504],104:[.688,.205,.521],105:[.673,.02,.279],106:[.672,.208,.281],107:[.689,.025,.389],108:[.685,.02,.28],109:[.475,.026,.767],110:[.475,.022,.527],111:[.48,.028,.489],112:[.541,.212,.5],113:[.479,.219,.489],114:[.474,.021,.389],115:[.478,.029,.443],116:[.64,.02,.333],117:[.474,.023,.517],118:[.53,.028,.512],119:[.532,.028,.774],120:[.472,.188,.389],121:[.528,.218,.499],122:[.471,.214,.391],160:[0,0,.25],913:[.716,0,.75],914:[.683,0,.708],917:[.68,0,.681],918:[.683,0,.611],919:[.683,0,.75],921:[.683,0,.361],922:[.683,0,.778],924:[.683,0,.917],925:[.683,0,.75],927:[.705,.022,.778],929:[.683,0,.681],930:[.705,.022,.778],932:[.677,0,.722],935:[.683,0,.75],978:[.705,0,.778],988:[.68,0,.653],8216:[.708,-.41,.215],8217:[.692,-.395,.215],8260:[.721,.182,.502],8460:[.666,.133,.72],8465:[.686,.026,.554],8476:[.686,.026,.828],8488:[.729,.139,.602],8493:[.685,.024,.613],58112:[.683,.032,.497],58113:[.616,.03,.498],58114:[.68,.215,.333],58115:[.679,.224,.329],58116:[.471,.214,.503],58117:[.686,.02,.333],58118:[.577,.021,.334],58119:[.475,.022,.501]}},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0});var i=s(0),t=s(14);f.italic=i.AddCSS(t.italic,{32:{c:" "},33:{c:"!"},35:{c:"#"},37:{c:"%"},38:{c:"&"},40:{c:"("},41:{c:")"},42:{c:"*"},43:{c:"+"},44:{c:","},45:{c:"-"},46:{c:"."},47:{c:"/"},48:{c:"0"},49:{c:"1"},50:{c:"2"},51:{c:"3"},52:{c:"4"},53:{c:"5"},54:{c:"6"},55:{c:"7"},56:{c:"8"},57:{c:"9"},58:{c:":"},59:{c:";"},61:{c:"="},63:{c:"?"},64:{c:"@"},65:{c:"A"},66:{c:"B"},67:{c:"C"},68:{c:"D"},69:{c:"E"},70:{c:"F"},71:{c:"G"},72:{c:"H"},73:{c:"I"},74:{c:"J"},75:{c:"K"},76:{c:"L"},77:{c:"M"},78:{c:"N"},79:{c:"O"},80:{c:"P"},81:{c:"Q"},82:{c:"R"},83:{c:"S"},84:{c:"T"},85:{c:"U"},86:{c:"V"},87:{c:"W"},88:{c:"X"},89:{c:"Y"},90:{c:"Z"},91:{c:"["},93:{c:"]"},94:{c:"^"},95:{c:"_"},97:{c:"a"},98:{c:"b"},99:{c:"c"},100:{c:"d"},101:{c:"e"},102:{c:"f"},103:{c:"g"},104:{c:"h"},105:{c:"i"},106:{c:"j"},107:{c:"k"},108:{c:"l"},109:{c:"m"},110:{c:"n"},111:{c:"o"},112:{c:"p"},113:{c:"q"},114:{c:"r"},115:{c:"s"},116:{c:"t"},117:{c:"u"},118:{c:"v"},119:{c:"w"},120:{c:"x"},121:{c:"y"},122:{c:"z"},126:{c:"~"},913:{c:"A"},914:{c:"B"},917:{c:"E"},918:{c:"Z"},919:{c:"H"},921:{c:"I"},922:{c:"K"},924:{c:"M"},925:{c:"N"},927:{c:"O"},929:{c:"P"},930:{c:"\\398"},932:{c:"T"},935:{c:"X"},978:{c:"\\3A5"},988:{c:"F"},8213:{c:"\\2014"},8215:{c:"_"},8260:{c:"/"},8462:{c:"h",f:"I"},8710:{c:"\\394"},10744:{c:"/",f:"I"}})},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0}),f.italic={32:[0,0,.25],33:[.716,0,.307,{ic:.073}],34:[.694,-.379,.514],35:[.694,.194,.818],37:[.75,.056,.818],38:[.716,.022,.767],39:[.694,-.379,.307,{ic:.07}],40:[.75,.25,.409,{ic:.108}],41:[.75,.25,.409],42:[.75,-.32,.511,{ic:.073}],43:[.557,.057,.767],44:[.121,.194,.307],45:[.251,-.18,.358],46:[.121,0,.307],47:[.716,.215,.778],48:[.665,.021,.511,{ic:.051}],49:[.666,0,.511],50:[.666,.022,.511],51:[.666,.022,.511,{ic:.051}],52:[.666,.194,.511],53:[.666,.022,.511,{ic:.056}],54:[.665,.022,.511,{ic:.054}],55:[.666,.022,.511,{ic:.123}],56:[.666,.021,.511],57:[.666,.022,.511],58:[.431,0,.307],59:[.431,.194,.307],61:[.367,-.133,.767],63:[.716,0,.511],64:[.705,.011,.767],65:[.716,0,.75,{sk:.139}],66:[.683,0,.759,{sk:.0833}],67:[.705,.022,.715,{sk:.0833}],68:[.683,0,.828,{sk:.0556}],69:[.68,0,.738,{sk:.0833}],70:[.68,0,.643,{ic:.106,sk:.0833}],71:[.705,.022,.786,{sk:.0833}],72:[.683,0,.831,{ic:.057,sk:.0556}],73:[.683,0,.44,{ic:.064,sk:.111}],74:[.683,.022,.555,{ic:.078,sk:.167}],75:[.683,0,.849,{sk:.0556}],76:[.683,0,.681,{sk:.0278}],77:[.683,0,.97,{ic:.081,sk:.0833}],78:[.683,0,.803,{ic:.085,sk:.0833}],79:[.704,.022,.763,{sk:.0833}],80:[.683,0,.642,{ic:.109,sk:.0833}],81:[.704,.194,.791,{sk:.0833}],82:[.683,.021,.759,{sk:.0833}],83:[.705,.022,.613,{sk:.0833}],84:[.677,0,.584,{ic:.12,sk:.0833}],85:[.683,.022,.683,{ic:.084,sk:.0278}],86:[.683,.022,.583,{ic:.186}],87:[.683,.022,.944,{ic:.104}],88:[.683,0,.828,{sk:.0833}],89:[.683,0,.581,{ic:.182}],90:[.683,0,.683,{sk:.0833}],91:[.75,.25,.307,{ic:.139}],93:[.75,.25,.307,{ic:.052}],94:[.694,-.527,.511],95:[-.025,.062,.511],97:[.441,.01,.529],98:[.694,.011,.429],99:[.442,.011,.433,{sk:.0556}],100:[.694,.01,.52,{sk:.167}],101:[.442,.011,.466,{sk:.0556}],102:[.705,.205,.49,{ic:.06,sk:.167}],103:[.442,.205,.477,{sk:.0278}],104:[.694,.011,.576,{sk:-.0278}],105:[.661,.011,.345],106:[.661,.204,.412],107:[.694,.011,.521],108:[.694,.011,.298,{sk:.0833}],109:[.442,.011,.878],110:[.442,.011,.6],111:[.441,.011,.485,{sk:.0556}],112:[.442,.194,.503,{sk:.0833}],113:[.442,.194,.446,{sk:.0833}],114:[.442,.011,.451,{sk:.0556}],115:[.442,.01,.469,{sk:.0556}],116:[.626,.011,.361,{sk:.0833}],117:[.442,.011,.572,{sk:.0278}],118:[.443,.011,.485,{sk:.0278}],119:[.443,.011,.716,{sk:.0833}],120:[.442,.011,.572,{sk:.0278}],121:[.442,.205,.49,{sk:.0556}],122:[.442,.011,.465,{sk:.0556}],126:[.318,-.208,.511,{ic:.06}],160:[0,0,.25],163:[.714,.011,.769],305:[.441,.01,.307],567:[.442,.204,.332],768:[.697,-.5,0],769:[.697,-.5,0],770:[.694,-.527,0],771:[.668,-.558,0,{ic:.06}],772:[.589,-.544,0,{ic:.054}],774:[.694,-.515,0,{ic:.062}],775:[.669,-.548,0],776:[.669,-.554,0],778:[.716,-.542,0],779:[.697,-.503,0,{ic:.065}],780:[.638,-.502,0],913:[.716,0,.75,{sk:.139}],914:[.683,0,.759,{sk:.0833}],915:[.68,0,.615,{ic:.106,sk:.0833}],916:[.716,0,.833,{sk:.167}],917:[.68,0,.738,{sk:.0833}],918:[.683,0,.683,{sk:.0833}],919:[.683,0,.831,{ic:.057,sk:.0556}],920:[.704,.022,.763,{sk:.0833}],921:[.683,0,.44,{ic:.064,sk:.111}],922:[.683,0,.849,{sk:.0556}],923:[.716,0,.694,{sk:.167}],924:[.683,0,.97,{ic:.081,sk:.0833}],925:[.683,0,.803,{ic:.085,sk:.0833}],926:[.677,0,.742,{sk:.0833}],927:[.704,.022,.763,{sk:.0833}],928:[.68,0,.831,{ic:.056,sk:.0556}],929:[.683,0,.642,{ic:.109,sk:.0833}],930:[.704,.022,.763,{sk:.0833}],931:[.683,0,.78,{sk:.0833}],932:[.677,0,.584,{ic:.12,sk:.0833}],933:[.705,0,.583,{ic:.117,sk:.0556}],934:[.683,0,.667,{sk:.0833}],935:[.683,0,.828,{sk:.0833}],936:[.683,0,.612,{ic:.08,sk:.0556}],937:[.704,0,.772,{sk:.0833}],945:[.442,.011,.64,{sk:.0278}],946:[.705,.194,.566,{sk:.0833}],947:[.441,.216,.518],948:[.717,.01,.444,{sk:.0556}],949:[.452,.022,.466,{sk:.0833}],950:[.704,.204,.438,{sk:.0833}],951:[.442,.216,.497,{sk:.0556}],952:[.705,.01,.469,{sk:.0833}],953:[.442,.01,.354,{sk:.0556}],954:[.442,.011,.576],955:[.694,.012,.583],956:[.442,.216,.603,{sk:.0278}],957:[.442,0,.494,{sk:.0278}],958:[.704,.205,.438,{sk:.111}],959:[.441,.011,.485,{sk:.0556}],960:[.431,.011,.57],961:[.442,.216,.517,{sk:.0833}],962:[.442,.107,.363,{sk:.0833}],963:[.431,.011,.571],964:[.431,.013,.437,{ic:.08,sk:.0278}],965:[.443,.01,.54,{sk:.0278}],966:[.442,.218,.654,{sk:.0833}],967:[.442,.204,.626,{sk:.0556}],968:[.694,.205,.651,{sk:.111}],969:[.443,.011,.622],977:[.705,.011,.591,{sk:.0833}],978:[.705,0,.583,{ic:.117,sk:.0556}],981:[.694,.205,.596,{sk:.0833}],982:[.431,.01,.828],988:[.68,0,.643,{ic:.106,sk:.0833}],1009:[.442,.194,.517,{sk:.0833}],1013:[.431,.011,.406,{sk:.0556}],8211:[.285,-.248,.511],8212:[.285,-.248,1.022],8213:[.285,-.248,1.022],8215:[-.025,.062,.511],8216:[.694,-.379,.307,{ic:.055}],8217:[.694,-.379,.307,{ic:.07}],8220:[.694,-.379,.514,{ic:.092}],8221:[.694,-.379,.514],8260:[.716,.215,.778],8462:[.694,.011,.576,{sk:-.0278}],8463:[.695,.013,.54],8710:[.716,0,.833,{sk:.167}],10744:[.716,.215,.778]}},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0});var i=s(0),t=s(16);f.largeop=i.AddCSS(t.largeop,{32:{c:" "},40:{c:"("},41:{c:")"},47:{c:"/"},91:{c:"["},93:{c:"]"},123:{c:"{"},125:{c:"}"},8260:{c:"/"},9001:{c:"\\27E8"},9002:{c:"\\27E9"},10072:{c:"\\2223"},10764:{c:"\\222C\\222C"},12296:{c:"\\27E8"},12297:{c:"\\27E9"}})},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0}),f.largeop={32:[0,0,.25],40:[1.15,.649,.597],41:[1.15,.649,.597],47:[1.15,.649,.811],91:[1.15,.649,.472],92:[1.15,.649,.811],93:[1.15,.649,.472],123:[1.15,.649,.667],125:[1.15,.649,.667],160:[0,0,.25],710:[.772,-.565,1],732:[.75,-.611,1],770:[.772,-.565,0],771:[.75,-.611,0],8214:[.602,0,.778],8260:[1.15,.649,.811],8593:[.6,0,.667],8595:[.6,0,.667],8657:[.599,0,.778],8659:[.6,0,.778],8719:[.95,.45,1.278],8720:[.95,.45,1.278],8721:[.95,.45,1.444],8730:[1.15,.65,1],8739:[.627,.015,.333],8741:[.627,.015,.556],8747:[1.36,.862,.556,{ic:.388}],8748:[1.36,.862,1.084,{ic:.388}],8749:[1.36,.862,1.592,{ic:.388}],8750:[1.36,.862,.556,{ic:.388}],8896:[.95,.45,1.111],8897:[.95,.45,1.111],8898:[.949,.45,1.111],8899:[.95,.449,1.111],8968:[1.15,.649,.528],8969:[1.15,.649,.528],8970:[1.15,.649,.528],8971:[1.15,.649,.528],9001:[1.15,.649,.611],9002:[1.15,.649,.611],9168:[.602,0,.667],10072:[.627,.015,.333],10216:[1.15,.649,.611],10217:[1.15,.649,.611],10752:[.949,.449,1.511],10753:[.949,.449,1.511],10754:[.949,.449,1.511],10756:[.95,.449,1.111],10758:[.95,.45,1.111],10764:[1.36,.862,2.168,{ic:.388}],12296:[1.15,.649,.611],12297:[1.15,.649,.611]}},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0});var i=s(0),t=s(18);f.monospace=i.AddCSS(t.monospace,{32:{c:" "},33:{c:"!"},35:{c:"#"},36:{c:"$"},37:{c:"%"},38:{c:"&"},40:{c:"("},41:{c:")"},42:{c:"*"},43:{c:"+"},44:{c:","},45:{c:"-"},46:{c:"."},47:{c:"/"},48:{c:"0"},49:{c:"1"},50:{c:"2"},51:{c:"3"},52:{c:"4"},53:{c:"5"},54:{c:"6"},55:{c:"7"},56:{c:"8"},57:{c:"9"},58:{c:":"},59:{c:";"},60:{c:"<"},61:{c:"="},62:{c:">"},63:{c:"?"},64:{c:"@"},65:{c:"A"},66:{c:"B"},67:{c:"C"},68:{c:"D"},69:{c:"E"},70:{c:"F"},71:{c:"G"},72:{c:"H"},73:{c:"I"},74:{c:"J"},75:{c:"K"},76:{c:"L"},77:{c:"M"},78:{c:"N"},79:{c:"O"},80:{c:"P"},81:{c:"Q"},82:{c:"R"},83:{c:"S"},84:{c:"T"},85:{c:"U"},86:{c:"V"},87:{c:"W"},88:{c:"X"},89:{c:"Y"},90:{c:"Z"},91:{c:"["},93:{c:"]"},94:{c:"^"},95:{c:"_"},96:{c:"`"},97:{c:"a"},98:{c:"b"},99:{c:"c"},100:{c:"d"},101:{c:"e"},102:{c:"f"},103:{c:"g"},104:{c:"h"},105:{c:"i"},106:{c:"j"},107:{c:"k"},108:{c:"l"},109:{c:"m"},110:{c:"n"},111:{c:"o"},112:{c:"p"},113:{c:"q"},114:{c:"r"},115:{c:"s"},116:{c:"t"},117:{c:"u"},118:{c:"v"},119:{c:"w"},120:{c:"x"},121:{c:"y"},122:{c:"z"},123:{c:"{"},124:{c:"|"},125:{c:"}"},126:{c:"~"},697:{c:"\\2032"},913:{c:"A"},914:{c:"B"},917:{c:"E"},918:{c:"Z"},919:{c:"H"},921:{c:"I"},922:{c:"K"},924:{c:"M"},925:{c:"N"},927:{c:"O"},929:{c:"P"},930:{c:"\\398"},932:{c:"T"},935:{c:"X"},978:{c:"\\3A5"},988:{c:"F"},8215:{c:"_"},8243:{c:"\\2032\\2032"},8244:{c:"\\2032\\2032\\2032"},8260:{c:"/"},8279:{c:"\\2032\\2032\\2032\\2032"},8710:{c:"\\394"}})},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0}),f.monospace={32:[0,0,.525],33:[.622,0,.525],34:[.623,-.333,.525],35:[.611,0,.525],36:[.694,.082,.525],37:[.694,.083,.525],38:[.622,.011,.525],39:[.611,-.287,.525],40:[.694,.082,.525],41:[.694,.082,.525],42:[.52,-.09,.525],43:[.531,-.081,.525],44:[.14,.139,.525],45:[.341,-.271,.525],46:[.14,0,.525],47:[.694,.083,.525],48:[.621,.01,.525],49:[.622,0,.525],50:[.622,0,.525],51:[.622,.011,.525],52:[.624,0,.525],53:[.611,.01,.525],54:[.622,.011,.525],55:[.627,.01,.525],56:[.621,.01,.525],57:[.622,.011,.525],58:[.431,0,.525],59:[.431,.139,.525],60:[.557,-.055,.525],61:[.417,-.195,.525],62:[.557,-.055,.525],63:[.617,0,.525],64:[.617,.006,.525],65:[.623,0,.525],66:[.611,0,.525],67:[.622,.011,.525],68:[.611,0,.525],69:[.611,0,.525],70:[.611,0,.525],71:[.622,.011,.525],72:[.611,0,.525],73:[.611,0,.525],74:[.611,.011,.525],75:[.611,0,.525],76:[.611,0,.525],77:[.611,0,.525],78:[.611,0,.525],79:[.621,.01,.525],80:[.611,0,.525],81:[.621,.138,.525],82:[.611,.011,.525],83:[.622,.011,.525],84:[.611,0,.525],85:[.611,.011,.525],86:[.611,.007,.525],87:[.611,.007,.525],88:[.611,0,.525],89:[.611,0,.525],90:[.611,0,.525],91:[.694,.082,.525],92:[.694,.083,.525],93:[.694,.082,.525],94:[.611,-.46,.525],95:[-.025,.095,.525],96:[.681,-.357,.525],97:[.439,.006,.525],98:[.611,.006,.525],99:[.44,.006,.525],100:[.611,.006,.525],101:[.44,.006,.525],102:[.617,0,.525],103:[.442,.229,.525],104:[.611,0,.525],105:[.612,0,.525],106:[.612,.228,.525],107:[.611,0,.525],108:[.611,0,.525],109:[.436,0,.525],110:[.436,0,.525],111:[.44,.006,.525],112:[.437,.221,.525],113:[.437,.221,.525],114:[.437,0,.525],115:[.44,.006,.525],116:[.554,.006,.525],117:[.431,.005,.525],118:[.431,0,.525],119:[.431,0,.525],120:[.431,0,.525],121:[.431,.228,.525],122:[.431,0,.525],123:[.694,.083,.525],124:[.694,.082,.525],125:[.694,.083,.525],126:[.611,-.466,.525],127:[.612,-.519,.525],160:[0,0,.525],305:[.431,0,.525],567:[.431,.228,.525],697:[.623,-.334,.525],768:[.611,-.485,0],769:[.611,-.485,0],770:[.611,-.46,0],771:[.611,-.466,0],772:[.577,-.5,0],774:[.611,-.504,0],776:[.612,-.519,0],778:[.619,-.499,0],780:[.577,-.449,0],913:[.623,0,.525],914:[.611,0,.525],915:[.611,0,.525],916:[.623,0,.525],917:[.611,0,.525],918:[.611,0,.525],919:[.611,0,.525],920:[.621,.01,.525],921:[.611,0,.525],922:[.611,0,.525],923:[.623,0,.525],924:[.611,0,.525],925:[.611,0,.525],926:[.611,0,.525],927:[.621,.01,.525],928:[.611,0,.525],929:[.611,0,.525],930:[.621,.01,.525],931:[.611,0,.525],932:[.611,0,.525],933:[.622,0,.525],934:[.611,0,.525],935:[.611,0,.525],936:[.611,0,.525],937:[.622,0,.525],978:[.622,0,.525],988:[.611,0,.525],8215:[-.025,.095,.525],8242:[.623,-.334,.525],8243:[.623,0,1.05],8244:[.623,0,1.575],8260:[.694,.083,.525],8279:[.623,0,2.1],8710:[.623,0,.525]}},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0});var i=s(0),t=s(20);f.normal=i.AddCSS(t.normal,{32:{c:" "},33:{c:"!"},35:{c:"#"},36:{c:"$"},37:{c:"%"},38:{c:"&"},40:{c:"("},41:{c:")"},42:{c:"*"},43:{c:"+"},44:{c:","},45:{c:"-"},46:{c:"."},47:{c:"/"},48:{c:"0"},49:{c:"1"},50:{c:"2"},51:{c:"3"},52:{c:"4"},53:{c:"5"},54:{c:"6"},55:{c:"7"},56:{c:"8"},57:{c:"9"},58:{c:":"},59:{c:";"},60:{c:"<"},61:{c:"="},62:{c:">"},63:{c:"?"},64:{c:"@"},65:{c:"A"},66:{c:"B"},67:{c:"C"},68:{c:"D"},69:{c:"E"},70:{c:"F"},71:{c:"G"},72:{c:"H"},73:{c:"I"},74:{c:"J"},75:{c:"K"},76:{c:"L"},77:{c:"M"},78:{c:"N"},79:{c:"O"},80:{c:"P"},81:{c:"Q"},82:{c:"R"},83:{c:"S"},84:{c:"T"},85:{c:"U"},86:{c:"V"},87:{c:"W"},88:{c:"X"},89:{c:"Y"},90:{c:"Z"},91:{c:"["},93:{c:"]"},94:{c:"^"},95:{c:"_"},96:{c:"`"},97:{c:"a"},98:{c:"b"},99:{c:"c"},100:{c:"d"},101:{c:"e"},102:{c:"f"},103:{c:"g"},104:{c:"h"},105:{c:"i"},106:{c:"j"},107:{c:"k"},108:{c:"l"},109:{c:"m"},110:{c:"n"},111:{c:"o"},112:{c:"p"},113:{c:"q"},114:{c:"r"},115:{c:"s"},116:{c:"t"},117:{c:"u"},118:{c:"v"},119:{c:"w"},120:{c:"x"},121:{c:"y"},122:{c:"z"},123:{c:"{"},124:{c:"|"},125:{c:"}"},126:{c:"~"},163:{f:"MI"},183:{c:"\\22C5"},697:{c:"\\2032"},913:{c:"A"},914:{c:"B"},917:{c:"E"},918:{c:"Z"},919:{c:"H"},921:{c:"I"},922:{c:"K"},924:{c:"M"},925:{c:"N"},927:{c:"O"},929:{c:"P"},930:{c:"\\398"},932:{c:"T"},935:{c:"X"},978:{c:"\\3A5"},988:{c:"F"},1014:{c:"\\220D"},8192:{c:""},8193:{c:""},8194:{c:""},8195:{c:""},8196:{c:""},8197:{c:""},8198:{c:""},8201:{c:""},8202:{c:""},8203:{c:""},8204:{c:""},8213:{c:"\\2014"},8214:{c:"\\2225"},8215:{c:"_"},8226:{c:"\\2219"},8243:{c:"\\2032\\2032"},8244:{c:"\\2032\\2032\\2032"},8246:{c:"\\2035\\2035"},8247:{c:"\\2035\\2035\\2035"},8254:{c:"\\2C9"},8260:{c:"/"},8279:{c:"\\2032\\2032\\2032\\2032"},8289:{c:""},8290:{c:""},8291:{c:""},8292:{c:""},8407:{c:"\\2192",f:"V"},8450:{c:"C",f:"A"},8459:{c:"H",f:"SC"},8460:{c:"H",f:"FR"},8461:{c:"H",f:"A"},8462:{c:"h",f:"I"},8463:{f:""},8464:{c:"J",f:"SC"},8465:{c:"I",f:"FR"},8466:{c:"L",f:"SC"},8469:{c:"N",f:"A"},8473:{c:"P",f:"A"},8474:{c:"Q",f:"A"},8475:{c:"R",f:"SC"},8476:{c:"R",f:"FR"},8477:{c:"R",f:"A"},8484:{c:"Z",f:"A"},8486:{c:"\\3A9",f:""},8488:{c:"Z",f:"FR"},8492:{c:"B",f:"SC"},8493:{c:"C",f:"FR"},8496:{c:"E",f:"SC"},8497:{c:"F",f:"SC"},8499:{c:"M",f:"SC"},8708:{c:"\\2203\\338"},8710:{c:"\\394"},8716:{c:"\\220B\\338"},8726:{f:""},8772:{c:"\\2243\\338"},8775:{c:"\\2246"},8777:{c:"\\2248\\338"},8802:{c:"\\2261\\338"},8813:{c:"\\224D\\338"},8820:{c:"\\2272\\338"},8821:{c:"\\2273\\338"},8824:{c:"\\2276\\338"},8825:{c:"\\2277\\338"},8836:{c:"\\2282\\338"},8837:{c:"\\2283\\338"},8930:{c:"\\2291\\338"},8931:{c:"\\2292\\338"},8965:{c:"\\22BC"},8966:{c:"\\2A5E"},8988:{c:"\\250C"},8989:{c:"\\2510"},8990:{c:"\\2514"},8991:{c:"\\2518"},9001:{c:"\\27E8"},9002:{c:"\\27E9"},9642:{c:"\\25A0"},9652:{c:"\\25B2"},9653:{c:"\\25B3"},9656:{c:"\\25B6"},9662:{c:"\\25BC"},9663:{c:"\\25BD"},9666:{c:"\\25C0"},9723:{c:"\\25A1"},9724:{c:"\\25A0"},10072:{c:"\\2223"},10744:{c:"/",f:"I"},10764:{c:"\\222C\\222C"},10799:{c:"\\D7"},12296:{c:"\\27E8"},12297:{c:"\\27E9"},119808:{c:"A",f:"B"},119809:{c:"B",f:"B"},119810:{c:"C",f:"B"},119811:{c:"D",f:"B"},119812:{c:"E",f:"B"},119813:{c:"F",f:"B"},119814:{c:"G",f:"B"},119815:{c:"H",f:"B"},119816:{c:"I",f:"B"},119817:{c:"J",f:"B"},119818:{c:"K",f:"B"},119819:{c:"L",f:"B"},119820:{c:"M",f:"B"},119821:{c:"N",f:"B"},119822:{c:"O",f:"B"},119823:{c:"P",f:"B"},119824:{c:"Q",f:"B"},119825:{c:"R",f:"B"},119826:{c:"S",f:"B"},119827:{c:"T",f:"B"},119828:{c:"U",f:"B"},119829:{c:"V",f:"B"},119830:{c:"W",f:"B"},119831:{c:"X",f:"B"},119832:{c:"Y",f:"B"},119833:{c:"Z",f:"B"},119834:{c:"a",f:"B"},119835:{c:"b",f:"B"},119836:{c:"c",f:"B"},119837:{c:"d",f:"B"},119838:{c:"e",f:"B"},119839:{c:"f",f:"B"},119840:{c:"g",f:"B"},119841:{c:"h",f:"B"},119842:{c:"i",f:"B"},119843:{c:"j",f:"B"},119844:{c:"k",f:"B"},119845:{c:"l",f:"B"},119846:{c:"m",f:"B"},119847:{c:"n",f:"B"},119848:{c:"o",f:"B"},119849:{c:"p",f:"B"},119850:{c:"q",f:"B"},119851:{c:"r",f:"B"},119852:{c:"s",f:"B"},119853:{c:"t",f:"B"},119854:{c:"u",f:"B"},119855:{c:"v",f:"B"},119856:{c:"w",f:"B"},119857:{c:"x",f:"B"},119858:{c:"y",f:"B"},119859:{c:"z",f:"B"},119860:{c:"A",f:"I"},119861:{c:"B",f:"I"},119862:{c:"C",f:"I"},119863:{c:"D",f:"I"},119864:{c:"E",f:"I"},119865:{c:"F",f:"I"},119866:{c:"G",f:"I"},119867:{c:"H",f:"I"},119868:{c:"I",f:"I"},119869:{c:"J",f:"I"},119870:{c:"K",f:"I"},119871:{c:"L",f:"I"},119872:{c:"M",f:"I"},119873:{c:"N",f:"I"},119874:{c:"O",f:"I"},119875:{c:"P",f:"I"},119876:{c:"Q",f:"I"},119877:{c:"R",f:"I"},119878:{c:"S",f:"I"},119879:{c:"T",f:"I"},119880:{c:"U",f:"I"},119881:{c:"V",f:"I"},119882:{c:"W",f:"I"},119883:{c:"X",f:"I"},119884:{c:"Y",f:"I"},119885:{c:"Z",f:"I"},119886:{c:"a",f:"I"},119887:{c:"b",f:"I"},119888:{c:"c",f:"I"},119889:{c:"d",f:"I"},119890:{c:"e",f:"I"},119891:{c:"f",f:"I"},119892:{c:"g",f:"I"},119893:{c:"h",f:"I"},119894:{c:"i",f:"I"},119895:{c:"j",f:"I"},119896:{c:"k",f:"I"},119897:{c:"l",f:"I"},119898:{c:"m",f:"I"},119899:{c:"n",f:"I"},119900:{c:"o",f:"I"},119901:{c:"p",f:"I"},119902:{c:"q",f:"I"},119903:{c:"r",f:"I"},119904:{c:"s",f:"I"},119905:{c:"t",f:"I"},119906:{c:"u",f:"I"},119907:{c:"v",f:"I"},119908:{c:"w",f:"I"},119909:{c:"x",f:"I"},119910:{c:"y",f:"I"},119911:{c:"z",f:"I"},119912:{c:"A",f:"BI"},119913:{c:"B",f:"BI"},119914:{c:"C",f:"BI"},119915:{c:"D",f:"BI"},119916:{c:"E",f:"BI"},119917:{c:"F",f:"BI"},119918:{c:"G",f:"BI"},119919:{c:"H",f:"BI"},119920:{c:"I",f:"BI"},119921:{c:"J",f:"BI"},119922:{c:"K",f:"BI"},119923:{c:"L",f:"BI"},119924:{c:"M",f:"BI"},119925:{c:"N",f:"BI"},119926:{c:"O",f:"BI"},119927:{c:"P",f:"BI"},119928:{c:"Q",f:"BI"},119929:{c:"R",f:"BI"},119930:{c:"S",f:"BI"},119931:{c:"T",f:"BI"},119932:{c:"U",f:"BI"},119933:{c:"V",f:"BI"},119934:{c:"W",f:"BI"},119935:{c:"X",f:"BI"},119936:{c:"Y",f:"BI"},119937:{c:"Z",f:"BI"},119938:{c:"a",f:"BI"},119939:{c:"b",f:"BI"},119940:{c:"c",f:"BI"},119941:{c:"d",f:"BI"},119942:{c:"e",f:"BI"},119943:{c:"f",f:"BI"},119944:{c:"g",f:"BI"},119945:{c:"h",f:"BI"},119946:{c:"i",f:"BI"},119947:{c:"j",f:"BI"},119948:{c:"k",f:"BI"},119949:{c:"l",f:"BI"},119950:{c:"m",f:"BI"},119951:{c:"n",f:"BI"},119952:{c:"o",f:"BI"},119953:{c:"p",f:"BI"},119954:{c:"q",f:"BI"},119955:{c:"r",f:"BI"},119956:{c:"s",f:"BI"},119957:{c:"t",f:"BI"},119958:{c:"u",f:"BI"},119959:{c:"v",f:"BI"},119960:{c:"w",f:"BI"},119961:{c:"x",f:"BI"},119962:{c:"y",f:"BI"},119963:{c:"z",f:"BI"},119964:{c:"A",f:"SC"},119965:{c:"B",f:"SC"},119966:{c:"C",f:"SC"},119967:{c:"D",f:"SC"},119968:{c:"E",f:"SC"},119969:{c:"F",f:"SC"},119970:{c:"G",f:"SC"},119971:{c:"H",f:"SC"},119972:{c:"I",f:"SC"},119973:{c:"J",f:"SC"},119974:{c:"K",f:"SC"},119975:{c:"L",f:"SC"},119976:{c:"M",f:"SC"},119977:{c:"N",f:"SC"},119978:{c:"O",f:"SC"},119979:{c:"P",f:"SC"},119980:{c:"Q",f:"SC"},119981:{c:"R",f:"SC"},119982:{c:"S",f:"SC"},119983:{c:"T",f:"SC"},119984:{c:"U",f:"SC"},119985:{c:"V",f:"SC"},119986:{c:"W",f:"SC"},119987:{c:"X",f:"SC"},119988:{c:"Y",f:"SC"},119989:{c:"Z",f:"SC"},119990:{c:"a",f:"I"},119991:{c:"b",f:"I"},119992:{c:"c",f:"I"},119993:{c:"d",f:"I"},119994:{c:"e",f:"I"},119995:{c:"f",f:"I"},119996:{c:"g",f:"I"},119997:{c:"h",f:"I"},119998:{c:"i",f:"I"},119999:{c:"j",f:"I"},12e4:{c:"k",f:"I"},120001:{c:"l",f:"I"},120002:{c:"m",f:"I"},120003:{c:"n",f:"I"},120004:{c:"o",f:"I"},120005:{c:"p",f:"I"},120006:{c:"q",f:"I"},120007:{c:"r",f:"I"},120008:{c:"s",f:"I"},120009:{c:"t",f:"I"},120010:{c:"u",f:"I"},120011:{c:"v",f:"I"},120012:{c:"w",f:"I"},120013:{c:"x",f:"I"},120014:{c:"y",f:"I"},120015:{c:"z",f:"I"},120016:{c:"A",f:"SC"},120017:{c:"B",f:"SC"},120018:{c:"C",f:"SC"},120019:{c:"D",f:"SC"},120020:{c:"E",f:"SC"},120021:{c:"F",f:"SC"},120022:{c:"G",f:"SC"},120023:{c:"H",f:"SC"},120024:{c:"I",f:"SC"},120025:{c:"J",f:"SC"},120026:{c:"K",f:"SC"},120027:{c:"L",f:"SC"},120028:{c:"M",f:"SC"},120029:{c:"N",f:"SC"},120030:{c:"O",f:"SC"},120031:{c:"P",f:"SC"},120032:{c:"Q",f:"SC"},120033:{c:"R",f:"SC"},120034:{c:"S",f:"SC"},120035:{c:"T",f:"SC"},120036:{c:"U",f:"SC"},120037:{c:"V",f:"SC"},120038:{c:"W",f:"SC"},120039:{c:"X",f:"SC"},120040:{c:"Y",f:"SC"},120041:{c:"Z",f:"SC"},120042:{c:"a",f:"BI"},120043:{c:"b",f:"BI"},120044:{c:"c",f:"BI"},120045:{c:"d",f:"BI"},120046:{c:"e",f:"BI"},120047:{c:"f",f:"BI"},120048:{c:"g",f:"BI"},120049:{c:"h",f:"BI"},120050:{c:"i",f:"BI"},120051:{c:"j",f:"BI"},120052:{c:"k",f:"BI"},120053:{c:"l",f:"BI"},120054:{c:"m",f:"BI"},120055:{c:"n",f:"BI"},120056:{c:"o",f:"BI"},120057:{c:"p",f:"BI"},120058:{c:"q",f:"BI"},120059:{c:"r",f:"BI"},120060:{c:"s",f:"BI"},120061:{c:"t",f:"BI"},120062:{c:"u",f:"BI"},120063:{c:"v",f:"BI"},120064:{c:"w",f:"BI"},120065:{c:"x",f:"BI"},120066:{c:"y",f:"BI"},120067:{c:"z",f:"BI"},120068:{c:"A",f:"FR"},120069:{c:"B",f:"FR"},120070:{c:"C",f:"FR"},120071:{c:"D",f:"FR"},120072:{c:"E",f:"FR"},120073:{c:"F",f:"FR"},120074:{c:"G",f:"FR"},120075:{c:"H",f:"FR"},120076:{c:"I",f:"FR"},120077:{c:"J",f:"FR"},120078:{c:"K",f:"FR"},120079:{c:"L",f:"FR"},120080:{c:"M",f:"FR"},120081:{c:"N",f:"FR"},120082:{c:"O",f:"FR"},120083:{c:"P",f:"FR"},120084:{c:"Q",f:"FR"},120085:{c:"R",f:"FR"},120086:{c:"S",f:"FR"},120087:{c:"T",f:"FR"},120088:{c:"U",f:"FR"},120089:{c:"V",f:"FR"},120090:{c:"W",f:"FR"},120091:{c:"X",f:"FR"},120092:{c:"Y",f:"FR"},120093:{c:"Z",f:"FR"},120094:{c:"a",f:"FR"},120095:{c:"b",f:"FR"},120096:{c:"c",f:"FR"},120097:{c:"d",f:"FR"},120098:{c:"e",f:"FR"},120099:{c:"f",f:"FR"},120100:{c:"g",f:"FR"},120101:{c:"h",f:"FR"},120102:{c:"i",f:"FR"},120103:{c:"j",f:"FR"},120104:{c:"k",f:"FR"},120105:{c:"l",f:"FR"},120106:{c:"m",f:"FR"},120107:{c:"n",f:"FR"},120108:{c:"o",f:"FR"},120109:{c:"p",f:"FR"},120110:{c:"q",f:"FR"},120111:{c:"r",f:"FR"},120112:{c:"s",f:"FR"},120113:{c:"t",f:"FR"},120114:{c:"u",f:"FR"},120115:{c:"v",f:"FR"},120116:{c:"w",f:"FR"},120117:{c:"x",f:"FR"},120118:{c:"y",f:"FR"},120119:{c:"z",f:"FR"},120120:{c:"A",f:"A"},120121:{c:"B",f:"A"},120122:{c:"C",f:"A"},120123:{c:"D",f:"A"},120124:{c:"E",f:"A"},120125:{c:"F",f:"A"},120126:{c:"G",f:"A"},120127:{c:"H",f:"A"},120128:{c:"I",f:"A"},120129:{c:"J",f:"A"},120130:{c:"K",f:"A"},120131:{c:"L",f:"A"},120132:{c:"M",f:"A"},120133:{c:"N",f:"A"},120134:{c:"O",f:"A"},120135:{c:"P",f:"A"},120136:{c:"Q",f:"A"},120137:{c:"R",f:"A"},120138:{c:"S",f:"A"},120139:{c:"T",f:"A"},120140:{c:"U",f:"A"},120141:{c:"V",f:"A"},120142:{c:"W",f:"A"},120143:{c:"X",f:"A"},120144:{c:"Y",f:"A"},120145:{c:"Z",f:"A"},120146:{c:"a",f:"B"},120147:{c:"b",f:"B"},120148:{c:"c",f:"B"},120149:{c:"d",f:"B"},120150:{c:"e",f:"B"},120151:{c:"f",f:"B"},120152:{c:"g",f:"B"},120153:{c:"h",f:"B"},120154:{c:"i",f:"B"},120155:{c:"j",f:"B"},120156:{c:"k",f:"A"},120157:{c:"l",f:"B"},120158:{c:"m",f:"B"},120159:{c:"n",f:"B"},120160:{c:"o",f:"B"},120161:{c:"p",f:"B"},120162:{c:"q",f:"B"},120163:{c:"r",f:"B"},120164:{c:"s",f:"B"},120165:{c:"t",f:"B"},120166:{c:"u",f:"B"},120167:{c:"v",f:"B"},120168:{c:"w",f:"B"},120169:{c:"x",f:"B"},120170:{c:"y",f:"B"},120171:{c:"z",f:"B"},120172:{c:"A",f:"FR-B"},120173:{c:"B",f:"FR-B"},120174:{c:"C",f:"FR-B"},120175:{c:"D",f:"FR-B"},120176:{c:"E",f:"FR-B"},120177:{c:"F",f:"FR-B"},120178:{c:"G",f:"FR-B"},120179:{c:"H",f:"FR-B"},120180:{c:"I",f:"FR-B"},120181:{c:"J",f:"FR-B"},120182:{c:"K",f:"FR-B"},120183:{c:"L",f:"FR-B"},120184:{c:"M",f:"FR-B"},120185:{c:"N",f:"FR-B"},120186:{c:"O",f:"FR-B"},120187:{c:"P",f:"FR-B"},120188:{c:"Q",f:"FR-B"},120189:{c:"R",f:"FR-B"},120190:{c:"S",f:"FR-B"},120191:{c:"T",f:"FR-B"},120192:{c:"U",f:"FR-B"},120193:{c:"V",f:"FR-B"},120194:{c:"W",f:"FR-B"},120195:{c:"X",f:"FR-B"},120196:{c:"Y",f:"FR-B"},120197:{c:"Z",f:"FR-B"},120198:{c:"a",f:"FR-B"},120199:{c:"b",f:"FR-B"},120200:{c:"c",f:"FR-B"},120201:{c:"d",f:"FR-B"},120202:{c:"e",f:"FR-B"},120203:{c:"f",f:"FR-B"},120204:{c:"g",f:"FR-B"},120205:{c:"h",f:"FR-B"},120206:{c:"i",f:"FR-B"},120207:{c:"j",f:"FR-B"},120208:{c:"k",f:"FR-B"},120209:{c:"l",f:"FR-B"},120210:{c:"m",f:"FR-B"},120211:{c:"n",f:"FR-B"},120212:{c:"o",f:"FR-B"},120213:{c:"p",f:"FR-B"},120214:{c:"q",f:"FR-B"},120215:{c:"r",f:"FR-B"},120216:{c:"s",f:"FR-B"},120217:{c:"t",f:"FR-B"},120218:{c:"u",f:"FR-B"},120219:{c:"v",f:"FR-B"},120220:{c:"w",f:"FR-B"},120221:{c:"x",f:"FR-B"},120222:{c:"y",f:"FR-B"},120223:{c:"z",f:"FR-B"},120224:{c:"A",f:"SS"},120225:{c:"B",f:"SS"},120226:{c:"C",f:"SS"},120227:{c:"D",f:"SS"},120228:{c:"E",f:"SS"},120229:{c:"F",f:"SS"},120230:{c:"G",f:"SS"},120231:{c:"H",f:"SS"},120232:{c:"I",f:"SS"},120233:{c:"J",f:"SS"},120234:{c:"K",f:"SS"},120235:{c:"L",f:"SS"},120236:{c:"M",f:"SS"},120237:{c:"N",f:"SS"},120238:{c:"O",f:"SS"},120239:{c:"P",f:"SS"},120240:{c:"Q",f:"SS"},120241:{c:"R",f:"SS"},120242:{c:"S",f:"SS"},120243:{c:"T",f:"SS"},120244:{c:"U",f:"SS"},120245:{c:"V",f:"SS"},120246:{c:"W",f:"SS"},120247:{c:"X",f:"SS"},120248:{c:"Y",f:"SS"},120249:{c:"Z",f:"SS"},120250:{c:"a",f:"SS"},120251:{c:"b",f:"SS"},120252:{c:"c",f:"SS"},120253:{c:"d",f:"SS"},120254:{c:"e",f:"SS"},120255:{c:"f",f:"SS"},120256:{c:"g",f:"SS"},120257:{c:"h",f:"SS"},120258:{c:"i",f:"SS"},120259:{c:"j",f:"SS"},120260:{c:"k",f:"SS"},120261:{c:"l",f:"SS"},120262:{c:"m",f:"SS"},120263:{c:"n",f:"SS"},120264:{c:"o",f:"SS"},120265:{c:"p",f:"SS"},120266:{c:"q",f:"SS"},120267:{c:"r",f:"SS"},120268:{c:"s",f:"SS"},120269:{c:"t",f:"SS"},120270:{c:"u",f:"SS"},120271:{c:"v",f:"SS"},120272:{c:"w",f:"SS"},120273:{c:"x",f:"SS"},120274:{c:"y",f:"SS"},120275:{c:"z",f:"SS"},120276:{c:"A",f:"SS-B"},120277:{c:"B",f:"SS-B"},120278:{c:"C",f:"SS-B"},120279:{c:"D",f:"SS-B"},120280:{c:"E",f:"SS-B"},120281:{c:"F",f:"SS-B"},120282:{c:"G",f:"SS-B"},120283:{c:"H",f:"SS-B"},120284:{c:"I",f:"SS-B"},120285:{c:"J",f:"SS-B"},120286:{c:"K",f:"SS-B"},120287:{c:"L",f:"SS-B"},120288:{c:"M",f:"SS-B"},120289:{c:"N",f:"SS-B"},120290:{c:"O",f:"SS-B"},120291:{c:"P",f:"SS-B"},120292:{c:"Q",f:"SS-B"},120293:{c:"R",f:"SS-B"},120294:{c:"S",f:"SS-B"},120295:{c:"T",f:"SS-B"},120296:{c:"U",f:"SS-B"},120297:{c:"V",f:"SS-B"},120298:{c:"W",f:"SS-B"},120299:{c:"X",f:"SS-B"},120300:{c:"Y",f:"SS-B"},120301:{c:"Z",f:"SS-B"},120302:{c:"a",f:"SS-B"},120303:{c:"b",f:"SS-B"},120304:{c:"c",f:"SS-B"},120305:{c:"d",f:"SS-B"},120306:{c:"e",f:"SS-B"},120307:{c:"f",f:"SS-B"},120308:{c:"g",f:"SS-B"},120309:{c:"h",f:"SS-B"},120310:{c:"i",f:"SS-B"},120311:{c:"j",f:"SS-B"},120312:{c:"k",f:"SS-B"},120313:{c:"l",f:"SS-B"},120314:{c:"m",f:"SS-B"},120315:{c:"n",f:"SS-B"},120316:{c:"o",f:"SS-B"},120317:{c:"p",f:"SS-B"},120318:{c:"q",f:"SS-B"},120319:{c:"r",f:"SS-B"},120320:{c:"s",f:"SS-B"},120321:{c:"t",f:"SS-B"},120322:{c:"u",f:"SS-B"},120323:{c:"v",f:"SS-B"},120324:{c:"w",f:"SS-B"},120325:{c:"x",f:"SS-B"},120326:{c:"y",f:"SS-B"},120327:{c:"z",f:"SS-B"},120328:{c:"A",f:"SS-I"},120329:{c:"B",f:"SS-I"},120330:{c:"C",f:"SS-I"},120331:{c:"D",f:"SS-I"},120332:{c:"E",f:"SS-I"},120333:{c:"F",f:"SS-I"},120334:{c:"G",f:"SS-I"},120335:{c:"H",f:"SS-I"},120336:{c:"I",f:"SS-I"},120337:{c:"J",f:"SS-I"},120338:{c:"K",f:"SS-I"},120339:{c:"L",f:"SS-I"},120340:{c:"M",f:"SS-I"},120341:{c:"N",f:"SS-I"},120342:{c:"O",f:"SS-I"},120343:{c:"P",f:"SS-I"},120344:{c:"Q",f:"SS-I"},120345:{c:"R",f:"SS-I"},120346:{c:"S",f:"SS-I"},120347:{c:"T",f:"SS-I"},120348:{c:"U",f:"SS-I"},120349:{c:"V",f:"SS-I"},120350:{c:"W",f:"SS-I"},120351:{c:"X",f:"SS-I"},120352:{c:"Y",f:"SS-I"},120353:{c:"Z",f:"SS-I"},120354:{c:"a",f:"SS-I"},120355:{c:"b",f:"SS-I"},120356:{c:"c",f:"SS-I"},120357:{c:"d",f:"SS-I"},120358:{c:"e",f:"SS-I"},120359:{c:"f",f:"SS-I"},120360:{c:"g",f:"SS-I"},120361:{c:"h",f:"SS-I"},120362:{c:"i",f:"SS-I"},120363:{c:"j",f:"SS-I"},120364:{c:"k",f:"SS-I"},120365:{c:"l",f:"SS-I"},120366:{c:"m",f:"SS-I"},120367:{c:"n",f:"SS-I"},120368:{c:"o",f:"SS-I"},120369:{c:"p",f:"SS-I"},120370:{c:"q",f:"SS-I"},120371:{c:"r",f:"SS-I"},120372:{c:"s",f:"SS-I"},120373:{c:"t",f:"SS-I"},120374:{c:"u",f:"SS-I"},120375:{c:"v",f:"SS-I"},120376:{c:"w",f:"SS-I"},120377:{c:"x",f:"SS-I"},120378:{c:"y",f:"SS-I"},120379:{c:"z",f:"SS-I"},120380:{c:"A",f:"SS-I"},120381:{c:"B",f:"SS-I"},120382:{c:"C",f:"SS-I"},120383:{c:"D",f:"SS-I"},120384:{c:"E",f:"SS-I"},120385:{c:"F",f:"SS-I"},120386:{c:"G",f:"SS-I"},120387:{c:"H",f:"SS-I"},120388:{c:"I",f:"SS-I"},120389:{c:"J",f:"SS-I"},120390:{c:"K",f:"SS-I"},120391:{c:"L",f:"SS-I"},120392:{c:"M",f:"SS-I"},120393:{c:"N",f:"SS-I"},120394:{c:"O",f:"SS-I"},120395:{c:"P",f:"SS-I"},120396:{c:"Q",f:"SS-I"},120397:{c:"R",f:"SS-I"},120398:{c:"S",f:"SS-I"},120399:{c:"T",f:"SS-I"},120400:{c:"U",f:"SS-I"},120401:{c:"V",f:"SS-I"},120402:{c:"W",f:"SS-I"},120403:{c:"X",f:"SS-I"},120404:{c:"Y",f:"SS-I"},120405:{c:"Z",f:"SS-I"},120406:{c:"a",f:"SS-I"},120407:{c:"b",f:"SS-I"},120408:{c:"c",f:"SS-I"},120409:{c:"d",f:"SS-I"},120410:{c:"e",f:"SS-I"},120411:{c:"f",f:"SS-I"},120412:{c:"g",f:"SS-I"},120413:{c:"h",f:"SS-I"},120414:{c:"i",f:"SS-I"},120415:{c:"j",f:"SS-I"},120416:{c:"k",f:"SS-I"},120417:{c:"l",f:"SS-I"},120418:{c:"m",f:"SS-I"},120419:{c:"n",f:"SS-I"},120420:{c:"o",f:"SS-I"},120421:{c:"p",f:"SS-I"},120422:{c:"q",f:"SS-I"},120423:{c:"r",f:"SS-I"},120424:{c:"s",f:"SS-I"},120425:{c:"t",f:"SS-I"},120426:{c:"u",f:"SS-I"},120427:{c:"v",f:"SS-I"},120428:{c:"w",f:"SS-I"},120429:{c:"x",f:"SS-I"},120430:{c:"y",f:"SS-I"},120431:{c:"z",f:"SS-I"},120432:{c:"A",f:"T"},120433:{c:"B",f:"T"},120434:{c:"C",f:"T"},120435:{c:"D",f:"T"},120436:{c:"E",f:"T"},120437:{c:"F",f:"T"},120438:{c:"G",f:"T"},120439:{c:"H",f:"T"},120440:{c:"I",f:"T"},120441:{c:"J",f:"T"},120442:{c:"K",f:"T"},120443:{c:"L",f:"T"},120444:{c:"M",f:"T"},120445:{c:"N",f:"T"},120446:{c:"O",f:"T"},120447:{c:"P",f:"T"},120448:{c:"Q",f:"T"},120449:{c:"R",f:"T"},120450:{c:"S",f:"T"},120451:{c:"T",f:"T"},120452:{c:"U",f:"T"},120453:{c:"V",f:"T"},120454:{c:"W",f:"T"},120455:{c:"X",f:"T"},120456:{c:"Y",f:"T"},120457:{c:"Z",f:"T"},120458:{c:"a",f:"T"},120459:{c:"b",f:"T"},120460:{c:"c",f:"T"},120461:{c:"d",f:"T"},120462:{c:"e",f:"T"},120463:{c:"f",f:"T"},120464:{c:"g",f:"T"},120465:{c:"h",f:"T"},120466:{c:"i",f:"T"},120467:{c:"j",f:"T"},120468:{c:"k",f:"T"},120469:{c:"l",f:"T"},120470:{c:"m",f:"T"},120471:{c:"n",f:"T"},120472:{c:"o",f:"T"},120473:{c:"p",f:"T"},120474:{c:"q",f:"T"},120475:{c:"r",f:"T"},120476:{c:"s",f:"T"},120477:{c:"t",f:"T"},120478:{c:"u",f:"T"},120479:{c:"v",f:"T"},120480:{c:"w",f:"T"},120481:{c:"x",f:"T"},120482:{c:"y",f:"T"},120483:{c:"z",f:"T"},120484:{c:"\\131",f:"MI"},120485:{c:"\\237",f:"MI"},120488:{c:"A",f:"B"},120489:{c:"B",f:"B"},120490:{c:"\\393",f:"B"},120491:{c:"\\394",f:"B"},120492:{c:"E",f:"B"},120493:{c:"Z",f:"B"},120494:{c:"H",f:"B"},120495:{c:"\\398",f:"B"},120496:{c:"I",f:"B"},120497:{c:"K",f:"B"},120498:{c:"\\39B",f:"B"},120499:{c:"M",f:"B"},120500:{c:"N",f:"B"},120501:{c:"\\39E",f:"B"},120502:{c:"O",f:"B"},120503:{c:"\\3A0",f:"B"},120504:{c:"P",f:"B"},120505:{c:"\\398",f:"B"},120506:{c:"\\3A3",f:"B"},120507:{c:"T",f:"B"},120508:{c:"\\3A5",f:"B"},120509:{c:"\\3A6",f:"B"},120510:{c:"X",f:"B"},120511:{c:"\\3A8",f:"B"},120512:{c:"\\3A9",f:"B"},120513:{c:"\\2207",f:"B"},120514:{c:"\\3B1",f:"BI"},120515:{c:"\\3B2",f:"BI"},120516:{c:"\\3B3",f:"BI"},120517:{c:"\\3B4",f:"BI"},120518:{c:"\\3B5",f:"BI"},120519:{c:"\\3B6",f:"BI"},120520:{c:"\\3B7",f:"BI"},120521:{c:"\\3B8",f:"BI"},120522:{c:"\\3B9",f:"BI"},120523:{c:"\\3BA",f:"BI"},120524:{c:"\\3BB",f:"BI"},120525:{c:"\\3BC",f:"BI"},120526:{c:"\\3BD",f:"BI"},120527:{c:"\\3BE",f:"BI"},120528:{c:"\\3BF",f:"BI"},120529:{c:"\\3C0",f:"BI"},120530:{c:"\\3C1",f:"BI"},120531:{c:"\\3C2",f:"BI"},120532:{c:"\\3C3",f:"BI"},120533:{c:"\\3C4",f:"BI"},120534:{c:"\\3C5",f:"BI"},120535:{c:"\\3C6",f:"BI"},120536:{c:"\\3C7",f:"BI"},120537:{c:"\\3C8",f:"BI"},120538:{c:"\\3C9",f:"BI"},120539:{c:"\\2202",f:"BI"},120540:{c:"\\3F5",f:"BI"},120541:{c:"\\3D1",f:"BI"},120542:{c:"\\E009",f:"A"},120543:{c:"\\3D5",f:"BI"},120544:{c:"\\3F1",f:"BI"},120545:{c:"\\3D6",f:"BI"},120546:{c:"A",f:"I"},120547:{c:"B",f:"I"},120548:{c:"\\393",f:"I"},120549:{c:"\\394",f:"I"},120550:{c:"E",f:"I"},120551:{c:"Z",f:"I"},120552:{c:"H",f:"I"},120553:{c:"\\398",f:"I"},120554:{c:"I",f:"I"},120555:{c:"K",f:"I"},120556:{c:"\\39B",f:"I"},120557:{c:"M",f:"I"},120558:{c:"N",f:"I"},120559:{c:"\\39E",f:"I"},120560:{c:"O",f:"I"},120561:{c:"\\3A0",f:"I"},120562:{c:"P",f:"I"},120563:{c:"\\398",f:"I"},120564:{c:"\\3A3",f:"I"},120565:{c:"T",f:"I"},120566:{c:"\\3A5",f:"I"},120567:{c:"\\3A6",f:"I"},120568:{c:"X",f:"I"},120569:{c:"\\3A8",f:"I"},120570:{c:"\\3A9",f:"I"},120571:{c:"\\2207",f:""},120572:{c:"\\3B1",f:"I"},120573:{c:"\\3B2",f:"I"},120574:{c:"\\3B3",f:"I"},120575:{c:"\\3B4",f:"I"},120576:{c:"\\3B5",f:"I"},120577:{c:"\\3B6",f:"I"},120578:{c:"\\3B7",f:"I"},120579:{c:"\\3B8",f:"I"},120580:{c:"\\3B9",f:"I"},120581:{c:"\\3BA",f:"I"},120582:{c:"\\3BB",f:"I"},120583:{c:"\\3BC",f:"I"},120584:{c:"\\3BD",f:"I"},120585:{c:"\\3BE",f:"I"},120586:{c:"\\3BF",f:"I"},120587:{c:"\\3C0",f:"I"},120588:{c:"\\3C1",f:"I"},120589:{c:"\\3C2",f:"I"},120590:{c:"\\3C3",f:"I"},120591:{c:"\\3C4",f:"I"},120592:{c:"\\3C5",f:"I"},120593:{c:"\\3C6",f:"I"},120594:{c:"\\3C7",f:"I"},120595:{c:"\\3C8",f:"I"},120596:{c:"\\3C9",f:"I"},120597:{c:"\\2202",f:""},120598:{c:"\\3F5",f:"I"},120599:{c:"\\3D1",f:"I"},120600:{c:"\\E009",f:"A"},120601:{c:"\\3D5",f:"I"},120602:{c:"\\3F1",f:"I"},120603:{c:"\\3D6",f:"I"},120604:{c:"A",f:"BI"},120605:{c:"B",f:"BI"},120606:{c:"\\393",f:"BI"},120607:{c:"\\394",f:"BI"},120608:{c:"E",f:"BI"},120609:{c:"Z",f:"BI"},120610:{c:"H",f:"BI"},120611:{c:"\\398",f:"BI"},120612:{c:"I",f:"BI"},120613:{c:"K",f:"BI"},120614:{c:"\\39B",f:"BI"},120615:{c:"M",f:"BI"},120616:{c:"N",f:"BI"},120617:{c:"\\39E",f:"BI"},120618:{c:"O",f:"BI"},120619:{c:"\\3A0",f:"BI"},120620:{c:"P",f:"BI"},120621:{c:"\\398",f:"BI"},120622:{c:"\\3A3",f:"BI"},120623:{c:"T",f:"BI"},120624:{c:"\\3A5",f:"BI"},120625:{c:"\\3A6",f:"BI"},120626:{c:"X",f:"BI"},120627:{c:"\\3A8",f:"BI"},120628:{c:"\\3A9",f:"BI"},120629:{c:"\\2207",f:""},120630:{c:"\\3B1",f:"BI"},120631:{c:"\\3B2",f:"BI"},120632:{c:"\\3B3",f:"BI"},120633:{c:"\\3B4",f:"BI"},120634:{c:"\\3B5",f:"BI"},120635:{c:"\\3B6",f:"BI"},120636:{c:"\\3B7",f:"BI"},120637:{c:"\\3B8",f:"BI"},120638:{c:"\\3B9",f:"BI"},120639:{c:"\\3BA",f:"BI"},120640:{c:"\\3BB",f:"BI"},120641:{c:"\\3BC",f:"BI"},120642:{c:"\\3BD",f:"BI"},120643:{c:"\\3BE",f:"BI"},120644:{c:"\\3BF",f:"BI"},120645:{c:"\\3C0",f:"BI"},120646:{c:"\\3C1",f:"BI"},120647:{c:"\\3C2",f:"BI"},120648:{c:"\\3C3",f:"BI"},120649:{c:"\\3C4",f:"BI"},120650:{c:"\\3C5",f:"BI"},120651:{c:"\\3C6",f:"BI"},120652:{c:"\\3C7",f:"BI"},120653:{c:"\\3C8",f:"BI"},120654:{c:"\\3C9",f:"BI"},120655:{c:"\\2202",f:""},120656:{c:"\\3F5",f:"BI"},120657:{c:"\\3D1",f:"BI"},120658:{c:"\\E009",f:"A"},120659:{c:"\\3D5",f:"BI"},120660:{c:"\\3F1",f:"BI"},120661:{c:"\\3D6",f:"BI"},120662:{c:"A",f:"SS-B"},120663:{c:"B",f:"SS-B"},120664:{c:"\\393",f:"SS-B"},120665:{c:"\\394",f:"SS-B"},120666:{c:"E",f:"SS-B"},120667:{c:"Z",f:"SS-B"},120668:{c:"H",f:"SS-B"},120669:{c:"\\398",f:"SS-B"},120670:{c:"I",f:"SS-B"},120671:{c:"K",f:"SS-B"},120672:{c:"\\39B",f:"SS-B"},120673:{c:"M",f:"SS-B"},120674:{c:"N",f:"SS-B"},120675:{c:"\\39E",f:"SS-B"},120676:{c:"O",f:"SS-B"},120677:{c:"\\3A0",f:"SS-B"},120678:{c:"P",f:"SS-B"},120679:{c:"\\398",f:"SS-B"},120680:{c:"\\3A3",f:"SS-B"},120681:{c:"T",f:"SS-B"},120682:{c:"\\3A5",f:"SS-B"},120683:{c:"\\3A6",f:"SS-B"},120684:{c:"X",f:"SS-B"},120685:{c:"\\3A8",f:"SS-B"},120686:{c:"\\3A9",f:"SS-B"},120687:{c:"\\2207",f:""},120688:{c:"\\3B1",f:"BI"},120689:{c:"\\3B2",f:"BI"},120690:{c:"\\3B3",f:"BI"},120691:{c:"\\3B4",f:"BI"},120692:{c:"\\3B5",f:"BI"},120693:{c:"\\3B6",f:"BI"},120694:{c:"\\3B7",f:"BI"},120695:{c:"\\3B8",f:"BI"},120696:{c:"\\3B9",f:"BI"},120697:{c:"\\3BA",f:"BI"},120698:{c:"\\3BB",f:"BI"},120699:{c:"\\3BC",f:"BI"},120700:{c:"\\3BD",f:"BI"},120701:{c:"\\3BE",f:"BI"},120702:{c:"\\3BF",f:"BI"},120703:{c:"\\3C0",f:"BI"},120704:{c:"\\3C1",f:"BI"},120705:{c:"\\3C2",f:"BI"},120706:{c:"\\3C3",f:"BI"},120707:{c:"\\3C4",f:"BI"},120708:{c:"\\3C5",f:"BI"},120709:{c:"\\3C6",f:"BI"},120710:{c:"\\3C7",f:"BI"},120711:{c:"\\3C8",f:"BI"},120712:{c:"\\3C9",f:"BI"},120713:{c:"\\2202",f:""},120714:{c:"\\3F5",f:"BI"},120715:{c:"\\3D1",f:"BI"},120716:{c:"\\E009",f:"A"},120717:{c:"\\3D5",f:"BI"},120718:{c:"\\3F1",f:"BI"},120719:{c:"\\3D6",f:"BI"},120720:{c:"A",f:"SS-I"},120721:{c:"B",f:"SS-I"},120722:{c:"\\393",f:"SS-I"},120723:{c:"\\394",f:"SS-I"},120724:{c:"E",f:"SS-I"},120725:{c:"Z",f:"SS-I"},120726:{c:"H",f:"SS-I"},120727:{c:"\\398",f:"SS-I"},120728:{c:"I",f:"SS-I"},120729:{c:"K",f:"SS-I"},120730:{c:"\\39B",f:"SS-I"},120731:{c:"M",f:"SS-I"},120732:{c:"N",f:"SS-I"},120733:{c:"\\39E",f:"SS-I"},120734:{c:"O",f:"SS-I"},120735:{c:"\\3A0",f:"SS-I"},120736:{c:"P",f:"SS-I"},120737:{c:"\\398",f:"SS-I"},120738:{c:"\\3A3",f:"SS-I"},120739:{c:"T",f:"SS-I"},120740:{c:"\\3A5",f:"SS-I"},120741:{c:"\\3A6",f:"SS-I"},120742:{c:"X",f:"SS-I"},120743:{c:"\\3A8",f:"SS-I"},120744:{c:"\\3A9",f:"SS-I"},120745:{c:"\\2207",f:""},120746:{c:"\\3B1",f:"BI"},120747:{c:"\\3B2",f:"BI"},120748:{c:"\\3B3",f:"BI"},120749:{c:"\\3B4",f:"BI"},120750:{c:"\\3B5",f:"BI"},120751:{c:"\\3B6",f:"BI"},120752:{c:"\\3B7",f:"BI"},120753:{c:"\\3B8",f:"BI"},120754:{c:"\\3B9",f:"BI"},120755:{c:"\\3BA",f:"BI"},120756:{c:"\\3BB",f:"BI"},120757:{c:"\\3BC",f:"BI"},120758:{c:"\\3BD",f:"BI"},120759:{c:"\\3BE",f:"BI"},120760:{c:"\\3BF",f:"BI"},120761:{c:"\\3C0",f:"BI"},120762:{c:"\\3C1",f:"BI"},120763:{c:"\\3C2",f:"BI"},120764:{c:"\\3C3",f:"BI"},120765:{c:"\\3C4",f:"BI"},120766:{c:"\\3C5",f:"BI"},120767:{c:"\\3C6",f:"BI"},120768:{c:"\\3C7",f:"BI"},120769:{c:"\\3C8",f:"BI"},120770:{c:"\\3C9",f:"BI"},120771:{c:"\\2202",f:""},120772:{c:"\\3F5",f:"BI"},120773:{c:"\\3D1",f:"BI"},120774:{c:"\\E009",f:"A"},120775:{c:"\\3D5",f:"BI"},120776:{c:"\\3F1",f:"BI"},120777:{c:"\\3D6",f:"BI"},120778:{c:"F",f:"I"},120779:{c:"\\3DD",f:"A"},120782:{c:"0",f:"B"},120783:{c:"1",f:"B"},120784:{c:"2",f:"B"},120785:{c:"3",f:"B"},120786:{c:"4",f:"B"},120787:{c:"5",f:"B"},120788:{c:"6",f:"B"},120789:{c:"7",f:"B"},120790:{c:"8",f:"B"},120791:{c:"9",f:"B"},120792:{c:"0",f:"B"},120793:{c:"1",f:"B"},120794:{c:"2",f:"B"},120795:{c:"3",f:"B"},120796:{c:"4",f:"B"},120797:{c:"5",f:"B"},120798:{c:"6",f:"B"},120799:{c:"7",f:"B"},120800:{c:"8",f:"B"},120801:{c:"9",f:"B"},120802:{c:"0",f:"SS"},120803:{c:"1",f:"SS"},120804:{c:"2",f:"SS"},120805:{c:"3",f:"SS"},120806:{c:"4",f:"SS"},120807:{c:"5",f:"SS"},120808:{c:"6",f:"SS"},120809:{c:"7",f:"SS"},120810:{c:"8",f:"SS"},120811:{c:"9",f:"SS"},120812:{c:"0",f:"SS-B"},120813:{c:"1",f:"SS-B"},120814:{c:"2",f:"SS-B"},120815:{c:"3",f:"SS-B"},120816:{c:"4",f:"SS-B"},120817:{c:"5",f:"SS-B"},120818:{c:"6",f:"SS-B"},120819:{c:"7",f:"SS-B"},120820:{c:"8",f:"SS-B"},120821:{c:"9",f:"SS-B"},120822:{c:"0",f:"T"},120823:{c:"1",f:"T"},120824:{c:"2",f:"T"},120825:{c:"3",f:"T"},120826:{c:"4",f:"T"},120827:{c:"5",f:"T"},120828:{c:"6",f:"T"},120829:{c:"7",f:"T"},120830:{c:"8",f:"T"},120831:{c:"9",f:"T"}})},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0}),f.normal={32:[0,0,.25],33:[.716,0,.278],34:[.694,-.379,.5],35:[.694,.194,.833],36:[.75,.056,.5],37:[.75,.056,.833],38:[.716,.022,.778],39:[.694,-.379,.278],40:[.75,.25,.389],41:[.75,.25,.389],42:[.75,-.32,.5],43:[.583,.082,.778],44:[.121,.194,.278],45:[.252,-.179,.333],46:[.12,0,.278],47:[.75,.25,.5],48:[.666,.022,.5],49:[.666,0,.5],50:[.666,0,.5],51:[.665,.022,.5],52:[.677,0,.5],53:[.666,.022,.5],54:[.666,.022,.5],55:[.676,.022,.5],56:[.666,.022,.5],57:[.666,.022,.5],58:[.43,0,.278],59:[.43,.194,.278],60:[.54,.04,.778],61:[.583,.082,.778],62:[.54,.04,.778],63:[.705,0,.472],64:[.705,.011,.778],65:[.716,0,.75],66:[.683,0,.708],67:[.705,.021,.722],68:[.683,0,.764],69:[.68,0,.681],70:[.68,0,.653],71:[.705,.022,.785],72:[.683,0,.75],73:[.683,0,.361],74:[.683,.022,.514],75:[.683,0,.778],76:[.683,0,.625],77:[.683,0,.917],78:[.683,0,.75],79:[.705,.022,.778],80:[.683,0,.681],81:[.705,.193,.778],82:[.683,.022,.736],83:[.705,.022,.556],84:[.677,0,.722],85:[.683,.022,.75],86:[.683,.022,.75],87:[.683,.022,1.028],88:[.683,0,.75],89:[.683,0,.75],90:[.683,0,.611],91:[.75,.25,.278],92:[.75,.25,.5],93:[.75,.25,.278],94:[.694,-.531,.5],95:[-.025,.062,.5],96:[.699,-.505,.5],97:[.448,.011,.5],98:[.694,.011,.556],99:[.448,.011,.444],100:[.694,.011,.556],101:[.448,.011,.444],102:[.705,0,.306,{ic:.066}],103:[.453,.206,.5],104:[.694,0,.556],105:[.669,0,.278],106:[.669,.205,.306],107:[.694,0,.528],108:[.694,0,.278],109:[.442,0,.833],110:[.442,0,.556],111:[.448,.01,.5],112:[.442,.194,.556],113:[.442,.194,.528],114:[.442,0,.392],115:[.448,.011,.394],116:[.615,.01,.389],117:[.442,.011,.556],118:[.431,.011,.528],119:[.431,.011,.722],120:[.431,0,.528],121:[.431,.204,.528],122:[.431,0,.444],123:[.75,.25,.5],124:[.75,.249,.278],125:[.75,.25,.5],126:[.318,-.215,.5],160:[0,0,.25],163:[.714,.011,.769],165:[.683,0,.75],168:[.669,-.554,.5],172:[.356,-.089,.667],174:[.709,.175,.947],175:[.59,-.544,.5],176:[.715,-.542,.5],177:[.666,0,.778],180:[.699,-.505,.5],183:[.31,-.19,.278],215:[.491,-.009,.778],240:[.749,.021,.556],247:[.537,.036,.778],295:[.695,.013,.54],305:[.442,0,.278,{sk:.0278}],567:[.442,.205,.306,{sk:.0833}],697:[.56,-.043,.275],710:[.694,-.531,.5],711:[.644,-.513,.5],713:[.59,-.544,.5],714:[.699,-.505,.5],715:[.699,-.505,.5],728:[.694,-.515,.5],729:[.669,-.549,.5],730:[.715,-.542,.5],732:[.668,-.565,.5],768:[.699,-.505,0],769:[.699,-.505,0],770:[.694,-.531,0],771:[.668,-.565,0],772:[.59,-.544,0],774:[.694,-.515,0],775:[.669,-.549,0],776:[.669,-.554,0],778:[.715,-.542,0],779:[.701,-.51,0],780:[.644,-.513,0],824:[.716,.215,0],913:[.716,0,.75],914:[.683,0,.708],915:[.68,0,.625],916:[.716,0,.833],917:[.68,0,.681],918:[.683,0,.611],919:[.683,0,.75],920:[.705,.022,.778],921:[.683,0,.361],922:[.683,0,.778],923:[.716,0,.694],924:[.683,0,.917],925:[.683,0,.75],926:[.677,0,.667],927:[.705,.022,.778],928:[.68,0,.75],929:[.683,0,.681],930:[.705,.022,.778],931:[.683,0,.722],932:[.677,0,.722],933:[.705,0,.778],934:[.683,0,.722],935:[.683,0,.75],936:[.683,0,.778],937:[.704,0,.722],978:[.705,0,.778],988:[.68,0,.653],989:[.605,.085,.778],1008:[.434,.006,.667,{ic:.067}],1014:[.44,0,.429],8192:[0,0,.5],8193:[0,0,1],8194:[0,0,.5],8195:[0,0,1],8196:[0,0,.333],8197:[0,0,.25],8198:[0,0,.167],8201:[0,0,.167],8202:[0,0,.1],8203:[0,0,0],8204:[0,0,0],8211:[.285,-.248,.5],8212:[.285,-.248,1],8213:[.285,-.248,1],8214:[.75,.25,.5],8215:[-.025,.062,.5],8216:[.694,-.379,.278],8217:[.694,-.379,.278],8220:[.694,-.379,.5],8221:[.694,-.379,.5],8224:[.705,.216,.444],8225:[.705,.205,.444],8226:[.444,-.055,.5],8230:[.12,0,1.172],8242:[.56,-.043,.275],8243:[.56,0,.55],8244:[.56,0,.825],8245:[.56,-.043,.275],8246:[.56,0,.55],8247:[.56,0,.825],8254:[.59,-.544,.5],8260:[.75,.25,.5],8279:[.56,0,1.1],8289:[0,0,0],8290:[0,0,0],8291:[0,0,0],8292:[0,0,0],8407:[.714,-.516,.5],8450:[.702,.019,.722],8459:[.717,.036,.969,{ic:.272,sk:.333}],8460:[.666,.133,.72],8461:[.683,0,.778],8462:[.694,.011,.576,{sk:-.0278}],8463:[.695,.013,.54],8464:[.717,.314,1.052,{ic:.081,sk:.417}],8465:[.686,.026,.554],8466:[.717,.017,.874,{ic:.161,sk:.306}],8467:[.705,.02,.417,{sk:.111}],8469:[.683,.02,.722],8472:[.453,.216,.636,{sk:.111}],8473:[.683,0,.611],8474:[.701,.181,.778],8475:[.717,.017,.85,{sk:.194}],8476:[.686,.026,.828],8477:[.683,0,.722],8484:[.683,0,.667],8486:[.704,0,.722],8487:[.684,.022,.722],8488:[.729,.139,.602],8492:[.708,.028,.908,{sk:.194}],8493:[.685,.024,.613],8496:[.707,.008,.562,{ic:.156,sk:.139}],8497:[.735,.036,.895,{ic:.095,sk:.222}],8498:[.695,0,.556],8499:[.721,.05,1.08,{ic:.136,sk:.444}],8501:[.694,0,.611],8502:[.763,.021,.667],8503:[.764,.043,.444],8504:[.764,.043,.667],8513:[.705,.023,.639],8592:[.511,.011,1],8593:[.694,.193,.5],8594:[.511,.011,1],8595:[.694,.194,.5],8596:[.511,.011,1],8597:[.772,.272,.5],8598:[.72,.195,1],8599:[.72,.195,1],8600:[.695,.22,1],8601:[.695,.22,1],8602:[.437,-.06,1],8603:[.437,-.06,1],8606:[.417,-.083,1],8608:[.417,-.083,1],8610:[.417,-.083,1.111],8611:[.417,-.083,1.111],8614:[.511,.011,1],8617:[.511,.011,1.126],8618:[.511,.011,1.126],8619:[.575,.041,1],8620:[.575,.041,1],8621:[.417,-.083,1.389],8622:[.437,-.06,1],8624:[.722,0,.5],8625:[.722,0,.5],8630:[.461,0,1],8631:[.46,0,1],8634:[.65,.083,.778],8635:[.65,.083,.778],8636:[.511,-.23,1],8637:[.27,.011,1],8638:[.694,.194,.417],8639:[.694,.194,.417],8640:[.511,-.23,1],8641:[.27,.011,1],8642:[.694,.194,.417],8643:[.694,.194,.417],8644:[.667,0,1],8646:[.667,0,1],8647:[.583,.083,1],8648:[.694,.193,.833],8649:[.583,.083,1],8650:[.694,.194,.833],8651:[.514,.014,1],8652:[.671,.011,1],8653:[.534,.035,1],8654:[.534,.037,1],8655:[.534,.035,1],8656:[.525,.024,1],8657:[.694,.194,.611],8658:[.525,.024,1],8659:[.694,.194,.611],8660:[.526,.025,1],8661:[.772,.272,.611],8666:[.611,.111,1],8667:[.611,.111,1],8669:[.417,-.083,1],8672:[.437,-.064,1.334],8674:[.437,-.064,1.334],8704:[.694,.022,.556],8705:[.846,.021,.5],8706:[.715,.022,.531,{sk:.0833}],8707:[.694,0,.556],8708:[.716,.215,.556],8709:[.772,.078,.5],8710:[.716,0,.833],8711:[.683,.033,.833],8712:[.54,.04,.667],8713:[.716,.215,.667],8715:[.54,.04,.667],8716:[.716,.215,.667],8717:[.44,0,.429],8719:[.75,.25,.944],8720:[.75,.25,.944],8721:[.75,.25,1.056],8722:[.583,.082,.778],8723:[.5,.166,.778],8724:[.766,.093,.778],8725:[.75,.25,.5],8726:[.43,.023,.778],8727:[.465,-.035,.5],8728:[.444,-.055,.5],8729:[.444,-.055,.5],8730:[.8,.2,.833],8733:[.442,.011,.778],8734:[.442,.011,1],8736:[.694,0,.722],8737:[.714,.02,.722],8738:[.551,.051,.722],8739:[.75,.249,.278],8740:[.75,.252,.278],8741:[.75,.25,.5],8742:[.75,.25,.5],8743:[.598,.022,.667],8744:[.598,.022,.667],8745:[.598,.022,.667],8746:[.598,.022,.667],8747:[.716,.216,.417,{ic:.055}],8748:[.805,.306,.819,{ic:.138}],8749:[.805,.306,1.166,{ic:.138}],8750:[.805,.306,.472,{ic:.138}],8756:[.471,.082,.667],8757:[.471,.082,.667],8764:[.367,-.133,.778],8765:[.367,-.133,.778],8768:[.583,.083,.278],8769:[.467,-.032,.778],8770:[.463,-.034,.778],8771:[.464,-.036,.778],8772:[.716,.215,.778],8773:[.589,-.022,.778],8774:[.652,.155,.778],8775:[.652,.155,.778],8776:[.483,-.055,.778],8777:[.716,.215,.778],8778:[.579,.039,.778],8781:[.484,-.016,.778],8782:[.492,-.008,.778],8783:[.492,-.133,.778],8784:[.67,-.133,.778],8785:[.609,.108,.778],8786:[.601,.101,.778],8787:[.601,.102,.778],8790:[.367,-.133,.778],8791:[.721,-.133,.778],8796:[.859,-.133,.778],8800:[.716,.215,.778],8801:[.464,-.036,.778],8802:[.716,.215,.778],8804:[.636,.138,.778],8805:[.636,.138,.778],8806:[.753,.175,.778],8807:[.753,.175,.778],8808:[.752,.286,.778],8809:[.752,.286,.778],8810:[.568,.067,1],8811:[.567,.067,1],8812:[.75,.25,.5],8813:[.716,.215,.778],8814:[.708,.209,.778],8815:[.708,.209,.778],8816:[.801,.303,.778],8817:[.801,.303,.778],8818:[.732,.228,.778],8819:[.732,.228,.778],8820:[.732,.228,.778],8821:[.732,.228,.778],8822:[.681,.253,.778],8823:[.681,.253,.778],8824:[.716,.253,.778],8825:[.716,.253,.778],8826:[.539,.041,.778],8827:[.539,.041,.778],8828:[.58,.153,.778],8829:[.58,.154,.778],8830:[.732,.228,.778],8831:[.732,.228,.778],8832:[.705,.208,.778],8833:[.705,.208,.778],8834:[.54,.04,.778],8835:[.54,.04,.778],8836:[.716,.215,.778],8837:[.716,.215,.778],8838:[.636,.138,.778],8839:[.636,.138,.778],8840:[.801,.303,.778],8841:[.801,.303,.778],8842:[.635,.241,.778],8843:[.635,.241,.778],8846:[.598,.022,.667],8847:[.539,.041,.778],8848:[.539,.041,.778],8849:[.636,.138,.778],8850:[.636,.138,.778],8851:[.598,0,.667],8852:[.598,0,.667],8853:[.583,.083,.778],8854:[.583,.083,.778],8855:[.583,.083,.778],8856:[.583,.083,.778],8857:[.583,.083,.778],8858:[.582,.082,.778],8859:[.582,.082,.778],8861:[.582,.082,.778],8862:[.689,0,.778],8863:[.689,0,.778],8864:[.689,0,.778],8865:[.689,0,.778],8866:[.694,0,.611],8867:[.694,0,.611],8868:[.668,0,.778],8869:[.668,0,.778],8872:[.75,.249,.867],8873:[.694,0,.722],8874:[.694,0,.889],8876:[.695,0,.611],8877:[.695,0,.611],8878:[.695,0,.722],8879:[.695,0,.722],8882:[.539,.041,.778],8883:[.539,.041,.778],8884:[.636,.138,.778],8885:[.636,.138,.778],8888:[.408,-.092,1.111],8890:[.431,.212,.556],8891:[.716,0,.611],8892:[.716,0,.611],8896:[.75,.249,.833],8897:[.75,.249,.833],8898:[.75,.249,.833],8899:[.75,.249,.833],8900:[.488,-.012,.5],8901:[.31,-.19,.278],8902:[.486,-.016,.5],8903:[.545,.044,.778],8904:[.505,.005,.9],8905:[.492,-.008,.778],8906:[.492,-.008,.778],8907:[.694,.022,.778],8908:[.694,.022,.778],8909:[.464,-.036,.778],8910:[.578,.021,.76],8911:[.578,.022,.76],8912:[.54,.04,.778],8913:[.54,.04,.778],8914:[.598,.022,.667],8915:[.598,.022,.667],8916:[.736,.022,.667],8918:[.541,.041,.778],8919:[.541,.041,.778],8920:[.568,.067,1.333],8921:[.568,.067,1.333],8922:[.886,.386,.778],8923:[.886,.386,.778],8926:[.734,0,.778],8927:[.734,0,.778],8928:[.801,.303,.778],8929:[.801,.303,.778],8930:[.716,.215,.778],8931:[.716,.215,.778],8934:[.73,.359,.778],8935:[.73,.359,.778],8936:[.73,.359,.778],8937:[.73,.359,.778],8938:[.706,.208,.778],8939:[.706,.208,.778],8940:[.802,.303,.778],8941:[.801,.303,.778],8942:[1.3,.03,.278],8943:[.31,-.19,1.172],8945:[1.52,-.1,1.282],8965:[.716,0,.611],8966:[.813,.097,.611],8968:[.75,.25,.444],8969:[.75,.25,.444],8970:[.75,.25,.444],8971:[.75,.25,.444],8988:[.694,-.306,.5],8989:[.694,-.306,.5],8990:[.366,.022,.5],8991:[.366,.022,.5],8994:[.388,-.122,1],8995:[.378,-.134,1],9001:[.75,.25,.389],9002:[.75,.25,.389],9136:[.744,.244,.412],9137:[.744,.244,.412],9168:[.602,0,.667],9416:[.709,.175,.902],9484:[.694,-.306,.5],9488:[.694,-.306,.5],9492:[.366,.022,.5],9496:[.366,.022,.5],9585:[.694,.195,.889],9586:[.694,.195,.889],9632:[.689,0,.778],9633:[.689,0,.778],9642:[.689,0,.778],9650:[.575,.02,.722],9651:[.716,0,.889],9652:[.575,.02,.722],9653:[.716,0,.889],9654:[.539,.041,.778],9656:[.539,.041,.778],9657:[.505,.005,.5],9660:[.576,.019,.722],9661:[.5,.215,.889],9662:[.576,.019,.722],9663:[.5,.215,.889],9664:[.539,.041,.778],9666:[.539,.041,.778],9667:[.505,.005,.5],9674:[.716,.132,.667],9711:[.715,.215,1],9723:[.689,0,.778],9724:[.689,0,.778],9733:[.694,.111,.944],9824:[.727,.13,.778],9825:[.716,.033,.778],9826:[.727,.162,.778],9827:[.726,.13,.778],9837:[.75,.022,.389],9838:[.734,.223,.389],9839:[.723,.223,.389],10003:[.706,.034,.833],10016:[.716,.022,.833],10072:[.75,.249,.278],10216:[.75,.25,.389],10217:[.75,.25,.389],10222:[.744,.244,.412],10223:[.744,.244,.412],10229:[.511,.011,1.609],10230:[.511,.011,1.638],10231:[.511,.011,1.859],10232:[.525,.024,1.609],10233:[.525,.024,1.638],10234:[.525,.024,1.858],10236:[.511,.011,1.638],10731:[.716,.132,.667],10744:[.716,.215,.778],10752:[.75,.25,1.111],10753:[.75,.25,1.111],10754:[.75,.25,1.111],10756:[.75,.249,.833],10758:[.75,.249,.833],10764:[.805,.306,1.638,{ic:.138}],10799:[.491,-.009,.778],10815:[.683,0,.75],10846:[.813,.097,.611],10877:[.636,.138,.778],10878:[.636,.138,.778],10885:[.762,.29,.778],10886:[.762,.29,.778],10887:[.635,.241,.778],10888:[.635,.241,.778],10889:[.761,.387,.778],10890:[.761,.387,.778],10891:[1.003,.463,.778],10892:[1.003,.463,.778],10901:[.636,.138,.778],10902:[.636,.138,.778],10927:[.636,.138,.778],10928:[.636,.138,.778],10933:[.752,.286,.778],10934:[.752,.286,.778],10935:[.761,.294,.778],10936:[.761,.294,.778],10937:[.761,.337,.778],10938:[.761,.337,.778],10949:[.753,.215,.778],10950:[.753,.215,.778],10955:[.783,.385,.778],10956:[.783,.385,.778],12296:[.75,.25,.389],12297:[.75,.25,.389],57350:[.43,.023,.222],57351:[.431,.024,.389],57352:[.605,.085,.778],57353:[.434,.006,.667,{ic:.067}],57356:[.752,.284,.778],57357:[.752,.284,.778],57358:[.919,.421,.778],57359:[.801,.303,.778],57360:[.801,.303,.778],57361:[.919,.421,.778],57366:[.828,.33,.778],57367:[.752,.332,.778],57368:[.828,.33,.778],57369:[.752,.333,.778],57370:[.634,.255,.778],57371:[.634,.254,.778],119808:[.698,0,.869],119809:[.686,0,.818],119810:[.697,.011,.831],119811:[.686,0,.882],119812:[.68,0,.756],119813:[.68,0,.724],119814:[.697,.01,.904],119815:[.686,0,.9],119816:[.686,0,.436],119817:[.686,.011,.594],119818:[.686,0,.901],119819:[.686,0,.692],119820:[.686,0,1.092],119821:[.686,0,.9],119822:[.696,.01,.864],119823:[.686,0,.786],119824:[.696,.193,.864],119825:[.686,.011,.862],119826:[.697,.011,.639],119827:[.675,0,.8],119828:[.686,.011,.885],119829:[.686,.007,.869],119830:[.686,.007,1.189],119831:[.686,0,.869],119832:[.686,0,.869],119833:[.686,0,.703],119834:[.453,.006,.559],119835:[.694,.006,.639],119836:[.453,.006,.511],119837:[.694,.006,.639],119838:[.452,.006,.527],119839:[.7,0,.351,{ic:.101}],119840:[.455,.201,.575],119841:[.694,0,.639],119842:[.695,0,.319],119843:[.695,.2,.351],119844:[.694,0,.607],119845:[.694,0,.319],119846:[.45,0,.958],119847:[.45,0,.639],119848:[.452,.005,.575],119849:[.45,.194,.639],119850:[.45,.194,.607],119851:[.45,0,.474],119852:[.453,.006,.454],119853:[.635,.005,.447],119854:[.45,.006,.639],119855:[.444,0,.607],119856:[.444,0,.831],119857:[.444,0,.607],119858:[.444,.2,.607],119859:[.444,0,.511],119860:[.716,0,.75,{sk:.139}],119861:[.683,0,.759,{sk:.0833}],119862:[.705,.022,.715,{sk:.0833}],119863:[.683,0,.828,{sk:.0556}],119864:[.68,0,.738,{sk:.0833}],119865:[.68,0,.643,{ic:.106,sk:.0833}],119866:[.705,.022,.786,{sk:.0833}],119867:[.683,0,.831,{ic:.057,sk:.0556}],119868:[.683,0,.44,{ic:.064,sk:.111}],119869:[.683,.022,.555,{ic:.078,sk:.167}],119870:[.683,0,.849,{sk:.0556}],119871:[.683,0,.681,{sk:.0278}],119872:[.683,0,.97,{ic:.081,sk:.0833}],119873:[.683,0,.803,{ic:.085,sk:.0833}],119874:[.704,.022,.763,{sk:.0833}],119875:[.683,0,.642,{ic:.109,sk:.0833}],119876:[.704,.194,.791,{sk:.0833}],119877:[.683,.021,.759,{sk:.0833}],119878:[.705,.022,.613,{sk:.0833}],119879:[.677,0,.584,{ic:.12,sk:.0833}],119880:[.683,.022,.683,{ic:.084,sk:.0278}],119881:[.683,.022,.583,{ic:.186}],119882:[.683,.022,.944,{ic:.104}],119883:[.683,0,.828,{sk:.0833}],119884:[.683,0,.581,{ic:.182}],119885:[.683,0,.683,{sk:.0833}],119886:[.441,.01,.529],119887:[.694,.011,.429],119888:[.442,.011,.433,{sk:.0556}],119889:[.694,.01,.52,{sk:.167}],119890:[.442,.011,.466,{sk:.0556}],119891:[.705,.205,.49,{ic:.06,sk:.167}],119892:[.442,.205,.477,{sk:.0278}],119893:[.694,.011,.576,{sk:-.0278}],119894:[.661,.011,.345],119895:[.661,.204,.412],119896:[.694,.011,.521],119897:[.694,.011,.298,{sk:.0833}],119898:[.442,.011,.878],119899:[.442,.011,.6],119900:[.441,.011,.485,{sk:.0556}],119901:[.442,.194,.503,{sk:.0833}],119902:[.442,.194,.446,{sk:.0833}],119903:[.442,.011,.451,{sk:.0556}],119904:[.442,.01,.469,{sk:.0556}],119905:[.626,.011,.361,{sk:.0833}],119906:[.442,.011,.572,{sk:.0278}],119907:[.443,.011,.485,{sk:.0278}],119908:[.443,.011,.716,{sk:.0833}],119909:[.442,.011,.572,{sk:.0278}],119910:[.442,.205,.49,{sk:.0556}],119911:[.442,.011,.465,{sk:.0556}],119912:[.711,0,.869,{sk:.16}],119913:[.686,0,.866,{sk:.0958}],119914:[.703,.017,.817,{sk:.0958}],119915:[.686,0,.938,{sk:.0639}],119916:[.68,0,.81,{sk:.0958}],119917:[.68,0,.689,{ic:.12,sk:.0958}],119918:[.703,.016,.887,{sk:.0958}],119919:[.686,0,.982,{sk:.0639}],119920:[.686,0,.511,{ic:.062,sk:.128}],119921:[.686,.017,.631,{ic:.063,sk:.192}],119922:[.686,0,.971,{sk:.0639}],119923:[.686,0,.756,{sk:.0319}],119924:[.686,0,1.142,{ic:.077,sk:.0958}],119925:[.686,0,.95,{ic:.077,sk:.0958}],119926:[.703,.017,.837,{sk:.0958}],119927:[.686,0,.723,{ic:.124,sk:.0958}],119928:[.703,.194,.869,{sk:.0958}],119929:[.686,.017,.872,{sk:.0958}],119930:[.703,.017,.693,{sk:.0958}],119931:[.675,0,.637,{ic:.135,sk:.0958}],119932:[.686,.016,.8,{ic:.077,sk:.0319}],119933:[.686,.016,.678,{ic:.208}],119934:[.686,.017,1.093,{ic:.114}],119935:[.686,0,.947,{sk:.0958}],119936:[.686,0,.675,{ic:.201}],119937:[.686,0,.773,{sk:.0958}],119938:[.452,.008,.633],119939:[.694,.008,.521],119940:[.451,.008,.513,{sk:.0639}],119941:[.694,.008,.61,{sk:.192}],119942:[.452,.008,.554,{sk:.0639}],119943:[.701,.201,.568,{ic:.056,sk:.192}],119944:[.452,.202,.545,{sk:.0319}],119945:[.694,.008,.668,{sk:-.0319}],119946:[.694,.008,.405],119947:[.694,.202,.471],119948:[.694,.008,.604],119949:[.694,.008,.348,{sk:.0958}],119950:[.452,.008,1.032],119951:[.452,.008,.713],119952:[.452,.008,.585,{sk:.0639}],119953:[.452,.194,.601,{sk:.0958}],119954:[.452,.194,.542,{sk:.0958}],119955:[.452,.008,.529,{sk:.0639}],119956:[.451,.008,.531,{sk:.0639}],119957:[.643,.007,.415,{sk:.0958}],119958:[.452,.008,.681,{sk:.0319}],119959:[.453,.008,.567,{sk:.0319}],119960:[.453,.008,.831,{sk:.0958}],119961:[.452,.008,.659,{sk:.0319}],119962:[.452,.202,.59,{sk:.0639}],119963:[.452,.008,.555,{sk:.0639}],119964:[.717,.008,.803,{ic:.213,sk:.389}],119965:[.708,.028,.908,{sk:.194}],119966:[.728,.026,.666,{ic:.153,sk:.278}],119967:[.708,.031,.774,{ic:.081,sk:.111}],119968:[.707,.008,.562,{ic:.156,sk:.139}],119969:[.735,.036,.895,{ic:.095,sk:.222}],119970:[.717,.037,.61,{ic:.128,sk:.25}],119971:[.717,.036,.969,{ic:.272,sk:.333}],119972:[.717,.017,.809,{ic:.137,sk:.333}],119973:[.717,.314,1.052,{ic:.081,sk:.417}],119974:[.717,.037,.914,{ic:.29,sk:.361}],119975:[.717,.017,.874,{ic:.161,sk:.306}],119976:[.721,.05,1.08,{ic:.136,sk:.444}],119977:[.726,.036,.902,{ic:.306,sk:.389}],119978:[.707,.008,.738,{ic:.067,sk:.167}],119979:[.716,.037,1.013,{sk:.222}],119980:[.717,.017,.883,{sk:.278}],119981:[.717,.017,.85,{sk:.194}],119982:[.708,.036,.868,{ic:.148,sk:.333}],119983:[.735,.037,.747,{ic:.249,sk:.222}],119984:[.717,.017,.8,{ic:.16,sk:.25}],119985:[.717,.017,.622,{ic:.228,sk:.222}],119986:[.717,.017,.805,{ic:.221,sk:.25}],119987:[.717,.017,.944,{ic:.187,sk:.278}],119988:[.716,.017,.71,{ic:.249,sk:.194}],119989:[.717,.016,.821,{ic:.211,sk:.306}],119990:[.441,.01,.529],119991:[.694,.011,.429],119992:[.442,.011,.433,{sk:.0556}],119993:[.694,.01,.52,{sk:.167}],119994:[.442,.011,.466,{sk:.0556}],119995:[.705,.205,.49,{ic:.06,sk:.167}],119996:[.442,.205,.477,{sk:.0278}],119997:[.694,.011,.576,{sk:-.0278}],119998:[.661,.011,.345],119999:[.661,.204,.412],12e4:[.694,.011,.521],120001:[.694,.011,.298,{sk:.0833}],120002:[.442,.011,.878],120003:[.442,.011,.6],120004:[.441,.011,.485,{sk:.0556}],120005:[.442,.194,.503,{sk:.0833}],120006:[.442,.194,.446,{sk:.0833}],120007:[.442,.011,.451,{sk:.0556}],120008:[.442,.01,.469,{sk:.0556}],120009:[.626,.011,.361,{sk:.0833}],120010:[.442,.011,.572,{sk:.0278}],120011:[.443,.011,.485,{sk:.0278}],120012:[.443,.011,.716,{sk:.0833}],120013:[.442,.011,.572,{sk:.0278}],120014:[.442,.205,.49,{sk:.0556}],120015:[.442,.011,.465,{sk:.0556}],120016:[.717,.008,.803,{ic:.213,sk:.389}],120017:[.708,.028,.908,{sk:.194}],120018:[.728,.026,.666,{ic:.153,sk:.278}],120019:[.708,.031,.774,{ic:.081,sk:.111}],120020:[.707,.008,.562,{ic:.156,sk:.139}],120021:[.735,.036,.895,{ic:.095,sk:.222}],120022:[.717,.037,.61,{ic:.128,sk:.25}],120023:[.717,.036,.969,{ic:.272,sk:.333}],120024:[.717,.017,.809,{ic:.137,sk:.333}],120025:[.717,.314,1.052,{ic:.081,sk:.417}],120026:[.717,.037,.914,{ic:.29,sk:.361}],120027:[.717,.017,.874,{ic:.161,sk:.306}],120028:[.721,.05,1.08,{ic:.136,sk:.444}],120029:[.726,.036,.902,{ic:.306,sk:.389}],120030:[.707,.008,.738,{ic:.067,sk:.167}],120031:[.716,.037,1.013,{sk:.222}],120032:[.717,.017,.883,{sk:.278}],120033:[.717,.017,.85,{sk:.194}],120034:[.708,.036,.868,{ic:.148,sk:.333}],120035:[.735,.037,.747,{ic:.249,sk:.222}],120036:[.717,.017,.8,{ic:.16,sk:.25}],120037:[.717,.017,.622,{ic:.228,sk:.222}],120038:[.717,.017,.805,{ic:.221,sk:.25}],120039:[.717,.017,.944,{ic:.187,sk:.278}],120040:[.716,.017,.71,{ic:.249,sk:.194}],120041:[.717,.016,.821,{ic:.211,sk:.306}],120042:[.452,.008,.633],120043:[.694,.008,.521],120044:[.451,.008,.513,{sk:.0639}],120045:[.694,.008,.61,{sk:.192}],120046:[.452,.008,.554,{sk:.0639}],120047:[.701,.201,.568,{ic:.056,sk:.192}],120048:[.452,.202,.545,{sk:.0319}],120049:[.694,.008,.668,{sk:-.0319}],120050:[.694,.008,.405],120051:[.694,.202,.471],120052:[.694,.008,.604],120053:[.694,.008,.348,{sk:.0958}],120054:[.452,.008,1.032],120055:[.452,.008,.713],120056:[.452,.008,.585,{sk:.0639}],120057:[.452,.194,.601,{sk:.0958}],120058:[.452,.194,.542,{sk:.0958}],120059:[.452,.008,.529,{sk:.0639}],120060:[.451,.008,.531,{sk:.0639}],120061:[.643,.007,.415,{sk:.0958}],120062:[.452,.008,.681,{sk:.0319}],120063:[.453,.008,.567,{sk:.0319}],120064:[.453,.008,.831,{sk:.0958}],120065:[.452,.008,.659,{sk:.0319}],120066:[.452,.202,.59,{sk:.0639}],120067:[.452,.008,.555,{sk:.0639}],120068:[.696,.026,.718],120069:[.691,.027,.884],120070:[.685,.024,.613],120071:[.685,.027,.832],120072:[.685,.024,.663],120073:[.686,.153,.611],120074:[.69,.026,.785],120075:[.666,.133,.72],120076:[.686,.026,.554],120077:[.686,.139,.552],120078:[.68,.027,.668],120079:[.686,.026,.666],120080:[.692,.027,1.05],120081:[.686,.025,.832],120082:[.729,.027,.827],120083:[.692,.218,.828],120084:[.729,.069,.827],120085:[.686,.026,.828],120086:[.692,.027,.829],120087:[.701,.027,.669],120088:[.697,.027,.646],120089:[.686,.026,.831],120090:[.686,.027,1.046],120091:[.688,.027,.719],120092:[.686,.218,.833],120093:[.729,.139,.602],120094:[.47,.035,.5],120095:[.685,.031,.513],120096:[.466,.029,.389],120097:[.609,.033,.499],120098:[.467,.03,.401],120099:[.681,.221,.326],120100:[.47,.209,.504],120101:[.688,.205,.521],120102:[.673,.02,.279],120103:[.672,.208,.281],120104:[.689,.025,.389],120105:[.685,.02,.28],120106:[.475,.026,.767],120107:[.475,.022,.527],120108:[.48,.028,.489],120109:[.541,.212,.5],120110:[.479,.219,.489],120111:[.474,.021,.389],120112:[.478,.029,.443],120113:[.64,.02,.333],120114:[.474,.023,.517],120115:[.53,.028,.512],120116:[.532,.028,.774],120117:[.472,.188,.389],120118:[.528,.218,.499],120119:[.471,.214,.391],120120:[.701,0,.722],120121:[.683,0,.667],120122:[.702,.019,.722],120123:[.683,0,.722],120124:[.683,0,.667],120125:[.683,0,.611],120126:[.702,.019,.778],120127:[.683,0,.778],120128:[.683,0,.389],120129:[.683,.077,.5],120130:[.683,0,.778],120131:[.683,0,.667],120132:[.683,0,.944],120133:[.683,.02,.722],120134:[.701,.019,.778],120135:[.683,0,.611],120136:[.701,.181,.778],120137:[.683,0,.722],120138:[.702,.012,.556],120139:[.683,0,.667],120140:[.683,.019,.722],120141:[.683,.02,.722],120142:[.683,.019,1],120143:[.683,0,.722],120144:[.683,0,.722],120145:[.683,0,.667],120146:[.453,.006,.559],120147:[.694,.006,.639],120148:[.453,.006,.511],120149:[.694,.006,.639],120150:[.452,.006,.527],120151:[.7,0,.351,{ic:.101}],120152:[.455,.201,.575],120153:[.694,0,.639],120154:[.695,0,.319],120155:[.695,.2,.351],120156:[.683,0,.556],120157:[.694,0,.319],120158:[.45,0,.958],120159:[.45,0,.639],120160:[.452,.005,.575],120161:[.45,.194,.639],120162:[.45,.194,.607],120163:[.45,0,.474],120164:[.453,.006,.454],120165:[.635,.005,.447],120166:[.45,.006,.639],120167:[.444,0,.607],120168:[.444,0,.831],120169:[.444,0,.607],120170:[.444,.2,.607],120171:[.444,0,.511],120172:[.686,.031,.847],120173:[.684,.031,1.044],120174:[.676,.032,.723],120175:[.683,.029,.982],120176:[.686,.029,.783],120177:[.684,.146,.722],120178:[.687,.029,.927],120179:[.683,.126,.851],120180:[.681,.025,.655],120181:[.68,.141,.652],120182:[.681,.026,.789],120183:[.683,.028,.786],120184:[.683,.032,1.239],120185:[.679,.03,.983],120186:[.726,.03,.976],120187:[.688,.223,.977],120188:[.726,.083,.976],120189:[.688,.028,.978],120190:[.685,.031,.978],120191:[.686,.03,.79],120192:[.688,.039,.851],120193:[.685,.029,.982],120194:[.683,.03,1.235],120195:[.681,.035,.849],120196:[.688,.214,.984],120197:[.677,.148,.711],120198:[.472,.032,.603],120199:[.69,.032,.59],120200:[.473,.026,.464],120201:[.632,.028,.589],120202:[.471,.027,.472],120203:[.687,.222,.388],120204:[.472,.208,.595],120205:[.687,.207,.615],120206:[.686,.025,.331],120207:[.682,.203,.332],120208:[.682,.025,.464],120209:[.681,.024,.337],120210:[.476,.031,.921],120211:[.473,.028,.654],120212:[.482,.034,.609],120213:[.557,.207,.604],120214:[.485,.211,.596],120215:[.472,.026,.46],120216:[.479,.034,.523],120217:[.648,.027,.393],120218:[.472,.032,.589],120219:[.546,.027,.604],120220:[.549,.032,.918],120221:[.471,.188,.459],120222:[.557,.221,.589],120223:[.471,.214,.461],120224:[.694,0,.667],120225:[.694,0,.667],120226:[.705,.011,.639],120227:[.694,0,.722],120228:[.691,0,.597],120229:[.691,0,.569],120230:[.704,.011,.667],120231:[.694,0,.708],120232:[.694,0,.278],120233:[.694,.022,.472],120234:[.694,0,.694],120235:[.694,0,.542],120236:[.694,0,.875],120237:[.694,0,.708],120238:[.715,.022,.736],120239:[.694,0,.639],120240:[.715,.125,.736],120241:[.694,0,.646],120242:[.716,.022,.556],120243:[.688,0,.681],120244:[.694,.022,.688],120245:[.694,0,.667],120246:[.694,0,.944],120247:[.694,0,.667],120248:[.694,0,.667],120249:[.694,0,.611],120250:[.46,.01,.481],120251:[.694,.011,.517],120252:[.46,.01,.444],120253:[.694,.01,.517],120254:[.461,.01,.444],120255:[.705,0,.306],120256:[.455,.206,.5],120257:[.694,0,.517],120258:[.68,0,.239],120259:[.68,.205,.267],120260:[.694,0,.489],120261:[.694,0,.239],120262:[.455,0,.794],120263:[.455,0,.517],120264:[.46,.01,.5],120265:[.455,.194,.517],120266:[.455,.194,.517],120267:[.455,0,.342],120268:[.46,.01,.383],120269:[.571,.01,.361],120270:[.444,.01,.517],120271:[.444,0,.461],120272:[.444,0,.683],120273:[.444,0,.461],120274:[.444,.204,.461],120275:[.444,0,.435],120276:[.694,0,.733],120277:[.694,0,.733],120278:[.704,.011,.703],120279:[.694,0,.794],120280:[.691,0,.642],120281:[.691,0,.611],120282:[.705,.011,.733],120283:[.694,0,.794],120284:[.694,0,.331],120285:[.694,.022,.519],120286:[.694,0,.764],120287:[.694,0,.581],120288:[.694,0,.978],120289:[.694,0,.794],120290:[.716,.022,.794],120291:[.694,0,.703],120292:[.716,.106,.794],120293:[.694,0,.703],120294:[.716,.022,.611],120295:[.688,0,.733],120296:[.694,.022,.764],120297:[.694,0,.733],120298:[.694,0,1.039],120299:[.694,0,.733],120300:[.694,0,.733],120301:[.694,0,.672],120302:[.475,.011,.525],120303:[.694,.01,.561],120304:[.475,.011,.489],120305:[.694,.011,.561],120306:[.474,.01,.511],120307:[.705,0,.336],120308:[.469,.206,.55],120309:[.694,0,.561],120310:[.695,0,.256],120311:[.695,.205,.286],120312:[.694,0,.531],120313:[.694,0,.256],120314:[.469,0,.867],120315:[.468,0,.561],120316:[.474,.011,.55],120317:[.469,.194,.561],120318:[.469,.194,.561],120319:[.469,0,.372],120320:[.474,.01,.422],120321:[.589,.01,.404],120322:[.458,.011,.561],120323:[.458,0,.5],120324:[.458,0,.744],120325:[.458,0,.5],120326:[.458,.205,.5],120327:[.458,0,.476],120328:[.694,0,.667],120329:[.694,0,.667],120330:[.705,.01,.639,{ic:.08}],120331:[.694,0,.722],120332:[.691,0,.597,{ic:.091}],120333:[.691,0,.569,{ic:.104}],120334:[.705,.011,.667,{ic:.063}],120335:[.694,0,.708,{ic:.06}],120336:[.694,0,.278,{ic:.06}],120337:[.694,.022,.472,{ic:.063}],120338:[.694,0,.694,{ic:.091}],120339:[.694,0,.542],120340:[.694,0,.875,{ic:.054}],120341:[.694,0,.708,{ic:.058}],120342:[.716,.022,.736],120343:[.694,0,.639,{ic:.051}],120344:[.716,.125,.736],120345:[.694,0,.646,{ic:.052}],120346:[.716,.022,.556,{ic:.053}],120347:[.688,0,.681,{ic:.109}],120348:[.694,.022,.688,{ic:.059}],120349:[.694,0,.667,{ic:.132}],120350:[.694,0,.944,{ic:.132}],120351:[.694,0,.667,{ic:.091}],120352:[.694,0,.667,{ic:.143}],120353:[.694,0,.611,{ic:.091}],120354:[.461,.01,.481],120355:[.694,.011,.517],120356:[.46,.011,.444,{ic:.055}],120357:[.694,.01,.517,{ic:.071}],120358:[.46,.011,.444],120359:[.705,0,.306,{ic:.188}],120360:[.455,.206,.5,{ic:.068}],120361:[.694,0,.517],120362:[.68,0,.239,{ic:.076}],120363:[.68,.204,.267,{ic:.069}],120364:[.694,0,.489,{ic:.054}],120365:[.694,0,.239,{ic:.072}],120366:[.455,0,.794],120367:[.454,0,.517],120368:[.461,.011,.5],120369:[.455,.194,.517],120370:[.455,.194,.517],120371:[.455,0,.342,{ic:.082}],120372:[.461,.011,.383,{ic:.053}],120373:[.571,.011,.361],120374:[.444,.01,.517],120375:[.444,0,.461,{ic:.079}],120376:[.444,0,.683,{ic:.079}],120377:[.444,0,.461,{ic:.076}],120378:[.444,.205,.461,{ic:.079}],120379:[.444,0,.435,{ic:.059}],120380:[.694,0,.667],120381:[.694,0,.667],120382:[.705,.01,.639,{ic:.08}],120383:[.694,0,.722],120384:[.691,0,.597,{ic:.091}],120385:[.691,0,.569,{ic:.104}],120386:[.705,.011,.667,{ic:.063}],120387:[.694,0,.708,{ic:.06}],120388:[.694,0,.278,{ic:.06}],120389:[.694,.022,.472,{ic:.063}],120390:[.694,0,.694,{ic:.091}],120391:[.694,0,.542],120392:[.694,0,.875,{ic:.054}],120393:[.694,0,.708,{ic:.058}],120394:[.716,.022,.736],120395:[.694,0,.639,{ic:.051}],120396:[.716,.125,.736],120397:[.694,0,.646,{ic:.052}],120398:[.716,.022,.556,{ic:.053}],120399:[.688,0,.681,{ic:.109}],120400:[.694,.022,.688,{ic:.059}],120401:[.694,0,.667,{ic:.132}],120402:[.694,0,.944,{ic:.132}],120403:[.694,0,.667,{ic:.091}],120404:[.694,0,.667,{ic:.143}],120405:[.694,0,.611,{ic:.091}],120406:[.461,.01,.481],120407:[.694,.011,.517],120408:[.46,.011,.444,{ic:.055}],120409:[.694,.01,.517,{ic:.071}],120410:[.46,.011,.444],120411:[.705,0,.306,{ic:.188}],120412:[.455,.206,.5,{ic:.068}],120413:[.694,0,.517],120414:[.68,0,.239,{ic:.076}],120415:[.68,.204,.267,{ic:.069}],120416:[.694,0,.489,{ic:.054}],120417:[.694,0,.239,{ic:.072}],120418:[.455,0,.794],120419:[.454,0,.517],120420:[.461,.011,.5],120421:[.455,.194,.517],120422:[.455,.194,.517],120423:[.455,0,.342,{ic:.082}],120424:[.461,.011,.383,{ic:.053}],120425:[.571,.011,.361],120426:[.444,.01,.517],120427:[.444,0,.461,{ic:.079}],120428:[.444,0,.683,{ic:.079}],120429:[.444,0,.461,{ic:.076}],120430:[.444,.205,.461,{ic:.079}],120431:[.444,0,.435,{ic:.059}],120432:[.623,0,.525],120433:[.611,0,.525],120434:[.622,.011,.525],120435:[.611,0,.525],120436:[.611,0,.525],120437:[.611,0,.525],120438:[.622,.011,.525],120439:[.611,0,.525],120440:[.611,0,.525],120441:[.611,.011,.525],120442:[.611,0,.525],120443:[.611,0,.525],120444:[.611,0,.525],120445:[.611,0,.525],120446:[.621,.01,.525],120447:[.611,0,.525],120448:[.621,.138,.525],120449:[.611,.011,.525],120450:[.622,.011,.525],120451:[.611,0,.525],120452:[.611,.011,.525],120453:[.611,.007,.525],120454:[.611,.007,.525],120455:[.611,0,.525],120456:[.611,0,.525],120457:[.611,0,.525],120458:[.439,.006,.525],120459:[.611,.006,.525],120460:[.44,.006,.525],120461:[.611,.006,.525],120462:[.44,.006,.525],120463:[.617,0,.525],120464:[.442,.229,.525],120465:[.611,0,.525],120466:[.612,0,.525],120467:[.612,.228,.525],120468:[.611,0,.525],120469:[.611,0,.525],120470:[.436,0,.525],120471:[.436,0,.525],120472:[.44,.006,.525],120473:[.437,.221,.525],120474:[.437,.221,.525],120475:[.437,0,.525],120476:[.44,.006,.525],120477:[.554,.006,.525],120478:[.431,.005,.525],120479:[.431,0,.525],120480:[.431,0,.525],120481:[.431,0,.525],120482:[.431,.228,.525],120483:[.431,0,.525],120484:[.441,.01,.307],120485:[.442,.204,.332],120488:[.698,0,.869],120489:[.686,0,.818],120490:[.68,0,.692],120491:[.698,0,.958],120492:[.68,0,.756],120493:[.686,0,.703],120494:[.686,0,.9],120495:[.696,.01,.894],120496:[.686,0,.436],120497:[.686,0,.901],120498:[.698,0,.806],120499:[.686,0,1.092],120500:[.686,0,.9],120501:[.675,0,.767],120502:[.696,.01,.864],120503:[.68,0,.9],120504:[.686,0,.786],120505:[.696,.01,.894],120506:[.686,0,.831],120507:[.675,0,.8],120508:[.697,0,.894],120509:[.686,0,.831],120510:[.686,0,.869],120511:[.686,0,.894],120512:[.696,0,.831],120513:[.686,.024,.958],120514:[.452,.008,.761,{sk:.0319}],120515:[.701,.194,.66,{sk:.0958}],120516:[.451,.211,.59],120517:[.725,.008,.522,{sk:.0639}],120518:[.461,.017,.529,{sk:.0958}],120519:[.711,.202,.508,{sk:.0958}],120520:[.452,.211,.6,{sk:.0639}],120521:[.702,.008,.562,{sk:.0958}],120522:[.452,.008,.412,{sk:.0639}],120523:[.452,.008,.668],120524:[.694,.013,.671],120525:[.452,.211,.708,{sk:.0319}],120526:[.452,0,.577,{sk:.0319}],120527:[.711,.201,.508,{sk:.128}],120528:[.452,.008,.585,{sk:.0639}],120529:[.444,.008,.682],120530:[.451,.211,.612,{sk:.0958}],120531:[.451,.105,.424,{sk:.0958}],120532:[.444,.008,.686],120533:[.444,.013,.521,{ic:.089,sk:.0319}],120534:[.453,.008,.631,{sk:.0319}],120535:[.452,.216,.747,{sk:.0958}],120536:[.452,.201,.718,{sk:.0639}],120537:[.694,.202,.758,{sk:.128}],120538:[.453,.008,.718],120539:[.71,.017,.628,{sk:.0958}],120540:[.444,.007,.483,{sk:.0639}],120541:[.701,.008,.692,{sk:.0958}],120542:[.434,.006,.667,{ic:.067}],120543:[.694,.202,.712,{sk:.0958}],120544:[.451,.194,.612,{sk:.0958}],120545:[.444,.008,.975],120546:[.716,0,.75,{sk:.139}],120547:[.683,0,.759,{sk:.0833}],120548:[.68,0,.615,{ic:.106,sk:.0833}],120549:[.716,0,.833,{sk:.167}],120550:[.68,0,.738,{sk:.0833}],120551:[.683,0,.683,{sk:.0833}],120552:[.683,0,.831,{ic:.057,sk:.0556}],120553:[.704,.022,.763,{sk:.0833}],120554:[.683,0,.44,{ic:.064,sk:.111}],120555:[.683,0,.849,{sk:.0556}],120556:[.716,0,.694,{sk:.167}],120557:[.683,0,.97,{ic:.081,sk:.0833}],120558:[.683,0,.803,{ic:.085,sk:.0833}],120559:[.677,0,.742,{sk:.0833}],120560:[.704,.022,.763,{sk:.0833}],120561:[.68,0,.831,{ic:.056,sk:.0556}],120562:[.683,0,.642,{ic:.109,sk:.0833}],120563:[.704,.022,.763,{sk:.0833}],120564:[.683,0,.78,{sk:.0833}],120565:[.677,0,.584,{ic:.12,sk:.0833}],120566:[.705,0,.583,{ic:.117,sk:.0556}],120567:[.683,0,.667,{sk:.0833}],120568:[.683,0,.828,{sk:.0833}],120569:[.683,0,.612,{ic:.08,sk:.0556}],120570:[.704,0,.772,{sk:.0833}],120571:[.683,.033,.833],120572:[.442,.011,.64,{sk:.0278}],120573:[.705,.194,.566,{sk:.0833}],120574:[.441,.216,.518],120575:[.717,.01,.444,{sk:.0556}],120576:[.452,.022,.466,{sk:.0833}],120577:[.704,.204,.438,{sk:.0833}],120578:[.442,.216,.497,{sk:.0556}],120579:[.705,.01,.469,{sk:.0833}],120580:[.442,.01,.354,{sk:.0556}],120581:[.442,.011,.576],120582:[.694,.012,.583],120583:[.442,.216,.603,{sk:.0278}],120584:[.442,0,.494,{sk:.0278}],120585:[.704,.205,.438,{sk:.111}],120586:[.441,.011,.485,{sk:.0556}],120587:[.431,.011,.57],120588:[.442,.216,.517,{sk:.0833}],120589:[.442,.107,.363,{sk:.0833}],120590:[.431,.011,.571],120591:[.431,.013,.437,{ic:.08,sk:.0278}],120592:[.443,.01,.54,{sk:.0278}],120593:[.442,.218,.654,{sk:.0833}],120594:[.442,.204,.626,{sk:.0556}],120595:[.694,.205,.651,{sk:.111}],120596:[.443,.011,.622],120597:[.715,.022,.531,{sk:.0833}],120598:[.431,.011,.406,{sk:.0556}],120599:[.705,.011,.591,{sk:.0833}],120600:[.434,.006,.667,{ic:.067}],120601:[.694,.205,.596,{sk:.0833}],120602:[.442,.194,.517,{sk:.0833}],120603:[.431,.01,.828],120604:[.711,0,.869,{sk:.16}],120605:[.686,0,.866,{sk:.0958}],120606:[.68,0,.657,{ic:.12,sk:.0958}],120607:[.711,0,.958,{sk:.192}],120608:[.68,0,.81,{sk:.0958}],120609:[.686,0,.773,{sk:.0958}],120610:[.686,0,.982,{sk:.0639}],120611:[.702,.017,.867,{sk:.0958}],120612:[.686,0,.511,{ic:.062,sk:.128}],120613:[.686,0,.971,{sk:.0639}],120614:[.711,0,.806,{sk:.192}],120615:[.686,0,1.142,{ic:.077,sk:.0958}],120616:[.686,0,.95,{ic:.077,sk:.0958}],120617:[.675,0,.841,{sk:.0958}],120618:[.703,.017,.837,{sk:.0958}],120619:[.68,0,.982,{sk:.0639}],120620:[.686,0,.723,{ic:.124,sk:.0958}],120621:[.702,.017,.867,{sk:.0958}],120622:[.686,0,.885,{sk:.0958}],120623:[.675,0,.637,{ic:.135,sk:.0958}],120624:[.703,0,.671,{ic:.131,sk:.0639}],120625:[.686,0,.767,{sk:.0958}],120626:[.686,0,.947,{sk:.0958}],120627:[.686,0,.714,{ic:.076,sk:.0639}],120628:[.703,0,.879,{sk:.0958}],120629:[.683,.033,.833],120630:[.452,.008,.761,{sk:.0319}],120631:[.701,.194,.66,{sk:.0958}],120632:[.451,.211,.59],120633:[.725,.008,.522,{sk:.0639}],120634:[.461,.017,.529,{sk:.0958}],120635:[.711,.202,.508,{sk:.0958}],120636:[.452,.211,.6,{sk:.0639}],120637:[.702,.008,.562,{sk:.0958}],120638:[.452,.008,.412,{sk:.0639}],120639:[.452,.008,.668],120640:[.694,.013,.671],120641:[.452,.211,.708,{sk:.0319}],120642:[.452,0,.577,{sk:.0319}],120643:[.711,.201,.508,{sk:.128}],120644:[.452,.008,.585,{sk:.0639}],120645:[.444,.008,.682],120646:[.451,.211,.612,{sk:.0958}],120647:[.451,.105,.424,{sk:.0958}],120648:[.444,.008,.686],120649:[.444,.013,.521,{ic:.089,sk:.0319}],120650:[.453,.008,.631,{sk:.0319}],120651:[.452,.216,.747,{sk:.0958}],120652:[.452,.201,.718,{sk:.0639}],120653:[.694,.202,.758,{sk:.128}],120654:[.453,.008,.718],120655:[.715,.022,.531,{sk:.0833}],120656:[.444,.007,.483,{sk:.0639}],120657:[.701,.008,.692,{sk:.0958}],120658:[.434,.006,.667,{ic:.067}],120659:[.694,.202,.712,{sk:.0958}],120660:[.451,.194,.612,{sk:.0958}],120661:[.444,.008,.975],120662:[.694,0,.733],120663:[.694,0,.733],120664:[.691,0,.581],120665:[.694,0,.917],120666:[.691,0,.642],120667:[.694,0,.672],120668:[.694,0,.794],120669:[.716,.022,.856],120670:[.694,0,.331],120671:[.694,0,.764],120672:[.694,0,.672],120673:[.694,0,.978],120674:[.694,0,.794],120675:[.688,0,.733],120676:[.716,.022,.794],120677:[.691,0,.794],120678:[.694,0,.703],120679:[.716,.022,.856],120680:[.694,0,.794],120681:[.688,0,.733],120682:[.715,0,.856],120683:[.694,0,.794],120684:[.694,0,.733],120685:[.694,0,.856],120686:[.716,0,.794],120687:[.683,.033,.833],120688:[.452,.008,.761,{sk:.0319}],120689:[.701,.194,.66,{sk:.0958}],120690:[.451,.211,.59],120691:[.725,.008,.522,{sk:.0639}],120692:[.461,.017,.529,{sk:.0958}],120693:[.711,.202,.508,{sk:.0958}],120694:[.452,.211,.6,{sk:.0639}],120695:[.702,.008,.562,{sk:.0958}],120696:[.452,.008,.412,{sk:.0639}],120697:[.452,.008,.668],120698:[.694,.013,.671],120699:[.452,.211,.708,{sk:.0319}],120700:[.452,0,.577,{sk:.0319}],120701:[.711,.201,.508,{sk:.128}],120702:[.452,.008,.585,{sk:.0639}],120703:[.444,.008,.682],120704:[.451,.211,.612,{sk:.0958}],120705:[.451,.105,.424,{sk:.0958}],120706:[.444,.008,.686],120707:[.444,.013,.521,{ic:.089,sk:.0319}],120708:[.453,.008,.631,{sk:.0319}],120709:[.452,.216,.747,{sk:.0958}],120710:[.452,.201,.718,{sk:.0639}],120711:[.694,.202,.758,{sk:.128}],120712:[.453,.008,.718],120713:[.715,.022,.531,{sk:.0833}],120714:[.444,.007,.483,{sk:.0639}],120715:[.701,.008,.692,{sk:.0958}],120716:[.434,.006,.667,{ic:.067}],120717:[.694,.202,.712,{sk:.0958}],120718:[.451,.194,.612,{sk:.0958}],120719:[.444,.008,.975],120720:[.694,0,.667],120721:[.694,0,.667],120722:[.691,0,.542,{ic:.104}],120723:[.694,0,.833],120724:[.691,0,.597,{ic:.091}],120725:[.694,0,.611,{ic:.091}],120726:[.694,0,.708,{ic:.06}],120727:[.715,.022,.778],120728:[.694,0,.278,{ic:.06}],120729:[.694,0,.694,{ic:.091}],120730:[.694,0,.611],120731:[.694,0,.875,{ic:.054}],120732:[.694,0,.708,{ic:.058}],120733:[.688,0,.667,{ic:.098}],120734:[.716,.022,.736],120735:[.691,0,.708,{ic:.06}],120736:[.694,0,.639,{ic:.051}],120737:[.715,.022,.778],120738:[.694,0,.722,{ic:.091}],120739:[.688,0,.681,{ic:.109}],120740:[.716,0,.778,{ic:.065}],120741:[.694,0,.722],120742:[.694,0,.667,{ic:.091}],120743:[.694,0,.778,{ic:.076}],120744:[.716,0,.722],120745:[.683,.033,.833],120746:[.452,.008,.761,{sk:.0319}],120747:[.701,.194,.66,{sk:.0958}],120748:[.451,.211,.59],120749:[.725,.008,.522,{sk:.0639}],120750:[.461,.017,.529,{sk:.0958}],120751:[.711,.202,.508,{sk:.0958}],120752:[.452,.211,.6,{sk:.0639}],120753:[.702,.008,.562,{sk:.0958}],120754:[.452,.008,.412,{sk:.0639}],120755:[.452,.008,.668],120756:[.694,.013,.671],120757:[.452,.211,.708,{sk:.0319}],120758:[.452,0,.577,{sk:.0319}],120759:[.711,.201,.508,{sk:.128}],120760:[.452,.008,.585,{sk:.0639}],120761:[.444,.008,.682],120762:[.451,.211,.612,{sk:.0958}],120763:[.451,.105,.424,{sk:.0958}],120764:[.444,.008,.686],120765:[.444,.013,.521,{ic:.089,sk:.0319}],120766:[.453,.008,.631,{sk:.0319}],120767:[.452,.216,.747,{sk:.0958}],120768:[.452,.201,.718,{sk:.0639}],120769:[.694,.202,.758,{sk:.128}],120770:[.453,.008,.718],120771:[.715,.022,.531,{sk:.0833}],120772:[.444,.007,.483,{sk:.0639}],120773:[.701,.008,.692,{sk:.0958}],120774:[.434,.006,.667,{ic:.067}],120775:[.694,.202,.712,{sk:.0958}],120776:[.451,.194,.612,{sk:.0958}],120777:[.444,.008,.975],120778:[.68,0,.643,{ic:.106,sk:.0833}],120779:[.605,.085,.778],120782:[.654,.01,.575],120783:[.655,0,.575],120784:[.654,0,.575],120785:[.655,.011,.575],120786:[.656,0,.575],120787:[.655,.011,.575],120788:[.655,.011,.575],120789:[.676,.011,.575],120790:[.654,.011,.575],120791:[.654,.011,.575],120792:[.654,.01,.575],120793:[.655,0,.575],120794:[.654,0,.575],120795:[.655,.011,.575],120796:[.656,0,.575],120797:[.655,.011,.575],120798:[.655,.011,.575],120799:[.676,.011,.575],120800:[.654,.011,.575],120801:[.654,.011,.575],120802:[.678,.022,.5],120803:[.678,0,.5],120804:[.677,0,.5],120805:[.678,.022,.5],120806:[.656,0,.5],120807:[.656,.021,.5],120808:[.677,.022,.5],120809:[.656,.011,.5],120810:[.678,.022,.5],120811:[.677,.022,.5],120812:[.715,.022,.55],120813:[.716,0,.55],120814:[.716,0,.55],120815:[.716,.022,.55],120816:[.694,0,.55],120817:[.694,.022,.55],120818:[.716,.022,.55],120819:[.695,.011,.55],120820:[.715,.022,.55],120821:[.716,.022,.55],120822:[.621,.01,.525],120823:[.622,0,.525],120824:[.622,0,.525],120825:[.622,.011,.525],120826:[.624,0,.525],120827:[.611,.01,.525],120828:[.622,.011,.525],120829:[.627,.01,.525],120830:[.621,.01,.525],120831:[.622,.011,.525]}},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0});var i=s(0),t=s(22);f.sansSerifBoldItalic=i.AddCSS(t.sansSerifBoldItalic,{32:{c:" "},33:{c:"!"},35:{c:"#"},36:{c:"$"},37:{c:"%"},38:{c:"&"},40:{c:"("},41:{c:")"},42:{c:"*"},43:{c:"+"},44:{c:","},45:{c:"-"},46:{c:"."},47:{c:"/"},48:{c:"0"},49:{c:"1"},50:{c:"2"},51:{c:"3"},52:{c:"4"},53:{c:"5"},54:{c:"6"},55:{c:"7"},56:{c:"8"},57:{c:"9"},58:{c:":"},59:{c:";"},61:{c:"="},63:{c:"?"},64:{c:"@"},65:{c:"A"},66:{c:"B"},67:{c:"C"},68:{c:"D"},69:{c:"E"},70:{c:"F"},71:{c:"G"},72:{c:"H"},73:{c:"I"},74:{c:"J"},75:{c:"K"},76:{c:"L"},77:{c:"M"},78:{c:"N"},79:{c:"O"},80:{c:"P"},81:{c:"Q"},82:{c:"R"},83:{c:"S"},84:{c:"T"},85:{c:"U"},86:{c:"V"},87:{c:"W"},88:{c:"X"},89:{c:"Y"},90:{c:"Z"},91:{c:"["},93:{c:"]"},94:{c:"^"},95:{c:"_"},97:{c:"a"},98:{c:"b"},99:{c:"c"},100:{c:"d"},101:{c:"e"},102:{c:"f"},103:{c:"g"},104:{c:"h"},105:{c:"i"},106:{c:"j"},107:{c:"k"},108:{c:"l"},109:{c:"m"},110:{c:"n"},111:{c:"o"},112:{c:"p"},113:{c:"q"},114:{c:"r"},115:{c:"s"},116:{c:"t"},117:{c:"u"},118:{c:"v"},119:{c:"w"},120:{c:"x"},121:{c:"y"},122:{c:"z"},126:{c:"~"},913:{c:"A"},914:{c:"B"},917:{c:"E"},918:{c:"Z"},919:{c:"H"},921:{c:"I"},922:{c:"K"},924:{c:"M"},925:{c:"N"},927:{c:"O"},929:{c:"P"},930:{c:"\\398"},932:{c:"T"},935:{c:"X"},978:{c:"\\3A5"},988:{c:"F"},8213:{c:"\\2014"},8215:{c:"_"},8260:{c:"/"},8710:{c:"\\394"}})},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0}),f.sansSerifBoldItalic={32:[0,0,.25],33:[.694,0,.319],34:[.694,-.471,.5],35:[.694,.194,.833],36:[.75,.056,.5,{ic:.065}],37:[.75,.056,.833],38:[.716,.022,.758],39:[.694,-.471,.278,{ic:.057}],40:[.75,.25,.389,{ic:.102}],41:[.75,.25,.389],42:[.75,-.306,.5,{ic:.068}],43:[.583,.083,.778],44:[.098,.125,.278],45:[.259,-.186,.333],46:[.098,0,.278],47:[.75,.25,.5,{ic:.1}],48:[.678,.022,.5],49:[.678,0,.5],50:[.678,0,.5,{ic:.051}],51:[.678,.022,.5],52:[.656,0,.5],53:[.656,.022,.5,{ic:.055}],54:[.678,.022,.5],55:[.656,.011,.5,{ic:.096}],56:[.678,.022,.5,{ic:.054}],57:[.677,.022,.5],58:[.444,0,.278],59:[.444,.125,.278],61:[.37,-.13,.778],63:[.704,0,.472,{ic:.064}],64:[.705,.01,.667],65:[.694,0,.667],66:[.694,0,.667],67:[.705,.01,.639,{ic:.08}],68:[.694,0,.722],69:[.691,0,.597,{ic:.091}],70:[.691,0,.569,{ic:.104}],71:[.705,.011,.667,{ic:.063}],72:[.694,0,.708,{ic:.06}],73:[.694,0,.278,{ic:.06}],74:[.694,.022,.472,{ic:.063}],75:[.694,0,.694,{ic:.091}],76:[.694,0,.542],77:[.694,0,.875,{ic:.054}],78:[.694,0,.708,{ic:.058}],79:[.716,.022,.736],80:[.694,0,.639,{ic:.051}],81:[.716,.125,.736],82:[.694,0,.646,{ic:.052}],83:[.716,.022,.556,{ic:.053}],84:[.688,0,.681,{ic:.109}],85:[.694,.022,.688,{ic:.059}],86:[.694,0,.667,{ic:.132}],87:[.694,0,.944,{ic:.132}],88:[.694,0,.667,{ic:.091}],89:[.694,0,.667,{ic:.143}],90:[.694,0,.611,{ic:.091}],91:[.75,.25,.289,{ic:.136}],93:[.75,.25,.289,{ic:.064}],94:[.694,-.527,.5],95:[-.038,.114,.5,{ic:.065}],97:[.461,.01,.481],98:[.694,.011,.517],99:[.46,.011,.444,{ic:.055}],100:[.694,.01,.517,{ic:.071}],101:[.46,.011,.444],102:[.705,0,.306,{ic:.188}],103:[.455,.206,.5,{ic:.068}],104:[.694,0,.517],105:[.68,0,.239,{ic:.076}],106:[.68,.204,.267,{ic:.069}],107:[.694,0,.489,{ic:.054}],108:[.694,0,.239,{ic:.072}],109:[.455,0,.794],110:[.454,0,.517],111:[.461,.011,.5],112:[.455,.194,.517],113:[.455,.194,.517],114:[.455,0,.342,{ic:.082}],115:[.461,.011,.383,{ic:.053}],116:[.571,.011,.361],117:[.444,.01,.517],118:[.444,0,.461,{ic:.079}],119:[.444,0,.683,{ic:.079}],120:[.444,0,.461,{ic:.076}],121:[.444,.205,.461,{ic:.079}],122:[.444,0,.435,{ic:.059}],126:[.327,-.193,.5,{ic:.06}],160:[0,0,.25],305:[.444,0,.239],567:[.444,.204,.267],768:[.694,-.527,0],769:[.694,-.527,0,{ic:.063}],770:[.694,-.527,0],771:[.677,-.543,0,{ic:.06}],772:[.631,-.552,0,{ic:.064}],774:[.694,-.508,0,{ic:.073}],775:[.68,-.576,0],776:[.68,-.582,0],778:[.693,-.527,0],779:[.694,-.527,0,{ic:.063}],780:[.654,-.487,0,{ic:.06}],913:[.694,0,.667],914:[.694,0,.667],915:[.691,0,.542,{ic:.104}],916:[.694,0,.833],917:[.691,0,.597,{ic:.091}],918:[.694,0,.611,{ic:.091}],919:[.694,0,.708,{ic:.06}],920:[.715,.022,.778],921:[.694,0,.278,{ic:.06}],922:[.694,0,.694,{ic:.091}],923:[.694,0,.611],924:[.694,0,.875,{ic:.054}],925:[.694,0,.708,{ic:.058}],926:[.688,0,.667,{ic:.098}],927:[.716,.022,.736],928:[.691,0,.708,{ic:.06}],929:[.694,0,.639,{ic:.051}],930:[.715,.022,.778],931:[.694,0,.722,{ic:.091}],932:[.688,0,.681,{ic:.109}],933:[.716,0,.778,{ic:.065}],934:[.694,0,.722],935:[.694,0,.667,{ic:.091}],936:[.694,0,.778,{ic:.076}],937:[.716,0,.722],978:[.716,0,.778,{ic:.065}],988:[.691,0,.569,{ic:.104}],8211:[.312,-.236,.5,{ic:.065}],8212:[.312,-.236,1,{ic:.065}],8213:[.312,-.236,1,{ic:.065}],8215:[-.038,.114,.5,{ic:.065}],8216:[.694,-.471,.278,{ic:.058}],8217:[.694,-.471,.278,{ic:.057}],8220:[.694,-.471,.5,{ic:.114}],8221:[.694,-.471,.5],8260:[.75,.25,.5,{ic:.1}],8710:[.694,0,.833]}},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0});var i=s(0),t=s(24);f.sansSerifBold=i.AddCSS(t.sansSerifBold,{32:{c:" "},33:{c:"!"},35:{c:"#"},36:{c:"$"},37:{c:"%"},38:{c:"&"},40:{c:"("},41:{c:")"},42:{c:"*"},43:{c:"+"},44:{c:","},45:{c:"-"},46:{c:"."},47:{c:"/"},48:{c:"0"},49:{c:"1"},50:{c:"2"},51:{c:"3"},52:{c:"4"},53:{c:"5"},54:{c:"6"},55:{c:"7"},56:{c:"8"},57:{c:"9"},58:{c:":"},59:{c:";"},61:{c:"="},63:{c:"?"},64:{c:"@"},65:{c:"A"},66:{c:"B"},67:{c:"C"},68:{c:"D"},69:{c:"E"},70:{c:"F"},71:{c:"G"},72:{c:"H"},73:{c:"I"},74:{c:"J"},75:{c:"K"},76:{c:"L"},77:{c:"M"},78:{c:"N"},79:{c:"O"},80:{c:"P"},81:{c:"Q"},82:{c:"R"},83:{c:"S"},84:{c:"T"},85:{c:"U"},86:{c:"V"},87:{c:"W"},88:{c:"X"},89:{c:"Y"},90:{c:"Z"},91:{c:"["},93:{c:"]"},94:{c:"^"},95:{c:"_"},97:{c:"a"},98:{c:"b"},99:{c:"c"},100:{c:"d"},101:{c:"e"},102:{c:"f"},103:{c:"g"},104:{c:"h"},105:{c:"i"},106:{c:"j"},107:{c:"k"},108:{c:"l"},109:{c:"m"},110:{c:"n"},111:{c:"o"},112:{c:"p"},113:{c:"q"},114:{c:"r"},115:{c:"s"},116:{c:"t"},117:{c:"u"},118:{c:"v"},119:{c:"w"},120:{c:"x"},121:{c:"y"},122:{c:"z"},126:{c:"~"},913:{c:"A"},914:{c:"B"},917:{c:"E"},918:{c:"Z"},919:{c:"H"},921:{c:"I"},922:{c:"K"},924:{c:"M"},925:{c:"N"},927:{c:"O"},929:{c:"P"},930:{c:"\\398"},932:{c:"T"},935:{c:"X"},978:{c:"\\3A5"},988:{c:"F"},8213:{c:"\\2014"},8215:{c:"_"},8260:{c:"/"},8710:{c:"\\394"}})},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0}),f.sansSerifBold={32:[0,0,.25],33:[.694,0,.367],34:[.694,-.442,.558],35:[.694,.193,.917],36:[.75,.056,.55],37:[.75,.056,1.029],38:[.716,.022,.831],39:[.694,-.442,.306],40:[.75,.249,.428],41:[.75,.25,.428],42:[.75,-.293,.55],43:[.617,.116,.856],44:[.146,.106,.306],45:[.273,-.186,.367],46:[.146,0,.306],47:[.75,.249,.55],48:[.715,.022,.55],49:[.716,0,.55],50:[.716,0,.55],51:[.716,.022,.55],52:[.694,0,.55],53:[.694,.022,.55],54:[.716,.022,.55],55:[.695,.011,.55],56:[.715,.022,.55],57:[.716,.022,.55],58:[.458,0,.306],59:[.458,.106,.306],61:[.407,-.094,.856],63:[.705,0,.519],64:[.704,.011,.733],65:[.694,0,.733],66:[.694,0,.733],67:[.704,.011,.703],68:[.694,0,.794],69:[.691,0,.642],70:[.691,0,.611],71:[.705,.011,.733],72:[.694,0,.794],73:[.694,0,.331],74:[.694,.022,.519],75:[.694,0,.764],76:[.694,0,.581],77:[.694,0,.978],78:[.694,0,.794],79:[.716,.022,.794],80:[.694,0,.703],81:[.716,.106,.794],82:[.694,0,.703],83:[.716,.022,.611],84:[.688,0,.733],85:[.694,.022,.764],86:[.694,0,.733],87:[.694,0,1.039],88:[.694,0,.733],89:[.694,0,.733],90:[.694,0,.672],91:[.75,.25,.343],93:[.75,.25,.343],94:[.694,-.537,.55],95:[-.023,.11,.55],97:[.475,.011,.525],98:[.694,.01,.561],99:[.475,.011,.489],100:[.694,.011,.561],101:[.474,.01,.511],102:[.705,0,.336],103:[.469,.206,.55],104:[.694,0,.561],105:[.695,0,.256],106:[.695,.205,.286],107:[.694,0,.531],108:[.694,0,.256],109:[.469,0,.867],110:[.468,0,.561],111:[.474,.011,.55],112:[.469,.194,.561],113:[.469,.194,.561],114:[.469,0,.372],115:[.474,.01,.422],116:[.589,.01,.404],117:[.458,.011,.561],118:[.458,0,.5],119:[.458,0,.744],120:[.458,0,.5],121:[.458,.205,.5],122:[.458,0,.476],126:[.344,-.198,.55],160:[0,0,.25],305:[.458,0,.256],567:[.458,.205,.286],768:[.694,-.537,0],769:[.694,-.537,0],770:[.694,-.537,0],771:[.694,-.548,0],772:[.66,-.56,0],774:[.694,-.552,0],775:[.695,-.596,0],776:[.695,-.595,0],778:[.694,-.538,0],779:[.694,-.537,0],780:[.657,-.5,0],913:[.694,0,.733],914:[.694,0,.733],915:[.691,0,.581],916:[.694,0,.917],917:[.691,0,.642],918:[.694,0,.672],919:[.694,0,.794],920:[.716,.022,.856],921:[.694,0,.331],922:[.694,0,.764],923:[.694,0,.672],924:[.694,0,.978],925:[.694,0,.794],926:[.688,0,.733],927:[.716,.022,.794],928:[.691,0,.794],929:[.694,0,.703],930:[.716,.022,.856],931:[.694,0,.794],932:[.688,0,.733],933:[.715,0,.856],934:[.694,0,.794],935:[.694,0,.733],936:[.694,0,.856],937:[.716,0,.794],978:[.715,0,.856],988:[.691,0,.611],8211:[.327,-.24,.55],8212:[.327,-.24,1.1],8213:[.327,-.24,1.1],8215:[-.023,.11,.55],8216:[.694,-.443,.306],8217:[.694,-.442,.306],8220:[.694,-.443,.558],8221:[.694,-.442,.558],8260:[.75,.249,.55],8710:[.694,0,.917]}},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0});var i=s(0),t=s(26);f.sansSerifItalic=i.AddCSS(t.sansSerifItalic,{32:{c:" "},33:{c:"!"},35:{c:"#"},36:{c:"$"},37:{c:"%"},38:{c:"&"},40:{c:"("},41:{c:")"},42:{c:"*"},43:{c:"+"},44:{c:","},45:{c:"-"},46:{c:"."},47:{c:"/"},48:{c:"0"},49:{c:"1"},50:{c:"2"},51:{c:"3"},52:{c:"4"},53:{c:"5"},54:{c:"6"},55:{c:"7"},56:{c:"8"},57:{c:"9"},58:{c:":"},59:{c:";"},61:{c:"="},63:{c:"?"},64:{c:"@"},65:{c:"A"},66:{c:"B"},67:{c:"C"},68:{c:"D"},69:{c:"E"},70:{c:"F"},71:{c:"G"},72:{c:"H"},73:{c:"I"},74:{c:"J"},75:{c:"K"},76:{c:"L"},77:{c:"M"},78:{c:"N"},79:{c:"O"},80:{c:"P"},81:{c:"Q"},82:{c:"R"},83:{c:"S"},84:{c:"T"},85:{c:"U"},86:{c:"V"},87:{c:"W"},88:{c:"X"},89:{c:"Y"},90:{c:"Z"},91:{c:"["},93:{c:"]"},94:{c:"^"},95:{c:"_"},97:{c:"a"},98:{c:"b"},99:{c:"c"},100:{c:"d"},101:{c:"e"},102:{c:"f"},103:{c:"g"},104:{c:"h"},105:{c:"i"},106:{c:"j"},107:{c:"k"},108:{c:"l"},109:{c:"m"},110:{c:"n"},111:{c:"o"},112:{c:"p"},113:{c:"q"},114:{c:"r"},115:{c:"s"},116:{c:"t"},117:{c:"u"},118:{c:"v"},119:{c:"w"},120:{c:"x"},121:{c:"y"},122:{c:"z"},126:{c:"~"},913:{c:"A"},914:{c:"B"},917:{c:"E"},918:{c:"Z"},919:{c:"H"},921:{c:"I"},922:{c:"K"},924:{c:"M"},925:{c:"N"},927:{c:"O"},929:{c:"P"},930:{c:"\\398"},932:{c:"T"},935:{c:"X"},978:{c:"\\3A5"},988:{c:"F"},8213:{c:"\\2014"},8215:{c:"_"},8260:{c:"/"},8710:{c:"\\394"}})},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0}),f.sansSerifItalic={32:[0,0,.25],33:[.694,0,.319],34:[.694,-.471,.5],35:[.694,.194,.833],36:[.75,.056,.5,{ic:.065}],37:[.75,.056,.833],38:[.716,.022,.758],39:[.694,-.471,.278,{ic:.057}],40:[.75,.25,.389,{ic:.102}],41:[.75,.25,.389],42:[.75,-.306,.5,{ic:.068}],43:[.583,.083,.778],44:[.098,.125,.278],45:[.259,-.186,.333],46:[.098,0,.278],47:[.75,.25,.5,{ic:.1}],48:[.678,.022,.5],49:[.678,0,.5],50:[.678,0,.5,{ic:.051}],51:[.678,.022,.5],52:[.656,0,.5],53:[.656,.022,.5,{ic:.055}],54:[.678,.022,.5],55:[.656,.011,.5,{ic:.096}],56:[.678,.022,.5,{ic:.054}],57:[.677,.022,.5],58:[.444,0,.278],59:[.444,.125,.278],61:[.37,-.13,.778],63:[.704,0,.472,{ic:.064}],64:[.705,.01,.667],65:[.694,0,.667],66:[.694,0,.667],67:[.705,.01,.639,{ic:.08}],68:[.694,0,.722],69:[.691,0,.597,{ic:.091}],70:[.691,0,.569,{ic:.104}],71:[.705,.011,.667,{ic:.063}],72:[.694,0,.708,{ic:.06}],73:[.694,0,.278,{ic:.06}],74:[.694,.022,.472,{ic:.063}],75:[.694,0,.694,{ic:.091}],76:[.694,0,.542],77:[.694,0,.875,{ic:.054}],78:[.694,0,.708,{ic:.058}],79:[.716,.022,.736],80:[.694,0,.639,{ic:.051}],81:[.716,.125,.736],82:[.694,0,.646,{ic:.052}],83:[.716,.022,.556,{ic:.053}],84:[.688,0,.681,{ic:.109}],85:[.694,.022,.688,{ic:.059}],86:[.694,0,.667,{ic:.132}],87:[.694,0,.944,{ic:.132}],88:[.694,0,.667,{ic:.091}],89:[.694,0,.667,{ic:.143}],90:[.694,0,.611,{ic:.091}],91:[.75,.25,.289,{ic:.136}],93:[.75,.25,.289,{ic:.064}],94:[.694,-.527,.5],95:[-.038,.114,.5,{ic:.065}],97:[.461,.01,.481],98:[.694,.011,.517],99:[.46,.011,.444,{ic:.055}],100:[.694,.01,.517,{ic:.071}],101:[.46,.011,.444],102:[.705,0,.306,{ic:.188}],103:[.455,.206,.5,{ic:.068}],104:[.694,0,.517],105:[.68,0,.239,{ic:.076}],106:[.68,.204,.267,{ic:.069}],107:[.694,0,.489,{ic:.054}],108:[.694,0,.239,{ic:.072}],109:[.455,0,.794],110:[.454,0,.517],111:[.461,.011,.5],112:[.455,.194,.517],113:[.455,.194,.517],114:[.455,0,.342,{ic:.082}],115:[.461,.011,.383,{ic:.053}],116:[.571,.011,.361],117:[.444,.01,.517],118:[.444,0,.461,{ic:.079}],119:[.444,0,.683,{ic:.079}],120:[.444,0,.461,{ic:.076}],121:[.444,.205,.461,{ic:.079}],122:[.444,0,.435,{ic:.059}],126:[.327,-.193,.5,{ic:.06}],160:[0,0,.25],305:[.444,0,.239],567:[.444,.204,.267],768:[.694,-.527,0],769:[.694,-.527,0,{ic:.063}],770:[.694,-.527,0],771:[.677,-.543,0,{ic:.06}],772:[.631,-.552,0,{ic:.064}],774:[.694,-.508,0,{ic:.073}],775:[.68,-.576,0],776:[.68,-.582,0],778:[.693,-.527,0],779:[.694,-.527,0,{ic:.063}],780:[.654,-.487,0,{ic:.06}],913:[.694,0,.667],914:[.694,0,.667],915:[.691,0,.542,{ic:.104}],916:[.694,0,.833],917:[.691,0,.597,{ic:.091}],918:[.694,0,.611,{ic:.091}],919:[.694,0,.708,{ic:.06}],920:[.715,.022,.778],921:[.694,0,.278,{ic:.06}],922:[.694,0,.694,{ic:.091}],923:[.694,0,.611],924:[.694,0,.875,{ic:.054}],925:[.694,0,.708,{ic:.058}],926:[.688,0,.667,{ic:.098}],927:[.716,.022,.736],928:[.691,0,.708,{ic:.06}],929:[.694,0,.639,{ic:.051}],930:[.715,.022,.778],931:[.694,0,.722,{ic:.091}],932:[.688,0,.681,{ic:.109}],933:[.716,0,.778,{ic:.065}],934:[.694,0,.722],935:[.694,0,.667,{ic:.091}],936:[.694,0,.778,{ic:.076}],937:[.716,0,.722],978:[.716,0,.778,{ic:.065}],988:[.691,0,.569,{ic:.104}],8211:[.312,-.236,.5,{ic:.065}],8212:[.312,-.236,1,{ic:.065}],8213:[.312,-.236,1,{ic:.065}],8215:[-.038,.114,.5,{ic:.065}],8216:[.694,-.471,.278,{ic:.058}],8217:[.694,-.471,.278,{ic:.057}],8220:[.694,-.471,.5,{ic:.114}],8221:[.694,-.471,.5],8260:[.75,.25,.5,{ic:.1}],8710:[.694,0,.833]}},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0});var i=s(0),t=s(28);f.sansSerif=i.AddCSS(t.sansSerif,{32:{c:" "},33:{c:"!"},35:{c:"#"},36:{c:"$"},37:{c:"%"},38:{c:"&"},40:{c:"("},41:{c:")"},42:{c:"*"},43:{c:"+"},44:{c:","},45:{c:"-"},46:{c:"."},47:{c:"/"},48:{c:"0"},49:{c:"1"},50:{c:"2"},51:{c:"3"},52:{c:"4"},53:{c:"5"},54:{c:"6"},55:{c:"7"},56:{c:"8"},57:{c:"9"},58:{c:":"},59:{c:";"},61:{c:"="},63:{c:"?"},64:{c:"@"},65:{c:"A"},66:{c:"B"},67:{c:"C"},68:{c:"D"},69:{c:"E"},70:{c:"F"},71:{c:"G"},72:{c:"H"},73:{c:"I"},74:{c:"J"},75:{c:"K"},76:{c:"L"},77:{c:"M"},78:{c:"N"},79:{c:"O"},80:{c:"P"},81:{c:"Q"},82:{c:"R"},83:{c:"S"},84:{c:"T"},85:{c:"U"},86:{c:"V"},87:{c:"W"},88:{c:"X"},89:{c:"Y"},90:{c:"Z"},91:{c:"["},93:{c:"]"},94:{c:"^"},95:{c:"_"},97:{c:"a"},98:{c:"b"},99:{c:"c"},100:{c:"d"},101:{c:"e"},102:{c:"f"},103:{c:"g"},104:{c:"h"},105:{c:"i"},106:{c:"j"},107:{c:"k"},108:{c:"l"},109:{c:"m"},110:{c:"n"},111:{c:"o"},112:{c:"p"},113:{c:"q"},114:{c:"r"},115:{c:"s"},116:{c:"t"},117:{c:"u"},118:{c:"v"},119:{c:"w"},120:{c:"x"},121:{c:"y"},122:{c:"z"},126:{c:"~"},913:{c:"A"},914:{c:"B"},917:{c:"E"},918:{c:"Z"},919:{c:"H"},921:{c:"I"},922:{c:"K"},924:{c:"M"},925:{c:"N"},927:{c:"O"},929:{c:"P"},930:{c:"\\398"},932:{c:"T"},935:{c:"X"},978:{c:"\\3A5"},988:{c:"F"},8213:{c:"\\2014"},8215:{c:"_"},8260:{c:"/"},8710:{c:"\\394"}})},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0}),f.sansSerif={32:[0,0,.25],33:[.694,0,.319],34:[.694,-.471,.5],35:[.694,.194,.833],36:[.75,.056,.5],37:[.75,.056,.833],38:[.716,.022,.758],39:[.694,-.471,.278],40:[.75,.25,.389],41:[.75,.25,.389],42:[.75,-.306,.5],43:[.583,.082,.778],44:[.098,.125,.278],45:[.259,-.186,.333],46:[.098,0,.278],47:[.75,.25,.5],48:[.678,.022,.5],49:[.678,0,.5],50:[.677,0,.5],51:[.678,.022,.5],52:[.656,0,.5],53:[.656,.021,.5],54:[.677,.022,.5],55:[.656,.011,.5],56:[.678,.022,.5],57:[.677,.022,.5],58:[.444,0,.278],59:[.444,.125,.278],61:[.37,-.13,.778],63:[.704,0,.472],64:[.704,.011,.667],65:[.694,0,.667],66:[.694,0,.667],67:[.705,.011,.639],68:[.694,0,.722],69:[.691,0,.597],70:[.691,0,.569],71:[.704,.011,.667],72:[.694,0,.708],73:[.694,0,.278],74:[.694,.022,.472],75:[.694,0,.694],76:[.694,0,.542],77:[.694,0,.875],78:[.694,0,.708],79:[.715,.022,.736],80:[.694,0,.639],81:[.715,.125,.736],82:[.694,0,.646],83:[.716,.022,.556],84:[.688,0,.681],85:[.694,.022,.688],86:[.694,0,.667],87:[.694,0,.944],88:[.694,0,.667],89:[.694,0,.667],90:[.694,0,.611],91:[.75,.25,.289],93:[.75,.25,.289],94:[.694,-.527,.5],95:[-.038,.114,.5],97:[.46,.01,.481],98:[.694,.011,.517],99:[.46,.01,.444],100:[.694,.01,.517],101:[.461,.01,.444],102:[.705,0,.306],103:[.455,.206,.5],104:[.694,0,.517],105:[.68,0,.239],106:[.68,.205,.267],107:[.694,0,.489],108:[.694,0,.239],109:[.455,0,.794],110:[.455,0,.517],111:[.46,.01,.5],112:[.455,.194,.517],113:[.455,.194,.517],114:[.455,0,.342],115:[.46,.01,.383],116:[.571,.01,.361],117:[.444,.01,.517],118:[.444,0,.461],119:[.444,0,.683],120:[.444,0,.461],121:[.444,.204,.461],122:[.444,0,.435],126:[.327,-.193,.5],160:[0,0,.25],305:[.444,0,.239],567:[.444,.205,.267],768:[.694,-.527,0],769:[.694,-.527,0],770:[.694,-.527,0],771:[.677,-.543,0],772:[.631,-.552,0],774:[.694,-.508,0],775:[.68,-.576,0],776:[.68,-.582,0],778:[.694,-.527,0],779:[.694,-.527,0],780:[.654,-.487,0],913:[.694,0,.667],914:[.694,0,.667],915:[.691,0,.542],916:[.694,0,.833],917:[.691,0,.597],918:[.694,0,.611],919:[.694,0,.708],920:[.716,.021,.778],921:[.694,0,.278],922:[.694,0,.694],923:[.694,0,.611],924:[.694,0,.875],925:[.694,0,.708],926:[.688,0,.667],927:[.715,.022,.736],928:[.691,0,.708],929:[.694,0,.639],930:[.716,.021,.778],931:[.694,0,.722],932:[.688,0,.681],933:[.716,0,.778],934:[.694,0,.722],935:[.694,0,.667],936:[.694,0,.778],937:[.716,0,.722],978:[.716,0,.778],988:[.691,0,.569],8211:[.312,-.236,.5],8212:[.312,-.236,1],8213:[.312,-.236,1],8215:[-.038,.114,.5],8216:[.694,-.471,.278],8217:[.694,-.471,.278],8220:[.694,-.471,.5],8221:[.694,-.471,.5],8260:[.75,.25,.5],8710:[.694,0,.833]}},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0});var i=s(0),t=s(30);f.scriptBold=i.AddCSS(t.scriptBold,{32:{c:" "},65:{c:"A"},66:{c:"B"},67:{c:"C"},68:{c:"D"},69:{c:"E"},70:{c:"F"},71:{c:"G"},72:{c:"H"},73:{c:"I"},74:{c:"J"},75:{c:"K"},76:{c:"L"},77:{c:"M"},78:{c:"N"},79:{c:"O"},80:{c:"P"},81:{c:"Q"},82:{c:"R"},83:{c:"S"},84:{c:"T"},85:{c:"U"},86:{c:"V"},87:{c:"W"},88:{c:"X"},89:{c:"Y"},90:{c:"Z"},913:{c:"A",f:"B"},914:{c:"B",f:"B"},917:{c:"E",f:"B"},918:{c:"Z",f:"B"},919:{c:"H",f:"B"},921:{c:"I",f:"B"},922:{c:"K",f:"B"},924:{c:"M",f:"B"},925:{c:"N",f:"B"},927:{c:"O",f:"B"},929:{c:"P",f:"B"},930:{c:"\\398",f:"B"},932:{c:"T",f:"B"},935:{c:"X",f:"B"},978:{c:"\\3A5",f:"B"},988:{c:"F",f:"B"}})},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0}),f.scriptBold={32:[0,0,.25],65:[.717,.008,.803,{ic:.213,sk:.389}],66:[.708,.028,.908,{sk:.194}],67:[.728,.026,.666,{ic:.153,sk:.278}],68:[.708,.031,.774,{ic:.081,sk:.111}],69:[.707,.008,.562,{ic:.156,sk:.139}],70:[.735,.036,.895,{ic:.095,sk:.222}],71:[.717,.037,.61,{ic:.128,sk:.25}],72:[.717,.036,.969,{ic:.272,sk:.333}],73:[.717,.017,.809,{ic:.137,sk:.333}],74:[.717,.314,1.052,{ic:.081,sk:.417}],75:[.717,.037,.914,{ic:.29,sk:.361}],76:[.717,.017,.874,{ic:.161,sk:.306}],77:[.721,.05,1.08,{ic:.136,sk:.444}],78:[.726,.036,.902,{ic:.306,sk:.389}],79:[.707,.008,.738,{ic:.067,sk:.167}],80:[.716,.037,1.013,{sk:.222}],81:[.717,.017,.883,{sk:.278}],82:[.717,.017,.85,{sk:.194}],83:[.708,.036,.868,{ic:.148,sk:.333}],84:[.735,.037,.747,{ic:.249,sk:.222}],85:[.717,.017,.8,{ic:.16,sk:.25}],86:[.717,.017,.622,{ic:.228,sk:.222}],87:[.717,.017,.805,{ic:.221,sk:.25}],88:[.717,.017,.944,{ic:.187,sk:.278}],89:[.716,.017,.71,{ic:.249,sk:.194}],90:[.717,.016,.821,{ic:.211,sk:.306}],160:[0,0,.25],913:[.698,0,.869],914:[.686,0,.818],917:[.68,0,.756],918:[.686,0,.703],919:[.686,0,.9],921:[.686,0,.436],922:[.686,0,.901],924:[.686,0,1.092],925:[.686,0,.9],927:[.696,.01,.864],929:[.686,0,.786],930:[.696,.01,.894],932:[.675,0,.8],935:[.686,0,.869],978:[.697,0,.894],988:[.68,0,.724]}},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0});var i=s(0),t=s(32);f.script=i.AddCSS(t.script,{32:{c:" "},65:{c:"A"},66:{c:"B"},67:{c:"C"},68:{c:"D"},69:{c:"E"},70:{c:"F"},71:{c:"G"},72:{c:"H"},73:{c:"I"},74:{c:"J"},75:{c:"K"},76:{c:"L"},77:{c:"M"},78:{c:"N"},79:{c:"O"},80:{c:"P"},81:{c:"Q"},82:{c:"R"},83:{c:"S"},84:{c:"T"},85:{c:"U"},86:{c:"V"},87:{c:"W"},88:{c:"X"},89:{c:"Y"},90:{c:"Z"},913:{c:"A",f:""},914:{c:"B",f:""},917:{c:"E",f:""},918:{c:"Z",f:""},919:{c:"H",f:""},921:{c:"I",f:""},922:{c:"K",f:""},924:{c:"M",f:""},925:{c:"N",f:""},927:{c:"O",f:""},929:{c:"P",f:""},930:{c:"\\398",f:""},932:{c:"T",f:""},935:{c:"X",f:""},978:{c:"\\3A5",f:""},988:{c:"F",f:""},8459:{c:"H",f:"SC"},8464:{c:"J",f:"SC"},8466:{c:"L",f:"SC"},8475:{c:"R",f:"SC"},8492:{c:"B",f:"SC"},8496:{c:"E",f:"SC"},8497:{c:"F",f:"SC"},8499:{c:"M",f:"SC"}})},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0}),f.script={32:[0,0,.25],65:[.717,.008,.803,{ic:.213,sk:.389}],66:[.708,.028,.908,{sk:.194}],67:[.728,.026,.666,{ic:.153,sk:.278}],68:[.708,.031,.774,{ic:.081,sk:.111}],69:[.707,.008,.562,{ic:.156,sk:.139}],70:[.735,.036,.895,{ic:.095,sk:.222}],71:[.717,.037,.61,{ic:.128,sk:.25}],72:[.717,.036,.969,{ic:.272,sk:.333}],73:[.717,.017,.809,{ic:.137,sk:.333}],74:[.717,.314,1.052,{ic:.081,sk:.417}],75:[.717,.037,.914,{ic:.29,sk:.361}],76:[.717,.017,.874,{ic:.161,sk:.306}],77:[.721,.05,1.08,{ic:.136,sk:.444}],78:[.726,.036,.902,{ic:.306,sk:.389}],79:[.707,.008,.738,{ic:.067,sk:.167}],80:[.716,.037,1.013,{sk:.222}],81:[.717,.017,.883,{sk:.278}],82:[.717,.017,.85,{sk:.194}],83:[.708,.036,.868,{ic:.148,sk:.333}],84:[.735,.037,.747,{ic:.249,sk:.222}],85:[.717,.017,.8,{ic:.16,sk:.25}],86:[.717,.017,.622,{ic:.228,sk:.222}],87:[.717,.017,.805,{ic:.221,sk:.25}],88:[.717,.017,.944,{ic:.187,sk:.278}],89:[.716,.017,.71,{ic:.249,sk:.194}],90:[.717,.016,.821,{ic:.211,sk:.306}],160:[0,0,.25],913:[.716,0,.75],914:[.683,0,.708],917:[.68,0,.681],918:[.683,0,.611],919:[.683,0,.75],921:[.683,0,.361],922:[.683,0,.778],924:[.683,0,.917],925:[.683,0,.75],927:[.705,.022,.778],929:[.683,0,.681],930:[.705,.022,.778],932:[.677,0,.722],935:[.683,0,.75],978:[.705,0,.778],988:[.68,0,.653],8459:[.717,.036,.969,{ic:.272,sk:.333}],8464:[.717,.314,1.052,{ic:.081,sk:.417}],8466:[.717,.017,.874,{ic:.161,sk:.306}],8475:[.717,.017,.85,{sk:.194}],8492:[.708,.028,.908,{sk:.194}],8496:[.707,.008,.562,{ic:.156,sk:.139}],8497:[.735,.036,.895,{ic:.095,sk:.222}],8499:[.721,.05,1.08,{ic:.136,sk:.444}]}},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0});var i=s(0),t=s(34);f.smallop=i.AddCSS(t.smallop,{32:{c:" "},40:{c:"("},41:{c:")"},47:{c:"/"},91:{c:"["},93:{c:"]"},123:{c:"{"},125:{c:"}"},8260:{c:"/"},9001:{c:"\\27E8"},9002:{c:"\\27E9"},10072:{c:"\\2223"},10764:{c:"\\222C\\222C"},12296:{c:"\\27E8"},12297:{c:"\\27E9"}})},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0}),f.smallop={32:[0,0,.25],40:[.85,.349,.458],41:[.85,.349,.458],47:[.85,.349,.578],91:[.85,.349,.417],92:[.85,.349,.578],93:[.85,.349,.417],123:[.85,.349,.583],125:[.85,.349,.583],160:[0,0,.25],710:[.744,-.551,.556],732:[.722,-.597,.556],770:[.744,-.551,0],771:[.722,-.597,0],8214:[.602,0,.778],8260:[.85,.349,.578],8593:[.6,0,.667],8595:[.6,0,.667],8657:[.599,0,.778],8659:[.6,0,.778],8719:[.75,.25,.944],8720:[.75,.25,.944],8721:[.75,.25,1.056],8730:[.85,.35,1],8739:[.627,.015,.333],8741:[.627,.015,.556],8747:[.805,.306,.472,{ic:.138}],8748:[.805,.306,.819,{ic:.138}],8749:[.805,.306,1.166,{ic:.138}],8750:[.805,.306,.472,{ic:.138}],8896:[.75,.249,.833],8897:[.75,.249,.833],8898:[.75,.249,.833],8899:[.75,.249,.833],8968:[.85,.349,.472],8969:[.85,.349,.472],8970:[.85,.349,.472],8971:[.85,.349,.472],9001:[.85,.35,.472],9002:[.85,.35,.472],9168:[.602,0,.667],10072:[.627,.015,.333],10216:[.85,.35,.472],10217:[.85,.35,.472],10752:[.75,.25,1.111],10753:[.75,.25,1.111],10754:[.75,.25,1.111],10756:[.75,.249,.833],10758:[.75,.249,.833],10764:[.805,.306,1.638,{ic:.138}],12296:[.85,.35,.472],12297:[.85,.35,.472]}},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0});var i=s(0),t=s(36);f.texCalligraphicBold=i.AddCSS(t.texCalligraphicBold,{32:{c:" "},47:{c:"/"},48:{c:"0"},49:{c:"1"},50:{c:"2"},51:{c:"3"},52:{c:"4"},53:{c:"5"},54:{c:"6"},55:{c:"7"},56:{c:"8"},57:{c:"9"},65:{c:"A"},66:{c:"B"},67:{c:"C"},68:{c:"D"},69:{c:"E"},70:{c:"F"},71:{c:"G"},72:{c:"H"},73:{c:"I"},74:{c:"J"},75:{c:"K"},76:{c:"L"},77:{c:"M"},78:{c:"N"},79:{c:"O"},80:{c:"P"},81:{c:"Q"},82:{c:"R"},83:{c:"S"},84:{c:"T"},85:{c:"U"},86:{c:"V"},87:{c:"W"},88:{c:"X"},89:{c:"Y"},90:{c:"Z"},97:{c:"a"},98:{c:"b"},99:{c:"c"},100:{c:"d"},101:{c:"e"},102:{c:"f"},103:{c:"g"},104:{c:"h"},105:{c:"i"},106:{c:"j"},107:{c:"k"},108:{c:"l"},109:{c:"m"},110:{c:"n"},111:{c:"o"},112:{c:"p"},113:{c:"q"},114:{c:"r"},115:{c:"s"},116:{c:"t"},117:{c:"u"},118:{c:"v"},119:{c:"w"},120:{c:"x"},121:{c:"y"},122:{c:"z"},913:{c:"A",f:"BI"},914:{c:"B",f:"BI"},917:{c:"E",f:"BI"},918:{c:"Z",f:"BI"},919:{c:"H",f:"BI"},921:{c:"I",f:"BI"},922:{c:"K",f:"BI"},924:{c:"M",f:"BI"},925:{c:"N",f:"BI"},927:{c:"O",f:"BI"},929:{c:"P",f:"BI"},930:{c:"\\398",f:"BI"},932:{c:"T",f:"BI"},935:{c:"X",f:"BI"},978:{c:"\\3A5",f:"BI"},988:{c:"F",f:"BI"},8260:{c:"/"},8710:{c:"\\394"}})},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0}),f.texCalligraphicBold={32:[0,0,.25],47:[.711,.21,.894],48:[.46,.017,.575],49:[.461,0,.575],50:[.46,0,.575],51:[.461,.211,.575],52:[.469,.194,.575],53:[.461,.211,.575],54:[.66,.017,.575],55:[.476,.211,.575],56:[.661,.017,.575],57:[.461,.21,.575],65:[.751,.049,.921,{ic:.068,sk:.224}],66:[.705,.017,.748,{sk:.16}],67:[.703,.02,.613,{sk:.16}],68:[.686,0,.892,{sk:.0958}],69:[.703,.016,.607,{sk:.128}],70:[.686,.03,.814,{ic:.116,sk:.128}],71:[.703,.113,.682,{sk:.128}],72:[.686,.048,.987,{sk:.128}],73:[.686,0,.642,{ic:.104,sk:.0319}],74:[.686,.114,.779,{ic:.158,sk:.192}],75:[.703,.017,.871,{sk:.0639}],76:[.703,.017,.788,{sk:.16}],77:[.703,.049,1.378,{sk:.16}],78:[.84,.049,.937,{ic:.168,sk:.0958}],79:[.703,.017,.906,{sk:.128}],80:[.686,.067,.81,{sk:.0958}],81:[.703,.146,.939,{sk:.128}],82:[.686,.017,.99,{sk:.0958}],83:[.703,.016,.696,{sk:.16}],84:[.72,.069,.644,{ic:.303,sk:.0319}],85:[.686,.024,.715,{ic:.056,sk:.0958}],86:[.686,.077,.737,{sk:.0319}],87:[.686,.077,1.169,{sk:.0958}],88:[.686,0,.817,{ic:.089,sk:.16}],89:[.686,.164,.759,{sk:.0958}],90:[.686,0,.818,{sk:.16}],97:[.452,.008,.633],98:[.694,.008,.521],99:[.451,.008,.513,{sk:.0639}],100:[.694,.008,.61,{sk:.192}],101:[.452,.008,.554,{sk:.0639}],102:[.701,.201,.568,{ic:.056,sk:.192}],103:[.452,.202,.545,{sk:.0319}],104:[.694,.008,.668,{sk:-.0319}],105:[.694,.008,.405],106:[.694,.202,.471],107:[.694,.008,.604],108:[.694,.008,.348,{sk:.0958}],109:[.452,.008,1.032],110:[.452,.008,.713],111:[.452,.008,.585,{sk:.0639}],112:[.452,.194,.601,{sk:.0958}],113:[.452,.194,.542,{sk:.0958}],114:[.452,.008,.529,{sk:.0639}],115:[.451,.008,.531,{sk:.0639}],116:[.643,.007,.415,{sk:.0958}],117:[.452,.008,.681,{sk:.0319}],118:[.453,.008,.567,{sk:.0319}],119:[.453,.008,.831,{sk:.0958}],120:[.452,.008,.659,{sk:.0319}],121:[.452,.202,.59,{sk:.0639}],122:[.452,.008,.555,{sk:.0639}],160:[0,0,.25],913:[.711,0,.869,{sk:.16}],914:[.686,0,.866,{sk:.0958}],915:[.68,0,.657,{ic:.12,sk:.0958}],916:[.711,0,.958,{sk:.192}],917:[.68,0,.81,{sk:.0958}],918:[.686,0,.773,{sk:.0958}],919:[.686,0,.982,{sk:.0639}],920:[.702,.017,.867,{sk:.0958}],921:[.686,0,.511,{ic:.062,sk:.128}],922:[.686,0,.971,{sk:.0639}],923:[.711,0,.806,{sk:.192}],924:[.686,0,1.142,{ic:.077,sk:.0958}],925:[.686,0,.95,{ic:.077,sk:.0958}],926:[.675,0,.841,{sk:.0958}],927:[.703,.017,.837,{sk:.0958}],928:[.68,0,.982,{sk:.0639}],929:[.686,0,.723,{ic:.124,sk:.0958}],930:[.702,.017,.867,{sk:.0958}],931:[.686,0,.885,{sk:.0958}],932:[.675,0,.637,{ic:.135,sk:.0958}],933:[.703,0,.671,{ic:.131,sk:.0639}],934:[.686,0,.767,{sk:.0958}],935:[.686,0,.947,{sk:.0958}],936:[.686,0,.714,{ic:.076,sk:.0639}],937:[.703,0,.879,{sk:.0958}],945:[.452,.008,.761,{sk:.0319}],946:[.701,.194,.66,{sk:.0958}],947:[.451,.211,.59],948:[.725,.008,.522,{sk:.0639}],949:[.461,.017,.529,{sk:.0958}],950:[.711,.202,.508,{sk:.0958}],951:[.452,.211,.6,{sk:.0639}],952:[.702,.008,.562,{sk:.0958}],953:[.452,.008,.412,{sk:.0639}],954:[.452,.008,.668],955:[.694,.013,.671],956:[.452,.211,.708,{sk:.0319}],957:[.452,0,.577,{sk:.0319}],958:[.711,.201,.508,{sk:.128}],959:[.452,.008,.585,{sk:.0639}],960:[.444,.008,.682],961:[.451,.211,.612,{sk:.0958}],962:[.451,.105,.424,{sk:.0958}],963:[.444,.008,.686],964:[.444,.013,.521,{ic:.089,sk:.0319}],965:[.453,.008,.631,{sk:.0319}],966:[.452,.216,.747,{sk:.0958}],967:[.452,.201,.718,{sk:.0639}],968:[.694,.202,.758,{sk:.128}],969:[.453,.008,.718],977:[.701,.008,.692,{sk:.0958}],978:[.703,0,.671,{ic:.131,sk:.0639}],981:[.694,.202,.712,{sk:.0958}],982:[.444,.008,.975],988:[.68,0,.689,{ic:.12,sk:.0958}],1009:[.451,.194,.612,{sk:.0958}],1013:[.444,.007,.483,{sk:.0639}],8260:[.711,.21,.894],8710:[.711,0,.958,{sk:.192}]}},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0});var i=s(0),t=s(38);f.texCalligraphic=i.AddCSS(t.texCalligraphic,{32:{c:" "},48:{c:"0"},49:{c:"1"},50:{c:"2"},51:{c:"3"},52:{c:"4"},53:{c:"5"},54:{c:"6"},55:{c:"7"},56:{c:"8"},57:{c:"9"},65:{c:"A"},66:{c:"B"},67:{c:"C"},68:{c:"D"},69:{c:"E"},70:{c:"F"},71:{c:"G"},72:{c:"H"},73:{c:"I"},74:{c:"J"},75:{c:"K"},76:{c:"L"},77:{c:"M"},78:{c:"N"},79:{c:"O"},80:{c:"P"},81:{c:"Q"},82:{c:"R"},83:{c:"S"},84:{c:"T"},85:{c:"U"},86:{c:"V"},87:{c:"W"},88:{c:"X"},89:{c:"Y"},90:{c:"Z"},913:{c:"A",f:"I"},914:{c:"B",f:"I"},917:{c:"E",f:"I"},918:{c:"Z",f:"I"},919:{c:"H",f:"I"},921:{c:"I",f:"I"},922:{c:"K",f:"I"},924:{c:"M",f:"I"},925:{c:"N",f:"I"},927:{c:"O",f:"I"},929:{c:"P",f:"I"},930:{c:"\\398",f:"I"},932:{c:"T",f:"I"},935:{c:"X",f:"I"},978:{c:"\\3A5",f:"I"},988:{c:"F",f:"I"}})},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0}),f.texCalligraphic={32:[0,0,.25],48:[.452,.022,.5],49:[.453,0,.5],50:[.453,0,.5],51:[.452,.216,.5],52:[.464,.194,.5],53:[.453,.216,.5],54:[.665,.022,.5],55:[.463,.216,.5],56:[.666,.021,.5],57:[.453,.216,.5],65:[.728,.05,.798,{sk:.194}],66:[.705,.022,.657,{sk:.139}],67:[.705,.025,.527,{sk:.139}],68:[.683,0,.771,{sk:.0833}],69:[.705,.022,.528,{sk:.111}],70:[.683,.032,.719,{ic:.11,sk:.111}],71:[.704,.119,.595,{sk:.111}],72:[.683,.048,.845,{sk:.111}],73:[.683,0,.545,{ic:.097,sk:.0278}],74:[.683,.119,.678,{ic:.161,sk:.167}],75:[.705,.022,.762,{sk:.0556}],76:[.705,.022,.69,{sk:.139}],77:[.705,.05,1.201,{sk:.139}],78:[.789,.05,.82,{ic:.159,sk:.0833}],79:[.705,.022,.796,{sk:.111}],80:[.683,.057,.696,{sk:.0833}],81:[.705,.131,.817,{sk:.111}],82:[.682,.022,.848,{sk:.0833}],83:[.705,.022,.606,{sk:.139}],84:[.717,.068,.545,{ic:.288,sk:.0278}],85:[.683,.028,.626,{ic:.061,sk:.0833}],86:[.683,.052,.613,{sk:.0278}],87:[.683,.053,.988,{sk:.0833}],88:[.683,0,.713,{ic:.094,sk:.139}],89:[.683,.143,.668,{sk:.0833}],90:[.683,0,.725,{sk:.139}],160:[0,0,.25],913:[.716,0,.75,{sk:.139}],914:[.683,0,.759,{sk:.0833}],917:[.68,0,.738,{sk:.0833}],918:[.683,0,.683,{sk:.0833}],919:[.683,0,.831,{ic:.057,sk:.0556}],921:[.683,0,.44,{ic:.064,sk:.111}],922:[.683,0,.849,{sk:.0556}],924:[.683,0,.97,{ic:.081,sk:.0833}],925:[.683,0,.803,{ic:.085,sk:.0833}],927:[.704,.022,.763,{sk:.0833}],929:[.683,0,.642,{ic:.109,sk:.0833}],930:[.704,.022,.763,{sk:.0833}],932:[.677,0,.584,{ic:.12,sk:.0833}],935:[.683,0,.828,{sk:.0833}],978:[.705,0,.583,{ic:.117,sk:.0556}],988:[.68,0,.643,{ic:.106,sk:.0833}]}},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0});var i=s(0),t=s(40);f.texMathit=i.AddCSS(t.texMathit,{32:{c:" "},33:{c:"!"},35:{c:"#"},37:{c:"%"},38:{c:"&"},40:{c:"("},41:{c:")"},42:{c:"*"},43:{c:"+"},44:{c:","},45:{c:"-"},46:{c:"."},47:{c:"/"},48:{c:"0"},49:{c:"1"},50:{c:"2"},51:{c:"3"},52:{c:"4"},53:{c:"5"},54:{c:"6"},55:{c:"7"},56:{c:"8"},57:{c:"9"},58:{c:":"},59:{c:";"},61:{c:"="},63:{c:"?"},64:{c:"@"},65:{c:"A"},66:{c:"B"},67:{c:"C"},68:{c:"D"},69:{c:"E"},70:{c:"F"},71:{c:"G"},72:{c:"H"},73:{c:"I"},74:{c:"J"},75:{c:"K"},76:{c:"L"},77:{c:"M"},78:{c:"N"},79:{c:"O"},80:{c:"P"},81:{c:"Q"},82:{c:"R"},83:{c:"S"},84:{c:"T"},85:{c:"U"},86:{c:"V"},87:{c:"W"},88:{c:"X"},89:{c:"Y"},90:{c:"Z"},91:{c:"["},93:{c:"]"},94:{c:"^"},95:{c:"_"},97:{c:"a"},98:{c:"b"},99:{c:"c"},100:{c:"d"},101:{c:"e"},102:{c:"f"},103:{c:"g"},104:{c:"h"},105:{c:"i"},106:{c:"j"},107:{c:"k"},108:{c:"l"},109:{c:"m"},110:{c:"n"},111:{c:"o"},112:{c:"p"},113:{c:"q"},114:{c:"r"},115:{c:"s"},116:{c:"t"},117:{c:"u"},118:{c:"v"},119:{c:"w"},120:{c:"x"},121:{c:"y"},122:{c:"z"},126:{c:"~"},913:{c:"A"},914:{c:"B"},917:{c:"E"},918:{c:"Z"},919:{c:"H"},921:{c:"I"},922:{c:"K"},924:{c:"M"},925:{c:"N"},927:{c:"O"},929:{c:"P"},930:{c:"\\398"},932:{c:"T"},935:{c:"X"},978:{c:"\\3A5"},988:{c:"F"},8213:{c:"\\2014"},8215:{c:"_"},8260:{c:"/"},8710:{c:"\\394"}})},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0}),f.texMathit={32:[0,0,.25],33:[.716,0,.307,{ic:.073}],34:[.694,-.379,.514],35:[.694,.194,.818],37:[.75,.056,.818],38:[.716,.022,.767],39:[.694,-.379,.307,{ic:.07}],40:[.75,.25,.409,{ic:.108}],41:[.75,.25,.409],42:[.75,-.32,.511,{ic:.073}],43:[.557,.057,.767],44:[.121,.194,.307],45:[.251,-.18,.358],46:[.121,0,.307],47:[.75,.25,.511,{ic:.106}],48:[.665,.021,.511,{ic:.051}],49:[.666,0,.511],50:[.666,.022,.511],51:[.666,.022,.511,{ic:.051}],52:[.666,.194,.511],53:[.666,.022,.511,{ic:.056}],54:[.665,.022,.511,{ic:.054}],55:[.666,.022,.511,{ic:.123}],56:[.666,.021,.511],57:[.666,.022,.511],58:[.431,0,.307],59:[.431,.194,.307],61:[.367,-.133,.767],63:[.716,0,.511],64:[.705,.011,.767],65:[.716,0,.743],66:[.683,0,.704],67:[.705,.021,.716,{ic:.096}],68:[.683,0,.755],69:[.68,0,.678,{ic:.065}],70:[.68,0,.653,{ic:.078}],71:[.705,.022,.774],72:[.683,0,.743,{ic:.117}],73:[.683,0,.386,{ic:.122}],74:[.683,.021,.525,{ic:.097}],75:[.683,0,.769,{ic:.09}],76:[.683,0,.627],77:[.683,0,.897,{ic:.113}],78:[.683,0,.743,{ic:.117}],79:[.704,.022,.767],80:[.683,0,.678,{ic:.051}],81:[.704,.194,.767],82:[.683,.022,.729],83:[.705,.022,.562,{ic:.071}],84:[.677,0,.716,{ic:.09}],85:[.683,.022,.743,{ic:.117}],86:[.683,.022,.743,{ic:.125}],87:[.683,.022,.999,{ic:.125}],88:[.683,0,.743,{ic:.082}],89:[.683,0,.743,{ic:.132}],90:[.683,0,.613,{ic:.091}],91:[.75,.25,.307,{ic:.139}],93:[.75,.25,.307,{ic:.052}],94:[.694,-.527,.511],95:[-.025,.062,.511],97:[.442,.011,.511],98:[.694,.011,.46],99:[.441,.01,.46],100:[.694,.011,.511,{ic:.056}],101:[.442,.01,.46],102:[.705,.204,.307,{ic:.143}],103:[.442,.205,.46],104:[.694,.011,.511],105:[.656,.01,.307],106:[.656,.204,.307,{ic:.057}],107:[.694,.011,.46],108:[.694,.011,.256,{ic:.056}],109:[.442,.011,.818],110:[.442,.011,.562],111:[.442,.011,.511],112:[.442,.194,.511],113:[.442,.194,.46],114:[.442,.011,.422,{ic:.062}],115:[.442,.011,.409],116:[.626,.011,.332],117:[.441,.011,.537],118:[.443,.01,.46],119:[.443,.011,.664],120:[.442,.011,.464],121:[.441,.205,.486],122:[.442,.011,.409,{ic:.057}],126:[.318,-.208,.511,{ic:.06}],160:[0,0,.25],163:[.714,.011,.769],305:[.441,.01,.307],567:[.442,.204,.332],768:[.697,-.5,0],769:[.697,-.5,0],770:[.694,-.527,0],771:[.668,-.558,0,{ic:.06}],772:[.589,-.544,0,{ic:.054}],774:[.694,-.515,0,{ic:.062}],775:[.669,-.548,0],776:[.669,-.554,0],778:[.716,-.542,0],779:[.697,-.503,0,{ic:.065}],780:[.638,-.502,0],913:[.716,0,.743],914:[.683,0,.704],915:[.68,0,.627,{ic:.078}],916:[.716,0,.818],917:[.68,0,.678,{ic:.065}],918:[.683,0,.613,{ic:.091}],919:[.683,0,.743,{ic:.117}],920:[.704,.022,.767],921:[.683,0,.386,{ic:.122}],922:[.683,0,.769,{ic:.09}],923:[.716,0,.692],924:[.683,0,.897,{ic:.113}],925:[.683,0,.743,{ic:.117}],926:[.677,0,.664,{ic:.09}],927:[.704,.022,.767],928:[.68,0,.743,{ic:.116}],929:[.683,0,.678,{ic:.051}],930:[.704,.022,.767],931:[.683,0,.716,{ic:.066}],932:[.677,0,.716,{ic:.09}],933:[.705,0,.767,{ic:.065}],934:[.683,0,.716],935:[.683,0,.743,{ic:.082}],936:[.683,0,.767,{ic:.057}],937:[.705,0,.716],978:[.705,0,.767,{ic:.065}],988:[.68,0,.653,{ic:.078}],8211:[.285,-.248,.511],8212:[.285,-.248,1.022],8213:[.285,-.248,1.022],8215:[-.025,.062,.511],8216:[.694,-.379,.307,{ic:.055}],8217:[.694,-.379,.307,{ic:.07}],8220:[.694,-.379,.514,{ic:.092}],8221:[.694,-.379,.514],8260:[.75,.25,.511,{ic:.106}],8463:[.695,.013,.54],8710:[.716,0,.818]}},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0});var i=s(0),t=s(42);f.texOldstyleBold=i.AddCSS(t.texOldstyleBold,{32:{c:" "},48:{c:"0"},49:{c:"1"},50:{c:"2"},51:{c:"3"},52:{c:"4"},53:{c:"5"},54:{c:"6"},55:{c:"7"},56:{c:"8"},57:{c:"9"},65:{c:"A"},66:{c:"B"},67:{c:"C"},68:{c:"D"},69:{c:"E"},70:{c:"F"},71:{c:"G"},72:{c:"H"},73:{c:"I"},74:{c:"J"},75:{c:"K"},76:{c:"L"},77:{c:"M"},78:{c:"N"},79:{c:"O"},80:{c:"P"},81:{c:"Q"},82:{c:"R"},83:{c:"S"},84:{c:"T"},85:{c:"U"},86:{c:"V"},87:{c:"W"},88:{c:"X"},89:{c:"Y"},90:{c:"Z"},913:{c:"A",f:"B"},914:{c:"B",f:"B"},917:{c:"E",f:"B"},918:{c:"Z",f:"B"},919:{c:"H",f:"B"},921:{c:"I",f:"B"},922:{c:"K",f:"B"},924:{c:"M",f:"B"},925:{c:"N",f:"B"},927:{c:"O",f:"B"},929:{c:"P",f:"B"},930:{c:"\\398",f:"B"},932:{c:"T",f:"B"},935:{c:"X",f:"B"},978:{c:"\\3A5",f:"B"},988:{c:"F",f:"B"}})},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0}),f.texOldstyleBold={32:[0,0,.25],48:[.46,.017,.575],49:[.461,0,.575],50:[.46,0,.575],51:[.461,.211,.575],52:[.469,.194,.575],53:[.461,.211,.575],54:[.66,.017,.575],55:[.476,.211,.575],56:[.661,.017,.575],57:[.461,.21,.575],65:[.751,.049,.921,{ic:.068,sk:.224}],66:[.705,.017,.748,{sk:.16}],67:[.703,.02,.613,{sk:.16}],68:[.686,0,.892,{sk:.0958}],69:[.703,.016,.607,{sk:.128}],70:[.686,.03,.814,{ic:.116,sk:.128}],71:[.703,.113,.682,{sk:.128}],72:[.686,.048,.987,{sk:.128}],73:[.686,0,.642,{ic:.104,sk:.0319}],74:[.686,.114,.779,{ic:.158,sk:.192}],75:[.703,.017,.871,{sk:.0639}],76:[.703,.017,.788,{sk:.16}],77:[.703,.049,1.378,{sk:.16}],78:[.84,.049,.937,{ic:.168,sk:.0958}],79:[.703,.017,.906,{sk:.128}],80:[.686,.067,.81,{sk:.0958}],81:[.703,.146,.939,{sk:.128}],82:[.686,.017,.99,{sk:.0958}],83:[.703,.016,.696,{sk:.16}],84:[.72,.069,.644,{ic:.303,sk:.0319}],85:[.686,.024,.715,{ic:.056,sk:.0958}],86:[.686,.077,.737,{sk:.0319}],87:[.686,.077,1.169,{sk:.0958}],88:[.686,0,.817,{ic:.089,sk:.16}],89:[.686,.164,.759,{sk:.0958}],90:[.686,0,.818,{sk:.16}],160:[0,0,.25],913:[.698,0,.869],914:[.686,0,.818],917:[.68,0,.756],918:[.686,0,.703],919:[.686,0,.9],921:[.686,0,.436],922:[.686,0,.901],924:[.686,0,1.092],925:[.686,0,.9],927:[.696,.01,.864],929:[.686,0,.786],930:[.696,.01,.894],932:[.675,0,.8],935:[.686,0,.869],978:[.697,0,.894],988:[.68,0,.724]}},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0});var i=s(0),t=s(44);f.texOldstyle=i.AddCSS(t.texOldstyle,{32:{c:" "},48:{c:"0"},49:{c:"1"},50:{c:"2"},51:{c:"3"},52:{c:"4"},53:{c:"5"},54:{c:"6"},55:{c:"7"},56:{c:"8"},57:{c:"9"},65:{c:"A"},66:{c:"B"},67:{c:"C"},68:{c:"D"},69:{c:"E"},70:{c:"F"},71:{c:"G"},72:{c:"H"},73:{c:"I"},74:{c:"J"},75:{c:"K"},76:{c:"L"},77:{c:"M"},78:{c:"N"},79:{c:"O"},80:{c:"P"},81:{c:"Q"},82:{c:"R"},83:{c:"S"},84:{c:"T"},85:{c:"U"},86:{c:"V"},87:{c:"W"},88:{c:"X"},89:{c:"Y"},90:{c:"Z"},913:{c:"A",f:""},914:{c:"B",f:""},917:{c:"E",f:""},918:{c:"Z",f:""},919:{c:"H",f:""},921:{c:"I",f:""},922:{c:"K",f:""},924:{c:"M",f:""},925:{c:"N",f:""},927:{c:"O",f:""},929:{c:"P",f:""},930:{c:"\\398",f:""},932:{c:"T",f:""},935:{c:"X",f:""},978:{c:"\\3A5",f:""},988:{c:"F",f:""}})},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0}),f.texOldstyle={32:[0,0,.25],48:[.452,.022,.5],49:[.453,0,.5],50:[.453,0,.5],51:[.452,.216,.5],52:[.464,.194,.5],53:[.453,.216,.5],54:[.665,.022,.5],55:[.463,.216,.5],56:[.666,.021,.5],57:[.453,.216,.5],65:[.728,.05,.798,{sk:.194}],66:[.705,.022,.657,{sk:.139}],67:[.705,.025,.527,{sk:.139}],68:[.683,0,.771,{sk:.0833}],69:[.705,.022,.528,{sk:.111}],70:[.683,.032,.719,{ic:.11,sk:.111}],71:[.704,.119,.595,{sk:.111}],72:[.683,.048,.845,{sk:.111}],73:[.683,0,.545,{ic:.097,sk:.0278}],74:[.683,.119,.678,{ic:.161,sk:.167}],75:[.705,.022,.762,{sk:.0556}],76:[.705,.022,.69,{sk:.139}],77:[.705,.05,1.201,{sk:.139}],78:[.789,.05,.82,{ic:.159,sk:.0833}],79:[.705,.022,.796,{sk:.111}],80:[.683,.057,.696,{sk:.0833}],81:[.705,.131,.817,{sk:.111}],82:[.682,.022,.848,{sk:.0833}],83:[.705,.022,.606,{sk:.139}],84:[.717,.068,.545,{ic:.288,sk:.0278}],85:[.683,.028,.626,{ic:.061,sk:.0833}],86:[.683,.052,.613,{sk:.0278}],87:[.683,.053,.988,{sk:.0833}],88:[.683,0,.713,{ic:.094,sk:.139}],89:[.683,.143,.668,{sk:.0833}],90:[.683,0,.725,{sk:.139}],160:[0,0,.25],913:[.716,0,.75],914:[.683,0,.708],917:[.68,0,.681],918:[.683,0,.611],919:[.683,0,.75],921:[.683,0,.361],922:[.683,0,.778],924:[.683,0,.917],925:[.683,0,.75],927:[.705,.022,.778],929:[.683,0,.681],930:[.705,.022,.778],932:[.677,0,.722],935:[.683,0,.75],978:[.705,0,.778],988:[.68,0,.653]}},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0});var i=s(0),t=s(46);f.texSize3=i.AddCSS(t.texSize3,{32:{c:" "},40:{c:"("},41:{c:")"},47:{c:"/"},91:{c:"["},93:{c:"]"},123:{c:"{"},125:{c:"}"},8260:{c:"/"},9001:{c:"\\27E8"},9002:{c:"\\27E9"},12296:{c:"\\27E8"},12297:{c:"\\27E9"}})},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0}),f.texSize3={32:[0,0,.25],40:[1.45,.949,.736],41:[1.45,.949,.736],47:[1.45,.949,1.044],91:[1.45,.949,.528],92:[1.45,.949,1.044],93:[1.45,.949,.528],123:[1.45,.949,.75],125:[1.45,.949,.75],160:[0,0,.25],710:[.772,-.564,1.444],732:[.749,-.61,1.444],770:[.772,-.564,0],771:[.749,-.61,0],8260:[1.45,.949,1.044],8730:[1.45,.95,1],8968:[1.45,.949,.583],8969:[1.45,.949,.583],8970:[1.45,.949,.583],8971:[1.45,.949,.583],9001:[1.45,.95,.75],9002:[1.45,.949,.75],10216:[1.45,.95,.75],10217:[1.45,.949,.75],12296:[1.45,.95,.75],12297:[1.45,.949,.75]}},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0});var i=s(0),t=s(48);f.texSize4=i.AddCSS(t.texSize4,{32:{c:" "},40:{c:"("},41:{c:")"},47:{c:"/"},91:{c:"["},93:{c:"]"},123:{c:"{"},125:{c:"}"},8260:{c:"/"},9001:{c:"\\27E8"},9002:{c:"\\27E9"},12296:{c:"\\27E8"},12297:{c:"\\27E9"},57685:{c:"\\E153\\E152"},57686:{c:"\\E151\\E150"}})},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0}),f.texSize4={32:[0,0,.25],40:[1.75,1.249,.792],41:[1.75,1.249,.792],47:[1.75,1.249,1.278],91:[1.75,1.249,.583],92:[1.75,1.249,1.278],93:[1.75,1.249,.583],123:[1.75,1.249,.806],125:[1.75,1.249,.806],160:[0,0,.25],710:[.845,-.561,1.889],732:[.823,-.583,1.889],770:[.845,-.561,0],771:[.823,-.583,0],8260:[1.75,1.249,1.278],8730:[1.75,1.25,1],8968:[1.75,1.249,.639],8969:[1.75,1.249,.639],8970:[1.75,1.249,.639],8971:[1.75,1.249,.639],9001:[1.75,1.248,.806],9002:[1.75,1.248,.806],9115:[1.154,.655,.875],9116:[.61,.01,.875],9117:[1.165,.644,.875],9118:[1.154,.655,.875],9119:[.61,.01,.875],9120:[1.165,.644,.875],9121:[1.154,.645,.667],9122:[.602,0,.667],9123:[1.155,.644,.667],9124:[1.154,.645,.667],9125:[.602,0,.667],9126:[1.155,.644,.667],9127:[.899,.01,.889],9128:[1.16,.66,.889],9129:[.01,.899,.889],9130:[.29,.015,.889],9131:[.899,.01,.889],9132:[1.16,.66,.889],9133:[.01,.899,.889],9143:[.935,.885,1.056],10216:[1.75,1.248,.806],10217:[1.75,1.248,.806],12296:[1.75,1.248,.806],12297:[1.75,1.248,.806],57344:[.625,.014,1.056],57345:[.605,.014,1.056],57680:[.12,.213,.45],57681:[.12,.213,.45],57682:[.333,0,.45],57683:[.333,0,.45],57684:[.32,.2,.4],57685:[.333,0,.9],57686:[.12,.213,.9]}},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0});var i=s(0),t=s(50);f.texVariant=i.AddCSS(t.texVariant,{32:{c:" "},65:{c:"A"},66:{c:"B"},67:{c:"C"},68:{c:"D"},69:{c:"E"},70:{c:"F"},71:{c:"G"},72:{c:"H"},73:{c:"I"},74:{c:"J"},75:{c:"K"},76:{c:"L"},77:{c:"M"},78:{c:"N"},79:{c:"O"},80:{c:"P"},81:{c:"Q"},82:{c:"R"},83:{c:"S"},84:{c:"T"},85:{c:"U"},86:{c:"V"},87:{c:"W"},88:{c:"X"},89:{c:"Y"},90:{c:"Z"},107:{c:"k"},988:{c:"\\E008"},1008:{c:"\\E009"},8463:{f:""},8726:{f:""},8740:{c:"\\E006"},8742:{c:"\\E007"},8808:{c:"\\E00C"},8809:{c:"\\E00D"},8816:{c:"\\E011"},8817:{c:"\\E00E"},8840:{c:"\\E016"},8841:{c:"\\E018"},8842:{c:"\\E01A"},8843:{c:"\\E01B"},10887:{c:"\\E010"},10888:{c:"\\E00F"},10955:{c:"\\E017"},10956:{c:"\\E019"}})},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0}),f.texVariant={32:[0,0,.25],65:[.701,0,.722],66:[.683,0,.667],67:[.702,.019,.722],68:[.683,0,.722],69:[.683,0,.667],70:[.683,0,.611],71:[.702,.019,.778],72:[.683,0,.778],73:[.683,0,.389],74:[.683,.077,.5],75:[.683,0,.778],76:[.683,0,.667],77:[.683,0,.944],78:[.683,.02,.722],79:[.701,.019,.778],80:[.683,0,.611],81:[.701,.181,.778],82:[.683,0,.722],83:[.702,.012,.556],84:[.683,0,.667],85:[.683,.019,.722],86:[.683,.02,.722],87:[.683,.019,1],88:[.683,0,.722],89:[.683,0,.722],90:[.683,0,.667],107:[.683,0,.556],160:[0,0,.25],165:[.683,0,.75],174:[.709,.175,.947],240:[.749,.021,.556],295:[.695,.013,.54],710:[.845,-.561,2.333],732:[.899,-.628,2.333],770:[.845,-.561,0],771:[.899,-.628,0],988:[.605,.085,.778],989:[.605,.085,.778],1008:[.434,.006,.667,{ic:.067}],8245:[.56,-.043,.275],8463:[.695,.013,.54],8487:[.684,.022,.722],8498:[.695,0,.556],8502:[.763,.021,.667],8503:[.764,.043,.444],8504:[.764,.043,.667],8513:[.705,.023,.639],8592:[.437,-.064,.5],8594:[.437,-.064,.5],8602:[.437,-.06,1],8603:[.437,-.06,1],8606:[.417,-.083,1],8608:[.417,-.083,1],8610:[.417,-.083,1.111],8611:[.417,-.083,1.111],8619:[.575,.041,1],8620:[.575,.041,1],8621:[.417,-.083,1.389],8622:[.437,-.06,1],8624:[.722,0,.5],8625:[.722,0,.5],8630:[.461,0,1],8631:[.46,0,1],8634:[.65,.083,.778],8635:[.65,.083,.778],8638:[.694,.194,.417],8639:[.694,.194,.417],8642:[.694,.194,.417],8643:[.694,.194,.417],8644:[.667,0,1],8646:[.667,0,1],8647:[.583,.083,1],8648:[.694,.193,.833],8649:[.583,.083,1],8650:[.694,.194,.833],8651:[.514,.014,1],8652:[.514,.014,1],8653:[.534,.035,1],8654:[.534,.037,1],8655:[.534,.035,1],8666:[.611,.111,1],8667:[.611,.111,1],8669:[.417,-.083,1],8672:[.437,-.064,1.334],8674:[.437,-.064,1.334],8705:[.846,.021,.5],8708:[.86,.166,.556],8709:[.587,0,.778],8717:[.44,0,.429],8722:[.27,-.23,.5],8724:[.766,.093,.778],8726:[.43,.023,.778],8733:[.472,-.028,.778],8736:[.694,0,.722],8737:[.714,.02,.722],8738:[.551,.051,.722],8739:[.43,.023,.222],8740:[.43,.023,.222],8741:[.431,.023,.389],8742:[.431,.024,.389],8756:[.471,.082,.667],8757:[.471,.082,.667],8764:[.365,-.132,.778],8765:[.367,-.133,.778],8769:[.467,-.032,.778],8770:[.463,-.034,.778],8774:[.652,.155,.778],8776:[.481,-.05,.778],8778:[.579,.039,.778],8782:[.492,-.008,.778],8783:[.492,-.133,.778],8785:[.609,.108,.778],8786:[.601,.101,.778],8787:[.601,.102,.778],8790:[.367,-.133,.778],8791:[.721,-.133,.778],8796:[.859,-.133,.778],8806:[.753,.175,.778],8807:[.753,.175,.778],8808:[.752,.284,.778],8809:[.752,.284,.778],8812:[.75,.25,.5],8814:[.708,.209,.778],8815:[.708,.209,.778],8816:[.919,.421,.778],8817:[.919,.421,.778],8818:[.732,.228,.778],8819:[.732,.228,.778],8822:[.681,.253,.778],8823:[.681,.253,.778],8828:[.58,.153,.778],8829:[.58,.154,.778],8830:[.732,.228,.778],8831:[.732,.228,.778],8832:[.705,.208,.778],8833:[.705,.208,.778],8840:[.828,.33,.778],8841:[.828,.33,.778],8842:[.634,.255,.778],8843:[.634,.254,.778],8847:[.539,.041,.778],8848:[.539,.041,.778],8858:[.582,.082,.778],8859:[.582,.082,.778],8861:[.582,.082,.778],8862:[.689,0,.778],8863:[.689,0,.778],8864:[.689,0,.778],8865:[.689,0,.778],8872:[.694,0,.611],8873:[.694,0,.722],8874:[.694,0,.889],8876:[.695,0,.611],8877:[.695,0,.611],8878:[.695,0,.722],8879:[.695,0,.722],8882:[.539,.041,.778],8883:[.539,.041,.778],8884:[.636,.138,.778],8885:[.636,.138,.778],8888:[.408,-.092,1.111],8890:[.431,.212,.556],8891:[.716,0,.611],8892:[.716,0,.611],8901:[.189,0,.278],8903:[.545,.044,.778],8905:[.492,-.008,.778],8906:[.492,-.008,.778],8907:[.694,.022,.778],8908:[.694,.022,.778],8909:[.464,-.036,.778],8910:[.578,.021,.76],8911:[.578,.022,.76],8912:[.54,.04,.778],8913:[.54,.04,.778],8914:[.598,.022,.667],8915:[.598,.022,.667],8916:[.736,.022,.667],8918:[.541,.041,.778],8919:[.541,.041,.778],8920:[.568,.067,1.333],8921:[.568,.067,1.333],8922:[.886,.386,.778],8923:[.886,.386,.778],8926:[.734,0,.778],8927:[.734,0,.778],8928:[.801,.303,.778],8929:[.801,.303,.778],8934:[.73,.359,.778],8935:[.73,.359,.778],8936:[.73,.359,.778],8937:[.73,.359,.778],8938:[.706,.208,.778],8939:[.706,.208,.778],8940:[.802,.303,.778],8941:[.801,.303,.778],8994:[.378,-.122,.778],8995:[.378,-.143,.778],9416:[.709,.175,.902],9484:[.694,-.306,.5],9488:[.694,-.306,.5],9492:[.366,.022,.5],9496:[.366,.022,.5],9585:[.694,.195,.889],9586:[.694,.195,.889],9632:[.689,0,.778],9633:[.689,0,.778],9650:[.575,.02,.722],9651:[.575,.02,.722],9654:[.539,.041,.778],9660:[.576,.019,.722],9661:[.576,.019,.722],9664:[.539,.041,.778],9674:[.716,.132,.667],9733:[.694,.111,.944],10003:[.706,.034,.833],10016:[.716,.022,.833],10731:[.716,.132,.667],10846:[.813,.097,.611],10877:[.636,.138,.778],10878:[.636,.138,.778],10885:[.762,.29,.778],10886:[.762,.29,.778],10887:[.801,.303,.778],10888:[.801,.303,.778],10889:[.761,.387,.778],10890:[.761,.387,.778],10891:[1.003,.463,.778],10892:[1.003,.463,.778],10901:[.636,.138,.778],10902:[.636,.138,.778],10933:[.752,.286,.778],10934:[.752,.286,.778],10935:[.761,.294,.778],10936:[.761,.294,.778],10937:[.761,.337,.778],10938:[.761,.337,.778],10949:[.753,.215,.778],10950:[.753,.215,.778],10955:[.752,.332,.778],10956:[.752,.333,.778],57350:[.43,.023,.222],57351:[.431,.024,.389],57352:[.605,.085,.778],57353:[.434,.006,.667,{ic:.067}],57356:[.752,.284,.778],57357:[.752,.284,.778],57358:[.919,.421,.778],57359:[.801,.303,.778],57360:[.801,.303,.778],57361:[.919,.421,.778],57366:[.828,.33,.778],57367:[.752,.332,.778],57368:[.828,.33,.778],57369:[.752,.333,.778],57370:[.634,.255,.778],57371:[.634,.254,.778]}},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0});var i=s(55);f.HDW1=[.75,.25,.875],f.HDW2=[.583,.082,1],f.HDW3=[.583,.082,.5],f.VSIZES=[1,1.2,1.8,2.4,3];var t={c:47,dir:i.V,sizes:f.VSIZES},e={c:175,dir:i.H,sizes:[.59],stretch:[0,175],HDW:[.59,-.544,.5]},k={c:710,dir:i.H,sizes:[.517,.817,1.335,1.777,1.909]},S={c:732,dir:i.H,sizes:[.583,.805,1.33,1.773,1.887]},r={c:8211,dir:i.H,sizes:[.5],stretch:[0,8211],HDW:[.285,-.248,.5]},B={c:8592,dir:i.H,sizes:[1],stretch:[8592,8722],HDW:f.HDW2},o={c:8594,dir:i.H,sizes:[1],stretch:[0,8722,8594],HDW:f.HDW2},a={c:8596,dir:i.H,sizes:[1],stretch:[8592,8722,8594],HDW:f.HDW2},I={c:8612,dir:i.H,stretch:[8592,8722,8739],HDW:f.HDW3,min:1.278},n={c:8614,dir:i.H,sizes:[1],stretch:[8739,8722,8594],HDW:f.HDW2},l={c:8656,dir:i.H,sizes:[1],stretch:[8656,61],HDW:f.HDW2},u={c:8658,dir:i.H,sizes:[1],stretch:[0,61,8658],HDW:f.HDW2},d={c:8660,dir:i.H,sizes:[1],stretch:[8656,61,8658],HDW:f.HDW2},X={c:8722,dir:i.H,sizes:[.778],stretch:[0,8722],HDW:[.583,.082,.778]},M={c:8739,dir:i.V,sizes:[1],stretch:[0,8739],HDW:[.75,.249,.278]},E={c:9180,dir:i.H,sizes:[.778,1],schar:[8994,8994],stretch:[57680,57684,57681],HDW:[.32,.2,.5]},m={c:9181,dir:i.H,sizes:[.778,1],schar:[8995,8995],stretch:[57682,57684,57683],HDW:[.32,.2,.5]},T={c:9182,dir:i.H,stretch:[57680,57684,57681,57685],HDW:[.32,.2,.5],min:1.8},J={c:9183,dir:i.H,stretch:[57682,57684,57683,57686],HDW:[.32,.2,.5],min:1.8},p={c:10216,dir:i.V,sizes:f.VSIZES},x={c:10217,dir:i.V,sizes:f.VSIZES},h={c:10502,dir:i.H,stretch:[8656,61,8739],HDW:f.HDW3,min:1.278},C={c:10503,dir:i.H,stretch:[8872,61,8658],HDW:f.HDW3,min:1.278};f.delimiters={40:{dir:i.V,sizes:f.VSIZES,stretch:[9115,9116,9117],HDW:[.75,.25,.875]},41:{dir:i.V,sizes:f.VSIZES,stretch:[9118,9119,9120],HDW:[.75,.25,.875]},45:X,47:t,61:{dir:i.H,sizes:[.767],stretch:[0,61],HDW:[.583,.082,.778]},91:{dir:i.V,sizes:f.VSIZES,stretch:[9121,9122,9123],HDW:f.HDW1},92:{dir:i.V,sizes:f.VSIZES},93:{dir:i.V,sizes:f.VSIZES,stretch:[9124,9125,9126],HDW:f.HDW1},94:k,95:r,123:{dir:i.V,sizes:f.VSIZES,stretch:[9127,9130,9129,9128],HDW:[.75,.25,.889]},124:{dir:i.V,sizes:[1],stretch:[0,8739],HDW:[.75,.249,.278]},125:{dir:i.V,sizes:f.VSIZES,stretch:[9131,9130,9133,9132],HDW:[.75,.25,.889]},126:S,175:e,710:k,713:e,732:S,770:k,771:S,818:r,8211:r,8212:r,8213:r,8214:{dir:i.V,sizes:[.602,1],schar:[0,8741],stretch:[0,8741],HDW:[.75,.25,.5]},8215:r,8254:e,8407:o,8592:B,8593:{dir:i.V,sizes:[.888],stretch:[8593,9168],HDW:[.694,.193,.667]},8594:o,8595:{dir:i.V,sizes:[.888],stretch:[0,9168,8595],HDW:[.694,.194,.667]},8596:a,8597:{dir:i.V,sizes:[1.044],stretch:[8593,9168,8595],HDW:[.772,.272,.667]},8606:{dir:i.H,sizes:[1],stretch:[8606,8722],HDW:f.HDW2},8608:{dir:i.H,sizes:[1],stretch:[0,8722,8608],HDW:f.HDW2},8612:I,8613:{dir:i.V,stretch:[8593,9168,8869],HDW:f.HDW1,min:1.555},8614:n,8615:{dir:i.V,stretch:[8868,9168,8595],HDW:f.HDW1,min:1.555},8624:{dir:i.V,sizes:[.722],stretch:[8624,9168],HDW:[.722,0,.667]},8625:{dir:i.V,sizes:[.722],stretch:[8625,9168],HDW:[.722,0,.667]},8636:{dir:i.H,sizes:[1],stretch:[8636,8722],HDW:f.HDW2},8637:{dir:i.H,sizes:[1],stretch:[8637,8722],HDW:f.HDW2},8638:{dir:i.V,sizes:[.888],stretch:[8638,9168],HDW:[.694,.194,.667]},8639:{dir:i.V,sizes:[.888],stretch:[8639,9168],HDW:[.694,.194,.667]},8640:{dir:i.H,sizes:[1],stretch:[0,8722,8640],HDW:f.HDW2},8641:{dir:i.H,sizes:[1],stretch:[0,8722,8641],HDW:f.HDW2},8642:{dir:i.V,sizes:[.888],stretch:[0,9168,8642],HDW:[.694,.194,.667]},8643:{dir:i.V,sizes:[.888],stretch:[0,9168,8643],HDW:[.694,.194,.667]},8656:l,8657:{dir:i.V,sizes:[.888],stretch:[8657,8214],HDW:[.694,.194,.778]},8658:u,8659:{dir:i.V,sizes:[.888],stretch:[0,8214,8659],HDW:[.694,.194,.778]},8660:d,8661:{dir:i.V,sizes:[1.044],stretch:[8657,8214,8659],HDW:[.772,.272,.778]},8666:{dir:i.H,sizes:[1],stretch:[8666,8801],HDW:[.464,-.036,1]},8667:{dir:i.H,sizes:[1],stretch:[0,8801,8667],HDW:[.464,-.036,1]},8722:X,8725:t,8730:{dir:i.V,sizes:f.VSIZES,stretch:[57345,57344,9143],HDW:[.8,.2,1.056]},8739:M,8741:{dir:i.V,sizes:[1],stretch:[0,8741],HDW:[.75,.25,.5]},8968:{dir:i.V,sizes:f.VSIZES,stretch:[9121,9122],HDW:f.HDW1},8969:{dir:i.V,sizes:f.VSIZES,stretch:[9124,9125],HDW:f.HDW1},8970:{dir:i.V,sizes:f.VSIZES,stretch:[0,9122,9123],HDW:f.HDW1},8971:{dir:i.V,sizes:f.VSIZES,stretch:[0,9125,9126],HDW:f.HDW1},8978:E,8994:E,8995:m,9001:p,9002:x,9130:{dir:i.V,sizes:[.32],stretch:[9130,9130,9130],HDW:[.29,.015,.889]},9135:r,9136:{dir:i.V,sizes:[.989],stretch:[9127,9130,9133],HDW:[.744,.244,.889]},9137:{dir:i.V,sizes:[.989],stretch:[9131,9130,9129],HDW:[.744,.244,.889]},9140:{dir:i.H,stretch:[9484,8722,9488],HDW:f.HDW3,min:1},9141:{dir:i.H,stretch:[9492,8722,9496],HDW:f.HDW3,min:1},9168:{dir:i.V,sizes:[.602,1],schar:[0,8739],stretch:[0,8739],HDW:[.602,0,.278]},9180:E,9181:m,9182:T,9183:J,9184:{dir:i.H,stretch:[714,713,715],HDW:[.59,-.544,.5],min:1},9185:{dir:i.H,stretch:[715,713,714],HDW:[.59,-.544,.5],min:1},9472:r,10072:M,10216:p,10217:x,10222:{dir:i.V,sizes:[.989],stretch:[9127,9130,9129],HDW:[.744,.244,.889]},10223:{dir:i.V,sizes:[.989],stretch:[9131,9130,9133],HDW:[.744,.244,.889]},10229:B,10230:o,10231:a,10232:l,10233:u,10234:d,10235:I,10236:n,10237:h,10238:C,10502:h,10503:C,10574:{dir:i.H,stretch:[8636,8722,8640],HDW:f.HDW3,min:2},10575:{dir:i.V,stretch:[8638,9168,8642],HDW:f.HDW1,min:1.776},10576:{dir:i.H,stretch:[8637,8722,8641],HDW:f.HDW3,min:2},10577:{dir:i.V,stretch:[8639,9168,8643],HDW:f.HDW1,min:.5},10586:{dir:i.H,stretch:[8636,8722,8739],HDW:f.HDW3,min:1.278},10587:{dir:i.H,stretch:[8739,8722,8640],HDW:f.HDW3,min:1.278},10588:{dir:i.V,stretch:[8638,9168,8869],HDW:f.HDW1,min:1.556},10589:{dir:i.V,stretch:[8868,9168,8642],HDW:f.HDW1,min:1.556},10590:{dir:i.H,stretch:[8637,8722,8739],HDW:f.HDW3,min:1.278},10591:{dir:i.H,stretch:[8739,8722,8641],HDW:f.HDW3,min:1.278},10592:{dir:i.V,stretch:[8639,9168,8869],HDW:f.HDW1,min:1.776},10593:{dir:i.V,stretch:[8868,9168,8643],HDW:f.HDW1,min:1.776},12296:p,12297:x,65079:T,65080:J}},function(c,f,s){"use strict";s(53);var i=s(1),t=s(56),e=s(57),k=s(2);if(MathJax.startup){(0,i.combineDefaults)(MathJax.config,"chtml",{fontURL:t.Package.resolvePath("output/chtml/fonts/woff-v2",!1)});var S=(0,e.selectOptionsFromKeys)(MathJax.config.chtml||{},k.TeXFont.OPTIONS);(0,i.combineDefaults)(MathJax.config,"chtml",{font:new k.TeXFont(S)})}},function(c,f,s){"use strict";var i=s(1),t=cc(s(2)),e=cc(s(3)),k=cc(s(5)),S=cc(s(7)),r=cc(s(9)),B=cc(s(11)),o=cc(s(13)),a=cc(s(15)),I=cc(s(17)),n=cc(s(19)),l=cc(s(21)),u=cc(s(23)),d=cc(s(25)),X=cc(s(27)),M=cc(s(29)),E=cc(s(31)),m=cc(s(33)),T=cc(s(35)),J=cc(s(37)),p=cc(s(39)),x=cc(s(41)),h=cc(s(43)),C=cc(s(45)),H=cc(s(47)),A=cc(s(49)),R=cc(s(4)),F=cc(s(6)),_=cc(s(51)),y=cc(s(8)),D=cc(s(10)),O=cc(s(12)),b=cc(s(14)),j=cc(s(16)),W=cc(s(18)),v=cc(s(20)),P=cc(s(22)),z=cc(s(24)),V=cc(s(26)),g=cc(s(28)),Z=cc(s(30)),w=cc(s(32)),N=cc(s(34)),L=cc(s(36)),K=cc(s(38)),U=cc(s(40)),Q=cc(s(42)),G=cc(s(44)),Y=cc(s(46)),q=cc(s(48)),$=cc(s(50));function cc(c){if(c&&c.__esModule)return c;var f={};if(null!=c)for(var s in c)Object.prototype.hasOwnProperty.call(c,s)&&(f[s]=c[s]);return f.default=c,f}(0,i.combineWithMathJax)({_:{output:{chtml:{fonts:{tex_ts:t,tex:{"bold-italic":e,bold:k,"double-struck":S,"fraktur-bold":r,fraktur:B,italic:o,largeop:a,monospace:I,normal:n,"sans-serif-bold-italic":l,"sans-serif-bold":u,"sans-serif-italic":d,"sans-serif":X,"script-bold":M,script:E,smallop:m,"tex-calligraphic-bold":T,"tex-calligraphic":J,"tex-mathit":p,"tex-oldstyle-bold":x,"tex-oldstyle":h,"tex-size3":C,"tex-size4":H,"tex-variant":A}}},common:{fonts:{tex:{"bold-italic":R,bold:F,delimiters:_,"double-struck":y,"fraktur-bold":D,fraktur:O,italic:b,largeop:j,monospace:W,normal:v,"sans-serif-bold-italic":P,"sans-serif-bold":z,"sans-serif-italic":V,"sans-serif":g,"script-bold":Z,script:w,smallop:N,"tex-calligraphic-bold":L,"tex-calligraphic":K,"tex-mathit":U,"tex-oldstyle-bold":Q,"tex-oldstyle":G,"tex-size3":Y,"tex-size4":q,"tex-variant":$}}}}}})},function(c,f,s){"use strict";var i,t=this&&this.__extends||(i=function(c,f){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(c,f){c.__proto__=f}||function(c,f){for(var s in f)f.hasOwnProperty(s)&&(c[s]=f[s])})(c,f)},function(c,f){function s(){this.constructor=c}i(c,f),c.prototype=null===f?Object.create(f):(s.prototype=f.prototype,new s)}),e=this&&this.__assign||function(){return(e=Object.assign||function(c){for(var f,s=1,i=arguments.length;s<i;s++)for(var t in f=arguments[s])Object.prototype.hasOwnProperty.call(f,t)&&(c[t]=f[t]);return c}).apply(this,arguments)},k=this&&this.__read||function(c,f){var s="function"==typeof Symbol&&c[Symbol.iterator];if(!s)return c;var i,t,e=s.call(c),k=[];try{for(;(void 0===f||0<f--)&&!(i=e.next()).done;)k.push(i.value)}catch(c){t={error:c}}finally{try{i&&!i.done&&(s=e.return)&&s.call(e)}finally{if(t)throw t.error}}return k},S=this&&this.__spread||function(){for(var c=[],f=0;f<arguments.length;f++)c=c.concat(k(arguments[f]));return c};Object.defineProperty(f,"__esModule",{value:!0}),f.CommonTeXFontMixin=function(c){var f,s;return t(i,s=c),i.prototype.getDelimiterData=function(c){return this.getChar("-smallop",c)||this.getChar("-size4",c)},(f=i).defaultVariants=S(c.defaultVariants,[["-smallop","normal"],["-largeop","normal"],["-size3","normal"],["-size4","normal"],["-tex-calligraphic","italic"],["-tex-bold-calligraphic","bold-italic"],["-tex-oldstyle","normal"],["-tex-bold-oldstyle","bold"],["-tex-mathit","italic"],["-tex-variant","normal"]]),f.defaultCssFonts=e(e({},c.defaultCssFonts),{"-smallop":["serif",!1,!1],"-largeop":["serif",!1,!1],"-size3":["serif",!1,!1],"-size4":["serif",!1,!1],"-tex-calligraphic":["cursive",!0,!1],"-tex-bold-calligraphic":["cursive",!0,!0],"-tex-oldstyle":["serif",!1,!1],"-tex-bold-oldstyle":["serif",!1,!0],"-tex-mathit":["serif",!0,!1]}),f.defaultSizeVariants=["normal","-smallop","-largeop","-size3","-size4"],f;function i(){return null!==s&&s.apply(this,arguments)||this}}},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0}),f.enum=MathJax._.output.common.FontData.enum,f.V=MathJax._.output.common.FontData.V,f.H=MathJax._.output.common.FontData.H,f.NOSTRETCH=MathJax._.output.common.FontData.NOSTRETCH,f.FontData=MathJax._.output.common.FontData.FontData},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0}),f.PackageError=MathJax._.components.package.PackageError,f.Package=MathJax._.components.package.Package},function(c,f,s){"use strict";Object.defineProperty(f,"__esModule",{value:!0}),f.APPEND=MathJax._.util.Options.APPEND,f.REMOVE=MathJax._.util.Options.REMOVE,f.Expandable=MathJax._.util.Options.Expandable,f.expandable=MathJax._.util.Options.expandable,f.makeArray=MathJax._.util.Options.makeArray,f.keys=MathJax._.util.Options.keys,f.copy=MathJax._.util.Options.copy,f.insert=MathJax._.util.Options.insert,f.defaultOptions=MathJax._.util.Options.defaultOptions,f.userOptions=MathJax._.util.Options.userOptions,f.selectOptions=MathJax._.util.Options.selectOptions,f.selectOptionsFromKeys=MathJax._.util.Options.selectOptionsFromKeys,f.separateOptions=MathJax._.util.Options.separateOptions}]);