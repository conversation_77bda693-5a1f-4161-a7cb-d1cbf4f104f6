var kInteractive={preRender:function(e,t){var n,a=(t=t||{}).singleElem,i=e||document;a?(n=[e],i=document):n=i.getElementsByClassName("kInteractive"),"undefined"!=typeof isKotobee&&(kInteractive.absoluteURL=kInteractive.getAbsoluteUrl(t));for(var r=n.length;r--;){var o=n[r];this.hasClass(o,"gallery")&&kInteractive.gallery.preRender(o,i,r),this.hasClass(o,"questions")&&kInteractive.questions.preRender(o,i,r),!this.hasClass(o,"image")&&"img"!=o.nodeName.toLowerCase()||kInteractive.image.preRender(o,i,r),this.hasClass(o,"link")&&kInteractive.link.preRender(o,i,r),this.hasClass(o,"container")&&kInteractive.container.preRender(o,i,r),this.hasClass(o,"equation")&&kInteractive.equation.preRender(o,i,r),this.hasClass(o,"lipsync")&&kInteractive.lipsync.preRender(o,i,r)}},postRender:function(t,e){var n=(e=e||{}).singleElem;if(kInteractive.absoluteURL=kInteractive.getAbsoluteUrl(e),kInteractive.videoIsFullscreen=!1,kInteractive.timestamp=new Date,kInteractive.clocks&&(kInteractive.clocks=[]),kInteractive.observers){for(var a=kInteractive.observers.length;a--;)kInteractive.observers[a].disconnect();kInteractive.observers=null}kInteractive.clearAudioVideo(t),kInteractive.currentVideo=kInteractive.currentAudio=null;var i,r=t||document;n?(i=[t],r=document):i=r.getElementsByClassName("kInteractive");for(var o=i.length;o--;){var s=i[o],c=o;null!=e.forcedIndex&&(c=e.forcedIndex),this.hasClass(s,"container")?kInteractive.container.postRender(r,s,c,e):this.hasClass(s,"questions")?kInteractive.questions.postRender(r,s,c,e):this.hasClass(s,"widget")?kInteractive.widget.postRender(r,s,c,e):this.hasClass(s,"video")?kInteractive.video.postRender(r,s,c):this.hasClass(s,"audio")?kInteractive.audio.postRender(r,s,c):this.hasClass(s,"threed")?kInteractive.threed.postRender(r,s,c,e):this.hasClass(s,"gallery")?kInteractive.gallery.postRender(r,s,c):this.hasClass(s,"image")?kInteractive.image.postRender(r,s,c):this.hasClass(s,"equation")?kInteractive.equation.postRender(r,s,c):this.hasClass(s,"lipsync")&&kInteractive.lipsync.postRender(r,s,c)}this.firstRun&&(window.addEventListener("resize",function(e){kInteractive.resizeEvent(t,n)}),this.firstRun=!1),kInteractive.resizeEvent(t,n)},getAbsoluteUrl:function(e){e=e||{};var t="";return"undefined"!=typeof isKotobee&&(t=e.chapterUrl?e.chapterUrl:stg.data.book.chapter.absoluteURL),"/"==t[0]&&(t=window.location.origin+t),t},actionEvent:function(e){return kInteractive.action(e.target,e)},action:function(e,t){if(!kInteractive.isEditorMode()){kInteractive.absoluteURL=kInteractive.getAbsoluteUrl();for(var n=e;!this.hasClass(n,"kInteractive");){if(!n.parentNode)return;if((n=n.parentNode)==document.body)return}return this.hasClass(n,"questions")?kInteractive.questions.action(n,e,t):this.hasClass(n,"widget")?kInteractive.widget.action(n):this.hasClass(e,"link")?kInteractive.link.action(n,e,t):this.hasClass(e,"image")?kInteractive.image.action(n,e,t):this.hasClass(n,"gallery")?kInteractive.gallery.action(n,e,t):this.hasClass(n,"audio")?kInteractive.audio.action(n,e,t):this.hasClass(n,"video")?kInteractive.video.action(n,e,t):this.hasClass(n,"threed")?kInteractive.threed.action(n,e,t):this.hasClass(n,"equation")?kInteractive.equation.action(n,e,t):this.hasClass(n,"lipsync")?kInteractive.lipsync.action(n,e,t):void 0}},trigger:function(e,t){this.action(t,e)},resize:function(e,t){kInteractive.resizeEvent(e,t)},resizeEvent:function(e,t){var n=e||document;t&&(n=document);for(var a=n.getElementsByClassName("kInteractive"),i=a.length;i--;){var r=a[i];this.hasClass(r,"image")?kInteractive.image.resize(r):this.hasClass(r,"gallery")?kInteractive.gallery.resize(r):this.hasClass(r,"container")?kInteractive.container.resize(r):this.hasClass(r,"video")?kInteractive.video.resize(r):this.hasClass(r,"widget")?kInteractive.widget.resize(r):this.hasClass(r,"equation")&&kInteractive.equation.resize(r)}},helpers:{},vQueue:[],youTubeReady:!1,firstRun:!0,scorm:"undefined"!=typeof scorm?scorm:null,events:"undefined"!=typeof events?events:null};if("undefined"==typeof isKotobee){kotobee={};var kotobeeListeners=[];function getKotobeeListener(e){if(e)for(var t=kotobeeListeners.length;t--;)if(kotobeeListeners[t].event==e)return kotobeeListeners[t]}function clearKotobeeListeners(e){for(var t=kotobeeListeners.length;t--;)e&&kotobeeListeners[t].event!=e||kotobeeListeners.splice(t,1)}function dispatchKotobeeEvent(){}function kInteractionStart(){window.log||(window.log=function(e){}),kInteractive.setDOMParser(),renderMiniapps(),kInteractive.preRender(),kInteractive.postRender()}function kInteractionStartSingleElem(e,t){kInteractive.setDOMParser(),kInteractive.preRender(e,{singleElem:!0}),kInteractive.postRender(e,{singleElem:!0,forcedIndex:t})}function renderMathJax(){MathJax&&MathJax.typeset&&MathJax.typeset()}document.addEventListener("DOMContentLoaded",function(e){"undefined"!=typeof kotobeeReady&&kotobeeReady(e),document.dispatchEvent(new Event("kotobeeReady")),document.dispatchEvent(new Event("kotobeeChapterLoaded")),kotobee.dispatchEvent("ready"),kotobee.dispatchEvent("chapterLoaded"),document.addEventListener("scroll",function(e){kotobee.dispatchEvent("scrolled",e)})}),kotobee.addEventListener=function(){var e,t,n,a;arguments.length<2||(e=arguments[0],t={},n=2==arguments.length?arguments[1]:(t=arguments[1],arguments[2]),t.unique&&clearKotobeeListeners(e),a={event:e,cb:n},kotobeeListeners.push(a))},kotobee.dispatchEvent=function(e,t){var n;!e||(n=getKotobeeListener(e))&&n.cb&&n.cb.apply(this,[t])};try{document.addEventListener("DOMContentLoaded",kInteractionStart,!1)}catch(e){window.addEventListener("load",kInteractionStart,!1)}function renderMiniapps(){try{if(window.top&&window.top.document&&window.top.document.body&&"kAuthor"==window.top.document.body.id)return}catch(e){}var chapterBody=document.body.textContent;window.miniapps={test:2},window.miniappsConfig={};var kmetaRoot=getRelativeRoot()+"_kmeta/",appids=[],scriptElem=document.createElement("script");function getRelativeRoot(){for(var e=(e=window.location.href.split("/")).reverse(),t="",n=0;n<e.length&&"EPUB"!=e[n];n++)t+="../";return t}function validate(e,t){for(var n,a=document.getElementsByTagName("meta"),i=0;i<a.length;i++)if(a[i].getAttribute("name")=="m-"+window.atob("Y2hlY2tzdW0=")){n=a[i];break}if(kInteractive.isSDK())return 1;if(n){var r=e.join(";"),o=t.replace(/\s+/g,"");return kInteractive.simpleHash(o+r)==n.getAttribute("content")}}scriptElem.type="text/javascript",document.body.appendChild(scriptElem),scriptElem.onload=function(result){var json=miniappJson.apps,arr=[],miniappContainer,miniappsBtn;for(var item in json)arr.push({folder:item,code:json[item]});function toggleMiniappsContainer(e){e&&e.preventDefault(),miniappContainer.classList.contains("open")?miniappContainer.classList.remove("open"):miniappContainer.classList.add("open")}function next(index){if(1<=index&&miniappJson.sdk)return finalize();if(index>=arr.length)return finalize();var folder=arr[index].folder,pageLang=document.documentElement.lang,pageLang=-1!=pageLang.indexOf("es")?"es":-1!=pageLang.indexOf("fr")?"fr":-1!=pageLang.indexOf("ar")?"ar":"en",scriptElem=document.createElement("script");scriptElem.type="text/javascript",document.body.appendChild(scriptElem),scriptElem.onload=function(){scriptElem.parentNode.removeChild(scriptElem);var configJson=window.miniappConfig;"string"==typeof configJson&&(configJson=JSON.parse(configJson));var appid=configJson.appid,code=arr[index].code,regex=new RegExp("{{"+appid+"Root}}","g"),code=code.replace(regex,kmetaRoot+"miniapps/"+folder);code=code.replace(/{{www}}/g,kmetaRoot+"../.."),eval(code),appids.push(appid);var miniappObj={};if(miniappObj.config=miniappsConfig[folder],miniappObj.locale=configJson.locale,miniappObj.appid=appid,miniappObj.appClass=window.miniappOutput,miniappObj.name=configJson.locale[pageLang].name,miniappObj.icon=configJson.icon,!miniappObj.appClass.open)return next(++index);function finishedGettingDataJson(){scriptElem.parentNode.removeChild(scriptElem);var e=document.createElement("a");e.className="app";var t=miniappObj.name,n=kmetaRoot+"miniapps/"+folder+"/icon.png";window.miniappData&&(window.miniappData._listingName&&(t=window.miniappData._listingName),window.miniappData._listingIcon&&(n=kInteractive.isAbsolutePath(window.miniappData._listingIcon)?window.miniappData._listingIcon:kmetaRoot+"miniapps/"+folder+"/userdata/"+window.miniappData._listingIcon)),e.innerHTML='<p class="name">'+t+'</p><img class="icon" src="'+n+'"/>',miniappObj.appClass.externalLink&&(e.setAttribute("href",miniappObj.appClass.externalLink),e.setAttribute("target","_blank")),miniappContainer.appendChild(e),e.addEventListener("click",function(){toggleMiniappsContainer(),miniappObj.appClass.externalLink||miniappObj.appClass.open()}),next(++index)}scriptElem=document.createElement("script"),scriptElem.type="text/javascript",document.body.appendChild(scriptElem),scriptElem.onload=finishedGettingDataJson,scriptElem.onerror=finishedGettingDataJson,scriptElem.src=kmetaRoot+"miniapps/"+folder+"/userdata/data.js"},scriptElem.src=kmetaRoot+"miniapps/"+folder+"/config.js"}function finalize(){(miniappJson.sdk||validate(appids,chapterBody))&&(document.body.appendChild(miniappsBtn),document.body.appendChild(miniappContainer))}arr.length&&(miniappContainer=document.createElement("div"),miniappContainer.id="kiMiniappContainer",miniappContainer.innerHTML="<a href='' class='closeBtn'>X</a>",miniappContainer.getElementsByClassName("closeBtn")[0].addEventListener("click",toggleMiniappsContainer),miniappsBtn=document.createElement("a"),miniappsBtn.id="kiMiniappsBtn",miniappsBtn.setAttribute("href",""),miniappsBtn.innerHTML="Apps",miniappsBtn.addEventListener("click",toggleMiniappsContainer),next(0))},scriptElem.src=kInteractive.c.join(kmetaRoot,"miniapps.js")}}kInteractive.audio={preRender:function(e,t){},postRender:function(e,t,n){var a=e.createDocumentFragment(),i=kInteractive.readData(t);t.setAttribute("id","ki-audio-"+n),t.innerHTML="";var r=document.createElement("div");r.setAttribute("id","ki-audio-"+n+"-container"),r.className="container";var o=document.createElement("a");o.className="playBtn ki-btn",o.appendChild(document.createElement("span")),"undefined"==typeof isKotobee&&o.addEventListener("click",kInteractive.actionEvent);var s=document.createElement("div");i.style&&kInteractive.c.addClass(s,i.style),a.appendChild(o),a.appendChild(r),s.appendChild(a),t.appendChild(s),i.autoplay&&kInteractive.action(t)},action:function(e){kInteractive.stopCurrentMedia();var t,n,a,i,r=kInteractive.readData(e);r&&(r.audioType||(r.audioType=r.type),e.getAttribute("id"),e.getElementsByClassName("playBtn")[0].className="playBtn ki-btn hide",t=r.src,"file"==r.audioType&&(t="undefined"==typeof isKotobee?r.audio:r.relToRoot?ph.join(bookPath,r.audio):ph.join(kInteractive.absoluteURL,r.audio)),kInteractive.scorm&&((n={}).id=kInteractive.getScormId(e.getAttribute("id"),t),n.description="Played audio: "+t,n.type="other",n.learnerResponses="Played",n.objective=r.options?r.options.objective:null,n.timestamp=new Date,kInteractive.scorm.setInteractions([n])),kInteractive.events&&kInteractive.events.add({action:"audioPlayed",param:t,elem:e,data:r}),(a=document.createElement("audio")).setAttribute("controls","true"),a.setAttribute("autoplay","true"),a.setAttribute("data-tap-disabled","false"),(i=document.createElement("source")).src=kInteractive.cleanURL(t),a.appendChild(i),a.appendChild(document.createTextNode("Your browser does not support the audio element")),a.className="ki-noHighlight",r.options&&r.options.hideDownloadBtn&&(a.className+=" hideDownloadBtn",a.setAttribute("controlsList","nodownload")),a.oncanplay=function(){kInteractive.currentAudio==e&&kInteractive.tinCan({verb:"played",activity:"Audio: "+t})},e.getElementsByClassName("container")[0].appendChild(a),a.play(),kInteractive.currentAudio=e,kInteractive.c.addClass(kInteractive.currentAudio,"playing"))}};var kInteractiveCommon={checkResponsiveFloat:function(e,t){var n;e&&e.disableWrapForMobile&&"none"!=e.float&&(n=t.parentNode,t.offsetWidth>.6*n.offsetWidth&&.4*(n.offsetWidth-t.offsetWidth)<100?kInteractive.c.addClass(t,"fullRow"):kInteractive.c.removeClass(t,"fullRow"))},openLink:function(n){var e={cb:function(e){var t=document.createElement("iframe");t.src=n,e.appendChild(t)},closed:function(){}};kInteractive.openFrame(e)},openFrame:function(p){var v=document.getElementById("kInteractiveFrame"),m=document.getElementById("kiDarkOverlay"),h="";if(p.dict||(p.dict={}),p.width&&(h+="width:"+p.width+"px;max-width:"+p.width+"px;"),p.height&&(h+="height:"+p.height+"px;max-height:"+p.height+"px;"),v||((m=document.createElement("div")).id="kiDarkOverlay",document.body.appendChild(m),m.style.display="none",m.style.position="fixed",m.addEventListener("click",kInteractive.closeFrame),(v=document.createElement("div")).id="kInteractiveFrame"),p.pos||(p.pos="top"),"none"!=p.pos){var e=document.createElement("a"),t="closeBtn";p.pos&&(t+=" "+p.pos),e.className=t;var n="Close";try{"undefined"!=typeof translit&&(n=translit.get("close"))}catch(e){}p.dict&&p.dict.close&&(n=p.dict.close),"side"!=p.pos&&(e.innerHTML=n),e.addEventListener("click",kInteractive.closeFrame),v.appendChild(e)}var a,g=p.class?p.class:"";(p.width||p.height)&&(g+=" fixed"),p.aspectRatio&&(g+=" aspectRatio"),v.style.display="block",v.className=g,document.body.appendChild(v),kInteractive.frameIsOpen=!0,kInteractive.frameOb=p;try{"undefined"!=typeof isKotobee&&(native||1<(a=window.location.hash.split("#")).length&&(window.location.hash+=(-1==a[1].indexOf("?")?"?":"&")+"popup",window.addEventListener("hashchange",this.backBtnPressed)))}catch(e){}function i(){m.style.display="block",m.className="show";var e=window.innerHeight||document.documentElement.clientHeight||document.getElementsByTagName("body")[0].clientHeight,t=window.innerWidth||document.documentElement.clientWidth||document.getElementsByTagName("body")[0].clientWidth;e-=20,t-=20;var n,a,i=1;p.width&&p.height&&(1<(i=(n=t/p.width)<(a=e/p.height)?n:a)&&(i=1));var r="display:block;";r+=h;var o=!1;try{"undefined"!=typeof isKotobee&&(o=stg.data.settings.rtl)}catch(e){}var s=p.width||p.height?(o?"50%":"-50%")+",-50%":"0%,0%";r+="-webkit-transform:translate("+s+") scale("+i+");-moz-transform:translate("+s+") scale("+i+");transform:translate("+s+") scale("+i+");",r+="position:fixed;","undefined"==typeof isKotobee&&"-50%,-50%"==s&&(r+="top:"+Math.round(window.innerHeight/2)+"px;",r+="left:"+Math.round(window.innerWidth/2)+"px;"),-1!=g.indexOf("aspectRatio")&&window.innerWidth/window.innerHeight>16/9&&(r+="padding-bottom: "+95*window.innerHeight/Math.round(window.innerWidth)+"%"),v.style.cssText=r,v.className=g+" ready";var c,l,d,u=v.getElementsByClassName("closeBtn");u.length&&(c=u[0],l=1/i,d="-50%",kInteractive.hasClass(c,"side")&&(d="0%"),c.style.cssText="-webkit-transform: translate("+d+",0%) scale("+l+");-webkit-transform-origin: 50% 50%;-moz-transform: translate("+d+",0%) scale("+l+");-moz-transform-origin: 50% 50%;transform: translate("+d+",0%) scale("+l+");transform-origin: 50% 50%;")}kInteractive.openFrame.resized=i,window.addEventListener("resize",i),setTimeout(function(){i(),p.cb&&p.cb(v)},100)},closeAlert:function(e){e&&e.stopPropagation();var t=document.getElementById("kiSystemAlertBox"),n=document.getElementById("kiSystemAlertBackdrop");t&&t.parentNode.removeChild(t),n&&n.parentNode.removeChild(n),kInteractive.alertIsOpen=!1,window.removeEventListener("resize",kInteractive.alert.resized),kInteractive.alert&&(kInteractive.alert.resized=null)},backBtnPressed:function(e){e.oldURL&&e.oldURL.match(/[?&]popup/g)&&(window.removeEventListener("hashchange",kInteractive.backBtnPressed),kInteractive.closeFrame())},closeFrame:function(){document.getElementById("kFullscreenGallery")&&kInteractive.gallery.exitFullscreen();var e=document.getElementById("kInteractiveFrame"),t=document.getElementById("kiDarkOverlay");kInteractive.frameIsOpen=!1,window.removeEventListener("resize",kInteractive.openFrame.resized),kInteractive.openFrame.resized=null,kInteractive.c.removeClass(e,"ready");var n=kInteractive.frameOb,a="";n.width&&(a+="width:"+n.width+"px;max-width:"+n.width+"px;"),n.height&&(a+="height:"+n.height+"px;max-height:"+n.height+"px;"),e.style.cssText=a,t.className="",setTimeout(function(){e.innerHTML="",e.style.display=t.style.display="none",n.closed&&n.closed(),kInteractive.frameOb=null},300),window.removeEventListener("hashchange",kInteractive.closeFrame),window.location.hash=window.location.hash.replace(/[?&]popup/g,"")},stopCurrentMedia:function(){var e,t,n,a,i;if(kInteractive.currentVideo&&(e=kInteractive.currentVideo.getAttribute("id")+"-container",(t=document.getElementById(e))&&(n=t.parentNode,t.parentNode.removeChild(t),(a=document.createElement("div")).setAttribute("id",e),a.className="container",(i=kInteractive.currentVideo.getElementsByClassName("playBtn")[0]).className="playBtn ki-btn",kInteractive.c.removeClass(kInteractive.currentVideo,"playing"),n.appendChild(a))),kInteractive.currentAudio)if(e=kInteractive.currentAudio.getAttribute("id"))e+="-container",(t=document.getElementById(e))&&(n=t.parentNode,t.parentNode.removeChild(t),(a=document.createElement("div")).setAttribute("id",e),a.className="container playerContainer",(i=kInteractive.currentAudio.getElementsByClassName("playBtn")[0])&&(i.className="playBtn ki-btn"),kInteractive.c.removeClass(kInteractive.currentAudio,"playing"),n.appendChild(a));else try{var r=kInteractive.currentAudio.getElementsByTagName("audio");r.length&&(r[0].pause(),r[0].src=""),kInteractive.currentAudio.pause&&(kInteractive.currentAudio.pause(),kInteractive.currentAudio.src="");for(var o=document.getElementsByClassName("kiAudioLoader"),s=o.length;s--;)o[s].parentNode.removeChild(o[s])}catch(e){}},hasClass:function(e,t){if(e&&e.className)for(var n=e.className.trim().split(" "),a=0;a<n.length;a++)if(n[a]&&n[a].trim()==t)return!0},appendAfterDelay:function(e,t){setTimeout(function(){e.appendChild(t)},30)},replaceHTML:function(e,t){var n="string"==typeof e?document.getElementById(e):e,a=n.cloneNode(!1);return a.innerHTML=t,n.parentNode.replaceChild(a,n),a},tinCan:function(e){if("undefined"!=typeof isKotobee)try{angular.element(document.body).scope().tinCanShortcut(e)}catch(e){}},setDOMParser:function(){!function(e){"use strict";var t=e.prototype,r=t.parseFromString;try{if((new e).parseFromString("","text/html"))return}catch(e){}t.parseFromString=function(e,t){if(/^\s*text\/html\s*(?:;|$)/i.test(t)){var n,a=document.implementation.createHTMLDocument(""),i=a.documentElement;return i.innerHTML=e,n=i.firstElementChild,1===i.childElementCount&&"html"===n.localName.toLowerCase()&&a.replaceChild(n,i),a}return r.apply(this,arguments)}}(DOMParser)},compareVersions:function(e,t){if(e==t)return 0;if(null==e)return 1;if(null==t)return-1;var n=e.split("."),a=t.split(".");return a.length?n.length?Number(n[0])>Number(a[0])?1:Number(a[0])>Number(n[0])?-1:(n.shift(1),a.shift(1),kInteractive.compareVersions(n.join("."),a.join("."))):-1:1},readData:function(e){var t=(t=e.getAttribute("data-kotobee"))||e.getAttribute("data");return this.readRawData(t)},readRawData:function(t){try{var n=1;if(!t)return;return JSON.parse(decodeURI(kInteractive.XORCipher(n).decode("kotobee%%author",t)))}catch(e){try{return JSON.parse(kInteractive.XORCipher(n).decode("kotobee%%author",t))}catch(e){}}},writeData:function(e,t,n){n=n||1;var a=encodeURI(JSON.stringify(t)),i=e.hasAttribute("data-kotobee")?"data-kotobee":"data";e.setAttribute(i,kInteractive.XORCipher(n).encode("kotobee%%author",a))},XORCipher:function(e){var u="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";return{encode:function(e,t){var n;return n=e,function(e){var t,n,a,i,r,o,s,c,l=0,d="";if(!e)return e;for(;t=e[l++],n=e[l++],a=e[l++],i=(s=t<<16|n<<8|a)>>12&63,r=s>>6&63,o=63&s,d+=u.charAt(s>>18&63)+u.charAt(i)+u.charAt(r)+u.charAt(o),l<e.length;);return((c=e.length%3)?d.slice(0,c-3):d)+"===".slice(c||3)}(t=i(t,function(e,t){return e.charCodeAt(0)^a(n,t)}))},decode:function(e,t){return t=function(e){var t,n,a,i,r,o,s,c,l=0,d=[];if(!e)return e;e+="";for(;i=u.indexOf(e.charAt(l++)),r=u.indexOf(e.charAt(l++)),o=u.indexOf(e.charAt(l++)),s=u.indexOf(e.charAt(l++)),t=(c=i<<18|r<<12|o<<6|s)>>16&255,n=c>>8&255,a=255&c,d.push(t),64!==o&&(d.push(n),64!==s&&d.push(a)),l<e.length;);return d}(t),n=e,i(t,function(e,t){return String.fromCharCode(e^a(n,t))}).join("");var n}};function a(e,t){return e.charCodeAt(Math.floor(t%e.length))}function i(e,t){for(var n=[],a=0;a<e.length;a++)n[a]=t(e[a],a);return n}},getScormId:function(e,t){return e+(t=t?"-"+t.replace(/[^a-zA-Z0-9]/g,"-").substr(0,80):"")},confirm:function(e){kInteractive.alert(e)},alert:function(e){if(e){this.closeAlert();var t,n,a=document.createElement("div");a.setAttribute("id","kiSystemAlertBox"),e.raw&&(a.className="raw"),a.style.position="fixed","undefined"==typeof isKotobee&&(a.style.top=Math.round(window.innerHeight/2)+"px",a.style.left=Math.round(window.innerWidth/2)+"px"),e.rtl&&a.setAttribute("direction","rtl"),e.fullHeight&&(a.className+=" fullHeight"),e.raw?a.innerHTML=e.content.replace("&nbsp;","&#160;"):((t=document.createElement("div")).innerHTML=e.content.replace("&nbsp;","&#160;"),t.className="content",a.appendChild(t)),e.title&&((n=document.createElement("div")).innerHTML=e.title.replace("&nbsp;","&#160;"),n.className="header",a.insertBefore(n,a.firstChild)),kInteractive.alertIsOpen=!0;var i=document.createElement("a");if(!e.raw){var r=document.createElement("div");r.className="footer";var o,s="OK";try{"undefined"!=typeof translit&&(s=translit.get("ok"))}catch(e){}e.okBtn&&(s=e.okBtn),i.innerHTML=s.replace("&nbsp;","&#160;"),i.className="okBtn",i.addEventListener("click",kInteractive.closeAlert),e.cb&&i.addEventListener("click",e.cb),r.appendChild(i),e.noBtn&&((o=document.createElement("a")).innerHTML=e.noBtn,o.className="cancelBtn",o.addEventListener("click",kInteractive.closeAlert),r.appendChild(o)),a.appendChild(r)}var c=document.createElement("div");e.noBackdrop||(c.setAttribute("id","kiSystemAlertBackdrop"),c.addEventListener("click",kInteractive.closeAlert),c.style.position="fixed",document.body.appendChild(c)),document.body.appendChild(a),i.focus(),kInteractive.alert.resized=l,window.addEventListener("resize",l),setTimeout(function(){kInteractive.c.addClass(a,"show"),kInteractive.c.addClass(c,"show")},50)}function l(){"undefined"==typeof isKotobee&&(a.style.top=Math.round(window.innerHeight/2)+"px",a.style.left=Math.round(window.innerWidth/2)+"px")}},c:{addClass:function(e,t){var n=e.className.trim();if(t instanceof Array){for(var a=!0,i=0;i<t.length;i++)-1==n.indexOf(t[i])&&(a=!1,n+=(""==n?"":" ")+t[i]);if(a)return}else{if(0<=n.indexOf(t))return;n+=(""==n?"":" ")+t}e.className=n},toggleClass:function(e,t){this.strHasClass(e.className,t)?this.removeClass(e,t):this.addClass(e,t)},hasClass:function(e,t){var n=e.className;return e instanceof SVGElement&&(n=n.baseVal),this.strHasClass(n,t)},strHasClass:function(e,t){for(var n=(e=e||"").trim().split(" "),a=0;a<n.length;a++)if(n[a]==t)return!0;return!1},removeClass:function(e,t){var n=e.className;if(t instanceof Array){for(var a=!0,i=0;i<t.length;i++)0<=n.indexOf(t[i])&&(a=!1,n=n.replace(t[i],""));if(a)return}else{if(-1==n.indexOf(t))return;n=(n=n.replace(t,"")).replace(/ {2,}?/g," ")}""==(n=n.trim())?e.removeAttribute("class"):e.className=n},removeHash:function(e){return-1==e.indexOf("#")?e:e.substring(0,e.lastIndexOf("#"))},removeFilename:function(e){var t=kInteractive.c.normalizedArray(e);if(-1==t[t.length-1].indexOf("."))return e;t.splice(t.length-1,1);var n=t.join("/");return arguments.length&&"/"==e.substr(0,1)&&(n="/"+n),n},normalizedArray:function(e){for(var t=(e=e.replace(/\\/g,"/")).split("/"),n=t.length;n--;)t[n]?".."==t[n]&&0<n&&".."!=t[n-1]&&(t.splice(n,1),t.splice(n-1,1)):t.splice(n,1);return t[0].match(/http[s]?:$/g)&&(t[0]+="//"+t[1],t.splice(1,1)),t[0].match(/kotobee:$/g)&&(t[0]+="//"+t[1],t.splice(1,1)),t[0].match(/chrome-extension:$/g)&&(t[0]+="//"+t[1],t.splice(1,1)),"file:"==t[0]&&(t[0]+="///"+t[1],t.splice(1,1)),t},normalize:function(e){var t=kInteractive.c.normalizedArray(e).join("/");return arguments.length&&"/"==e.substr(0,1)&&(t="file:///"+t),t},join:function(){for(var e=new Array,t=0;t<arguments.length;t++){var n=kInteractive.c.normalizedArray(arguments[t]);t<arguments.length-1&&!kInteractive.isAbsolutePath(n[n.length-1])&&0<=n[n.length-1].indexOf(".")&&n.splice(n.length-1,1),e=e.concat(n)}var a=kInteractive.c.normalize(e.join("/"));return"/"==arguments[0].substr(0,1)&&(a=desktop?"file:////"+a:"/"+a),a}},shuffleArray:function(e){e.sort(function(){return Math.random()-.5})},allowDrop:function(e){e.preventDefault()},drag:function(e){e.stopPropagation(),e.dataTransfer&&e.dataTransfer.setData("text",e.target.id),dragged=e.target},drop:function(e){e.preventDefault(),e.target.hasAttribute("ondragover")&&e.target.appendChild(e.view.dragged)},escapeHtml:function(e){var t={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#039;"};return e.replace(/[&<>"']/g,function(e){return t[e]})},getChildIndex:function(e){for(var t=0;null!=(e=e.previousSibling);)t++;return t},isAbsolutePath:function(e){if(e)return 0==e.trim().indexOf("http://")||(0==e.trim().indexOf("https://")||(0==e.trim().indexOf("//")||(0==e.trim().indexOf("data:")||(!!/.\:[\\\/].*/g.test(e.trim())||void 0))))},isMobile:function(e){if(/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent))return!0;try{if(0<window.location.href.indexOf("?preview")){for(var t=(t=window.location.href.split("?"))[1].split("&"),n={},a=0;a<t.length;a++){var i=t[a].split("=");n[i[0]]=i[1]}if(n.mobile)return!0}}catch(e){}},closeFullscreenVideo:function(){kInteractive.currentVideo&&(kInteractive.currentVideo.webkitExitFullscreen?kInteractive.currentVideo.webkitExitFullscreen():kInteractive.currentVideo.mozCancelFullScreen?kInteractive.currentVideo.mozCancelFullScreen():document.webkitExitFullscreen?document.webkitExitFullscreen():document.mozCancelFullScreen&&document.mozCancelFullScreen(),kInteractive.videoIsFullscreen=!1)},getWidgetHome:function(e,t){return e.path?"undefined"==typeof isKotobee?e.path:ph.join(angular.element(document.body).scope().data.book.chapter.absoluteURL,e.path):"undefined"==typeof isKotobee?"../js/widgets/":bookPath+"EPUB/js/widgets/"},isKotobeeUser:function(){return"undefined"!=typeof kotobee&&kotobee.user&&kotobee.user.loggedIn},getWidgetUrl:function(e,t){var n,a=kInteractive.getWidgetHome(e,t)+"/"+e.name+"/"+e.src;return"undefined"==typeof isKotobee||(n=angular.element(document.body).scope().data.user)&&n.email&&n.signatures&&n.signatures.bookwidgets&&e.id&&0==e.id.indexOf("com.kidimedia")&&(a+="?oauth_signature="+n.signatures.bookwidgets.signature,a+="&oauth_nonce="+n.signatures.bookwidgets.nonce,a+="&oauth_timestamp="+n.signatures.bookwidgets.timestamp,a+="&oauth_consumer_key="+n.signatures.bookwidgets.key,a+="&lti_version=LTI-1p0",a+="&oauth_signature_method=HMAC-SHA1",a+="&oauth_version=1.0",a+="&tool_consumer_info_product_family_code=kotobee",a+="&lis_person_contact_email_primary="+n.email,a+="&lis_person_name_full="+(n.name?n.name:""),a+="&user_id="+n.id,a+="&lti_message_type=basic-lti-launch-request",angular.element(document.body).scope().refreshSignatures&&angular.element(document.body).scope().refreshSignatures()),a=kInteractive.cleanURL(a)},clearAudioVideo:function(e){var t=e||document;kInteractive.stopCurrentMedia();for(var n=t.getElementsByTagName("audio"),a=n.length;a--;)try{if(n[a].hasAttribute("data-dontclose"))continue;n[a].pause(),n[a].children.length||(n[a].src="")}catch(e){}for(var i=t.getElementsByTagName("video"),a=i.length;a--;)i[a].pause(),i[a].children.length||(i[a].src="")},cleanURL:function(e){try{native&&mobile&&(e=(e=window.Ionic.WebView.convertFileSrc(e)).replace(/([^:])\/\//g,"$1/"))}catch(e){}return e},listenForSwiping:function(o){o.addEventListener("touchstart",function(e){s=e.touches[0].clientX,c=e.touches[0].clientY},!1),o.addEventListener("touchmove",function(e){if(null===s)return;if(null===c)return;var t=e.touches[0].clientX,n=e.touches[0].clientY,a=s-t,i=c-n;5<a?(o.dispatchEvent(new Event("swipeleft")),r()):a<-5?(o.dispatchEvent(new Event("swiperight")),r()):5<i?(o.dispatchEvent(new Event("swipeup")),r()):i<-5&&(o.dispatchEvent(new Event("swipedown")),r());function r(){c=s=null}e.preventDefault()},!1);var s=null,c=null},unique:function(e){return e.filter(function(e,t,n){return n.indexOf(e)===t})},getFile:function(e,t){var n=new XMLHttpRequest;n.open("GET",e),n.onload=function(){200===n.status&&t(n.responseText)},n.send()},getBookID:function(){var e=document.head.querySelector('meta[name="kotobee-book"]');return"undefined"!=typeof isKotobee&&kotobee.book?kotobee.book.meta.dc.identifier.replace("urn:uuid:","")+(kotobee.user&&kotobee.user.email?kotobee.user.email:""):e?e.content.replace("urn:uuid:",""):""},getChapterID:function(){var e,t;return(t="undefined"!=typeof isKotobee?kotobee.currentChapter?kotobee.currentChapter.url:"/ch1.xhtml":(e=document.head.querySelector('meta[name="kotobee-chapter"]'))?e.content:"").substring(t.lastIndexOf("/")+1).replace(".xhtml","")},setStorage:function(e){kInteractive.storage=e||sessionStorage},getLocalData:function(t,e){var n,a=t.label||t.category;t&&e&&(a?(n=kInteractive.storage.getItem("ebook-"+kInteractive.getBookID())||"[]",e(n=(n=JSON.parse(n)).filter(function(e){return t.label?t.category?t.subcategory?e.label==t.label&&e.category==t.category&&e.subcategory==t.subcategory:e.label==t.label&&e.category==t.category:e.label==t.label:t.subcategory?e.category==t.category&&e.subcategory==t.subcategory:e.category==t.category}))):e({error:"invalid label or category!"}))},getLocalDataArray:function(e,t){var n,a;Array.isArray(e)?t&&(n=kInteractive.storage.getItem("ebook-"+kInteractive.getBookID())||"[]",n=JSON.parse(n),a=[],e.forEach(function(t){(t.label||t.category)&&a.push({results:n.filter(function(e){return t.label?t.category?t.subcategory?e.label==t.label&&e.category==t.category&&e.subcategory==t.subcategory:e.label==t.label&&e.category==t.category:e.label==t.label:t.subcategory?e.category==t.category&&e.subcategory==t.subcategory:e.category==t.category})})}),t(a.length?a:n)):t([{error:"invalid array!"}])},setLocalData:function(t,e){var n,a,i;t&&e&&(t.label?t.data?(n={label:t.label,data:t.data,date:(new Date).toUTCString()},t.category&&(n.category=t.category),t.subcategory&&(n.subcategory=t.subcategory),i=kInteractive.storage.getItem("ebook-"+kInteractive.getBookID())||"[]",i=JSON.parse(i),t.overwrite&&-1!=(a=i.findIndex(function(e){return t.category?t.subcategory?e.label==t.label&&e.category==t.category&&e.subcategory==t.subcategory:e.label==t.label&&e.category==t.category:e.label==t.label}))?i.splice(a,1,n):i.push(n),kInteractive.storage.setItem("ebook-"+kInteractive.getBookID(),JSON.stringify(i)),e({success:"added"})):e({error:"invalid data!"}):e({error:"invalid label!"}))},setLocalDataArray:function(e,t){var a,i;Array.isArray(e)?t&&(a=kInteractive.storage.getItem("ebook-"+kInteractive.getBookID())||"[]",a=JSON.parse(a),i={},e.forEach(function(t){var e,n;t.label?i.hasOwnProperty("error")||(e={label:t.label,data:t.data,date:(new Date).toUTCString()},t.category&&(e.category=t.category),t.subcategory&&(e.subcategory=t.subcategory),t.overwrite&&-1!=(n=a.findIndex(function(e){return t.category?t.subcategory?e.label==t.label&&e.category==t.category&&e.subcategory==t.subcategory:e.label==t.label&&e.category==t.category:e.label==t.label}))?a.splice(n,1,e):a.push(e)):i.error="invalid label!"}),i.hasOwnProperty("error")||(i.success="added"),kInteractive.storage.setItem("ebook-"+kInteractive.getBookID(),JSON.stringify(a)),t(i)):t([{error:"invalid array!"}])},deleteLocalData:function(t,e){var n,a;t&&e&&(t.label?(n=kInteractive.storage.getItem("ebook-"+kInteractive.getBookID())||"[]",-1!=(a=(n=JSON.parse(n)).findIndex(function(e){return t.category?t.subcategory?e.label==t.label&&e.category==t.category&&e.subcategory==t.subcategory:e.label==t.label&&e.category==t.category:e.label==t.label}))&&n.splice(a,1),kInteractive.storage.setItem("ebook-"+kInteractive.getBookID(),JSON.stringify(n)),e({success:!0})):e({error:"invalid label!"}))},deleteLocalDataArray:function(e,t){var n,a;Array.isArray(e)?t&&(n=kInteractive.storage.getItem("ebook-"+kInteractive.getBookID())||"[]",n=JSON.parse(n),a={},e.forEach(function(t){var e;t.label?a.hasOwnProperty("error")||-1!=(e=n.findIndex(function(e){return t.category?t.subcategory?e.label==t.label&&e.category==t.category&&e.subcategory==t.subcategory:e.label==t.label&&e.category==t.category:e.label==t.label}))&&n.splice(e,1):a.error="invalid label!"}),a.hasOwnProperty("error")||(a.success=!0),kInteractive.storage.setItem("ebook-"+kInteractive.getBookID(),JSON.stringify(n)),t(a)):t([{error:"invalid array!"}])},clearLocalData:function(){kInteractive.storage?kInteractive.storage.removeItem("ebook-"+kInteractive.getBookID()):kInteractive.error("unable to find storage!")},isEditorMode:function(){return"undefined"!=typeof editorMode&&editorMode},isPreview:function(){return/\/Kotobee\s?Author\//.test(location.pathname)},isSDK:function(){return"undefined"!=typeof isKotobee?!isRelease:-1!=location.host.indexOf("localhost")&&-1!=["http:","https:"].indexOf(location.protocol)||-1!=location.href.indexOf("reader/www/epub/EPUB")},getBoundingBox:function(e){if(3!=e.nodeType)return e.getBoundingClientRect();var t=document.createRange();t.selectNode(e);var n=t.getBoundingClientRect();return t.detach(),n},simpleHash:function(e){for(var t=0,n=0;n<e.length;n++)t=(t<<5)-t+e.charCodeAt(n),t|=0;return t},phpDateToJS:function(e){var t=(e=(e=(e=e.split("+")[0]).split(".")[0]).replace("T"," ")).split(/[- :]/);return new Date(t[0],t[1]-1,t[2],t[3],t[4],t[5])},getClock:function(e){if(kInteractive.clocks||(kInteractive.clocks=[],setInterval(function(){for(var e=0;e<kInteractive.clocks.length;e++){var t=kInteractive.clocks[e];t.paused||(t.time++,t.cb&&t.cb(t.time))}},1e3)),kInteractive.clocks[e])return kInteractive.clocks[e];var t={id:e,time:0,pause:function(){this.paused=!0},resume:function(){this.paused=!1},destroy:function(){for(var e=0;e<kInteractive.clocks.length;e++)if(kInteractive.clocks[e]==this)return kInteractive.clocks.splice(e,1)}};return kInteractive.clocks.push(t),t},observe:function(e,n,a){kInteractive.observers||(kInteractive.observers=[]);var t=new IntersectionObserver(function(e){for(var t=0;t<e.length;t++)0==e[t].intersectionRatio?(a&&a(),e[t].hidden&&e[t].hidden()):(n&&n(),e[t].shown&&e[t].shown())},{threshold:[0,1],rootMargin:"0px"});kInteractive.observers.push(t),t.observe(e)},getTimezone:function(){try{return Intl.DateTimeFormat().resolvedOptions().timeZone}catch(e){return"GMT"}}};for(var item in kInteractiveCommon)kInteractive[item]=kInteractiveCommon[item];function kPersister(e){this.enabled=!0,this.delegateEvents=!0,this.storage="cloud",this.root=document.body,this.interval=200,this.maxDataSize=0,this.book="",this.chapter="",this.data=[],this.text=[],this.number=[],this.radio=[],this.checkbox=[],this.textarea=[],this.dragdrop=[],this.callback=null,this.silentMode=!1,this.appendDataMode=!1,this.forceNotification=!1,this.saveGlobal=!1,this.isSaveGlobal=!1,this.listener=null,this.testMode=!1,this.enableUncheckRadio=!1,this.setRadioGroup=!0,this.messages={save:"Save",unsaved:"You have unsaved changes",saving:"Saving your responses ..",saved:"Changes has been saved!",dismiss:"Dismiss",lastSaved:"Last saved on",longDataError:"Some answers not saved (marked in red)"},this.design={placement:"bottom-right",unsaved:{color:"#fff",background:"#de7a7a"},success:{color:"#fff",background:"#14c39a"}},this.enable=function(e){this.enabled=!0,this.silentMode=e,this.renderInlineBar(),this.renderFloatingBar()},this.disable=function(){this.enabled=!1,u(".unsaved-changes-"+n.root.id)&&u(".unsaved-changes-"+n.root.id).classList.remove("show")},this.setOptions=function(e){var t=this;for(var n in e)"messages"==n?t.setMessages(e.messages):"design"==n?t.setDesign(e.design):t.hasOwnProperty(n)&&(t[n]=e[n],"root"==n&&(t[n].id=e[n].id||"_"+Math.random().toString(36).substr(2,9)))},this.setDesign=function(e){for(var t in e)this.design.hasOwnProperty(t)?(-1!=["placement"].indexOf(t)||e[t].color||e[t].background)&&(this.design[t]=e[t]):p("error",["invalid design state:",t])},this.setMessages=function(e){var t=this;for(var n in e)if(t.messages.hasOwnProperty(n)){if(t.messages[n]=e[n],u(".unsaved-changes-"+t.root.id)&&!kInteractive.isEditorMode()&&u(".unsaved-changes-"+t.root.id+" ."+n))if("button"==u(".unsaved-changes-"+t.root.id+" ."+n).nodeName.toLowerCase())try{u(".unsaved-changes-"+t.root.id+" ."+n).innerText=e[n]}catch(e){}else try{u(".unsaved-changes-"+t.root.id+" ."+n+" span").innerText=e[n]}catch(e){}}else p("error",["invalid message key:",n])},this.waitFor=function(e,t,n){var a=this;n=n||0,window[e]||0==e.indexOf("#")&&"undefined"!=typeof document&&document.querySelector(e)||0==e.indexOf(".")&&"undefined"!=typeof document&&document.querySelector(e)||"document.body"==e&&"undefined"!=typeof document&&document.body?t():n<3e3?setTimeout(function(){a.waitFor(e,t,n+100)},100):(p("error",["unable to find:",e]),t&&t())},this.renderInlineBar=function(){var e,t,n=this;u(".persist-log-"+n.root.id)&&u(".persist-log-"+n.root.id).parentNode.removeChild(u(".persist-log-"+n.root.id)),n.silentMode&&!kInteractive.isEditorMode()&&(u(e=n.getRootSelector())?((t=u(e)).length&&(t=t[0]),t.insertAdjacentHTML("beforeend",'<div class="persist-log persist-log-'+n.root.id+'"><span class="saving" style="display:none;color:'+n.design.unsaved.background+'">'+n.messages.saving+'</span><span class="success" style="display:none;color:'+n.design.success.background+'">'+n.messages.lastSaved.trim()+" "+(new Date).toLocaleDateString()+'</span><span class="error" style="display:none"></span></div>')):p("warn",["unable to find root selector:",e]))},this.renderFloatingBar=function(){var e=this;if(u(".unsaved-changes-"+e.root.id)&&u(".unsaved-changes-"+e.root.id).parentNode.removeChild(u(".unsaved-changes-"+e.root.id)),!e.silentMode&&!kInteractive.isEditorMode()){var t="",n="",a="",i=e.design.placement.split("-");if(i.length&&(a+=i[0]+":70px;"),1<i.length)switch(a+=i[1]+":0px;",i[1]){case"left":t="-",n="";break;case"right":t="",n="-";break;default:p("error",["invalid horizontal placement:",i[1]])}var r,o="#fff",s="#333";"undefined"==typeof kotobee||(r=document.querySelectorAll("#kotobee .tabs")).length&&(o=getComputedStyle(r[0]).backgroundColor,s=getComputedStyle(r[0]).color);var c='<style id="'+e.root.id+'-persister-styles">.unsaved-changes-'+e.root.id+"{"+a+";display:none;transform:translateX("+t+"100%);background:"+o+";color:"+s+"}.unsaved-changes-"+e.root.id+".show{display:block;transform: translateX("+n+"20px)}.unsaved-changes-"+e.root.id+" button{background-color:"+e.design.unsaved.background+";color:"+e.design.unsaved.color+"}.unsaved-changes-"+e.root.id+".success button{background-color:"+e.design.success.background+";color:"+e.design.success.color+"</style>";document.head.querySelector("#"+e.root.id+"-persister-styles")||document.head.insertAdjacentHTML("beforeend",c);var l=u(".perspectiveContainer")?u(".perspectiveContainer"):document.body;if(l){var d='<div class="unsaved-changes unsaved-changes-'+e.root.id+'"><div class="unsaved"><span>'+e.messages.unsaved+'</span> <button class="dismiss">'+e.messages.dismiss+'</button> <button class="save-now">'+e.messages.save+'</button></div><div class="saving" style="display:none;"><svg width="20px" height="20px" style="background:none;float:left;margin-top:5px;margin-right:5px" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid" class="lds-rolling"><circle cx="50" cy="50" fill="none" stroke="#ffffff" stroke-width="10" r="35" stroke-dasharray="164.93361431346415 56.97787143782138" transform="rotate(149.879 50 50)"><animateTransform attributeName="transform" type="rotate" calcMode="linear" values="0 50 50;360 50 50" keyTimes="0;1" dur="1s" begin="0s" repeatCount="indefinite"></animateTransform></circle></svg><span>'+e.messages.saving+'</span></div><div class="saved" style="display:none;"><span>'+e.messages.saved+'</span> <button class="dismiss">'+e.messages.dismiss+'</button></div><div class="notsaved" style="display:none;"><span></span> <button class="dismiss">'+e.messages.dismiss+"</button></div></div>";try{l.insertAdjacentHTML("afterbegin",d),e.waitFor(".unsaved-changes-"+e.root.id,function(){u(".unsaved-changes-"+e.root.id+" .dismiss",!0)?u(".unsaved-changes-"+e.root.id+" .dismiss",!0).forEach(function(e){e.addEventListener("click",function(e){u(e.currentTarget).parents(".unsaved-changes").classList.remove("show")})}):p("error",["unable to find dismiss button!"]),u(".unsaved-changes-"+e.root.id+" .save-now")?u(".unsaved-changes-"+e.root.id+" .save-now").addEventListener("click",function(){e.save()}):p("error",["unable to find save button!"])})}catch(e){p("error",[e,d])}}else p("error",["unable to find suitable container!"])}},this.getRootSelector=function(){return n.root.id?"#"+n.root.id:"."+n.root.className.splice(" ").join(".")},this.setNotify=function(e){var t=this;t.enabled&&u(".unsaved-changes-"+t.root.id)&&!kInteractive.isEditorMode()&&(e&&e.constructor===Object&&0<Object.keys(e).length&&t.setOptions(e),u(".unsaved-changes-"+t.root.id).classList.contains("show")&&!u(".unsaved-changes-"+t.root.id).classList.contains("success")||t.updateNotification("reset"))},this.save=function(e){var t=this;if(t.enabled){t.collectInputs(),t.updateNotification("saving");var n=[];t.text&&t.text.forEach(function(e){e.id&&n.push(e)}),t.number&&t.number.forEach(function(e){e.id&&n.push(e)}),t.radio&&t.radio.forEach(function(e){e.id&&n.push(e)}),t.checkbox&&t.checkbox.forEach(function(e){e.id&&n.push(e)}),t.textarea&&t.textarea.forEach(function(e){e.id&&n.push(e)}),t.dragdrop&&t.dragdrop.forEach(function(e){e.dataset.category=Array.prototype.slice.apply(e.parentNode.classList).find(function(e){return-1!=e.indexOf("ki-dd-category")})||"",e.id&&n.push(e)});var a=n.map(function(e){var t=-1!=["radio","checkbox"].indexOf(e.type)?e.checked:"div"==e.nodeName.toLowerCase()?e.dataset.category||"":e.value;return{id:e.id,type:e.type||e.nodeName.toLowerCase(),value:t}});if(this.appendDataMode)for(var i=0;i<t.data.length;i++){var r=t.data[i],o=a.find(function(e){return e.id==r.id});o?Object.assign(r,o):a.push(r)}t.setLocalData(a,function(){"cloud"==t.storage&&kInteractive.isKotobeeUser()&&"undefined"!=typeof isKotobee?t.push(e):(t.updateNotification("success"),e&&e())})}},this.setLocalData=function(e,n){var a=this,t={label:kInteractive.getChapterID()+"-"+a.root.id,data:JSON.stringify(e),overwrite:!0};kInteractive.setLocalData(t,function(e){window.unsaved="cloud"==a.storage,"cloud"!=a.storage&&a.updateNotification("success");var t={target:a};e.error?t.error=e.error:t.result=e,a.listener.emit("persister.saved",t),"function"==typeof n&&n()})},this.getLocalData=function(){var a=this;return new Promise(function(n){if("cloud"==a.storage&&"undefined"!=typeof isKotobee&&kInteractive.isKotobeeUser())try{kotobee.getData({label:kInteractive.getChapterID()+"-"+a.root.id},function(e){e.length?(a.merge(e),t()):(a.data=[],n())})}catch(e){a.listener.emit("persister.error",e.toString()),n()}else kInteractive.getLocalDataArray([{}],function(e){e.length?kInteractive.setLocalDataArray(a.cleanupData(e).map(function(e){return Object.assign(e,{overwrite:!0})}),t):t()});function t(){kInteractive.getLocalData({label:kInteractive.getChapterID()+"-"+a.root.id},function(t){if(Array.isArray(t)){if(t.length)try{a.data=JSON.parse(t[0].data)}catch(e){a.data=[],u("body.cke_editable")||a.listener.emit("persister.error",{target:a,error:'invalid or empty slot for "'+kInteractive.getChapterID()+"-"+a.root.id+'": '+JSON.stringify(t)})}else a.data=[];n()}else u("body.cke_editable")||a.listener.emit("persister.error",{target:a,error:'invalid or empty slot for "'+kInteractive.getChapterID()+"-"+a.root.id+'": '+JSON.stringify(t)})})}})},this.cleanupData=function(e){return Array.isArray(e)?e.slice(0).sort(function(e,t){return e.date>t.date?-1:e.date<t.date?1:0}).filter(function(t,e,n){return n.findIndex(function(e){return e.label==t.label})==e}):(p("error",["invalid data:",e]),[])},this.clear=function(n){var a=this;function t(e){p("warn",[e]),window.unsaved=!1;var t={target:a};e.error?t.error=e.error:t.result=e,a.listener.emit("persister.unsaved",t),"function"==typeof n&&n()}kInteractive.deleteLocalData({label:kInteractive.getChapterID()+"-"+a.root.id},function(e){e.success&&("cloud"==a.storage&&kInteractive.isKotobeeUser()?kotobee.deleteData({label:kInteractive.getChapterID()+"-"+a.root.id},function(e){e.success&&t(e)}):t(e))})},this.merge=function(e){var t,n,a=this;1<e.length&&(p("warn",["duplicate slots:",t=e.sort(function(e,t){return e.date>t.date?-1:e.date<t.date?1:0})]),p("warn",["deleting all slots matching",(n=t[0]).label]),kotobee.deleteData({label:n.label,all:!0},function(e){p("warn",[e]),p("warn",["insert only last slot for",n.label]),kotobee.setData(n,function(e){p("warn",[e])})}));var i=a.storageAPI.getItem("ebook-"+kInteractive.getBookID())||"[]",r=(i=JSON.parse(i)).findIndex(function(e){return e.label==kInteractive.getChapterID()+"-"+a.root.id});-1!=r&&i.splice(r,1),i=i.concat(e),i=a.cleanupData(i),a.storageAPI.setItem("ebook-"+kInteractive.getBookID(),JSON.stringify(i))},this.push=function(t){var n=this;if(n.enabled){var e=n.storageAPI.getItem("ebook-"+kInteractive.getBookID())||"[]";if(e=(e=JSON.parse(e)).map(function(e){var t=JSON.parse(JSON.stringify(e));return t.overwrite=!0,delete t.date,t}),"cloud"==n.storage&&kInteractive.isKotobeeUser())try{var a=n.cleanupData(e),i=a;i.forEach(function(e){JSON.parse(e.data).forEach(function(e){var t=u("#"+e.id);t&&t.classList.remove("inputError")})});a.filter(function(e){return e.data.length>n.maxDataSize});var r=[];0,kotobee.setDataArray(i,function(e){p("warn",[e]),o(t,e.error||(r.length?n.messages.longDataError:null))})}catch(e){o(t,e.toString())}else o(t)}function o(e,t){window.unsaved=!1,t?(n.updateNotification("error",t),n.listener.emit("persister.error",{target:n,error:t})):(n.updateNotification("success"),n.listener.emit("persister.pushed",{target:n,result:{success:"pushed"}}),n.isSaveGlobal?("function"==typeof n.saveGlobal?n.saveGlobal():"object"==typeof n.saveGlobal&&n.saveGlobal.hasOwnProperty("callback")&&n.saveGlobal.callback(),n.isSaveGlobal=!1):"function"==typeof e&&e())}},this.updateNotification=function(e,t){var n=this;try{if(n.enabled&&!kInteractive.isEditorMode())if(u(".persist-log-"+n.root.id)&&n.silentMode)switch(e){case"saving":u(".persist-log-"+n.root.id+" span.saving").style.display="block",u(".persist-log-"+n.root.id+" span.success").style.display="none",u(".persist-log-"+n.root.id+" span.error").style.display="none";break;case"success":u(".persist-log-"+n.root.id+" span.saving").style.display="none",u(".persist-log-"+n.root.id+" span.success").style.display="block",u(".persist-log-"+n.root.id+" span.error").style.display="none";break;case"error":u(".persist-log-"+n.root.id+" span.saving").style.display="none",u(".persist-log-"+n.root.id+" span.success").style.display="none",u(".persist-log-"+n.root.id+" span.error").style.display="block",u(".persist-log-"+n.root.id+" span.error").textContent=t||"Unexpected error!"}else if(u(".unsaved-changes-"+n.root.id))switch(e){case"saving":u(".unsaved-changes-"+n.root.id+" .saved").style.display="none",u(".unsaved-changes-"+n.root.id+" .saving").style.display="block",u(".unsaved-changes-"+n.root.id+" .unsaved").style.display="none",u(".unsaved-changes-"+n.root.id+" .notsaved").style.display="none",u(".unsaved-changes-"+n.root.id).classList.contains("success")&&u(".unsaved-changes-"+n.root.id).classList.remove("success"),n.silentMode||u(".unsaved-changes-"+n.root.id).classList.contains("show")||u(".unsaved-changes-"+n.root.id).classList.add("show");break;case"success":u(".unsaved-changes-"+n.root.id+" .saving").style.display="none",n.silentMode||n.isSaveGlobal?u(".unsaved-changes-"+n.root.id).classList.remove("show"):(u(".unsaved-changes-"+n.root.id).classList.add("success"),u(".unsaved-changes-"+n.root.id+" .saved").style.display="block");break;case"error":u(".unsaved-changes-"+n.root.id+" .saving").style.display="none",n.silentMode||n.isSaveGlobal?u(".unsaved-changes-"+n.root.id).classList.remove("show"):(u(".unsaved-changes-"+n.root.id).classList.add("error"),u(".unsaved-changes-"+n.root.id+" .notsaved span").textContent=t||"Unexpected error!",u(".unsaved-changes-"+n.root.id+" .notsaved").style.display="block");break;case"reset":u(".unsaved-changes-"+n.root.id+" .saved").style.display="none",u(".unsaved-changes-"+n.root.id+" .saving").style.display="none",u(".unsaved-changes-"+n.root.id+" .unsaved").style.display=n.silentMode?"none":"block",u(".unsaved-changes-"+n.root.id+" .notsaved").style.display="none",u(".unsaved-changes-"+n.root.id).classList.contains("success")&&u(".unsaved-changes-"+n.root.id).classList.remove("success"),n.silentMode||u(".unsaved-changes-"+n.root.id).classList.contains("show")||u(".unsaved-changes-"+n.root.id).classList.add("show")}}catch(e){}},this.collectInputs=function(){var e=this,t=this.getRootSelector();e.root=u(t),e.root?(e.text=u(e.root).find('input[type="text"]',!0),e.number=u(e.root).find('input[type="number"]',!0),e.radio=u(e.root).find('input[type="radio"]',!0),e.checkbox=u(e.root).find('input[type="checkbox"]:not([name="preserveAnswers"])',!0),e.textarea=u(e.root).find("textarea",!0),e.dragdrop=u(e.root).find(".ki-draggable",!0)):p("error",["unable to re-select root element by:",t])},this.handleChange=function(){var e=this;e.enabled&&(e.silentMode?e.save():e.saveGlobal&&!e.forceNotification||e.setNotify())},this.processInputs=function(){var s=this;function c(){p("warn",["persister change handler:",this.id,-1!=["radio","checkbox"].indexOf(this.type)?this.checked:this.value]),s.handleChange()}function t(e,t){document.body.addEventListener(e,function(e){var a,i,r,o;e.target.id==t.id&&(-1!=["radio","checkbox"].indexOf(t.type)?t.checked=e.target.checked:t.value=e.target.value,a=c.bind(t),i=(i=s.interval)||20,r=r||!0,function(){var e=this,t=arguments,n=r&&!o;clearTimeout(o),o=setTimeout(function(){o=null,r||a.apply(e,t)},i),n&&a.apply(e,t)}())})}s.collectInputs();var a=0;if(s.text&&s.text.forEach(function(e){e.id||(e.id=(e.name?e.name:"tx")+"-"+a),a++,e.id&&!e.dataset.freeze&&(t("keyup",e),t("change",e))}),s.number&&s.number.forEach(function(e){e.id||(e.id=(e.name?e.name:"num")+"-"+a),a++,e.id&&!e.dataset.freeze&&(t("keyup",e),t("change",e))}),s.radio&&s.radio.forEach(function(n){n.id||(n.id=(n.name?n.name:"rb")+"-"+a),a++,n.id&&!n.dataset.freeze&&(s.enabledUncheckRadio?document.body.addEventListener("click",function(e){var t;e.target.id==n.id&&(t=e.target.dataset.state||"",Boolean(t.toString())?(e.target.checked=!1,e.target.dataset.state=""):e.target.dataset.state=e.target.checked,c.call(n))}):t("change",n))}),s.checkbox&&s.checkbox.forEach(function(e){e.id||(e.id=(e.name?e.name:"cb")+"-"+a),a++,e.id&&!e.dataset.freeze&&t("change",e)}),s.textarea&&s.textarea.forEach(function(e){e.id||(e.id=(e.name?e.name:"ta")+"-"+a),a++,e.id&&!e.dataset.freeze&&(t("keyup",e),t("change",e))}),s.setRadioGroup)try{s.radio&&s.radio.forEach(function(e){e.name=e.id?e.id.match(/(.*?)(\d+)/)[0].replace("-0-",""):e.name})}catch(e){}},this.restore=function(e){var o=this;setTimeout(function(){o.getLocalData().then(function(){o.data.length&&o.data.forEach(function(e){var t=e.id;if(t)if(isNaN(t)){var n,a,i,r=u("#"+t);if(r)switch(e.type){case"text":case"number":case"textarea":r.value=e.value||"";break;case"radio":case"checkbox":r.checked=e.value;break;case"div":e.value&&((n=r.cloneNode(!0)).dataset.category=e.value,!(a=o.getQContainer(r))||(i=a.getElementsByClassName(e.value)[0])&&(i.appendChild(n),r.parentNode.removeChild(r)));break;default:p("warn",["invalid input type:",e.type])}else p("error",["unable to find input by id:",t])}else p("error",["invalid input selector:",t]);else p("error",["invalid input selector:",t])}),o.renderInlineBar(),"function"==typeof e?e():(window.unsaved=!1,o.listener.emit("persister.restored",{target:o}))})},0)},this.getQContainer=function(e){for(;!e.classList.contains("ques");)e=e.parentNode;return e},this.renderSaveTab=function(e){var t,n,a,i,r=this;e?e.addEventListener("click",function(e){e.preventDefault(),r.isSaveGlobal=!0,r.push()}):u("#save-changes")||(t=u("#tabMenu"))&&((n=t.querySelectorAll("a:not(.chapterBtn)")).length&&(a=(n=n[0]).parentNode,(i=n.cloneNode(!0)).id="save-changes",i.querySelector(".icon").className="icon ion-"+(r.saveGlobal.icon||"checkmark-circled"),i.querySelector(".tab-title").innerText=r.saveGlobal.label||r.messages.save,a.insertAdjacentHTML("beforeend",i.outerHTML)),u("#save-changes").addEventListener("click",function(e){e.preventDefault(),r.isSaveGlobal=!0,r.push()}))},this.init=function(){var t=this;function n(){t.delegateEvents&&t.processInputs(),t.enabled&&(t.renderInlineBar(),t.renderFloatingBar()),window.addEventListener("beforeunload",function(e){"undefined"!=typeof kotobee&&(-1!=!location.hostname.indexOf("localhost")&&-1!=!location.hostname.indexOf("127.0.0.1")||"undefined"!=typeof config&&!config.kotobee.public||t.testMode)&&window.unsaved&&(t.setNotify(),e.preventDefault(),e.returnValue="",delete e.returnValue)})}kInteractive.isEditorMode()||("cloud"==t.storage&&kInteractive.isKotobeeUser()?kotobee.getDataPerms(function(e){e&&e.maxDataSize&&(t.maxDataSize=e.maxDataSize,p("warn",["data size limit:",t.maxDataSize]),n())}):n()),window.unsaved=!1,t.listener.emit("persister.ready",{target:t})},this.listener=new function(){this.listeners={},this.on=function(e,t){return this.addListener(e,t)},this.once=function(e,t){this.listeners[e]=this.listeners[e]||[];var n=function(){t(),this.off(e,n)};return this.listeners[e].push(n),this},this.off=function(e,t){return this.removeListener(e,t)},this.die=function(e){return this.removeAllListeners(e)},this.addListener=function(e,t){return this.listeners[e]=this.listeners[e]||[],this.listeners[e].push(t),this},this.removeListener=function(e,t){var n=this.listeners[e]||[];return this.listeners[e]=n.filter(function(e){return e!==t}),this},this.removeAllListeners=function(e){return delete this.listeners[e],this},this.emit=function(){if(arguments.length<1)return!1;var e=arguments[0],t=this.listeners[e];if(!t)return!1;var n=1<arguments.length?Array.prototype.slice.call(arguments).slice(1):[];return t.forEach(function(e){e.apply(this,n)}),!0},this.listListeners=function(e){return this.listeners[e]||[]}};var t,n=this;function u(e){if(arguments.length<1)return null;var t=e,i=null;if("string"==typeof t){var n=!1,a=document;if(1<arguments.length)for(var r,o=Array.prototype.slice.call(arguments).slice(1),s=0;s<o.length;s++)"boolean"==typeof(r=o[s])?n=r:"object"==typeof r&&(a=r);if(0==(i=Array.prototype.slice.call(a.querySelectorAll(t))).length)return null;i=n||1!=i.length?i:i[0]}else i=t;return Array.isArray(i)||(i.find=function(e,t){return u(e,i,t)},i.parents=function e(t){var n=null;var a=-1!=t.indexOf(".")?t.split(".")[1]:"";i.parentNode.nodeName.toLowerCase()==t.toLowerCase()||""!=a&&i.parentNode.classList.contains(a)||i.parentNode.id==t.replace("#","")?n=i.parentNode:"body"==i.parentNode.nodeName.toLowerCase()||n||(i=i.parentNode,n=e(t));return n},i.closest=function(e){return function(e,t){for(Element.prototype.matches||(Element.prototype.matches=Element.prototype.matchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector||Element.prototype.webkitMatchesSelector||function(e){for(var t=(this.document||this.ownerDocument).querySelectorAll(e),n=t.length;0<=--n&&t.item(n)!==this;);return-1<n});e&&e!==document;e=e.parentNode)if(e.matches(t))return e;return null}(i,e)},i.ascendants=function(e){return function(e){Element.prototype.matches||(Element.prototype.matches=Element.prototype.matchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector||Element.prototype.webkitMatchesSelector||function(e){for(var t=(this.document||this.ownerDocument).querySelectorAll(e),n=t.length;0<=--n&&t.item(n)!==this;);return-1<n});for(var t=[];e&&e!==document;e=e.parentNode)t.push(e);return t}(i)}),i}function p(e,t){"dev"==(window.debug||-1!=location.hostname.indexOf(".local")||-1!=location.hostname.indexOf("kotobee.com")||-1!=location.hostname.indexOf("localhost")||-1!=location.hostname.indexOf("localhost")||-1!=location.hostname.indexOf("192.168.")?"dev":"production")&&console[e].apply(null,t)}t=this.listener,Object.getOwnPropertyNames(t).filter(function(e){return"function"==typeof t[e]}).forEach(function(e){n[e]||(n[e]=n.listener[e].bind(n.listener))}),e&&this.setOptions(e),this.storageAPI=-1!=["session","cloud"].indexOf(this.storage)?sessionStorage:localStorage,kInteractive.setStorage(this.storageAPI)}kInteractive.setStorage(),kInteractive.container={preRender:function(e,t){var n,a,i=kInteractive.readData(e);i&&i.path&&(n=e.getAttribute("style"),a=kInteractive.cleanURL(i.path),e.setAttribute("style",n+' background-image:url("'+a+'");'))},postRender:function(e,t,n){kInteractive.readData(t)},action:function(e){},resize:function(e){var t=kInteractive.readData(e);t&&kInteractive.checkResponsiveFloat(t,e)}},kInteractive.equation={preRender:function(e,t){var n,a;kInteractive.isEditorMode()||"undefined"==typeof MathJax&&(MathJax={loader:{load:["input/asciimath","input/mml","input/tex","output/chtml","ui/menu"]}},n=document.createElement("script"),a="",a="undefined"==typeof isKotobee?"../../js/lib/mathjax/startup.js":kInteractive.c.join(bookPath,"EPUB/js/lib/mathjax/startup.js"),n.src=kInteractive.cleanURL(a),n.setAttribute("type","text/javascript"),document.head.appendChild(n))},postRender:function(root,item,index){var frag=root.createDocumentFragment(),eData=kInteractive.readData(item);if(eData){item.setAttribute("id","ki-equation-"+index),item.innerHTML="";var content=eData.content,img,path,path;!eData.img||"image"!=eData.rendermode&&supportsEs6()?"mathml"==eData.type?content="<math>"+content+"</math>":"asciimath"==eData.type?content="`"+content+"`":"tex"==eData.type&&(content="\\("+content+"\\)"):(img=document.createElement("img"),path="undefined"==typeof isKotobee?eData.img:ph.join(kInteractive.absoluteURL,eData.img),path=kInteractive.cleanURL(path),img.className="equationImg",img.src=path,eData.imgHeight?img.style.maxHeight=eData.imgHeight+"px":eData.imgWidth&&(img.style.maxWidth=eData.maxWidth+"px"),content=img.outerHTML);var tagName="inline"==eData.placement?"span":"div",container=document.createElement(tagName);container.setAttribute("id","ki-equation-"+index+"-container"),container.className="parsed container";try{container.innerHTML=content}catch(e){container.textContent=content}var styleContainer=document.createElement(tagName);eData.style&&kInteractive.c.addClass(styleContainer,eData.style),frag.appendChild(container),styleContainer.appendChild(frag),item.appendChild(styleContainer),kInteractive.isEditorMode()||MathJax&&MathJax.typeset&&MathJax.typeset()}function supportsEs6(){try{return eval('"use strict"; class foo {}'),1}catch(e){}}},action:function(e){kInteractive.readData(e)},resize:function(e){kInteractive.readData(e)}},kInteractive.gallery={preRender:function(e,t){var n=t.createDocumentFragment(),a=kInteractive.readData(e);if(a){e.innerHTML="";var i=(i=e.getAttribute("data-index"))||0;a.rtl&&kInteractive.c.addClass(e,"dirRtl");var r=document.createElement("div");r.className="imgMask";var o=document.createElement("div");o.className="images "+a.scale;var s=a.rtl?"right":"left";o.style[s]="0",r.appendChild(o);for(var c=0;c<a.imgs.length;c++){var l,d=document.createElement("div");d.className="imgContainer ki-btn",l="url"==a.imgs[c].type?a.imgs[c].url:"undefined"==typeof isKotobee?a.imgs[c].path:ph.join(kInteractive.absoluteURL,a.imgs[c].path),l=kInteractive.cleanURL(l),d.setAttribute("style","background-color:"+a.bgColor+";background-image:url('"+l+"')"),a.imgs[c].caption&&d.setAttribute("data-caption",a.imgs[c].caption),"undefined"!=typeof isKotobee||kInteractive.c.hasClass(e,"fullscreen")||d.addEventListener("click",kInteractive.actionEvent),o.appendChild(d)}var u=document.createElement("div");u.className="navBtns";var p=document.createElement("a");p.className="next btn ki-btn",u.appendChild(p);var v=document.createElement("a");v.className="prev btn ki-btn",u.appendChild(v),n.appendChild(u),"undefined"!=typeof isKotobee&&!kInteractive.c.hasClass(e,"fullscreen")||(p.addEventListener("click",kInteractive.actionEvent),v.addEventListener("click",kInteractive.actionEvent)),n.appendChild(r);var m=document.createElement("div");m.className="kFooter",a.footerShadow&&(m.className+=" shadow"),n.appendChild(m);var h=document.createElement("div");h.className="imgCaption";var g,f,k=document.createElement("div");k.className="inner",k.style.display="none",a.imgs[0]&&a.imgs[0].caption&&(k.innerHTML=a.imgs[0].caption,k.style.display=null),h.appendChild(k),m.appendChild(h),a.thumbnails&&((g=document.createElement("div")).className="kThumbs",a.thumbShowOnHover&&(g.className+=" showOnHover"),"small"==a.thumbSize?kInteractive.c.addClass(g,"small"):"large"==a.thumbSize?kInteractive.c.addClass(g,"large"):kInteractive.c.addClass(g,"medium"),m.appendChild(g)),kInteractive.c.hasClass(e,"fullscreen")&&((f=document.createElement("div")).className="fullscreenControls",f.innerHTML="<a class='closeBtn ki-btn'></a>",e.appendChild(f),f.children[0].addEventListener("click",kInteractive.actionEvent),document.addEventListener("keydown",kInteractive.gallery.detectKey));var b=document.createElement("div");a.style&&kInteractive.c.addClass(b,a.style),b.appendChild(n),e.appendChild(b),kInteractive.gallery.dimBtns(e),kInteractive.gallery.resize(e),this.showImage(e,{index:i,dontGoFullscreen:!0}),setTimeout(function(){kInteractive.gallery.showImage(e,{index:i,dontGoFullscreen:!0})},80)}},postRender:function(e,t,n){kInteractive.gallery.resize(t);var a=this.getState(t);this.dimBtns(t),this.showImage(t,{index:a.selectedIndex,dontGoFullscreen:!0})},action:function(e,t,n){var a;kInteractive.readData(e),e.getElementsByClassName("images")[0].getElementsByClassName("selected")[0];if(kInteractive.hasClass(t,"btn")){if(kInteractive.hasClass(t,"disable"))return n.stopPropagation();kInteractive.hasClass(t,"next")?kInteractive.gallery.showImage(e,{dir:"next"}):kInteractive.hasClass(t,"prev")&&kInteractive.gallery.showImage(e,{dir:"prev"})}else if(kInteractive.hasClass(t,"closeBtn"))kInteractive.gallery.exitFullscreen();else if(kInteractive.hasClass(t,"imgContainer")){if(kInteractive.readData(e).fullscreenMode)return kInteractive.gallery.goToFullscreen(e)}else{kInteractive.hasClass(t,"kMapItem")?(a=kInteractive.getChildIndex(t),kInteractive.gallery.goToThumb(e,{windowIndex:a,highlightThumb:!1})):kInteractive.hasClass(t,"kThumb")?(a=t.getAttribute("data-index"),kInteractive.gallery.showImage(e,{index:t.getAttribute("data-index")})):kInteractive.hasClass(t,"thumbNavBtn")&&(kInteractive.hasClass(t,"next")?kInteractive.gallery.goToThumb(e,{dir:"nextWindow",highlightThumb:!1}):kInteractive.hasClass(t,"prev")&&kInteractive.gallery.goToThumb(e,{dir:"prevWindow",highlightThumb:!1}))}kInteractive.gallery.dimBtns(e)},goToThumb:function(e,t){var n=kInteractive.readData(e);t=t||{};var a,i,r,o,s=this.getState(e),c=s.thumbWindows,l=s.activeWindow,d=s.currentIndex,u=s.selectedIndex,p=s.currentThumb,v=t.index;if(null==v&&(null!=t.windowIndex?v=t.windowIndex*c[0].children[0].children.length:("nextThumb"==t.dir&&(v=u+1),"prevThumb"==t.dir&&(v=u-1),"nextWindow"==t.dir&&(v=d+c[0].children[0].children.length),"prevWindow"==t.dir&&(v=d-c[0].children[0].children.length))),c.length)for(var m=c[0].getElementsByClassName("kThumb"),h=m.length;h--;)if(m[h].getAttribute("data-index")==v){a=m[h],i=a.parentNode;break}i&&(r=n.rtl?"right":"left",l!=i&&(d<v?(l.style[r]=-e.offsetWidth+"px",i.style[r]=e.offsetWidth/2+"px"):(l.style[r]=e.offsetWidth+"px",i.style[r]=-e.offsetWidth/2+"px"),i.style.display="none",setTimeout(function(){i.style.display="block",setTimeout(function(){i.style[r]=0},20)},20),(o=e.getElementsByClassName("kMapItem")).length&&(l&&kInteractive.c.removeClass(o[kInteractive.getChildIndex(l)],"active"),kInteractive.c.addClass(o[kInteractive.getChildIndex(i)],"active")),kInteractive.c.addClass(i,"active"),kInteractive.c.removeClass(l,"active")),t.highlightThumb&&(kInteractive.c.removeClass(p,"active"),kInteractive.c.addClass(a,"active")))},dimBtns:function(e){for(var t,n=e.getElementsByClassName("kThumbWindow"),a=n.length;a--;)if(kInteractive.c.hasClass(n[a],"active")){t=n[a];break}if(t)for(var i=e.getElementsByClassName("thumbNavBtn"),a=0;a<i.length;a++)kInteractive.c.hasClass(i[a],"next")?t.nextElementSibling?kInteractive.c.removeClass(i[a],"disable"):kInteractive.c.addClass(i[a],"disable"):kInteractive.c.hasClass(i[a],"prev")&&(t.previousElementSibling?kInteractive.c.removeClass(i[a],"disable"):kInteractive.c.addClass(i[a],"disable"));var r=e.getElementsByClassName("imgContainer");if(r.length)for(var o=kInteractive.c.hasClass(r[0],"selected"),s=kInteractive.c.hasClass(r[r.length-1],"selected"),c=e.getElementsByClassName("btn"),a=0;a<c.length;a++)kInteractive.c.hasClass(c[a],"next")?s?kInteractive.c.addClass(c[a],"disable"):kInteractive.c.removeClass(c[a],"disable"):kInteractive.c.hasClass(c[a],"prev")&&(o?kInteractive.c.addClass(c[a],"disable"):kInteractive.c.removeClass(c[a],"disable"))},getState:function(e){var t={};t.images=e.getElementsByClassName("images")[0],t.selectedImg=t.images.getElementsByClassName("selected"),t.imgContainer=e.getElementsByClassName("imgContainer"),t.thumbWindows=e.getElementsByClassName("kThumbWindows");for(var n=0;n<t.imgContainer.length;n++)if(kInteractive.c.hasClass(t.imgContainer[n],"selected")){t.selectedIndex=n;break}if(t.thumbWindows.length)for(var a=t.thumbWindows[0].getElementsByClassName("active"),n=0;n<a.length;n++)kInteractive.c.hasClass(a[n],"kThumbWindow")?(t.activeWindow=a[n],t.currentIndex=Number(t.activeWindow.children[0].getAttribute("data-index"))):t.currentThumb=a[n];return t},goToFullscreen:function(e){var t,n,a,i;kInteractive.frameIsOpen=!0,kInteractive.c.hasClass(e,"fullscreen")||((t=document.createElement("div")).innerHTML=e.outerHTML,n=t.children[0],document.body.appendChild(n),a=this.getState(e),i=e.getBoundingClientRect(),n.setAttribute("data-index",a.selectedIndex),n.setAttribute("id","kFullscreenGallery"),n.style.top=i.top+"px",n.style.left=i.left+"px",n.style.width=i.width+"px",n.style.height=i.height+"px",kInteractive.listenForSwiping(n),n.addEventListener("swipeleft",function(){kInteractive.gallery.showImage(n,{dir:"next"})}),n.addEventListener("swiperight",function(){kInteractive.gallery.showImage(n,{dir:"prev"})},!1),"undefined"==typeof isKotobee&&(n.style.position="fixed"),kInteractive.c.addClass(n,"fullscreen"),kInteractive.preRender(n,{singleElem:!0}),kInteractive.postRender(n,{singleElem:!0}),setTimeout(function(){kInteractive.c.addClass(n,"in")},20))},showImage:function(n,e){e=e||{};var a=kInteractive.readData(n),t=this.getState(n),i=t.images,r=t.selectedImg,o=t.imgContainer,s=e.index;null==s&&r.length&&(s=kInteractive.getChildIndex(r[0])+("next"==e.dir?1:-1)),s<0&&(s=0),null==s&&(s=0),r.length&&kInteractive.c.removeClass(r[0],"selected"),kInteractive.c.addClass(o[s],"selected");var c=a.rtl?"right":"left";o[s].style[c]=i.offsetWidth*s+"px",o[s].style.display="block",i.style[c]=-i.offsetWidth*s+"px",this.goToThumb(n,{index:s,highlightThumb:!0});var l=n.getElementsByClassName("imgCaption");if(l.length){var d=(l=l[0]).getElementsByClassName("inner");if(d.length){if((d=d[0]).style.display="none",o[s].hasAttribute("data-caption")&&(d.innerHTML=o[s].getAttribute("data-caption"),a.captionFontSize&&(d.style.fontSize=a.captionFontSize+"em"),a.captionColor&&(d.style.color=a.captionColor),d.style.display=null),!a.footerOverlap){var u=n.getElementsByClassName("imgMask");if(!u.length)return;u=u[0];var p=n.getElementsByClassName("kFooter")[0];u.style.bottom=p.offsetHeight+"px"}if(a.slideshowMode){if(!document.body.contains(n))return;var v=a.transitionTime;function m(){clearTimeout(kInteractive.gallery.timer);var e=n.getElementsByClassName("timerIcon")[0];e&&e.parentNode.removeChild(e)}function h(){var e,t;m(),a.slideshowTimer&&((e=n.getElementsByClassName("timerIcon")[0])||((e=document.createElement("div")).className="timerIcon",e.innerHTML='<svg><circle r="18" cx="20" cy="20"></circle></svg>',n.appendChild(e)),(t=e.getElementsByTagName("circle")[0])&&(t.style.animationDuration=v+"s")),kInteractive.gallery.timer=setTimeout(function(){var e=n.getElementsByClassName("next")[0];e&&(kInteractive.c.hasClass(e,"disable")?kInteractive.gallery.showImage(n,{index:0}):kInteractive.gallery.showImage(n,{dir:"next"}),kInteractive.gallery.dimBtns(n))},1e3*v)}v&&!isNaN(v)||(v=5),kInteractive.gallery.timer||(n.addEventListener("mouseover",m),n.addEventListener("mouseout",h)),h()}}}},detectKey:function(e){kInteractive.gallery.exitFullscreen()},exitFullscreen:function(){var e=document.getElementById("kFullscreenGallery");e&&(kInteractive.c.removeClass(e,"in"),e.style.opacity=0,setTimeout(function(){e.parentNode.removeChild(e),document.removeEventListener("keydown",kInteractive.gallery.detectKey)},1e3))},resize:function(e){var t=kInteractive.readData(e);if(t&&(kInteractive.checkResponsiveFloat(t,e),t.thumbnails)){var n=kInteractive.gallery.getState(e).selectedIndex;null==n&&(e.getAttribute("data-index"),n=n||0);var a=e.getElementsByClassName("kThumbs")[0];a.innerHTML="";var i=50;"small"==t.thumbSize?i=40:"large"==t.thumbSize&&(i=60);var r,o=Math.floor((e.offsetWidth-80)/i),s=document.createElement("div");s.className="kThumbWindows";for(var c,l,d,u,p=0;p<t.imgs.length;p++){t.imgs[p].thumbPath&&((c=document.createElement("a")).className="ki-btn kThumb"+(p==n?" active":""),c.setAttribute("data-index",p),l="undefined"==typeof isKotobee?t.imgs[p].thumbPath:ph.join(kInteractive.absoluteURL,t.imgs[p].thumbPath),l=kInteractive.cleanURL(l),c.setAttribute("style","background-image:url('"+l+"')"),"undefined"!=typeof isKotobee&&!kInteractive.c.hasClass(e,"fullscreen")||c.addEventListener("click",kInteractive.actionEvent),p%o==0&&((r=document.createElement("div")).className="kThumbWindow",t.rtl&&r.setAttribute("dir","rtl"),s.appendChild(r)),p==n&&kInteractive.c.addClass(r,"active"),r.appendChild(c))}if(a.appendChild(s),t.thumbMap){var v=document.createElement("div");v.className="kThumbMap",t.rtl&&v.setAttribute("dir","rtl");for(var m,p=0;p<t.imgs.length;p++){p%o==0&&((m=document.createElement("a")).className="ki-btn kMapItem"+(p==n?" active":""),v.appendChild(m),"undefined"!=typeof isKotobee&&!kInteractive.c.hasClass(e,"fullscreen")||m.addEventListener("click",kInteractive.actionEvent))}a.appendChild(v)}1<s.children.length&&((d=document.createElement("a")).className="thumbNavBtn next",a.appendChild(d),(u=document.createElement("a")).className="thumbNavBtn prev",a.appendChild(u),"undefined"!=typeof isKotobee&&!kInteractive.c.hasClass(e,"fullscreen")||(d.addEventListener("click",kInteractive.actionEvent),u.addEventListener("click",kInteractive.actionEvent)))}}},kInteractive.image={preRender:function(e,t){var n=kInteractive.readData(e);n&&(n.style&&kInteractive.c.addClass(e,n.style),n.popup&&n.splash&&(e.src=n.splash),e.parentNode&&kInteractive.hasClass(e.parentNode,"link")||"undefined"==typeof isKotobee&&e.addEventListener("click",kInteractive.actionEvent))},postRender:function(e,t,n){kInteractive.readData(t)},action:function(e,a,t){var n,i;function r(e,t){e.style.webkitAnimation=e.style.mozAnimation=e.style.animation=t}function o(e,t){e.style.webkitAnimationDuration=e.style.mozAnimationDuration=e.style.animationDuration=t}function s(e,t){e.style.webkitAnimationIterationCount=e.style.mozAnimationIterationCount=e.style.animationIterationCount=t}a.parentNode&&kInteractive.hasClass(a.parentNode,"kInteractive")&&kInteractive.hasClass(a.parentNode,"link")?kInteractive.action(a.parentNode,event):!(n=kInteractive.readData(a))||"none"!=(i=n.behavior)&&(r(a,"none"),o(a,"none"),s(a,"none"),setTimeout(function(){"wiggle"==i?(r(a,"wiggle"),o(a,"0.5s"),s(a,"1")):"jump"==i?(r(a,"jump"),o(a,"0.7s"),s(a,"1")):"scale"==i&&(r(a,"scale"),o(a,"0.5s"),s(a,"1"));var n=function(){var e,t=document.createElement("fakeelement"),n={animation:"animationend",OAnimation:"oAnimationEnd",MozAnimation:"animationend",WebkitAnimation:"webkitAnimationEnd"};for(e in n)if(void 0!==t.style[e])return n[e]}();n&&a.addEventListener(n,function e(t){r(a,"none"),o(a,"none"),s(a,"none"),a.removeEventListener(n,e)})}),kInteractive.tinCan({verb:"clicked",activity:i+"image: "+a.getAttribute("src")}))},resize:function(e){var t=kInteractive.readData(e);t&&kInteractive.checkResponsiveFloat(t,e)}},kInteractive.link={preRender:function(e,t){var n,a,i=kInteractive.readData(e);i&&(kInteractive.isEditorMode()||("#"==e.getAttribute("href")&&e.setAttribute("href",""),"div"==e.nodeName.toLowerCase()&&(n=document.createElement("a"),a=e.getAttribute("href")?e.getAttribute("href"):i.href,e.getAttribute("data-href")&&(a=e.getAttribute("data-href")),a=a||"",n.setAttribute("href",a),n.setAttribute("target",i.target?i.target:""),n.setAttribute("style",e.getAttribute("style")),n.setAttribute("data-kotobee",e.getAttribute("data-kotobee")),n.setAttribute("data",e.getAttribute("data-kotobee")),n.className=e.className,e.id?n.id=e.id:i.id&&(n.id=i.id),kInteractive.c.addClass(n,"btn"),n.style.opacity=Number(i.transparency)/100,e.parentNode.replaceChild(n,e),e=n),"undefined"==typeof isKotobee&&("popup"!=i.type&&"audio"!=i.type&&"action"!=i.type||e.setAttribute("onclick","return false;"),e.addEventListener("click",kInteractive.actionEvent))))},postRender:function(e,t,n){},action:function(t,e,n){var a=kInteractive.readData(e);if(a){if("popup"==a.type){var i,r=(r="<div class='kbAlertScroll'>"+a.msg+"</div>").replace(/src="(.*?)"/g,function(){var e=arguments[1];return"undefined"!=typeof isKotobee&&(e=ph.join(kInteractive.absoluteURL,e)),'src="'+e+'"'});"image"==a.popupMode&&(i=a.popupImg,"undefined"!=typeof isKotobee&&(i=a.relToRoot?ph.join(bookPath,i):ph.join(kInteractive.absoluteURL,i)),r="<img src='"+(i=kInteractive.cleanURL(i))+"'/>"),kInteractive.alert({content:r}),setTimeout(function(){var e;kInteractive.scorm&&((e={}).id=kInteractive.getScormId("popup",a.msg),e.description="Shown popup message: "+a.msg,e.type="other",e.learnerResponses="Shown",e.timestamp=new Date,kInteractive.scorm.setInteractions([e])),kInteractive.events&&kInteractive.events.add({action:"popup",param:a.msg,elem:t,data:a})})}else if("action"==a.type){var o,s=a.asset,c=a.action;if(s.remote)return void("widget"==s.type&&((o=document.createElement("div")).className="kInteractive widget kbHidden",o.setAttribute("data-kotobee",s.data),o.setAttribute("data",s.data),document.body.appendChild(o),kInteractive.trigger("click",o)));for(var l=document.getElementsByClassName(s.classname),d=0;d<l.length;d++)if(kInteractive.hasClass(l[d],"kInteractive")&&kInteractive.hasClass(l[d],s.type))if("activate"==c)if("gallery"==s.type){var u=l[d].getElementsByClassName("next");kInteractive.trigger("click",u[0])}else if("questions"==s.type){for(var p=l[d].getElementsByClassName("ki-btn"),v=0;v<p.length;v++)if("submit"==p[v].type){kInteractive.trigger("click",p[v]);break}}else kInteractive.trigger("click",l[d]);else"visibility"==c&&kInteractive.c.toggleClass(l[d],"kbHidden")}else if("audio"==a.type){kInteractive.stopCurrentMedia();var m,h=document.createElement("audio"),g=a.src;function f(){m||(kInteractive.c.addClass(y,"hide"),m=!0)}"file"==a.audioType&&(g="undefined"==typeof isKotobee?a.audio:ph.join(kInteractive.absoluteURL,a.audio)),g=kInteractive.cleanURL(g),h.setAttribute("src",g);var k=document.createElement("div");k.className="kiAudioLoader",k.style.transform=k.style.webkitTransform=k.style.mozTransform="scale(1)";var b,y=document.createElement("div");y.className="kiAudioSpinner",k.appendChild(y),e.parentNode.insertBefore(k,e),h.onplaying=f,setTimeout(f,5e3),h.play(),kInteractive.currentAudio=h,kInteractive.tinCan({verb:"played",activity:"Audio: "+g}),kInteractive.scorm&&((b={}).id=kInteractive.getScormId(e.textContent,g),b.description="Played audio link: "+g,b.type="other",b.learnerResponses="Played",b.objective=a.options?a.options.objective:null,b.timestamp=new Date,kInteractive.scorm.setInteractions([b])),kInteractive.events&&kInteractive.events.add({action:"audioLinkClicked",param:g,elem:t,data:a})}else if("attachment"==a.type){var I=a.file;if("undefined"!=typeof isKotobee&&(I=a.relToRoot?ph.join(bookPath,a.file):ph.join(kInteractive.absoluteURL,a.file)),"undefined"!=typeof kotobee&&native)return void angular.element(document.body).scope().getService("crdv").mobileDownload(I);var C=document.createElement("a");document.body.appendChild(C),C.setAttribute("style","display: none"),C.href=kInteractive.cleanURL(I),C.target="_blank",C.download=a.file.split("/")[a.file.split("/").length-1],C.setAttribute("onclick","");var w=document.createEvent("MouseEvents");w.initMouseEvent("click"),C.dispatchEvent(w),C.remove()}return!1}}},kInteractive.lipsync={preRender:function(e,t){},postRender:function(e,t,n){var a,i=e.createDocumentFragment(),r=kInteractive.readData(t);t.setAttribute("id",a="ki-lipsync-"+n),t.innerHTML="";var o,s,c,l=document.createElement("div");l.className="container",r.sync&&((o=document.createElement("div")).className="audioContainer",(s=document.createElement("a")).className="playBtn ki-btn",s.appendChild(document.createElement("span")),"undefined"==typeof isKotobee&&s.addEventListener("click",kInteractive.actionEvent),o.appendChild(s),(c=document.createElement("div")).setAttribute("id",a+"-container"),c.className="playerContainer",o.appendChild(c),r.interaction.playBtn?r.interaction.btnAlignment&&kInteractive.c.addClass(o,r.interaction.btnAlignment):o.style.display="none",l.appendChild(o));var d=document.createElement("div");d.className="contentContainer",d.innerHTML=function(e){document.createElement("div");var t=/(>)([^<\n]*?[^<]+?)(<[^\/])/g;e=e.replace(/([< \/][^>]*?>)((\s*[^<\s]+\s+?)+)([^<\s]+\s*)(<)/g,function(e,t){if(0<=t.indexOf('class="parsed"'))return e;if(0==t.indexOf("<pre"))return e;if(0==t.indexOf("<code"))return e;if(0==t.indexOf("<script"))return e;var n=arguments[2].split(" ");""==n[n.length-1]&&n.splice(-1,1),n.push(arguments[4]);for(var a="",i=0;i<n.length;i++){var r=i==n.length-1?"":" ";a+="<span>"+n[i]+r+"</span>"}return t+a+"<"}),e=e.replace(t,function(e,t){return arguments[2].trim()?t+("<span>"+arguments[2]+"</span>")+arguments[3]:e});return e=(e=e.replace(/(<a [\s\S]*?)(>)/g,'$1 onclick="return false;" $2')).replace(/&nbsp;/g,"&#160;")}(r.content),l.appendChild(d);var u=document.createElement("div");r.style&&kInteractive.c.addClass(u,r.style),i.appendChild(l),u.appendChild(i),t.appendChild(u);var p=document.createElement("style");p.className="system parsed";var v="";v+="#"+a+" .lipSyncCurrentWord {",v+="color:"+r.layout.color+";",r.layout.underline&&(v+="text-decoration:underline;"),r.layout.scale&&1<Number(r.layout.scale)&&(v+="transform:scale("+r.layout.scale+");",v+="padding:0 3px;",v+="display:inline-block;"),v+="}",p.innerHTML=v,t.appendChild(p),r.autoplay&&kInteractive.action(t)},action:function(r,e){kInteractive.stopCurrentMedia();var t,n,a,i,o,s,c=kInteractive.readData(r);c&&(c.audioType||(c.audioType=c.type),r.getAttribute("id"),r.getElementsByClassName("playBtn")[0].className="playBtn ki-btn hide",t=c.src,"file"==c.audioType&&(t="undefined"==typeof isKotobee?c.audio:c.relToRoot?ph.join(bookPath,c.audio):ph.join(kInteractive.absoluteURL,c.audio)),kInteractive.scorm&&((n={}).id=kInteractive.getScormId(r.getAttribute("id"),t),n.description="Played audio: "+t,n.type="other",n.learnerResponses="Played",n.objective=c.options?c.options.objective:null,n.timestamp=new Date,kInteractive.scorm.setInteractions([n])),kInteractive.events&&kInteractive.events.add({action:"audioPlayed",param:t,elem:r,data:c}),(a=document.createElement("audio")).setAttribute("controls","true"),a.setAttribute("autoplay","true"),a.setAttribute("data-tap-disabled","false"),(i=document.createElement("source")).src=kInteractive.cleanURL(t),a.appendChild(i),a.appendChild(document.createTextNode("Your browser does not support the audio element")),a.className="ki-noHighlight",a.oncanplay=function(){kInteractive.currentAudio==r&&kInteractive.tinCan({verb:"played",activity:"Audio: "+t})},a.ontimeupdate=function(e){!function(e){var t,n=c;for(var a=n.sync.length;a--;)if(e>=n.sync[a]){t=a;break}if(null==t)return;s!=t&&(null!=s&&o[s]&&kInteractive.c.removeClass(o[s],"lipSyncCurrentWord"),o[t]&&kInteractive.c.addClass(o[t],"lipSyncCurrentWord"),s=t)}(a.currentTime)},c.interaction.nextAuto&&(a.onended=function(e){var t,n,a,i=[].slice.call(document.getElementsByClassName("lipsync"));i.length&&(t=i.indexOf(r),!(n=i[t+1])||(a=n.getElementsByClassName("playBtn")[0])&&a.click())}),r.getElementsByClassName("playerContainer")[0].appendChild(a),a.play(),kInteractive.currentAudio=r,kInteractive.c.addClass(kInteractive.currentAudio,"playing"),o=function(){for(var e=[],t=r.getElementsByClassName("contentContainer")[0],n=t.getElementsByClassName("lipSyncCurrentWord"),a=n.length;a--;)hasClass(n[a],"lipSyncCurrentWord")&&removeClass(n[a],"lipSyncCurrentWord");for(;t.children&&t.children.length;)t=t.children[0];e.push(t);for(;t;)if(t.nextSibling){for(t=t.nextSibling;t.children&&t.children.length;)t=t.children[0];if(3==t.nodeType)continue;if(""==t.textContent.trim())continue;e.push(t)}else if("contentContainer"==(t=t.parentNode).className)break;return e}(),s=0,kInteractive.c.addClass(o[s],"lipSyncCurrentWord"))}},kInteractive.questions={preRender:function(e,t,n,a){a=a||{};var i=t.createDocumentFragment(),r=kInteractive.readData(e);if(r){var o,s,c=(c=r.dict)||{},l=(l=r.userDict)||c;if(r.options||(r.options={}),e.id||(e.id="ki-qs-"+n+"-"+Math.ceil(1e3*Math.random())),e.innerHTML="",r.layout&&r.layout.popup&&!kInteractive.hasClass(e,"inpopup")){var d=document.createElement("img");d.className="clickable";var u=r.layout.popupImg,u=kInteractive.cleanURL(u);return d.src=u,i.appendChild(d),"undefined"==typeof isKotobee&&d.addEventListener("click",kInteractive.actionEvent),void e.appendChild(i)}if(r.options.randomize){for(var p=0;p<r.q.length;p++)r.q[p].origin=p;for(var v,m,h=r.q.length;0!==h;)m=Math.floor(Math.random()*(r.q.length-1e-4)),h--,v=r.q[h],r.q[h]=r.q[m],r.q[m]=v;if("slides"==r.options.displayMode){for(var g="",p=0;p<r.q.length;p++)g+=(g?",":"")+r.q[p].origin;e.setAttribute("data-o",g)}}r.options.questionsDisplayed&&(o=Number(r.options.questionsDisplayed),r.q=r.q.slice(0,o)),r.title&&((s=document.createElement("span")).innerHTML="<h4>"+kInteractive.escapeHtml(r.title)+"</h4>",kInteractive.c.hasClass(e,"parsed")&&kInteractive.c.addClass(s.children[0],"parsed"),i.appendChild(s));var f=null;r.options&&r.options.emailForm&&(f=this.f.createEmailForm(l));var k=null;"none"!=r.action&&(k=this.f.createSubmitBtn(l,e));var b=null,y=null,I=null;r.options&&r.options.clearAnswers&&(b=this.f.createClearBtn(l)),r.options&&r.options.addToNotebook&&(I=this.f.createAddToNotebookBtn(l)),r.options&&r.options.preserveAnswers&&!r.options.preserveAnswers.auto&&(y=this.f.createPreserveBtn(l,e,r.layout.persist||{}));var C=this.f.createSeparator(),w=document.createElement("div");w.className="qContainer";var E=document.createElement("div");if(E.className="btnContainer","slides"==r.options.displayMode)e.qIndex=0,w.appendChild(kInteractive.questions.f.createQElem(r,{questionNo:1,index:0,elem:e,rendered:!1})),kInteractive.isEditorMode()||r.options.dateRangeEnabled&&!a.skipDateValidation&&(w.style.display="none",E.style.display="none"),i.appendChild(w),i.appendChild(E),kInteractive.questions.f.showNavBtns(0,i,{isFrag:!0,userDict:l,qsData:r});else{for(var x=0,p=0;p<r.q.length;p++)"sp"!=r.q[p].type&&x++,w.appendChild(kInteractive.questions.f.createQElem(r,{questionNo:x,index:p,elem:e,rendered:!1}));kInteractive.isEditorMode()||r.options.dateRangeEnabled&&!a.skipDateValidation&&(w.style.display="none",E.style.display="none"),i.appendChild(w),y&&E.appendChild(y),f&&E.appendChild(f),k&&E.appendChild(k),I&&E.appendChild(I),b&&E.appendChild(b),C&&i.appendChild(C),i.appendChild(E)}r.options.rtl?(e.setAttribute("dir","rtl"),kInteractive.c.addClass(e,"rtl")):e.setAttribute("dir","ltr");var N=document.createElement("div");r.style&&kInteractive.c.addClass(N,r.style),N.appendChild(i),"undefined"==typeof isKotobee&&kInteractive.c.addClass(N,"parsed"),e.appendChild(N)}},postRender:function(s,u,e,c){var l=kInteractive.readData(u);if(l){c=c||{};var t=(t=l.dict)||{};x=(x=l.userDict)||t;for(var n=u.getElementsByClassName("ques"),a=u.getElementsByClassName("explanation"),i=a.length;i--;)a[i].parentNode.removeChild(a[i]);for(var r,o,d=u.getElementsByClassName("reference"),i=d.length;i--;)d[i].parentNode.removeChild(d[i]);if(l.options.scoreHistory&&kInteractive.isKotobeeUser()&&(A=(config.kotobee.liburl?config.kotobee.liburl:"https://www.kotobee.com/")+"admin/library/questions/get",r=angular.element(document.body).scope(),o={env:"library"==config.kotobee.mode?"library":"book",cloudid:config.kotobee.cloudid,email:r.data.user.email,code:r.data.user.code,pwd:r.data.user.pwd,qsxid:u.id,mode:"submissions"},"library"==config.kotobee.mode&&r.data.book&&(o.bid=r.data.book.id),kInteractive.questions.f.sendPost(A,o,function(e){e.target;var t=e.target;if(4===t.readyState&&200===t.status){var n=JSON.parse(t.response);if(n&&n.scores&&n.scores.length){for(var a=u.getElementsByClassName("scoreHistory"),i=a.length;i--;)a[0].parentNode.removeChild(a[0]);var r=document.createElement("div");r.className="scoreHistory";var o=document.createElement("h5");o.textContent=x.scoreHistoryLabel?x.scoreHistoryLabel:"Score history",r.appendChild(o);for(i=0;i<n.scores.length;i++){var s=n.scores[i],c=kInteractive.questions.f.phpDateToJs(s.date),l=(x.scoreLabel?x.scoreLabel:"Score")+": "+s.fullscore,d=document.createElement("div");d.className="scoreItem",d.innerHTML="<div><span class='date'>"+c.toLocaleString()+"</span> <span class='score'>"+l+"</span></div>",r.appendChild(d)}u.appendChild(r)}}})),kInteractive.isEditorMode()||!l.options.dateRangeEnabled||c.skipDateValidation){if(!kInteractive.isEditorMode()){var p=kInteractive.getBookID()+"-"+u.id;u.clock=kInteractive.getClock(p),u.clock.pause(),u.clock.cb=function(e){var t,n,a,i,r;l.options.visibilityDuration&&(t=l.options.visibilityDuration-e,l.options.visibilityDurationTimer&&((n=u.getElementsByClassName("qTimer")[0])||((n=document.createElement("div")).className="qTimer",u.appendChild(n)),i=t-60*(a=Math.floor(t/60)),n.textContent=("0"+a).substr(-2)+":"+("0"+i).substr(-2)),t<0&&(!l.options.visibilityDurationAutoSubmit||(r=u.getElementsByClassName("questions-submit")[0])&&r.click(),kInteractive.questions.f.replaceWithError(u,{msg:x.questionsTimeUp?x.questionsTimeUp:x.questionsInactive,className:"qInactive"})))};try{kInteractive.observe(u,function(){u.clock&&u.clock.resume()},function(){u.clock&&u.clock.pause()})}catch(e){}}kInteractive.isEditorMode()||l.options.disableAfterAttempts&&kInteractive.isKotobeeUser()&&kInteractive.questions.f.getAttempts(u,function(e){e&&e>=l.options.disableAfterAttempts&&kInteractive.questions.f.replaceWithError(u,{msg:x.questionsInactive,className:"qInactive"})});for(var v,m,h,g,f=0;f<n.length;f++){var k=n[f].getElementsByClassName("ans"),b=(k.length,f);l.options.randomize&&(b="slides"==l.options.displayMode&&u.originArray?u.originArray[f]:Number(n[f].getAttribute("data-o"))),document[u.id]&&document[u.id].preserve&&document[u.id].q&&document[u.id].q[b]&&kInteractive.questions.f.showExp(l.userDict,n[f],l.q[b],l.options);for(var y,i=0;i<k.length;i++)if(kInteractive.c.removeClass(k[i].parentNode,"correct"),0<=k[i].getAttribute("id").indexOf("ki-tf-"))document[u.id]&&document[u.id].preserve&&document[u.id].q&&document[u.id].q[b]&&(document[u.id].q[b][i]?k[i].setAttribute("checked",!0):k[i].removeAttribute("checked"),l.options&&l.options.highlightCorrect&&(0==i&&l.q[b].a||1==i&&!l.q[b].a)&&kInteractive.c.addClass(k[i].parentNode,"correct"));else if(0<=k[i].getAttribute("id").indexOf("ki-dd-")){if(document[u.id]&&document[u.id].preserve&&document[u.id].q&&document[u.id].q[b])for(var I=0;I<document[u.id].q[b].length;I++){var C=document[u.id].q[b][I];try{for(var w=document.getElementById(C.id),E=w;!kInteractive.c.hasClass(E,"ques");)E=E.parentNode;E.getElementsByClassName("categoryContainer")[0].children[C.category].appendChild(w)}catch(e){}}}else 0<=k[i].getAttribute("id").indexOf("ki-sa-")?document[u.id]&&document[u.id].preserve&&document[u.id].q&&document[u.id].q[b]&&(k[i].removeAttribute("value"),document[u.id].q[b]&&k[i].setAttribute("value",document[u.id].q[b])):document[u.id]&&document[u.id].preserve&&document[u.id].q&&document[u.id].q[b]&&(document[u.id].q[b][i]?k[i].setAttribute("checked",!0):k[i].removeAttribute("checked"),l.options&&l.options.highlightCorrect&&l.q[b].c[i].a&&kInteractive.c.addClass(k[i].parentNode,"correct"));!document[u.id]||(y=u.getElementsByClassName("questions-preserve")).length&&(document[u.id].preserve?y[0].setAttribute("checked",!0):y[0].removeAttribute("checked"))}kInteractive.isEditorMode()||kInteractive.isPreview()||(v="persist-"+kInteractive.getBookID()+"-"+u.id,m="true"==sessionStorage.getItem(v)||l.options.preserveAnswers.auto,(h=u.querySelector("input.questions-preserve"))&&(m?h.setAttribute("checked",!0):h.removeAttribute("checked")),kInteractive.helpers&&kInteractive.helpers.persister&&(kInteractive.helpers.persister.instances[u.id]?kInteractive.helpers.persister.instances[u.id]instanceof kPersister&&kInteractive.helpers.persister.instances[u.id].restore():(g={storage:l.options.preserveAnswers.option,enabled:m,root:u,silentMode:l.options.preserveAnswers.auto,appendDataMode:"slides"==l.options.displayMode,delegateEvents:!1,messages:{save:x.save?x.save:"Save",unsaved:x.unsaved?x.unsaved:"You have unsaved changes",saving:x.saving?x.saving:"Saving ..",saved:x.saved?x.saved:"Changes has been saved!",dismiss:x.dismiss?x.dismiss:"Dismiss",lastSaved:x.lastSaved?x.lastSaved:"Last saved on",longDataError:x.longDataError?x.longDataError:"Some answers not saved (marked in red)"}},Object.keys(l.options.preserveAnswers).some(function(e){return-1!=e.indexOf("design")})&&(g.design=function(e){var t={};e.designPlacement&&(t.placement=e.designPlacement);(e.designUnsavedColor||e.designUnsavedBackground)&&(t.unsaved={});e.designUnsavedColor&&(t.unsaved.color=e.designUnsavedColor);e.designUnsavedBackground&&(t.unsaved.background=e.designUnsavedBackground);(e.designSuccessColor||e.designSuccessBackground)&&(t.success={});e.designSuccessColor&&(t.success.color=e.designSuccessColor);e.designSuccessBackground&&(t.success.background=e.designSuccessBackground);return t}(l.options.preserveAnswers)),kInteractive.helpers.persister.addInstance(u.id,g),kInteractive.helpers.persister.instances[u.id].on("persister.ready",function(e){e.target.enabled&&e.target.restore()}),kInteractive.helpers.persister.instances[u.id].on("persister.restored",function(e){}),kInteractive.helpers.persister.instances[u.id].on("persister.error",function(e){}),kInteractive.helpers.persister.instances[u.id].on("persister.saved",function(e){}),kInteractive.helpers.persister.instances[u.id].on("persister.unsaved",function(e){}),kInteractive.helpers.persister.instances[u.id].on("persister.pushed",function(e){}),kInteractive.helpers.persister.instances[u.id].init())))}else{var x=l.userDict,N="https://www.kotobee.com/";"undefined"!=typeof isKotobee&&(N=config.kotobee.liburl?config.kotobee.liburl:"https://www.kotobee.com/");var A=N+"timestamp";kInteractive.questions.f.sendPost(A,{timezone:kInteractive.getTimezone()},function(e){var t,n,a,i,r,o=e.target;4===o.readyState&&200===o.status&&(t=o.responseText,(n=kInteractive.questions.f.checkTimeValidity(t,l.options.dateRange)).valid?(a=u.getElementsByClassName("qContainer")[0],i=u.getElementsByClassName("btnContainer")[0],a&&(a.style.display="block"),i&&(i.style.display="block"),r=Object.assign({},c,{skipDateValidation:!0}),kInteractive.questions.postRender(s,u,I,r)):n.expired?kInteractive.questions.f.replaceWithError(u,{msg:x.questionsExpired,className:"qExpired"}):n.early&&kInteractive.questions.f.replaceWithError(u,{msg:x.questionsNotStarted,className:"qEarly"}))})}}},action:function(k,b){var y=kInteractive.readData(k);if(y){var e,I=y.dict,C=y.userDict,I=I||{},C=C||I;if(y.layout&&y.layout.popup&&!kInteractive.hasClass(k,"inpopup")&&"img"==b.nodeName.toLowerCase()){var t=document.getElementById("epubContent");t&&k.setAttribute("data-loc",function(e,t){for(var n="";e!=t;){n=function(e){for(var t=0;e=e.previousSibling;)"#text"!=e.nodeName&&8!=e.nodeType&&t++;return t}(e)+"."+n;e=e.parentNode}return""==n?"":n.substr(0,n.length-1)}(k,t));var f=document.createElement("iframe");f.setAttribute("nwdisable","true"),f.setAttribute("scrolling","yes");var w=document.createElement("div");return w.className="scroller",w.appendChild(f),y.cb=function(e){var t,n=document.createElement("head");if("undefined"==typeof isKotobee){for(var a=document.getElementsByTagName("link"),i=0;i<a.length;i++){var r=a[i];if(0<=r.getAttribute("href").indexOf("base.css")){var o=r.getAttribute("href").split("/");delete o[o.length-1],t=o.join("/");break}}if(!t)return}var s="undefined"==typeof isKotobee?kInteractive.c.removeHash(window.location.href):kInteractive.absoluteURL,s=kInteractive.cleanURL(kInteractive.c.removeFilename(s)+"/");"undefined"!=typeof isKotobee&&(kInteractive.isAbsolutePath(s)||(s=kInteractive.cleanURL(kInteractive.c.removeFilename(kInteractive.c.removeHash(window.location.href))+"/"+s)));for(var c=s.split("EPUB/xhtml"),l=c[c.length-1].split("/").length-1,d="",u=0;u<l;u++)d+="../";var p,n="<head>";n+='<base href="'+s+'"/>',n+='<link rel="stylesheet" type="text/css" href="'+(d+"css/base.css")+'" />',n+='<link rel="stylesheet" type="text/css" href="'+(d+"css/global.css")+'" />',n+='<link rel="stylesheet" type="text/css" href="'+(d+"css/kotobeeInteractive.css")+'" />',n+='<script type="text/javascript" src="'+(d+"js/kotobeeInteractive.js")+'"><\/script>',n+='<script type="text/javascript" src="'+(d+"js/global.js")+'"><\/script>',"undefined"!=typeof isKotobee&&((p=clone(config)).kotobee.email=angular.element(document.body).scope().data.user.email,n+='<script type="text/javascript">var config='+JSON.stringify(p)+";<\/script>"),n+='<meta name="viewport" content="viewport-fit=cover,initial-scale=1,maximum-scale=1,minimum-scale=1,user-scalable=no,width=device-width">',n+='<meta charset="utf-8" />',n+="</head>";var v=k.outerHTML;v=(v=(v=(v=(v=(v=v.replace("<img",'<img style="display:none"')).replace(/([;\s]*?height:)(.*)?;/i,"$1auto")).replace(/([;\s]*?width:)(.*)?;/i,"$1auto")).replace(/([;\s]*?top:)(.*)?;/i,"$1auto")).replace(/([;\s]*?left:)(.*)?;/i,"$1auto")).replace('class="','class="inpopup ');var m="<html>"+n+"<body>"+(v+='<div class="vSpace20"></div>')+"</body></html>";f.setAttribute("srcdoc",m);var h="javascript: window.frameElement.getAttribute('srcdoc');";function g(){var e;"undefined"!=typeof isKotobee&&((e={}).root=f.contentDocument.documentElement.ownerDocument.body,e.rootIndex=k.getAttribute("data-loc"),angular.element(document.body).scope().getNotebookShortcut(e,function(){}))}f.setAttribute("src",h),f.contentWindow&&(f.contentWindow.location=h),e.appendChild(w),f.addEventListener?f.addEventListener("load",g,!0):f.attachEvent&&f.attachEvent("onload",g)},y.closed=function(){},y.pos=y.layout.pos,kInteractive.openFrame(y),kInteractive.tinCan({verb:"opened",activity:"Questions: "+y.title}),void setTimeout(function(){var e;kInteractive.scorm&&((e={}).id=kInteractive.getScormId("question",y.title),e.description="Opened question popup: "+y.title,e.learnerResponses="Opened",e.type="other",e.timestamp=new Date,kInteractive.scorm.setInteractions([e])),kInteractive.events&&kInteractive.events.add({action:"questionPopup",param:y.title,elem:k,data:y})},800)}if(kInteractive.hasClass(b,"ki-inputbox"))kInteractive.helpers.persister.instances[k.id]&&kInteractive.helpers.persister.instances[k.id].handleChange();else if(kInteractive.hasClass(b,"ki-textfield"))kInteractive.helpers.persister.instances[k.id]&&kInteractive.helpers.persister.instances[k.id].handleChange();else if(kInteractive.hasClass(b,"ki-draggable"))kInteractive.helpers.persister.instances[k.id]&&kInteractive.helpers.persister.instances[k.id].handleChange();else if(!kInteractive.hasClass(b,"kInteractive")){if(kInteractive.hasClass(b,"questions-next")){var n=k.qIndex?k.qIndex:0;if(k.questions||(k.questions=[]),++n<0)return;if(n>=y.q.length)return;var a=(i=k.getElementsByClassName("qContainer")[0]).children[0];if(y.options.strictNav)if(!(O=kInteractive.questions.f.assess(a,y.q[n-1],y)).correct)return void kInteractive.questions.f.showExp(y.userDict,a,y.q[n-1],y.options);return k.questions[n-1]||(k.questions[n-1]=a),k.questions[n]||(k.questions[n]=kInteractive.questions.f.createQElem(y,{questionNo:n+1,index:n,elem:k,rendered:!0})),i.innerHTML="",i.appendChild(k.questions[n]),kInteractive.questions.f.showNavBtns(n,k,{userDict:C,qsData:y}),k.qIndex=n,void kInteractive.helpers.persister.instances[k.id].restore()}if(kInteractive.hasClass(b,"questions-prev")){if(!k.questions||!k.questions.length)return;var i,n=k.qIndex?k.qIndex:0;return n--,(i=k.getElementsByClassName("qContainer")[0]).innerHTML="",i.appendChild(k.questions[n]),kInteractive.questions.f.showNavBtns(n,k,{userDict:C,qsData:y}),k.qIndex=n,void kInteractive.helpers.persister.instances[k.id].restore()}if(kInteractive.hasClass(b,"questions-clear")){for(var r=k.getElementsByClassName("ans"),o=[],s=0;s<r.length;s++)r[s].checked=!1,r[s].value=null,kInteractive.c.removeClass(r[s].parentNode,"correct"),kInteractive.c.removeClass(r[s],"incorrect"),kInteractive.c.hasClass(r[s].parentNode,"dCategory")&&o.push(r[s]);for(s=0;s<o.length;s++)o[s].parentNode.parentNode.parentNode.getElementsByClassName("ki-dd-answers")[0].appendChild(o[s]);for(var c=k.getElementsByClassName("explanation"),s=c.length;s--;)c[s].parentNode.removeChild(c[s]);for(var l=k.getElementsByClassName("reference"),s=l.length;s--;)l[s].parentNode.removeChild(l[s]);return document[k.id]=null,void(kInteractive.helpers.persister.instances&&kInteractive.helpers.persister.instances[k.id].clear())}if(kInteractive.hasClass(b,"questions-notebook")){var d=y.layout&&y.layout.popup&&kInteractive.hasClass(k,"inpopup");if("undefined"==typeof isKotobee&&!d)return void kInteractive.alert({content:I.kotobeeOnly,title:C.sorry?C.sorry:"Sorry"});var u,p={elem:k};if("undefined"!=typeof isKotobee)angular.element(document.body).scope();else{try{window.parent.getGlobal()}catch(e){for(var v=k.getElementsByClassName("ans"),m=0;m<v.length;m++)v[m].checked?v[m].setAttribute("checked",""):v[m].removeAttribute("checked");kInteractive.c.addClass(k,"parsed"),p.elem=k.outerHTML;var h={msg:C.savedToNotebook?C.savedToNotebook:"Saved to notebook!",nOb:p};return window.parent.postMessage("addtonotebook:"+encodeURIComponent(JSON.stringify(h)),"*"),void kInteractive.alert({content:C.savedToNotebook?C.savedToNotebook:"Saved to notebook!"})}p.rootIndex=k.getAttribute("data-loc"),p.root=k}return window.compInstances&&window.compInstances.global&&(u=window.compInstances.global),kInteractive.hasClass(k,"inpopup")&&(u=window.top.compInstances.global),void(u&&(kInteractive.c.addClass(b,"busy"),u.questionsAddToNotebookShortcut(p,function(){kInteractive.c.removeClass(b,"busy"),kInteractive.alert({content:C.savedToNotebook?C.savedToNotebook:"Saved to notebook!"})})))}if(kInteractive.hasClass(b,"questions-preserve"))return document[k.id]||(document[k.id]={}),document[k.id].preserve=b.checked,void(kInteractive.helpers.persister.instances&&(e="persist-"+kInteractive.getBookID()+"-"+k.id,b.checked?(sessionStorage.setItem(e,"true"),kInteractive.helpers.persister.instances[k.id].enable(y.options.preserveAnswers.auto)):(sessionStorage.setItem(e,"false"),kInteractive.helpers.persister.instances[k.id].disable())));if(kInteractive.hasClass(b,"questions-submit")){var E="Untitled questions",g=k.getElementsByTagName("h4");g.length&&(E=g[0].textContent||g[0].data);for(var x=0,N="",A=0,L=0,q=[],T="slides"==y.options.displayMode?k.questions.slice(0):k.getElementsByClassName("ques"),D=0,s=0;s<T.length;s++)if(!kInteractive.hasClass(T[s],"sp")){D++;var B=((r=T[s].getElementsByClassName("ans")).length,0),S=y.q[s],R=kInteractive.questions.f.getItemOrder(k);if(y.options.randomize&&(S="slides"==y.options.displayMode&&R?y.q[Number(R[s])]:y.q[Number(T[s].getAttribute("data-o"))]),S.q||(S.q=""),S.q=S.q.replace(/<html.*<body>([\s\S]*?)<\/body><\/html>/g,"$1"),kInteractive.scorm&&y.options&&y.options.answerOnce&&kInteractive.scorm.interactionExists(kInteractive.getScormId(k.id+"-"+D,S.q))){var M="<p style='text-align:center'>"+(C.alreadySubmitted?C.alreadySubmitted:"This test has already been submitted!")+"</p>";return void kInteractive.alert({content:M})}var O,H={},j=r[0].getAttribute("id");q.push(H),H.id=kInteractive.getScormId(k.id+"-"+D,S.q),H.qid=k.id,0<=j.indexOf("ki-tf-")?H.type="true-false":0<=j.indexOf("ki-mcq-")||j.indexOf("ki-mmcq-")?H.type="choice":0<=j.indexOf("ki-dd-")?H.type="drag-drop":0<=j.indexOf("ki-sa-")&&(H.type="short-answer"),H.objective=y.options?y.options.objective:null,H.timestamp=kInteractive.timestamp,H.latency=Math.round(((new Date).getTime()-kInteractive.timestamp.getTime())/1e3),H.description=S.q,(O=kInteractive.questions.f.assess(T[s],S,y)).correctResponses&&(H.correctResponses=O.correctResponses),O.learnerResponses&&(H.learnerResponses=O.learnerResponses);var P=(P=O.ddInteractionOb)||[],B=O.correct;if(P.length)for(var U=0;U<P.length;U++)H.correctResponses&&(H.correctResponses+="; "),H.learnerResponses&&(H.learnerResponses+="; "),H.correctResponses+=P[U].correct,H.learnerResponses+=P[U].learner;H.result=0,B&&x++,null!=S.weight&&(A+=+S.weight,B&&(L+=+S.weight,H.result=+S.weight)),H.scoreMax=y.options?Number(y.options.totalScore):null,kInteractive.questions.f.showExp(y.userDict,T[s],S,y.options);var z=document.createElement("div");z.innerHTML=S.q.replace(/'/g,"&#39;");var F=z.textContent,K=C.question?C.question.replace("[no]",D):"Question "+D;N+=B?"<span style='border-bottom: dotted 1px #aaa;' title='"+F+"'>"+K+".</span>  <span style='color:#366c20'>"+C.correct+"</span><br/>":"<span style='border-bottom: dotted 1px #aaa;' title='"+F+"'>"+K+".</span>  <span style='color:#7a1818'>"+C.incorrect+"</span><br/>"}y.options&&y.options.visibilityDurationStopAfterSubmit&&k.clock&&(k.clock.destroy&&k.clock.destroy(),k.clock=null),setTimeout(function(){"undefined"!=typeof isKotobee&&"undefined"!=typeof markers&&markers.refreshMarkers()},100);for(s=0;s<q.length;s++)q[s].weighting=Math.round(1e3*y.options.totalScore/A)/1e3;function W(){var e="Questions: "+E+". Score: "+x+" questions out of "+D;e+=". Action: "+_,"selfAnswerReport"==_&&(e+=". Details: "+N),kInteractive.tinCan({verb:"solved",activity:e})}var J,_=y.action;y.options.totalScore&&A&&(J=Math.round(10*y.options.totalScore*L/A)/10);var G="";"ar"==y.options.lang&&(G=' direction="rtl"');var V,Y="";C.scoreWeight?(null!=J&&(V="",y.options.passScore&&(J>=y.options.passScore||J==y.options.totalScore?V+=C.pass:V+=C.fail),V&&(V+=". "),Y+="<h3"+G+">"+(V||"")+C.scoreWeight.replace("[score]",J).replace("[total]",y.options.totalScore)+"</h3>"),Y+="<p"+G+">"+C.score.replace("[correct]",x).replace("[total]",D)+"</p>"):Y="You scored "+x+" question(s) out of "+D,"selfAnswerReport"==_&&(Y="<p"+G+"><strong>"+Y+"</strong></p><p><strong>"+(C.details?C.details:"Details")+"</strong></p><p class='kbAlertScroll'>"+N+"</p>"),"selfAnswer"!=_&&"selfAnswerReport"!=_||(kInteractive.alert({content:Y}),W()),("email"==_||y.options.reportEmail||y.options.emailForm||y.options.serverReports)&&function(){var e="email"==_||y.options.reportEmail||y.options.emailForm,n="selfAnswer"!=_&&"selfAnswerReport"!=_&&"email"!=_;try{if(!config.kotobee.cloudid)return n&&kInteractive.alert({content:I.cloudOnly,title:C.sorry?C.sorry:"Sorry"})}catch(e){return n&&kInteractive.alert({content:I.cloudOnly,title:C.sorry?C.sorry:"Sorry"})}var t={};t.ans=new Array;for(var a=0,i=0;i<T.length;i++){var r=T[i].getElementsByClassName("ans"),o=kInteractive.questions.f.getQClass(T[i]),s=T[i].getElementsByClassName("questionTxt"),c=s.length?s[0].textContent:"",l=T[i].id,d=y.q[i];if("tf"==o||"mcq"==o||"mmcq"==o)for(var u=0;u<r.length;u++)r[u].checked&&t.ans.push({q:a,id:l,title:c,type:o,weight:d.weight,index:u,label:r[u].nextSibling.textContent||r[u].nextSibling.data});else if("sa"==o)t.ans.push({q:a,id:l,title:c,type:o,weight:d.weight,label:r[0].value});else if("dd"==o)for(u=0;u<r.length;u++)t.ans.push({q:a,id:l,title:c,type:o,weight:d.weight,index:u,label:r[u].textContent,category:kInteractive.hasClass(r[u].parentNode,"dCategory")?r[u].parentNode.children[0].textContent:""});"sp"!=o&&a++}t.id=k.id,t.title=E,y.options.reportLabel&&(t.title=y.options.reportLabel);t.fullScore=J,t.maxScore=y.options.totalScore,t.weightedScore=L,t.maxWeight=A,t.score=x,t.total=D,t.correctQuestions=x,t.totalQuestions=D,t.rtl=y.options.rtl;var p={};p.recipient=y.address,y.options.reportEmail&&(p.recipient=y.options.reportEmail);{var v,m;y.options.emailForm&&(!(v=k.getElementsByClassName("emailForm")).length||(m=v[0].getElementsByClassName("txtfield")).length&&(p.recipient=p.recipient?p.recipient+","+m[0].value:m[0].value))}p.content=JSON.stringify(t),p.mode=config.kotobee.mode,p.cloudid=config.kotobee.cloudid;try{var h=angular.element(document.body).scope();"undefined"!=typeof kotobee&&(p.env=config.kotobee.mode,"library"==config.kotobee.mode&&h.data.book&&(p.bid=h.data.book.id)),p.email=h.data.user.email,p.code=h.data.user.code,p.pwd=h.data.user.pwd}catch(e){}{var g;e&&p.recipient&&(g=b.value,b.value=C.submitting?C.submitting:"Submitting ..",b.setAttribute("disabled","true"),f=(config.kotobee.liburl?config.kotobee.liburl:"https://www.kotobee.com/")+"library/report/questions",kInteractive.questions.f.sendPost(f,p,function(e){try{var t=e.target;if(b.value=g,b.removeAttribute("disabled"),4===t.readyState&&200===t.status){if(!n)return;JSON.parse(t.responseText).success?kInteractive.alert({content:C.submitted?C.submitted:"Answers submitted!"}):kInteractive.alert({content:C.errorSubmitting?C.errorSubmitting:"An error has occurred while sending answers to the server"})}}catch(e){if(b.value=g,b.setAttribute("value",b.value),b.removeAttribute("disabled"),!n)return;kInteractive.alert({content:C.errorSubmitting?C.errorSubmitting:"An error has occurred while sending answers to the server"})}}))}y.options.dateRange&&y.options.dateRangeEnabled&&(p.drange=encodeURI(JSON.stringify(y.options.dateRange)));y.options.reportsubmission&&(p.reportsubmission=y.options.reportsubmission);k.clock&&(p.duration=k.clock.time);{var f;"undefined"!=typeof isKotobee&&(y.options.serverReports&&(f=(config.kotobee.liburl?config.kotobee.liburl:"https://www.kotobee.com/")+"library/questions/add",kInteractive.questions.f.sendPost(f,p,function(e){try{var t,n=e.target;4===n.readyState&&(200!==n.status||(t=JSON.parse(n.responseText)).error&&("s_userAlreadySubmitted"==t.error?kInteractive.alert({content:C.errorAlreadySubmitted?C.errorAlreadySubmitted:"Answers have already been submitted previously"}):"early"==t.error||"expired"==t.error?kInteractive.alert({content:C.questionsExpired}):kotobee.popup("An error has occurred. Please contact support if you believe this is a mistake.",{title:"Error Code: "+t.error})))}catch(e){}})),config.kotobee.ltireporting&&"undefined"!=typeof launchid&&(p.launchid=launchid,f=(config.kotobee.liburl?config.kotobee.liburl:"https://www.kotobee.com/")+"lti/1.3/report",kInteractive.questions.f.sendPost(f,p)),W())}}(),document[k.id]||(document[k.id]={}),document[k.id].q=[];for(var X=k.getElementsByClassName("ques"),m=0;m<X.length;m++){var Q=m,R=kInteractive.questions.f.getItemOrder(k);y.options.randomize&&(Q="slides"==y.options.displayMode&&R?Number(R[m]):Number(X[m].getAttribute("data-o"))),document[k.id].q[Q]=[];for(var $=X[m].getElementsByClassName("ans"),s=0;s<$.length;s++)kInteractive.hasClass($[s],"txtfield")?document[k.id].q[Q][s]=$[s].value:$[s].type&&"checkbox"==$[s].type.toLowerCase()||$[s].type&&"radio"==$[s].type.toLowerCase()?document[k.id].q[Q][s]=$[s].checked:kInteractive.c.hasClass($[s].parentNode,"dCategory")&&(document[k.id].q[Q][s]={id:$[s].id,category:kInteractive.getChildIndex($[s].parentNode)})}setTimeout(function(){kInteractive.scorm&&kInteractive.scorm.setInteractions(q),kInteractive.events&&kInteractive.events.add({action:"questionsSubmitted",param:E,elem:k,data:y}),kInteractive.isEditorMode()||y.options.disableAfterAttempts&&kInteractive.isKotobeeUser()&&kInteractive.questions.f.getAttempts(k,function(e){e=e||0,e++;var t={label:k.id+"-attempts"};if(y.options.serverReports&&kInteractive.isKotobeeUser()||kotobee.setData(Object.assign(t,{overwrite:1,data:e}),function(){}),e>=y.options.disableAfterAttempts){kInteractive.c.addClass(k,"disabled");for(var n=k.getElementsByTagName("input"),a=0;a<n.length;a++)n[a].setAttribute("disabled","disabled")}})},500),y.options.hideAfterSubmit&&Y&&kInteractive.questions.f.replaceWithError(k,{msg:Y,className:"qSubmitted",tag:"div"})}}}},f:{showExp:function(e,t,n,a){var i,r,o,s;if(a=a||{},n.exp&&(t.getElementsByClassName("explanation").length||(a.expInPopup?((i=document.createElement("a")).href="",i.className="explanation explanationBtn ki-noHighlight",i.innerHTML=(e.explanation?e.explanation:"Explanation")+" ..",i.addEventListener("click",function(e){e.preventDefault(),kInteractive.alert({content:n.exp})})):((i=document.createElement("p")).className="explanation",i.innerHTML=n.exp),kInteractive.appendAfterDelay(t,i))),(n.ref||n.reftype)&&(t.getElementsByClassName("reference").length||((r=document.createElement("a")).className="reference",r.innerHTML=e.learnMore?e.learnMore:"Learn more",n.reftype&&"website"!=n.reftype?"booklocation"==n.reftype&&n.refchapter&&(o=ph.relative(bookPath,kInteractive.absoluteURL),s=ph.relative(o,n.refchapter.ahref),n.refanchor&&(s+="#"+n.refanchor),r.setAttribute("href",s),r.setAttribute("onclick","return false;"),kInteractive.appendAfterDelay(t,r)):(r.setAttribute("href",kInteractive.escapeHtml(n.ref)),r.setAttribute("target","_blank"),r.setAttribute("onclick","return false;"),kInteractive.appendAfterDelay(t,r)))),n.c&&n.c.length){for(var c=t.getElementsByClassName("answer"),l=t.getElementsByClassName("explanation"),d=l.length;d--;)l[d].parentNode.classList.contains("answer")&&l[d].parentNode.removeChild(l[d]);for(d=0;d<n.c.length;d++)(!a.expForSelection||c[d].getElementsByTagName("input")[0].checked)&&kInteractive.questions.f.showExp(e,c[d],n.c[d],a)}},getQClass:function(e){var t=e.className;return-1!=t.indexOf("mmcq")?"mmcq":-1!=t.indexOf("mcq")?"mcq":-1!=t.indexOf("tf")?"tf":-1!=t.indexOf("sa")?"sa":-1!=t.indexOf("dd")?"dd":void 0},getItemOrder:function(e){var t=e.getAttribute("data-o");return t&&(e.originArray=t.split(",")),e.originArray},createQElem:function(e,t){var n=(t=t||{}).index,a=t.elem,i=t.rendered,r=this.getQId(a),o=kInteractive.questions.f.getItemOrder(a),s=e.q[n];e.options.randomize&&o&&"slides"==e.options.displayMode&&(s=e.q[a.originArray[n]]);var c=document.createElement("div");c.className="ques",s.id&&(c.id=s.id),null!=s.origin&&c.setAttribute("data-o",s.origin),s.q||(s.q="");var l=e.options.numbering;"sp"==s.type&&(l=!1);var d,u,p="";l&&(p="<span class='no'>"+(t.questionNo?t.questionNo:n)+"</span>"),s.q=s.q.replace(/<html.*<body>([\s\S]*?)<\/body><\/html>/g,"$1");try{c.innerHTML="<div class='questionTxt'><strong>"+p+"</strong> <span class='inner'> "+s.q+"</span></div>"}catch(e){c.innerHTML="<div class='questionTxt'><strong>"+p+"</strong> <span class='inner'>"+kInteractive.escapeHtml(" "+s.q)+"</span></div>"}if(kInteractive.c.hasClass(a,"parsed")&&kInteractive.c.addClass(c.children[0].getElementsByClassName("inner")[0],"parsed"),s.path&&(d=document.createElement("img"),u=!i||"undefined"==typeof isKotobee?s.path:ph.join(kInteractive.absoluteURL,s.path),d.src=kInteractive.cleanURL(u),d.className=typeof isKotobee,c.appendChild(d)),c.classList.add(s.type),"sp"==s.type&&(s.topBorder&&kInteractive.c.addClass(c,"topBorder"),s.bottomBorder&&kInteractive.c.addClass(c,"bottomBorder"),kInteractive.c.addClass(c,"separator")),"sa"==s.type){var v=s.lines?s.lines:1,m=document.createElement(1<v?"textarea":"input"),h="ki-sa-"+r+"-"+n;m.id=h,m.name="sa-"+r+"-"+n,1==v?m.type="text":m.rows=s.lines?s.lines:1,m.className="ki-textfield ans txtfield","undefined"==typeof isKotobee&&(m.addEventListener("keyup",kInteractive.actionEvent),m.addEventListener("change",kInteractive.actionEvent)),document[item.id]&&document[item.id].preserve&&document[item.id].q&&document[item.id].q[n]&&(m.value=document[item.id].q[n]),c.appendChild(m)}else if("mcq"==s.type||"mmcq"==s.type)for(var g=s.c,f=0;f<g.length;f++){var h="ki-mcq-"+r+"-"+n+"-"+f,k=document.createElement("input");k.id=h,"mmcq"==s.type?(k.name="name",k.type="checkbox"):(k.name="mcq-"+r+"-"+n,k.type="radio"),k.className="ki-inputbox ans","undefined"==typeof isKotobee&&k.addEventListener("click",kInteractive.actionEvent),(b=document.createElement("label")).htmlFor=h,b.innerHTML=" "+kInteractive.escapeHtml(g[f].t),b.className="ki-noHighlight",kInteractive.c.hasClass(a,"parsed")&&kInteractive.c.addClass(b,"parsed"),(I=document.createElement("div")).className="answer",document[item.id]&&document[item.id].preserve&&document[item.id].q&&document[item.id].q[n]&&(k.checked=document[item.id].q[n][f],e.options&&e.options.highlightCorrect&&s.c[f].a&&kInteractive.c.addClass(I,"correct")),I.appendChild(k),I.appendChild(b),c.appendChild(I)}else if("tf"==s.type)for(f=0;f<2;f++){var b,h="ki-tf-"+r+"-"+n+"-"+f,y=document.createElement("input");y.type="radio",y.className="ki-inputbox ans",y.id=h,y.name="tf-"+r+"-"+n,"undefined"==typeof isKotobee&&y.addEventListener("click",kInteractive.actionEvent),(b=document.createElement("label")).htmlFor=h,s.choice1&&s.choice2?b.innerHTML=kInteractive.escapeHtml(" "+(0==f?s.choice1:s.choice2)):b.innerHTML=" "+(0==f?"True":"False"),b.className="ki-noHighlight";var I=document.createElement("div");document[item.id]&&document[item.id].preserve&&document[item.id].q&&document[item.id].q[n]&&(y.checked=document[item.id].q[n][f],e.options&&e.options.highlightCorrect&&(0==f&&s.a||1==f&&!s.a)&&kInteractive.c.addClass(I,"correct")),I.appendChild(y),I.appendChild(b),c.appendChild(I)}else if("dd"==s.type){var C=document.createElement("div");C.className="categoryContainer";for(var w=[],f=0;f<s.k.length;f++){var E=s.k[f],x=f,N=document.createElement("div");N.className="dCategory ki-noHighlight ki-dd-category-"+x,N.setAttribute("ondragover","kInteractive.allowDrop(event)"),N.setAttribute("ondrop","kInteractive.drop(event)");var A=document.createElement("div");A.innerHTML=E.category.name,A.className="qTitle",N.appendChild(A);for(var L=0;L<E.category.answers.length;L++)w.push(E.category.answers[L].t);C.appendChild(N)}c.appendChild(C);var q=document.createElement("div");q.className="ki-dd-clearfix",c.appendChild(q);var T=document.createElement("div");T.setAttribute("ondragover","kInteractive.allowDrop(event)"),T.setAttribute("ondrop","kInteractive.drop(event)"),T.className="ki-dd-answers ki-noHighlight";for(var D=[],f=0;f<w.length;f++){var x=f,I=w[f],B=document.createElement("div"),h="ki-dd-"+r+"-"+n+"-"+x;B.id=h,B.innerHTML="<span></span>",B.children[0].innerText=I,B.className="ki-draggable ans ki-noHighlight",B.draggable=!0,B.setAttribute("ondragstart","kInteractive.drag(event);"),B.setAttribute("onmousedown","event.stopPropagation();"),B.setAttribute("ontouchstart","event.stopPropagation();"),kInteractive.questions.manualDrag.isIOS()&&(B.setAttribute("ontouchstart","kInteractive.questions.manualDrag.dragStart(event);"),B.setAttribute("ontouchmove","kInteractive.questions.manualDrag.dragMove(event);"),document.addEventListener("touchend",function(){kInteractive.questions.manualDrag.isDown=!1})),"undefined"==typeof isKotobee&&B.addEventListener("dragend",kInteractive.actionEvent),D.push(B)}kInteractive.shuffleArray(D);for(f=0;f<D.length;f++)T.appendChild(D[f]);c.appendChild(T)}var S=document.createElement("p");return S.className="separator",c.appendChild(S),document[item.id]&&document[item.id].preserve&&document[item.id].q&&document[item.id].q[n]&&kInteractive.questions.f.showExp(e.userDict,c,s,e.options),c},getQId:function(e){return Number(e.id.replace("ki-qs-",""))},createSubmitBtn:function(e,t){var n=this.getQId(t),a=document.createElement("input"),i="ki-"+n+"-btn";return a.type="submit",a.value=e.submit?e.submit:"Submit Answers",a.id=i,a.className="btn ki-btn questions-submit","undefined"==typeof isKotobee&&a.addEventListener("click",kInteractive.actionEvent),a},createClearBtn:function(e){var t=document.createElement("input");return t.type="submit",t.value=e.clear?e.clear:"Clear Answers",t.className="btn ki-btn ki-questions questions-clear","undefined"==typeof isKotobee&&t.addEventListener("click",kInteractive.actionEvent),t},createAddToNotebookBtn:function(e){var t=document.createElement("input");return t.type="submit",t.value=e.notebook?e.notebook:"Save to Notebook",t.className="btn ki-btn ki-questions questions-notebook","undefined"==typeof isKotobee&&t.addEventListener("click",kInteractive.actionEvent),t},createPreserveBtn:function(e,t,n){var a=this.getQId(t),i=document.createElement("div");i.style.marginBottom="10px";var r=document.createElement("input");function o(e){return e.slice(0,1).toUpperCase()+e.slice(1)}r.type="checkbox",r.name="preserveAnswers",r.className="ki-btn ki-questions questions-preserve",r.id="ki-"+a+"-preserveAnswersBtn",n&&(0<Object.keys(n).length&&Object.keys(n).forEach(function(t){"placement"==t?r.dataset["design"+o(t)]=n[t]:Object.entries(n[t]).forEach(function(e){r.dataset["design"+o(t)+o(e[0])]=e[1]})}),"undefined"!=typeof isKotobee&&t.addEventListener("change",function(e){e.target.matches("input.questions-preserve")&&kInteractive.actionEvent(e)})),document[item.id]&&(r.checked=document[item.id].preserve),i.appendChild(r);var s=document.createElement("label");return s.htmlFor=r.id,s.innerHTML=" "+(e.preserve?e.preserve:"Preserve submitted answers"),s.className="ki-noHighlight",i.appendChild(s),"undefined"==typeof isKotobee&&r.addEventListener("change",kInteractive.actionEvent),i},createNextBtn:function(e){var t=document.createElement("input");return t.type="submit",t.value=e.next?e.next:"Next",t.className="btn ki-btn ki-questions questions-next","undefined"==typeof isKotobee&&t.addEventListener("click",kInteractive.actionEvent),t},createPrevBtn:function(e){var t=document.createElement("input");return t.type="submit",t.value=e.prev?e.prev:"Previous",t.className="btn ki-btn ki-questions questions-prev","undefined"==typeof isKotobee&&t.addEventListener("click",kInteractive.actionEvent),t},createSeparator:function(){document.createElement("p").className="separator"},createEmailForm:function(e){var o=document.createElement("div");return o.setAttribute("class","emailForm"),function(e,t,n){var a=document.createElement("div");a.className="address",a.innerHTML="<p><strong>"+e+"</strong><em>"+t+"</em></p>";var i=document.createElement("input");i.name=n,i.type="text",i.className="txtfield",a.appendChild(i);var r=document.createElement("p");r.className="separator",a.appendChild(r),o.appendChild(a)}(e.recipientEmail?e.recipientEmail:"Recipient email",e.separateEmails?e.separateEmails:"Separate multiple emails with commas","recipientEmail"),o},showNavBtns:function(e,t,n){var a;(a=(n=n||{}).isFrag?t.lastElementChild:t.getElementsByClassName("btnContainer")[0]).innerHTML="";var i=kInteractive.questions.f.createPrevBtn(n.userDict);a.appendChild(i),e<=0&&i.classList.add("disabled");var r,o,s=kInteractive.questions.f.createNextBtn(n.userDict);a.appendChild(s),e==n.qsData.q.length-1&&(s.classList.add("disabled"),(r=kInteractive.questions.f.createSubmitBtn(n.userDict,t)).classList.add("shift"),a.appendChild(r),!n.qsData.options.emailForm||(o=kInteractive.questions.f.createEmailForm(n.userDict))&&a.insertBefore(o,a.firstChild))},assess:function(e,t,n){for(var a={ddInteractionOb:[]},i=e.getElementsByClassName("ans"),r=0<i.length,o=0;o<i.length;o++)if(0<=i[o].getAttribute("id").indexOf("ki-tf-"))0==o&&t.a?a.correctResponses=t.choice1:1!=o||t.a||(a.correctResponses=t.choice2),i[o].checked?(a.learnerResponses=o?t.choice2:t.choice1,(0==o&&!t.a||1==o&&t.a)&&(r=!1)):(0==o&&t.a||1==o&&!t.a)&&(r=!1),n.options&&n.options.highlightCorrect&&(0==o&&t.a||1==o&&!t.a)&&kInteractive.c.addClass(i[o].parentNode,"correct");else if(0<=i[o].getAttribute("id").indexOf("ki-sa-")){for(var s=(s=i[o].value.toLowerCase())&&s.trim(),r=!0,c="",l=0;l<t.k.length;l++){var d=t.k[l].t;if(!d){r=!1;break}c&&(c+="+"),c+=d=d.toLowerCase();for(var u=d.split(","),p=!1,v=0;v<u.length;v++)if(0<=s.indexOf(u[v].trim())){p=!0;break}if(!p){r=!1;break}}a.correctResponses=c,a.learnerResponses=s}else if(0<=i[o].getAttribute("id").indexOf("ki-dd-")){kInteractive.c.removeClass(i[o],"incorrect");for(var m,h=i[o].id.split("-")[4],g=0,f=0;f<t.k.length;f++){if(h<(g+=t.k[f].category.answers.length)){m=f;break}}var k,b,y,I=e.getElementsByClassName("dCategory")[m],C={};C.correct=i[o].innerText+" -> "+I.children[0].innerText,kInteractive.c.hasClass(i[o].parentNode,"ki-dd-answers")?(kInteractive.c.addClass(i[o],"incorrect"),r=!1,C.learner=i[o].innerText+" unassigned"):(b=(k=i[o].parentNode).children[0].innerText,y=kInteractive.getChildIndex(k),C.learner=i[o].innerText+" -> "+b,y!=m&&n.options&&n.options.highlightCorrect&&(r=!1,kInteractive.c.addClass(i[o],"incorrect"))),a.ddInteractionOb.push(C)}else t.c[o].a&&(a.correctResponses=t.c[o].t),i[o].checked&&(a.learnerResponses=t.c[o].t),(i[o].checked&&!t.c[o].a||!i[o].checked&&t.c[o].a)&&(r=!1),n.options&&n.options.highlightCorrect&&t.c[o].a&&kInteractive.c.addClass(i[o].parentNode,"correct");return a.correct=r,a},checkTimeValidity:function(e,t){var n=kInteractive.phpDateToJS(e),a={};function i(e){var t=e.split(":");return 1==t[0].length&&(t[0]="0"+t[0]),1==t[1].length&&(t[1]="0"+t[1]),t[0]+":"+t[1]}if(!t.fromDate){var r=o(n.getHours())+":"+o(n.getMinutes());return r>i(t.toTime)?a.expired=!0:r<i(t.fromTime)?a.early=!0:a.valid=!0,a;function o(e){return String(e+100).substr(1)}}var s=t.fromDate.split("-"),c=t.fromTime.split(":"),l=new Date(s[2],s[1]-1,s[0],c[0],c[1]),d=t.toDate.split("-"),u=t.toTime.split(":");return new Date(d[2],d[1]-1,d[0],u[0],u[1])<n?a.expired=!0:n<l?a.early=!0:a.valid=!0,a},sendPost:function(e,t,n){t=t||{};var a=new XMLHttpRequest;n&&(a.onreadystatechange=n),a.open("POST",e),a.setRequestHeader("Content-type","application/x-www-form-urlencoded");var i="a=a";for(var r in"undefined"!=typeof uuid&&(i+="&fingerprint="+uuid),t){var o=t[r];null!=o&&"object"!=typeof o&&("string"==typeof o&&(o=encodeURIComponent(o)),"boolean"==typeof o&&(o=o?1:0),i+="&"+r+"="+o)}a.send(i)},replaceWithError:function(e,t){t=t||{},this.stopClock(e);var n=e.getElementsByClassName("scoreHistory")[0],a=e.getElementsByTagName("h4")[0],i=kInteractive.readData(e);e.innerHTML="";var r=document.createElement("div");i.style&&kInteractive.c.addClass(r,i.style),e.appendChild(r),a&&!i.options.hideTitleOnError&&r.appendChild(a);var o=document.createElement("div"),s=(s=t.tag)||"p";o.innerHTML="<"+s+" class='"+t.className+"'>"+t.msg+"</"+s+">",r.appendChild(o.children[0]),n&&r.appendChild(n)},phpDateToJs:function(e){var t=(e=(e=(e=e.split("+")[0]).split(".")[0]).replace("T"," ")).split(/[- :]/);return new Date(t[0],t[1]-1,t[2],t[3],t[4],t[5])},stopClock:function(e){e&&e.clock&&(e.clock.pause(),e.clock.destroy(),e.clock=null)},getAttempts:function(e,i){var t,n,a;kInteractive.readData(e).options.serverReports&&kInteractive.isKotobeeUser()?(t=(config.kotobee.liburl?config.kotobee.liburl:"https://www.kotobee.com/")+"admin/library/questions/get",n=angular.element(document.body).scope(),a={env:"library"==config.kotobee.mode?"library":"book",cloudid:config.kotobee.cloudid,email:n.data.user.email,code:n.data.user.code,pwd:n.data.user.pwd,qsxid:e.id,mode:"submissions"},"library"==config.kotobee.mode&&n.data.book&&(a.bid=n.data.book.id),kInteractive.questions.f.sendPost(t,a,function(e){var t=e.target;if(4===t.readyState&&200===t.status){var n=0;try{var a=JSON.parse(t.responseText),n=Number(a.scores.length)}catch(e){}i(n)}})):kotobee.getData({label:e.id+"-attempts"},function(e){return e&&e.length?void i(Number(e[0].data)):i(0)})},clearSavedAnswers:function(){for(obj in document)0==obj.indexOf("ki-qs-")&&delete document[obj]}},manualDrag:{isDown:null,startX:null,startY:null,currentX:null,currentY:null,walkX:null,walkY:null,margin:0,currentAnswerObj:{},answersContainers:[],store:null,dragStart:function(e){e.stopPropagation(),this.isDown=!0;var t=(e=e||window.event).target;this.getContainers(t);var n=t.getElementsByTagName("span")[0];n&&(n.style.pointerEvents="none"),t.style.zIndex="999",this.startX=Math.round(e.touches[0].clientX)-this.margin,this.startY=Math.round(e.touches[0].clientY)-this.margin,this.dragEnd(t)},dragEnd:function(t){t.addEventListener("touchend",function(){function e(){return kInteractive.questions.manualDrag}e().isDown=!1,t.style.left="initial",t.style.top="initial",t.style.zIndex="initial",null!==e().store&&e().store.container.append(e().store.answer)})},dragMove:function(e){var t=(e=e||window.event).target;this.currentAnswerObj=t.getBoundingClientRect(),this.isDown&&(this.currentX=Math.round(e.touches[0].clientX)-this.margin,this.currentY=Math.round(e.touches[0].clientY)-this.margin,this.walkX=this.currentX-this.startX,this.walkY=this.currentY-this.startY,t.style.left=this.walkX+"px",t.style.top=this.walkY+"px",this.detectBoundaries(t))},detectBoundaries:function(i){function r(){return kInteractive.questions.manualDrag}this.answersContainers.forEach(function(e){var t,n,a;e&&(n={w:(t=e.getBoundingClientRect()).width,h:t.height,x:t.x,y:t.y},a={w:r().currentAnswerObj.width,h:r().currentAnswerObj.height,x:r().currentAnswerObj.x,y:r().currentAnswerObj.y},kInteractive.questions.manualDrag.hasIntersection(a,n)&&(r().store={container:e,answer:i}))})},hasIntersection:function(e,t){return!(e.y+0>t.y+t.h)&&(!(e.x+e.w+0<t.x)&&(!(e.y+e.h+0<t.y)&&!(e.x+0>t.x+t.w)))},getContainers:function(e){function n(){return kInteractive.questions.manualDrag}var a=e.parentElement,t=a?a.className:"";function i(e){var t;a&&e?(t=e.getElementsByClassName("dCategory")).length&&(n().answersContainers=Array.from(t)):n().answersContainers=[]}t.includes("ki-dd-answers")?i(a.parentElement.getElementsByClassName("categoryContainer")[0]):t.includes("dCategory")&&i(a.parentElement)},isIOS:function(){return"undefined"!=typeof isKotobee&&"undefined"!=typeof ios&&ios}}},kInteractive.threed={preRender:function(e,t){},postRender:function(e,t,n,a){var i,r,o,s=e.createDocumentFragment(),c=kInteractive.readData(t);c&&(t.innerHTML="",i=t.getAttribute("style"),c.inter.placeholderPath&&(r="undefined"==typeof isKotobee?c.inter.placeholderPath:ph.join(kInteractive.absoluteURL,c.inter.placeholderPath),i+="background-image:url('"+(r=kInteractive.cleanURL(r))+"');"),t.setAttribute("style",i),o=document.createElement("a"),c.inter.includeMsg?(o.innerHTML=kInteractive.escapeHtml(c.inter.msg),o.className="msgBtn ki-btn"):o.className="invisibleBtn ki-btn",s.appendChild(o),"undefined"==typeof isKotobee&&o&&o.addEventListener("click",kInteractive.actionEvent),t.appendChild(s))},action:function(s,e,t){var d=kInteractive.readData(s);if(d){"inPanel"==d.inter.target?kInteractive.c.addClass(s,"running"):s.innerHTML="";var u=document.createElement("div");if(u.className="loading",s.appendChild(u),"undefined"==typeof THREE){var n,a=document.getElementsByTagName("head")[0],i=a.getElementsByTagName("script");if("undefined"!=typeof isKotobee)n=bookPath+"EPUB/js/kotobeeInteractive3D.js";else for(var r=0;r<i.length;r++)if(0<=i[r].src.indexOf("kotobeeinteractive.js")){n=i[r].src.replace("kotobeeinteractive.js","kotobeeinteractive3D.js");break}if(!n)return;var o=document.createElement("script");o.type="text/javascript",o.src=n,o.onload=c,o.onreadystatechange=function(){"complete"==this.readyState&&c()},a.appendChild(o)}else c()}function c(){var o,e,t=s;function n(t){if(setTimeout(function(){var e;kInteractive.scorm&&((e={}).id=kInteractive.getScormId("_3D",d.name?d.name:"Model"),e.description="Opened 3D model: "+(d.name?d.name:"No name"),e.learnerResponses="Viewed",e.type="other",e.timestamp=new Date,kInteractive.scorm.setInteractions([e])),kInteractive.events&&kInteractive.events.add({action:"3dModelViewed",param:d.name?d.name:"No name",elem:s,data:d})},800),o&&cancelAnimationFrame(o),"fbx"!==d.type){if(!kInteractive.threed.webglAvailable)return kInteractive.threed.alert();var n=new THREE.WebGLRenderer({precision:"highp"}),e=t.offsetWidth,a=t.offsetHeight,i=new THREE.PerspectiveCamera(d.camera.viewAngle,e/a,d.camera.near,d.camera.far),c=new THREE.Scene;null!=d.scene.bgColor&&n.setClearColor(d.scene.bgColor,1),c.add(i),i.position.x=d.camera.x,i.position.y=d.camera.y,i.position.z=d.camera.z,n.setSize(e,a),d.scene.floor&&(floor=new THREE.Mesh(new THREE.PlaneGeometry(500,500,1,1),new THREE.MeshPhongMaterial({color:d.scene.floorColor?d.scene.floorColor:10066329})),floor.applyMatrix((new THREE.Matrix4).makeRotationX(-Math.PI/2)),floor.position.y=-80,floor.receiveShadow=!0,c.add(floor));var l=new THREE.LoadingManager;l.onProgress=function(e,t,n){},function r(o){var s,e;o>=d.objects.length||(s=d.objects[o],e="undefined"==typeof isKotobee?s.path:ph.join(kInteractive.absoluteURL,s.path),e=kInteractive.cleanURL(e),new OBJLoader(l).load(e,function(i){u&&u.parentNode&&u.parentNode.removeChild(u),n.domElement.parentNode||t.appendChild(n.domElement),i.position.set(s.x,s.y,s.z),i.traverse(function(n){var a,e,t;n instanceof THREE.Mesh&&(a=n==i.children[i.children.length-1],s.textPath?(e=new THREE.ImageLoader(l),t="undefined"==typeof isKotobee?s.textPath:ph.join(kInteractive.absoluteURL,s.textPath),e.load(t,function(e){var t=new THREE.Texture;t.image=e,t.needsUpdate=!0,n.material.map=t,a&&c.add(i),r(++o)},function(){},function(e){})):(a&&c.add(i),r(++o)))})},function(e){e.lengthComputable&&(e.loaded,e.total)},function(e){}))}(0),new OrbitControls(i,n.domElement).target=new THREE.Vector3(0,0,0),kInteractive.threed.addLight(c,d);var r=new THREE.Clock;!function e(){try{n.render(c,i)}catch(e){return}r.getDelta();o=requestAnimFrame(e)}()}else"inPanel"===d.inter.target?setTimeout(function(){kInteractive.threed.fbxLoader(t,d)},500):kInteractive.threed.fbxLoader(t,d)}"inPanel"==d.inter.target?(e={class:"threed",cb:function(e){var t=document.createElement("div");t.className="container",e.appendChild(t),n(t)},closed:function(){kInteractive.c.removeClass(s,"running")}},kInteractive.openFrame(e)):n(t)}},get webglAvailable(){try{var e=document.createElement("canvas");return!(!window.WebGLRenderingContext||!e.getContext("webgl")&&!e.getContext("experimental-webgl"))}catch(e){return!1}},alert:function(){kotobee.popup(translit.get("webglNotAvailable"),{title:"WebGl",okBtn:translit.get("ok"),raw:!1,noBackdrop:!1,cb:function(){}})},addLight:function(s,e){e.lights.forEach(function(e){var t,n,a,i,r,o;"pointLight"==e.type?((t=new THREE.PointLight(e.color)).position.set(e.x,e.y,e.z),s.add(t)):"directionalLight"==e.type?((n=new THREE.DirectionalLight(e.color,e.intensity)).position.set(e.x,e.y,e.z),s.add(n)):"hemisphereLight"==e.type?((a=new THREE.HemisphereLight(e.sky,e.ground,e.intensity)).position.set(e.x,e.y,e.z),s.add(a)):"spotLight"==e.type?((i=new THREE.SpotLight(e.color,e.intensity,e.distance,e.angle,e.exponent,e.decay)).position.set(e.x,e.y,e.z),s.add(i)):"ambientLight"==e.type?(r=new THREE.AmbientLight(e.color),s.add(r)):"areaLight"==e.type&&(o=new THREE.RectAreaLight(e.color,e.intensity),s.add(o))})},fbxLoader:function(e,n){if(!kInteractive.threed.webglAvailable)return kInteractive.threed.alert();var a,t,i="inPanel"===n.inter.target?(t=e.getBoundingClientRect().width,e.getBoundingClientRect().height):(t=+n.scene.width,+n.scene.height),r=n.scene.bgColor,o=new THREE.Scene,s=new THREE.PerspectiveCamera(+n.camera.viewAngle,t/i,n.camera.near,+n.camera.far);s.position.setX(+n.camera.x),s.position.setY(+n.camera.y),s.position.setZ(+n.camera.z);var c=new THREE.Clock;kInteractive.threed.addLight(o,n);var l,d=((l=new THREE.WebGLRenderer({antialias:!0})).setPixelRatio(window.devicePixelRatio),l.setSize(t,i),l.shadowMap.enabled=!0,l.setClearColor(r,1),l);e.appendChild(d.domElement);var u=new OrbitControls(s,d.domElement);u.target.set(0,0,0),u.update();var p=new FBXLoader,v=function(e){if(Array.isArray(e.objects)&&e.objects.length){var t=e.objects[0],n="undefined"==typeof isKotobee?t.path:ph.join(kInteractive.absoluteURL,t.path);return kInteractive.cleanURL(n)}return!1}(n);v&&(p.load(v,function(e){a=new THREE.AnimationMixer(e),Array.isArray(e.animations)&&e.animations.length&&a.clipAction(e.animations[0]).play(),e.traverse(function(e){var t;e.isMesh&&(e.castShadow=!0,e.receiveShadow=!0,(t=document.getElementsByClassName(n.classname)[0])&&[].slice.call(t.getElementsByClassName("loading")).forEach(function(e){kInteractive.c.removeClass(e,"loading")}))}),o.add(e)}),function e(){requestAnimationFrame(e);var t=c.getDelta();a&&a.update(t),d.render(o,s)}())}},kInteractive.video={preRender:function(e,t){},postRender:function(e,t,n){var a,i,r,o,s,c,l=e.createDocumentFragment(),d=kInteractive.readData(t);d&&(i="",d.splash&&(i="undefined"==typeof isKotobee?d.splash:ph.join(kInteractive.absoluteURL,d.splash)),i&&(t.style.backgroundImage=null,a=t.style.cssText,i=kInteractive.cleanURL(i),t.setAttribute("style","background-image:url('"+i+"');"+a)),t.setAttribute("id","ki-video-"+n),t.innerHTML="",(r=document.createElement("div")).setAttribute("id","ki-video-"+n+"-container"),r.className="container",o=document.createElement("div"),d.style&&kInteractive.c.addClass(o,d.style),(s=document.createElement("a")).className="playBtn ki-btn",s.appendChild(document.createElement("span")),"undefined"==typeof isKotobee&&s.addEventListener("click",kInteractive.actionEvent),l.appendChild(s),l.appendChild(r),o.appendChild(l),t.appendChild(o),c=function(){for(var e=[],t=document.getElementsByClassName("kInteractive"),n=0;n<t.length;n++){if(-1!==t[n].className.indexOf("video"))if(kInteractive.readData(t[n]).autoplay){e.push(t[n]);break}}return e.length?e[0]:null}(),d.autoplay&&c&&t===c&&kInteractive.action(c))},action:function(o){var s=kInteractive.readData(o);if(s){if(s.popup&&"kInteractiveFrame"!=o.parentNode.id){var e={pos:"side",aspectRatio:"16:9",cb:function(e){var t=document.createElement("div");t.innerHTML=o.outerHTML;var n=t.children[0];n.style.width="100%",n.style.height="100%",n.style.margin="0",n.style.position="absolute",n.style.left=0,n.style.top=0,kInteractive.c.removeClass(n,"kbHidden"),e.appendChild(n);var a=o.id.split("ki-video-")[1];kInteractive.postRender(document,n,a),setTimeout(function(){kInteractive.action(n)},100)}};return kInteractive.openFrame(e)}var t,n=o.getAttribute("id")+"-container",c=o.getElementsByClassName("playBtn")[0];if(!(0<=c.className.indexOf("loading"))){if(kInteractive.stopCurrentMedia(),c.className="playBtn ki-btn loading",s.autoplay&&(c.className="playBtn ki-btn hide"),kInteractive.scorm&&((t={}).id=kInteractive.getScormId(o.getAttribute("id"),s.src),t.description="Played video: "+s.src,t.type="other",t.learnerResponses="Played",t.objective=s.options?s.options.objective:null,t.timestamp=new Date,kInteractive.scorm.setInteractions([t])),kInteractive.events&&kInteractive.events.add({action:"videoPlayed",param:s.src,elem:o,data:s}),"file"==s.type){u("undefined"==typeof isKotobee?s.video:ph.join(kInteractive.absoluteURL,s.video))}else{var a,i=new URL(s.src);if(i.origin.includes("youtube.com")||i.origin.includes("youtu.be")){if(!(a=i.origin.includes("youtube.com")?i.searchParams.get("v"):i.pathname.split("/")[1]))return;if(window&&window.iBooks)return setTimeout(function(){c.className="playBtn ki-btn"},1500),void(window.location.href=s.src);var r={videoId:a,playerVars:{autoplay:1,rel:"0"},events:{onReady:function(){},onStateChange:function(e){e.data==YT.PlayerState.PLAYING&&kInteractive.tinCan({verb:"played",activity:"Video: "+s.src})}}};if(kInteractive.youTubeStatus)if("loading"==kInteractive.youTubeStatus){if(kInteractive.vQueue)return;kInteractive.vQueue=function(){c.className="playBtn ki-btn hide",new YT.Player(n,r)}}else"ready"==kInteractive.youTubeStatus&&(c.className="playBtn ki-btn hide",new YT.Player(n,r));else{window.onYouTubePlayerAPIReady=function(){kInteractive.youTubeStatus="ready",kInteractive.vQueue(),kInteractive.vQueue=null};var l=document.createElement("script");l.src="https://www.youtube.com/player_api";var d=document.getElementsByTagName("script")[0];d.parentNode.insertBefore(l,d),kInteractive.youTubeStatus="loading",kInteractive.vQueue=function(){c.className="playBtn ki-btn hide",new YT.Player(n,r)}}}else u(s.src)}kInteractive.currentVideo=o,kInteractive.c.addClass(kInteractive.currentVideo,"playing")}}function u(e){var t,n=document.createElement("video");n.setAttribute("width","100%"),n.setAttribute("height","100%"),n.setAttribute("src",kInteractive.cleanURL(e)),n.setAttribute("autoplay","true"),n.setAttribute("disablePictureInPicture","true"),n.setAttribute("data-tap-disabled","false"),0!=e.indexOf("http://")&&0!=e.indexOf("https://")||(t=!0),t&&n.setAttribute("crossorigin","anonymous");var a=!1;try{a=-1<navigator.userAgent.toLowerCase().indexOf("android")}catch(e){}a||n.setAttribute("type","video/mp4"),n.setAttribute("webkit-playsinline","true"),n.setAttribute("controls","true");var i="";s.splash&&(i="undefined"==typeof isKotobee?s.splash:ph.join(kInteractive.absoluteURL,s.splash)),i&&n.setAttribute("poster",kInteractive.cleanURL(i)),n.className="ki-noHighlight",s.hideDownloadBtn&&(n.className+=" hideDownloadBtn",n.setAttribute("controlsList","nodownload")),n.innerHTML="Your browser doesn't support HTML5 video.",t&&(n.crossOrigin="anonymous"),n.oncanplay=function(){kInteractive.currentVideo==o&&(c.className="playBtn ki-btn hide",kInteractive.tinCan({verb:"played",activity:"Video: "+e}))},n.addEventListener("error",function(e){e.target.error&&kInteractive.stopCurrentMedia()}),n.addEventListener("webkitfullscreenchange",function(e){var t=null!==document.webkitFullscreenElement;kInteractive.videoIsFullscreen=t});var r=o.getElementsByClassName("container")[0];r.setAttribute("data-tap-disabled","true"),r.appendChild(n)}},resize:function(e){var t=kInteractive.readData(e);t&&(kInteractive.checkResponsiveFloat(t,e),t.maintainRatio&&"px"==t.widthUnit&&t.height&&t.width&&"absolute"!=e.style.position&&(e.style.height=e.offsetWidth*(t.height/t.width)+"px"))}},kInteractive.widget={preRender:function(e,t){},postRender:function(e,t,n,a){a=a||{};var i=e.createDocumentFragment(),r=kInteractive.readData(t);if(r){var o,s=r.name,c=(r.src,r.width),l=r.height;if("page"==r.mode){if(t.children.length)return;var d=document.createElement("div");d.className="cover",i.appendChild(d),r.interaction&&(d.style.pointerEvents="none");var u=document.createElement("div");u.className="iframeContainer";var p,v,m=document.createElement("iframe");m.setAttribute("nwdisable","true"),m.setAttribute("nwfaketop","true"),"undefined"!=typeof desktop&&desktop&&(p=!0),"undefined"!=typeof isReaderApp&&isReaderApp&&(p=!0),p&&m.setAttribute("sandbox","allow-same-origin allow-scripts allow-forms allow-modals allow-popups allow-top-navigation"),m.setAttribute("width",t.style.width?t.style.width:c+r.widthUnit),m.setAttribute("height",t.style.height?t.style.height:l+"px"),m.src=kInteractive.getWidgetUrl(r,a),"undefined"!=typeof kotobee&&-1!==m.src.indexOf(".amazonaws.com")&&((v=document.createElement("a")).href=m.src,m.addEventListener("load",function(e){e.target.contentWindow.postMessage({user:{name:kotobee.user.name?kotobee.user.name:"",email:kotobee.user.email?kotobee.user.email:"",loggedIn:kotobee.user.loggedIn},book:kotobee.book.meta.dc.identifier,chapter:kotobee.currentChapter.url.split("EPUB/xhtml/")[1]},v.protocol+"//"+v.hostname)})),u.appendChild(m),i.appendChild(u);var h=t.style.width,g=t.style.height;0<=h.indexOf("px")&&(r.widthUnit="px"),0<=h.indexOf("%")&&(r.widthUnit="%");try{r.widthUnit?r.width=Number(h.split(r.widthUnit)[0]):r.width=Number(h)}catch(e){r.width=Number(h)}try{r.height=Number(g.split("px")[0])}catch(e){r.height=Number(g)}kInteractive.writeData(t,r)}else{t.children.length||t.innerHTML.trim()||((o=document.createElement("img")).src=kInteractive.cleanURL(kInteractive.getWidgetHome(r,a)+"/"+s+"/Icon.png?c="+Math.round(9999*Math.random())),o.className="wdgtIcon",i.appendChild(o)),"undefined"==typeof isKotobee&&t.addEventListener("click",kInteractive.actionEvent)}t.appendChild(i)}},action:function(t){var n,a,e,i=kInteractive.readData(t);i&&(n=kInteractive.getWidgetUrl(i),(a=document.createElement("iframe")).setAttribute("nwdisable","true"),a.setAttribute("nwfaketop","true"),"undefined"!=typeof desktop&&desktop&&(e=!0),"undefined"!=typeof isReaderApp&&isReaderApp&&(e=!0),e&&a.setAttribute("sandbox","allow-same-origin allow-scripts allow-forms allow-modals allow-popups allow-top-navigation"),i.cb=function(e){a.src=n,e.appendChild(a)},i.cb1="yes",i.closed=function(){},i.responsive&&(i.width=i.height=null),kInteractive.openFrame(i),kInteractive.tinCan({verb:"opened",activity:"Widget: "+i.name}),setTimeout(function(){var e;kInteractive.scorm&&((e={}).id=kInteractive.getScormId("widget",i.name),e.description="Opened popup widget: "+i.name,e.learnerResponses="Opened",e.type="other",e.timestamp=new Date,kInteractive.scorm.setInteractions([e])),kInteractive.events&&kInteractive.events.add({action:"popupWidgetOpened",param:i.name,elem:t,data:i})},800))},resize:function(e){var t=kInteractive.readData(e);if(t&&"page"==t.mode&&"px"==t.widthUnit){var n=t.width,a=t.height,i=e.parentNode;if(i)if(n>i.offsetWidth){var r=i.offsetWidth/n,o=e.children.length-1;e.children[o].style.transform=e.children[o].style.webkitTransform=e.children[o].style.mozTransform="scale("+r+")",e.children[o].style.transformOrigin=e.children[o].style.webkitTransformOrigin=e.children[o].style.mozTransformOrigin="0 0",e.style.maxWidth=e.style.width,e.style.height=Math.round(a*r)+"px"}else{for(o=0;o<e.children.length;o++)e.children[o].style.transform=e.children[o].style.webkitTransform=e.children[o].style.mozTransform=e.children[o].style.transformOrigin=e.children[o].style.webkitTransformOrigin=e.children[o].style.mozTransformOrigin=null;e.style.maxWidth=null,e.style.height=a+"px"}}}},kInteractive.helpers.persister={instances:{},addInstance:function(e,t){kInteractive.helpers.persister.instances[e]=new kPersister(t)}};