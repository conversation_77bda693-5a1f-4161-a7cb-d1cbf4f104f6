/**
 * Minified by jsDelivr using UglifyJS v3.1.10.
 * Original file: /npm/html-docx-js@0.3.1/dist/html-docx.js
 * 
 * Do NOT use SRI with dynamically generated files! More information: https://www.jsdelivr.com/using-sri-with-dynamic-files
 */
!function(t){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{var e;"undefined"!=typeof window?e=window:"undefined"!=typeof global?e=global:"undefined"!=typeof self&&(e=self),e.htmlDocx=t()}}(function(){var define,module,exports;return function t(e,r,n){function i(o,s){if(!r[o]){if(!e[o]){var u="function"==typeof require&&require;if(!s&&u)return u(o,!0);if(a)return a(o,!0);throw new Error("Cannot find module '"+o+"'")}var l=r[o]={exports:{}};e[o][0].call(l.exports,function(t){var r=e[o][1][t];return i(r||t)},l,l.exports,t,e,r,n)}return r[o].exports}for(var a="function"==typeof require&&require,o=0;o<n.length;o++)i(n[o]);return i}({1:[function(t,e,r){function n(t,e,r){if(!(this instanceof n))return new n(t,e,r);var i,a=typeof t;if("number"===a)i=t>0?t>>>0:0;else if("string"===a)"base64"===e&&(t=function(t){t=function(t){return t.trim?t.trim():t.replace(/^\s+|\s+$/g,"")}(t).replace(x,"");for(;t.length%4!=0;)t+="=";return t}(t)),i=n.byteLength(t,e);else{if("object"!==a||null===t)throw new TypeError("must start with number, buffer, array or string");"Buffer"===t.type&&w(t.data)&&(t=t.data),i=+t.length>0?Math.floor(+t.length):0}if(this.length>y)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+y.toString(16)+" bytes");var o;n.TYPED_ARRAY_SUPPORT?o=n._augment(new Uint8Array(i)):((o=this).length=i,o._isBuffer=!0);var s;if(n.TYPED_ARRAY_SUPPORT&&"number"==typeof t.byteLength)o._set(t);else if(function(t){return w(t)||n.isBuffer(t)||t&&"object"==typeof t&&"number"==typeof t.length}(t))if(n.isBuffer(t))for(s=0;s<i;s++)o[s]=t.readUInt8(s);else for(s=0;s<i;s++)o[s]=(t[s]%256+256)%256;else if("string"===a)o.write(t,0,e);else if("number"===a&&!n.TYPED_ARRAY_SUPPORT&&!r)for(s=0;s<i;s++)o[s]=0;return o}function i(t,e,r,n){return m(function(t){for(var e=[],r=0;r<t.length;r++)e.push(255&t.charCodeAt(r));return e}(e),t,r,n)}function a(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;i++)n+=String.fromCharCode(t[i]);return n}function o(t,e,r){if(t%1!=0||t<0)throw new RangeError("offset is not uint");if(t+e>r)throw new RangeError("Trying to access beyond buffer length")}function s(t,e,r,i,a,o){if(!n.isBuffer(t))throw new TypeError("buffer must be a Buffer instance");if(e>a||e<o)throw new TypeError("value is out of bounds");if(r+i>t.length)throw new TypeError("index out of range")}function u(t,e,r,n){e<0&&(e=65535+e+1);for(var i=0,a=Math.min(t.length-r,2);i<a;i++)t[r+i]=(e&255<<8*(n?i:1-i))>>>8*(n?i:1-i)}function l(t,e,r,n){e<0&&(e=4294967295+e+1);for(var i=0,a=Math.min(t.length-r,4);i<a;i++)t[r+i]=e>>>8*(n?i:3-i)&255}function h(t,e,r,n,i,a){if(e>i||e<a)throw new TypeError("value is out of bounds");if(r+n>t.length)throw new TypeError("index out of range")}function f(t,e,r,n,i){return i||h(t,e,r,4,3.4028234663852886e38,-3.4028234663852886e38),b.write(t,e,r,n,23,4),r+4}function c(t,e,r,n,i){return i||h(t,e,r,8,1.7976931348623157e308,-1.7976931348623157e308),b.write(t,e,r,n,52,8),r+8}function d(t){for(var e=[],r=0;r<t.length;r++){var n=t.charCodeAt(r);if(n<=127)e.push(n);else{var i=r;n>=55296&&n<=57343&&r++;for(var a=encodeURIComponent(t.slice(i,r+1)).substr(1).split("%"),o=0;o<a.length;o++)e.push(parseInt(a[o],16))}}return e}function p(t){return g.toByteArray(t)}function m(t,e,r,n,i){i&&(n-=n%i);for(var a=0;a<n&&!(a+r>=e.length||a>=t.length);a++)e[a+r]=t[a];return a}function _(t){try{return decodeURIComponent(t)}catch(t){return String.fromCharCode(65533)}}var g=t("base64-js"),b=t("ieee754"),w=t("is-array");r.Buffer=n,r.SlowBuffer=n,r.INSPECT_MAX_BYTES=50,n.poolSize=8192;var y=1073741823;n.TYPED_ARRAY_SUPPORT=function(){try{var t=new ArrayBuffer(0),e=new Uint8Array(t);return e.foo=function(){return 42},42===e.foo()&&"function"==typeof e.subarray&&0===new Uint8Array(1).subarray(1,1).byteLength}catch(t){return!1}}(),n.isBuffer=function(t){return!(null==t||!t._isBuffer)},n.compare=function(t,e){if(!n.isBuffer(t)||!n.isBuffer(e))throw new TypeError("Arguments must be Buffers");for(var r=t.length,i=e.length,a=0,o=Math.min(r,i);a<o&&t[a]===e[a];a++);return a!==o&&(r=t[a],i=e[a]),r<i?-1:i<r?1:0},n.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"raw":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},n.concat=function(t,e){if(!w(t))throw new TypeError("Usage: Buffer.concat(list[, length])");if(0===t.length)return new n(0);if(1===t.length)return t[0];var r;if(void 0===e)for(e=0,r=0;r<t.length;r++)e+=t[r].length;var i=new n(e),a=0;for(r=0;r<t.length;r++){var o=t[r];o.copy(i,a),a+=o.length}return i},n.byteLength=function(t,e){var r;switch(t+="",e||"utf8"){case"ascii":case"binary":case"raw":r=t.length;break;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":r=2*t.length;break;case"hex":r=t.length>>>1;break;case"utf8":case"utf-8":r=d(t).length;break;case"base64":r=p(t).length;break;default:r=t.length}return r},n.prototype.length=void 0,n.prototype.parent=void 0,n.prototype.toString=function(t,e,r){var n=!1;if(e>>>=0,r=void 0===r||r===1/0?this.length:r>>>0,t||(t="utf8"),e<0&&(e=0),r>this.length&&(r=this.length),r<=e)return"";for(;;)switch(t){case"hex":return function(t,e,r){var n=t.length;(!e||e<0)&&(e=0),(!r||r<0||r>n)&&(r=n);for(var i="",a=e;a<r;a++)i+=function(t){return t<16?"0"+t.toString(16):t.toString(16)}(t[a]);return i}(this,e,r);case"utf8":case"utf-8":return function(t,e,r){var n="",i="";r=Math.min(t.length,r);for(var a=e;a<r;a++)t[a]<=127?(n+=_(i)+String.fromCharCode(t[a]),i=""):i+="%"+t[a].toString(16);return n+_(i)}(this,e,r);case"ascii":return a(this,e,r);case"binary":return function(t,e,r){return a(t,e,r)}(this,e,r);case"base64":return function(t,e,r){return 0===e&&r===t.length?g.fromByteArray(t):g.fromByteArray(t.slice(e,r))}(this,e,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(t,e,r){for(var n=t.slice(e,r),i="",a=0;a<n.length;a+=2)i+=String.fromCharCode(n[a]+256*n[a+1]);return i}(this,e,r);default:if(n)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),n=!0}},n.prototype.equals=function(t){if(!n.isBuffer(t))throw new TypeError("Argument must be a Buffer");return 0===n.compare(this,t)},n.prototype.inspect=function(){var t="",e=r.INSPECT_MAX_BYTES;return this.length>0&&(t=this.toString("hex",0,e).match(/.{2}/g).join(" "),this.length>e&&(t+=" ... ")),"<Buffer "+t+">"},n.prototype.compare=function(t){if(!n.isBuffer(t))throw new TypeError("Argument must be a Buffer");return n.compare(this,t)},n.prototype.get=function(t){return console.log(".get() is deprecated. Access using array indexes instead."),this.readUInt8(t)},n.prototype.set=function(t,e){return console.log(".set() is deprecated. Access using array indexes instead."),this.writeUInt8(t,e)},n.prototype.write=function(t,e,r,n){if(isFinite(e))isFinite(r)||(n=r,r=void 0);else{var a=n;n=e,e=r,r=a}e=Number(e)||0;var o=this.length-e;r?(r=Number(r))>o&&(r=o):r=o;var s;switch(n=String(n||"utf8").toLowerCase()){case"hex":s=function(t,e,r,n){r=Number(r)||0;var i=t.length-r;n?(n=Number(n))>i&&(n=i):n=i;var a=e.length;if(a%2!=0)throw new Error("Invalid hex string");n>a/2&&(n=a/2);for(var o=0;o<n;o++){var s=parseInt(e.substr(2*o,2),16);if(isNaN(s))throw new Error("Invalid hex string");t[r+o]=s}return o}(this,t,e,r);break;case"utf8":case"utf-8":s=function(t,e,r,n){return m(d(e),t,r,n)}(this,t,e,r);break;case"ascii":s=i(this,t,e,r);break;case"binary":s=function(t,e,r,n){return i(t,e,r,n)}(this,t,e,r);break;case"base64":s=function(t,e,r,n){return m(p(e),t,r,n)}(this,t,e,r);break;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":s=function(t,e,r,n){return m(function(t){for(var e,r,n,i=[],a=0;a<t.length;a++)e=t.charCodeAt(a),r=e>>8,n=e%256,i.push(n),i.push(r);return i}(e),t,r,n,2)}(this,t,e,r);break;default:throw new TypeError("Unknown encoding: "+n)}return s},n.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},n.prototype.slice=function(t,e){var r=this.length;if(t=~~t,e=void 0===e?r:~~e,t<0?(t+=r)<0&&(t=0):t>r&&(t=r),e<0?(e+=r)<0&&(e=0):e>r&&(e=r),e<t&&(e=t),n.TYPED_ARRAY_SUPPORT)return n._augment(this.subarray(t,e));for(var i=e-t,a=new n(i,void 0,!0),o=0;o<i;o++)a[o]=this[o+t];return a},n.prototype.readUInt8=function(t,e){return e||o(t,1,this.length),this[t]},n.prototype.readUInt16LE=function(t,e){return e||o(t,2,this.length),this[t]|this[t+1]<<8},n.prototype.readUInt16BE=function(t,e){return e||o(t,2,this.length),this[t]<<8|this[t+1]},n.prototype.readUInt32LE=function(t,e){return e||o(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},n.prototype.readUInt32BE=function(t,e){return e||o(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},n.prototype.readInt8=function(t,e){return e||o(t,1,this.length),128&this[t]?-1*(255-this[t]+1):this[t]},n.prototype.readInt16LE=function(t,e){e||o(t,2,this.length);var r=this[t]|this[t+1]<<8;return 32768&r?4294901760|r:r},n.prototype.readInt16BE=function(t,e){e||o(t,2,this.length);var r=this[t+1]|this[t]<<8;return 32768&r?4294901760|r:r},n.prototype.readInt32LE=function(t,e){return e||o(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},n.prototype.readInt32BE=function(t,e){return e||o(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},n.prototype.readFloatLE=function(t,e){return e||o(t,4,this.length),b.read(this,t,!0,23,4)},n.prototype.readFloatBE=function(t,e){return e||o(t,4,this.length),b.read(this,t,!1,23,4)},n.prototype.readDoubleLE=function(t,e){return e||o(t,8,this.length),b.read(this,t,!0,52,8)},n.prototype.readDoubleBE=function(t,e){return e||o(t,8,this.length),b.read(this,t,!1,52,8)},n.prototype.writeUInt8=function(t,e,r){return t=+t,e>>>=0,r||s(this,t,e,1,255,0),n.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),this[e]=t,e+1},n.prototype.writeUInt16LE=function(t,e,r){return t=+t,e>>>=0,r||s(this,t,e,2,65535,0),n.TYPED_ARRAY_SUPPORT?(this[e]=t,this[e+1]=t>>>8):u(this,t,e,!0),e+2},n.prototype.writeUInt16BE=function(t,e,r){return t=+t,e>>>=0,r||s(this,t,e,2,65535,0),n.TYPED_ARRAY_SUPPORT?(this[e]=t>>>8,this[e+1]=t):u(this,t,e,!1),e+2},n.prototype.writeUInt32LE=function(t,e,r){return t=+t,e>>>=0,r||s(this,t,e,4,4294967295,0),n.TYPED_ARRAY_SUPPORT?(this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=t):l(this,t,e,!0),e+4},n.prototype.writeUInt32BE=function(t,e,r){return t=+t,e>>>=0,r||s(this,t,e,4,4294967295,0),n.TYPED_ARRAY_SUPPORT?(this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=t):l(this,t,e,!1),e+4},n.prototype.writeInt8=function(t,e,r){return t=+t,e>>>=0,r||s(this,t,e,1,127,-128),n.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),t<0&&(t=255+t+1),this[e]=t,e+1},n.prototype.writeInt16LE=function(t,e,r){return t=+t,e>>>=0,r||s(this,t,e,2,32767,-32768),n.TYPED_ARRAY_SUPPORT?(this[e]=t,this[e+1]=t>>>8):u(this,t,e,!0),e+2},n.prototype.writeInt16BE=function(t,e,r){return t=+t,e>>>=0,r||s(this,t,e,2,32767,-32768),n.TYPED_ARRAY_SUPPORT?(this[e]=t>>>8,this[e+1]=t):u(this,t,e,!1),e+2},n.prototype.writeInt32LE=function(t,e,r){return t=+t,e>>>=0,r||s(this,t,e,4,2147483647,-2147483648),n.TYPED_ARRAY_SUPPORT?(this[e]=t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24):l(this,t,e,!0),e+4},n.prototype.writeInt32BE=function(t,e,r){return t=+t,e>>>=0,r||s(this,t,e,4,2147483647,-2147483648),t<0&&(t=4294967295+t+1),n.TYPED_ARRAY_SUPPORT?(this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=t):l(this,t,e,!1),e+4},n.prototype.writeFloatLE=function(t,e,r){return f(this,t,e,!0,r)},n.prototype.writeFloatBE=function(t,e,r){return f(this,t,e,!1,r)},n.prototype.writeDoubleLE=function(t,e,r){return c(this,t,e,!0,r)},n.prototype.writeDoubleBE=function(t,e,r){return c(this,t,e,!1,r)},n.prototype.copy=function(t,e,r,i){if(r||(r=0),i||0===i||(i=this.length),e||(e=0),i!==r&&0!==t.length&&0!==this.length){if(i<r)throw new TypeError("sourceEnd < sourceStart");if(e<0||e>=t.length)throw new TypeError("targetStart out of bounds");if(r<0||r>=this.length)throw new TypeError("sourceStart out of bounds");if(i<0||i>this.length)throw new TypeError("sourceEnd out of bounds");i>this.length&&(i=this.length),t.length-e<i-r&&(i=t.length-e+r);var a=i-r;if(a<1e3||!n.TYPED_ARRAY_SUPPORT)for(var o=0;o<a;o++)t[o+e]=this[o+r];else t._set(this.subarray(r,r+a),e)}},n.prototype.fill=function(t,e,r){if(t||(t=0),e||(e=0),r||(r=this.length),r<e)throw new TypeError("end < start");if(r!==e&&0!==this.length){if(e<0||e>=this.length)throw new TypeError("start out of bounds");if(r<0||r>this.length)throw new TypeError("end out of bounds");var n;if("number"==typeof t)for(n=e;n<r;n++)this[n]=t;else{var i=d(t.toString()),a=i.length;for(n=e;n<r;n++)this[n]=i[n%a]}return this}},n.prototype.toArrayBuffer=function(){if("undefined"!=typeof Uint8Array){if(n.TYPED_ARRAY_SUPPORT)return new n(this).buffer;for(var t=new Uint8Array(this.length),e=0,r=t.length;e<r;e+=1)t[e]=this[e];return t.buffer}throw new TypeError("Buffer.toArrayBuffer not supported in this browser")};var v=n.prototype;n._augment=function(t){return t.constructor=n,t._isBuffer=!0,t._get=t.get,t._set=t.set,t.get=v.get,t.set=v.set,t.write=v.write,t.toString=v.toString,t.toLocaleString=v.toString,t.toJSON=v.toJSON,t.equals=v.equals,t.compare=v.compare,t.copy=v.copy,t.slice=v.slice,t.readUInt8=v.readUInt8,t.readUInt16LE=v.readUInt16LE,t.readUInt16BE=v.readUInt16BE,t.readUInt32LE=v.readUInt32LE,t.readUInt32BE=v.readUInt32BE,t.readInt8=v.readInt8,t.readInt16LE=v.readInt16LE,t.readInt16BE=v.readInt16BE,t.readInt32LE=v.readInt32LE,t.readInt32BE=v.readInt32BE,t.readFloatLE=v.readFloatLE,t.readFloatBE=v.readFloatBE,t.readDoubleLE=v.readDoubleLE,t.readDoubleBE=v.readDoubleBE,t.writeUInt8=v.writeUInt8,t.writeUInt16LE=v.writeUInt16LE,t.writeUInt16BE=v.writeUInt16BE,t.writeUInt32LE=v.writeUInt32LE,t.writeUInt32BE=v.writeUInt32BE,t.writeInt8=v.writeInt8,t.writeInt16LE=v.writeInt16LE,t.writeInt16BE=v.writeInt16BE,t.writeInt32LE=v.writeInt32LE,t.writeInt32BE=v.writeInt32BE,t.writeFloatLE=v.writeFloatLE,t.writeFloatBE=v.writeFloatBE,t.writeDoubleLE=v.writeDoubleLE,t.writeDoubleBE=v.writeDoubleBE,t.fill=v.fill,t.inspect=v.inspect,t.toArrayBuffer=v.toArrayBuffer,t};var x=/[^+\/0-9A-z]/g},{"base64-js":2,ieee754:3,"is-array":4}],2:[function(t,e,r){var n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";!function(t){"use strict";function e(t){var e=t.charCodeAt(0);return e===i?62:e===a?63:e<o?-1:e<o+10?e-o+26+26:e<u+26?e-u:e<s+26?e-s+26:void 0}var r="undefined"!=typeof Uint8Array?Uint8Array:Array,i="+".charCodeAt(0),a="/".charCodeAt(0),o="0".charCodeAt(0),s="a".charCodeAt(0),u="A".charCodeAt(0);t.toByteArray=function(t){function n(t){l[f++]=t}var i,a,o,s,u,l;if(t.length%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var h=t.length;u="="===t.charAt(h-2)?2:"="===t.charAt(h-1)?1:0,l=new r(3*t.length/4-u),o=u>0?t.length-4:t.length;var f=0;for(i=0,a=0;i<o;i+=4,a+=3)n((16711680&(s=e(t.charAt(i))<<18|e(t.charAt(i+1))<<12|e(t.charAt(i+2))<<6|e(t.charAt(i+3))))>>16),n((65280&s)>>8),n(255&s);return 2===u?n(255&(s=e(t.charAt(i))<<2|e(t.charAt(i+1))>>4)):1===u&&(n((s=e(t.charAt(i))<<10|e(t.charAt(i+1))<<4|e(t.charAt(i+2))>>2)>>8&255),n(255&s)),l},t.fromByteArray=function(t){function e(t){return n.charAt(t)}function r(t){return e(t>>18&63)+e(t>>12&63)+e(t>>6&63)+e(63&t)}var i,a,o,s=t.length%3,u="";for(i=0,o=t.length-s;i<o;i+=3)u+=r(a=(t[i]<<16)+(t[i+1]<<8)+t[i+2]);switch(s){case 1:u+=e((a=t[t.length-1])>>2),u+=e(a<<4&63),u+="==";break;case 2:u+=e((a=(t[t.length-2]<<8)+t[t.length-1])>>10),u+=e(a>>4&63),u+=e(a<<2&63),u+="="}return u}}(void 0===r?this.base64js={}:r)},{}],3:[function(t,e,r){r.read=function(t,e,r,n,i){var a,o,s=8*i-n-1,u=(1<<s)-1,l=u>>1,h=-7,f=r?i-1:0,c=r?-1:1,d=t[e+f];for(f+=c,a=d&(1<<-h)-1,d>>=-h,h+=s;h>0;a=256*a+t[e+f],f+=c,h-=8);for(o=a&(1<<-h)-1,a>>=-h,h+=n;h>0;o=256*o+t[e+f],f+=c,h-=8);if(0===a)a=1-l;else{if(a===u)return o?NaN:1/0*(d?-1:1);o+=Math.pow(2,n),a-=l}return(d?-1:1)*o*Math.pow(2,a-n)},r.write=function(t,e,r,n,i,a){var o,s,u,l=8*a-i-1,h=(1<<l)-1,f=h>>1,c=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,d=n?0:a-1,p=n?1:-1,m=e<0||0===e&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(s=isNaN(e)?1:0,o=h):(o=Math.floor(Math.log(e)/Math.LN2),e*(u=Math.pow(2,-o))<1&&(o--,u*=2),(e+=o+f>=1?c/u:c*Math.pow(2,1-f))*u>=2&&(o++,u/=2),o+f>=h?(s=0,o=h):o+f>=1?(s=(e*u-1)*Math.pow(2,i),o+=f):(s=e*Math.pow(2,f-1)*Math.pow(2,i),o=0));i>=8;t[r+d]=255&s,d+=p,s/=256,i-=8);for(o=o<<i|s,l+=i;l>0;t[r+d]=255&o,d+=p,o/=256,l-=8);t[r+d-p]|=128*m}},{}],4:[function(t,e,r){var n=Array.isArray,i=Object.prototype.toString;e.exports=n||function(t){return!!t&&"[object Array]"==i.call(t)}},{}],5:[function(t,e,r){"use strict";function n(t){if(t){this.data=t,this.length=this.data.length,this.index=0,this.zero=0;for(var e=0;e<this.data.length;e++)t[e]=255&t[e]}}var i=t("./dataReader");(n.prototype=new i).byteAt=function(t){return this.data[this.zero+t]},n.prototype.lastIndexOfSignature=function(t){for(var e=t.charCodeAt(0),r=t.charCodeAt(1),n=t.charCodeAt(2),i=t.charCodeAt(3),a=this.length-4;a>=0;--a)if(this.data[a]===e&&this.data[a+1]===r&&this.data[a+2]===n&&this.data[a+3]===i)return a-this.zero;return-1},n.prototype.readData=function(t){if(this.checkOffset(t),0===t)return[];var e=this.data.slice(this.zero+this.index,this.zero+this.index+t);return this.index+=t,e},e.exports=n},{"./dataReader":10}],6:[function(t,e,r){"use strict";var n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";r.encode=function(t,e){for(var r,i,a,o,s,u,l,h="",f=0;f<t.length;)o=(r=t.charCodeAt(f++))>>2,s=(3&r)<<4|(i=t.charCodeAt(f++))>>4,u=(15&i)<<2|(a=t.charCodeAt(f++))>>6,l=63&a,isNaN(i)?u=l=64:isNaN(a)&&(l=64),h=h+n.charAt(o)+n.charAt(s)+n.charAt(u)+n.charAt(l);return h},r.decode=function(t,e){var r,i,a,o,s,u,l="",h=0;for(t=t.replace(/[^A-Za-z0-9\+\/\=]/g,"");h<t.length;)r=n.indexOf(t.charAt(h++))<<2|(o=n.indexOf(t.charAt(h++)))>>4,i=(15&o)<<4|(s=n.indexOf(t.charAt(h++)))>>2,a=(3&s)<<6|(u=n.indexOf(t.charAt(h++))),l+=String.fromCharCode(r),64!=s&&(l+=String.fromCharCode(i)),64!=u&&(l+=String.fromCharCode(a));return l}},{}],7:[function(t,e,r){"use strict";function n(){this.compressedSize=0,this.uncompressedSize=0,this.crc32=0,this.compressionMethod=null,this.compressedContent=null}n.prototype={getContent:function(){return null},getCompressedContent:function(){return null}},e.exports=n},{}],8:[function(t,e,r){"use strict";r.STORE={magic:"\0\0",compress:function(t,e){return t},uncompress:function(t){return t},compressInputType:null,uncompressInputType:null},r.DEFLATE=t("./flate")},{"./flate":13}],9:[function(t,e,r){"use strict";var n=t("./utils"),i=[0,1996959894,3993919788,2567524794,124634137,1886057615,3915621685,2657392035,249268274,2044508324,3772115230,2547177864,162941995,2125561021,3887607047,2428444049,498536548,1789927666,4089016648,2227061214,450548861,1843258603,4107580753,2211677639,325883990,1684777152,4251122042,2321926636,335633487,1661365465,4195302755,2366115317,997073096,1281953886,3579855332,2724688242,1006888145,1258607687,3524101629,2768942443,901097722,1119000684,3686517206,2898065728,853044451,1172266101,3705015759,2882616665,651767980,1373503546,3369554304,3218104598,565507253,1454621731,3485111705,3099436303,671266974,1594198024,3322730930,2970347812,795835527,1483230225,3244367275,3060149565,1994146192,31158534,2563907772,4023717930,1907459465,112637215,2680153253,3904427059,2013776290,251722036,2517215374,3775830040,2137656763,141376813,2439277719,3865271297,1802195444,476864866,2238001368,4066508878,1812370925,453092731,2181625025,4111451223,1706088902,314042704,2344532202,4240017532,1658658271,366619977,2362670323,4224994405,1303535960,984961486,2747007092,3569037538,1256170817,1037604311,2765210733,3554079995,1131014506,879679996,2909243462,3663771856,1141124467,855842277,2852801631,3708648649,1342533948,654459306,3188396048,3373015174,1466479909,544179635,3110523913,3462522015,1591671054,702138776,2966460450,3352799412,1504918807,783551873,3082640443,3233442989,3988292384,2596254646,62317068,1957810842,3939845945,2647816111,81470997,1943803523,3814918930,2489596804,225274430,2053790376,3826175755,2466906013,167816743,2097651377,4027552580,2265490386,503444072,1762050814,4150417245,2154129355,426522225,1852507879,4275313526,2312317920,282753626,1742555852,4189708143,2394877945,397917763,1622183637,3604390888,2714866558,953729732,1340076626,3518719985,2797360999,1068828381,1219638859,3624741850,2936675148,906185462,1090812512,3747672003,2825379669,829329135,1181335161,3412177804,3160834842,628085408,1382605366,3423369109,3138078467,570562233,1426400815,3317316542,2998733608,733239954,1555261956,3268935591,3050360625,752459403,1541320221,2607071920,3965973030,1969922972,40735498,2617837225,3943577151,1913087877,83908371,2512341634,3803740692,2075208622,213261112,2463272603,3855990285,2094854071,198958881,2262029012,4057260610,1759359992,534414190,2176718541,4139329115,1873836001,414664567,2282248934,4279200368,1711684554,285281116,2405801727,4167216745,1634467795,376229701,2685067896,3608007406,1308918612,956543938,2808555105,3495958263,1231636301,1047427035,2932959818,3654703836,1088359270,936918e3,2847714899,3736837829,1202900863,817233897,3183342108,3401237130,1404277552,615818150,3134207493,3453421203,1423857449,601450431,3009837614,3294710456,1567103746,711928724,3020668471,3272380065,1510334235,755167117];e.exports=function(t,e){if(void 0===t||!t.length)return 0;var r="string"!==n.getTypeOf(t);void 0===e&&(e=0);var a=0;e^=-1;for(var o=0,s=t.length;o<s;o++)a=r?t[o]:t.charCodeAt(o),e=e>>>8^i[255&(e^a)];return-1^e}},{"./utils":26}],10:[function(t,e,r){"use strict";function n(t){this.data=null,this.length=0,this.index=0,this.zero=0}var i=t("./utils");n.prototype={checkOffset:function(t){this.checkIndex(this.index+t)},checkIndex:function(t){if(this.length<this.zero+t||t<0)throw new Error("End of data reached (data length = "+this.length+", asked index = "+t+"). Corrupted zip ?")},setIndex:function(t){this.checkIndex(t),this.index=t},skip:function(t){this.setIndex(this.index+t)},byteAt:function(t){},readInt:function(t){var e,r=0;for(this.checkOffset(t),e=this.index+t-1;e>=this.index;e--)r=(r<<8)+this.byteAt(e);return this.index+=t,r},readString:function(t){return i.transformTo("string",this.readData(t))},readData:function(t){},lastIndexOfSignature:function(t){},readDate:function(){var t=this.readInt(4);return new Date(1980+(t>>25&127),(t>>21&15)-1,t>>16&31,t>>11&31,t>>5&63,(31&t)<<1)}},e.exports=n},{"./utils":26}],11:[function(t,e,r){"use strict";r.base64=!1,r.binary=!1,r.dir=!1,r.createFolders=!1,r.date=null,r.compression=null,r.compressionOptions=null,r.comment=null,r.unixPermissions=null,r.dosPermissions=null},{}],12:[function(t,e,r){"use strict";var n=t("./utils");r.string2binary=function(t){return n.string2binary(t)},r.string2Uint8Array=function(t){return n.transformTo("uint8array",t)},r.uint8Array2String=function(t){return n.transformTo("string",t)},r.string2Blob=function(t){var e=n.transformTo("arraybuffer",t);return n.arrayBuffer2Blob(e)},r.arrayBuffer2Blob=function(t){return n.arrayBuffer2Blob(t)},r.transformTo=function(t,e){return n.transformTo(t,e)},r.getTypeOf=function(t){return n.getTypeOf(t)},r.checkSupport=function(t){return n.checkSupport(t)},r.MAX_VALUE_16BITS=n.MAX_VALUE_16BITS,r.MAX_VALUE_32BITS=n.MAX_VALUE_32BITS,r.pretty=function(t){return n.pretty(t)},r.findCompression=function(t){return n.findCompression(t)},r.isRegExp=function(t){return n.isRegExp(t)}},{"./utils":26}],13:[function(t,e,r){"use strict";var n="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Uint32Array,i=t("pako");r.uncompressInputType=n?"uint8array":"array",r.compressInputType=n?"uint8array":"array",r.magic="\b\0",r.compress=function(t,e){return i.deflateRaw(t,{level:e.level||-1})},r.uncompress=function(t){return i.inflateRaw(t)}},{pako:29}],14:[function(t,e,r){"use strict";function n(t,e){if(!(this instanceof n))return new n(t,e);this.files={},this.comment=null,this.root="",t&&this.load(t,e),this.clone=function(){var t=new n;for(var e in this)"function"!=typeof this[e]&&(t[e]=this[e]);return t}}var i=t("./base64");(n.prototype=t("./object")).load=t("./load"),n.support=t("./support"),n.defaults=t("./defaults"),n.utils=t("./deprecatedPublicUtils"),n.base64={encode:function(t){return i.encode(t)},decode:function(t){return i.decode(t)}},n.compressions=t("./compressions"),e.exports=n},{"./base64":6,"./compressions":8,"./defaults":11,"./deprecatedPublicUtils":12,"./load":15,"./object":18,"./support":22}],15:[function(t,e,r){"use strict";var n=t("./base64"),i=t("./utf8"),a=t("./utils"),o=t("./zipEntries");e.exports=function(t,e){var r,s,u,l;for((e=a.extend(e||{},{base64:!1,checkCRC32:!1,optimizedBinaryString:!1,createFolders:!1,decodeFileName:i.utf8decode})).base64&&(t=n.decode(t)),r=(s=new o(t,e)).files,u=0;u<r.length;u++)l=r[u],this.file(l.fileNameStr,l.decompressed,{binary:!0,optimizedBinaryString:!0,date:l.date,dir:l.dir,comment:l.fileCommentStr.length?l.fileCommentStr:null,unixPermissions:l.unixPermissions,dosPermissions:l.dosPermissions,createFolders:e.createFolders});return s.zipComment.length&&(this.comment=s.zipComment),this}},{"./base64":6,"./utf8":25,"./utils":26,"./zipEntries":27}],16:[function(t,e,r){(function(t){"use strict";e.exports=function(e,r){return new t(e,r)},e.exports.test=function(e){return t.isBuffer(e)}}).call(this,t("buffer").Buffer)},{buffer:1}],17:[function(t,e,r){"use strict";function n(t){this.data=t,this.length=this.data.length,this.index=0,this.zero=0}var i=t("./uint8ArrayReader");(n.prototype=new i).readData=function(t){this.checkOffset(t);var e=this.data.slice(this.zero+this.index,this.zero+this.index+t);return this.index+=t,e},e.exports=n},{"./uint8ArrayReader":23}],18:[function(t,e,r){"use strict";var n=t("./support"),i=t("./utils"),a=t("./crc32"),o=t("./signature"),s=t("./defaults"),u=t("./base64"),l=t("./compressions"),h=t("./compressedObject"),f=t("./nodeBuffer"),c=t("./utf8"),d=t("./stringWriter"),p=t("./uint8ArrayWriter"),m=function(t){if(t._data instanceof h&&(t._data=t._data.getContent(),t.options.binary=!0,t.options.base64=!1,"uint8array"===i.getTypeOf(t._data))){var e=t._data;t._data=new Uint8Array(e.length),0!==e.length&&t._data.set(e,0)}return t._data},_=function(t){var e=m(t);return"string"===i.getTypeOf(e)?!t.options.binary&&n.nodebuffer?f(e,"utf-8"):t.asBinary():e},g=function(t){var e=m(this);return null===e||void 0===e?"":(this.options.base64&&(e=u.decode(e)),e=t&&this.options.binary?z.utf8decode(e):i.transformTo("string",e),t||this.options.binary||(e=i.transformTo("string",z.utf8encode(e))),e)},b=function(t,e,r){this.name=t,this.dir=r.dir,this.date=r.date,this.comment=r.comment,this.unixPermissions=r.unixPermissions,this.dosPermissions=r.dosPermissions,this._data=e,this.options=r,this._initialMetadata={dir:r.dir,date:r.date}};b.prototype={asText:function(){return g.call(this,!0)},asBinary:function(){return g.call(this,!1)},asNodeBuffer:function(){var t=_(this);return i.transformTo("nodebuffer",t)},asUint8Array:function(){var t=_(this);return i.transformTo("uint8array",t)},asArrayBuffer:function(){return this.asUint8Array().buffer}};var w=function(t,e){var r,n="";for(r=0;r<e;r++)n+=String.fromCharCode(255&t),t>>>=8;return n},y=function(t,e,r){var n,a=i.getTypeOf(e);if("string"==typeof(r=function(t){return!0!==(t=t||{}).base64||null!==t.binary&&void 0!==t.binary||(t.binary=!0),t=i.extend(t,s),t.date=t.date||new Date,null!==t.compression&&(t.compression=t.compression.toUpperCase()),t}(r)).unixPermissions&&(r.unixPermissions=parseInt(r.unixPermissions,8)),r.unixPermissions&&16384&r.unixPermissions&&(r.dir=!0),r.dosPermissions&&16&r.dosPermissions&&(r.dir=!0),r.dir&&(t=x(t)),r.createFolders&&(n=v(t))&&k.call(this,n,!0),r.dir||null===e||void 0===e)r.base64=!1,r.binary=!1,e=null,a=null;else if("string"===a)r.binary&&!r.base64&&!0!==r.optimizedBinaryString&&(e=i.string2binary(e));else{if(r.base64=!1,r.binary=!0,!(a||e instanceof h))throw new Error("The data of '"+t+"' is in an unsupported format !");"arraybuffer"===a&&(e=i.transformTo("uint8array",e))}var o=new b(t,e,r);return this.files[t]=o,o},v=function(t){"/"==t.slice(-1)&&(t=t.substring(0,t.length-1));var e=t.lastIndexOf("/");return e>0?t.substring(0,e):""},x=function(t){return"/"!=t.slice(-1)&&(t+="/"),t},k=function(t,e){return e=void 0!==e&&e,t=x(t),this.files[t]||y.call(this,t,null,{dir:!0,createFolders:e}),this.files[t]},E=function(t,e,r){var n,o=new h;return t._data instanceof h?(o.uncompressedSize=t._data.uncompressedSize,o.crc32=t._data.crc32,0===o.uncompressedSize||t.dir?(e=l.STORE,o.compressedContent="",o.crc32=0):t._data.compressionMethod===e.magic?o.compressedContent=t._data.getCompressedContent():(n=t._data.getContent(),o.compressedContent=e.compress(i.transformTo(e.compressInputType,n),r))):((n=_(t))&&0!==n.length&&!t.dir||(e=l.STORE,n=""),o.uncompressedSize=n.length,o.crc32=a(n),o.compressedContent=e.compress(i.transformTo(e.compressInputType,n),r)),o.compressedSize=o.compressedContent.length,o.compressionMethod=e.magic,o},A=function(t,e,r,n,s,u){r.compressedContent;var l,h,f,d,p=u!==c.utf8encode,m=i.transformTo("string",u(e.name)),_=i.transformTo("string",c.utf8encode(e.name)),g=e.comment||"",b=i.transformTo("string",u(g)),y=i.transformTo("string",c.utf8encode(g)),v=_.length!==e.name.length,x=y.length!==g.length,k=e.options,E="",A="",z="";f=e._initialMetadata.dir!==e.dir?e.dir:k.dir,d=e._initialMetadata.date!==e.date?e.date:k.date;var I=0,C=0;f&&(I|=16),"UNIX"===s?(C=798,I|=function(t,e){var r=t;return t||(r=e?16893:33204),(65535&r)<<16}(e.unixPermissions,f)):(C=20,I|=function(t,e){return 63&(t||0)}(e.dosPermissions)),l=d.getHours(),l<<=6,l|=d.getMinutes(),l<<=5,l|=d.getSeconds()/2,h=d.getFullYear()-1980,h<<=4,h|=d.getMonth()+1,h<<=5,h|=d.getDate(),v&&(A=w(1,1)+w(a(m),4)+_,E+="up"+w(A.length,2)+A),x&&(z=w(1,1)+w(this.crc32(b),4)+y,E+="uc"+w(z.length,2)+z);var S="";S+="\n\0",S+=p||!v&&!x?"\0\0":"\0\b",S+=r.compressionMethod,S+=w(l,2),S+=w(h,2),S+=w(r.crc32,4),S+=w(r.compressedSize,4),S+=w(r.uncompressedSize,4),S+=w(m.length,2),S+=w(E.length,2);return{fileRecord:o.LOCAL_FILE_HEADER+S+m+E,dirRecord:o.CENTRAL_FILE_HEADER+w(C,2)+S+w(b.length,2)+"\0\0\0\0"+w(I,4)+w(n,4)+m+E+b,compressedObject:r}},z={load:function(t,e){throw new Error("Load method is not defined. Is the file jszip-load.js included ?")},filter:function(t){var e,r,n,a,o=[];for(e in this.files)this.files.hasOwnProperty(e)&&(n=this.files[e],a=new b(n.name,n._data,i.extend(n.options)),r=e.slice(this.root.length,e.length),e.slice(0,this.root.length)===this.root&&t(r,a)&&o.push(a));return o},file:function(t,e,r){if(1===arguments.length){if(i.isRegExp(t)){var n=t;return this.filter(function(t,e){return!e.dir&&n.test(t)})}return this.filter(function(e,r){return!r.dir&&e===t})[0]||null}return t=this.root+t,y.call(this,t,e,r),this},folder:function(t){if(!t)return this;if(i.isRegExp(t))return this.filter(function(e,r){return r.dir&&t.test(e)});var e=this.root+t,r=k.call(this,e),n=this.clone();return n.root=r.name,n},remove:function(t){t=this.root+t;var e=this.files[t];if(e||("/"!=t.slice(-1)&&(t+="/"),e=this.files[t]),e&&!e.dir)delete this.files[t];else for(var r=this.filter(function(e,r){return r.name.slice(0,t.length)===t}),n=0;n<r.length;n++)delete this.files[r[n].name];return this},generate:function(t){t=i.extend(t||{},{base64:!0,compression:"STORE",compressionOptions:null,type:"base64",platform:"DOS",comment:null,mimeType:"application/zip",encodeFileName:c.utf8encode}),i.checkSupport(t.type),"darwin"!==t.platform&&"freebsd"!==t.platform&&"linux"!==t.platform&&"sunos"!==t.platform||(t.platform="UNIX"),"win32"===t.platform&&(t.platform="DOS");var e,r,n=[],a=0,s=0,h=i.transformTo("string",t.encodeFileName(t.comment||this.comment||""));for(var f in this.files)if(this.files.hasOwnProperty(f)){var m=this.files[f],_=m.options.compression||t.compression.toUpperCase(),g=l[_];if(!g)throw new Error(_+" is not a valid compression method !");var b=m.options.compressionOptions||t.compressionOptions||{},y=E.call(this,m,g,b),v=A.call(this,f,m,y,a,t.platform,t.encodeFileName);a+=v.fileRecord.length+y.compressedSize,s+=v.dirRecord.length,n.push(v)}var x="";x=o.CENTRAL_DIRECTORY_END+"\0\0\0\0"+w(n.length,2)+w(n.length,2)+w(s,4)+w(a,4)+w(h.length,2)+h;var k=t.type.toLowerCase();for(e="uint8array"===k||"arraybuffer"===k||"blob"===k||"nodebuffer"===k?new p(a+s+x.length):new d(a+s+x.length),r=0;r<n.length;r++)e.append(n[r].fileRecord),e.append(n[r].compressedObject.compressedContent);for(r=0;r<n.length;r++)e.append(n[r].dirRecord);e.append(x);var z=e.finalize();switch(t.type.toLowerCase()){case"uint8array":case"arraybuffer":case"nodebuffer":return i.transformTo(t.type.toLowerCase(),z);case"blob":return i.arrayBuffer2Blob(i.transformTo("arraybuffer",z),t.mimeType);case"base64":return t.base64?u.encode(z):z;default:return z}},crc32:function(t,e){return a(t,e)},utf8encode:function(t){return i.transformTo("string",c.utf8encode(t))},utf8decode:function(t){return c.utf8decode(t)}};e.exports=z},{"./base64":6,"./compressedObject":7,"./compressions":8,"./crc32":9,"./defaults":11,"./nodeBuffer":16,"./signature":19,"./stringWriter":21,"./support":22,"./uint8ArrayWriter":24,"./utf8":25,"./utils":26}],19:[function(t,e,r){"use strict";r.LOCAL_FILE_HEADER="PK",r.CENTRAL_FILE_HEADER="PK",r.CENTRAL_DIRECTORY_END="PK",r.ZIP64_CENTRAL_DIRECTORY_LOCATOR="PK",r.ZIP64_CENTRAL_DIRECTORY_END="PK",r.DATA_DESCRIPTOR="PK\b"},{}],20:[function(t,e,r){"use strict";function n(t,e){this.data=t,e||(this.data=a.string2binary(this.data)),this.length=this.data.length,this.index=0,this.zero=0}var i=t("./dataReader"),a=t("./utils");(n.prototype=new i).byteAt=function(t){return this.data.charCodeAt(this.zero+t)},n.prototype.lastIndexOfSignature=function(t){return this.data.lastIndexOf(t)-this.zero},n.prototype.readData=function(t){this.checkOffset(t);var e=this.data.slice(this.zero+this.index,this.zero+this.index+t);return this.index+=t,e},e.exports=n},{"./dataReader":10,"./utils":26}],21:[function(t,e,r){"use strict";var n=t("./utils"),i=function(){this.data=[]};i.prototype={append:function(t){t=n.transformTo("string",t),this.data.push(t)},finalize:function(){return this.data.join("")}},e.exports=i},{"./utils":26}],22:[function(t,e,r){(function(t){"use strict";if(r.base64=!0,r.array=!0,r.string=!0,r.arraybuffer="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array,r.nodebuffer=void 0!==t,r.uint8array="undefined"!=typeof Uint8Array,"undefined"==typeof ArrayBuffer)r.blob=!1;else{var e=new ArrayBuffer(0);try{r.blob=0===new Blob([e],{type:"application/zip"}).size}catch(t){try{var n=new(window.BlobBuilder||window.WebKitBlobBuilder||window.MozBlobBuilder||window.MSBlobBuilder);n.append(e),r.blob=0===n.getBlob("application/zip").size}catch(t){r.blob=!1}}}}).call(this,t("buffer").Buffer)},{buffer:1}],23:[function(t,e,r){"use strict";function n(t){t&&(this.data=t,this.length=this.data.length,this.index=0,this.zero=0)}var i=t("./arrayReader");(n.prototype=new i).readData=function(t){if(this.checkOffset(t),0===t)return new Uint8Array(0);var e=this.data.subarray(this.zero+this.index,this.zero+this.index+t);return this.index+=t,e},e.exports=n},{"./arrayReader":5}],24:[function(t,e,r){"use strict";var n=t("./utils"),i=function(t){this.data=new Uint8Array(t),this.index=0};i.prototype={append:function(t){0!==t.length&&(t=n.transformTo("uint8array",t),this.data.set(t,this.index),this.index+=t.length)},finalize:function(){return this.data}},e.exports=i},{"./utils":26}],25:[function(t,e,r){"use strict";for(var n=t("./utils"),i=t("./support"),a=t("./nodeBuffer"),o=new Array(256),s=0;s<256;s++)o[s]=s>=252?6:s>=248?5:s>=240?4:s>=224?3:s>=192?2:1;o[254]=o[254]=1;var u=function(t,e){var r;for((e=e||t.length)>t.length&&(e=t.length),r=e-1;r>=0&&128==(192&t[r]);)r--;return r<0?e:0===r?e:r+o[t[r]]>e?r:e},l=function(t){var e,r,i,a,s=t.length,u=new Array(2*s);for(r=0,e=0;e<s;)if((i=t[e++])<128)u[r++]=i;else if((a=o[i])>4)u[r++]=65533,e+=a-1;else{for(i&=2===a?31:3===a?15:7;a>1&&e<s;)i=i<<6|63&t[e++],a--;a>1?u[r++]=65533:i<65536?u[r++]=i:(i-=65536,u[r++]=55296|i>>10&1023,u[r++]=56320|1023&i)}return u.length!==r&&(u.subarray?u=u.subarray(0,r):u.length=r),n.applyFromCharCode(u)};r.utf8encode=function(t){return i.nodebuffer?a(t,"utf-8"):function(t){var e,r,n,a,o,s=t.length,u=0;for(a=0;a<s;a++)55296==(64512&(r=t.charCodeAt(a)))&&a+1<s&&56320==(64512&(n=t.charCodeAt(a+1)))&&(r=65536+(r-55296<<10)+(n-56320),a++),u+=r<128?1:r<2048?2:r<65536?3:4;for(e=i.uint8array?new Uint8Array(u):new Array(u),o=0,a=0;o<u;a++)55296==(64512&(r=t.charCodeAt(a)))&&a+1<s&&56320==(64512&(n=t.charCodeAt(a+1)))&&(r=65536+(r-55296<<10)+(n-56320),a++),r<128?e[o++]=r:r<2048?(e[o++]=192|r>>>6,e[o++]=128|63&r):r<65536?(e[o++]=224|r>>>12,e[o++]=128|r>>>6&63,e[o++]=128|63&r):(e[o++]=240|r>>>18,e[o++]=128|r>>>12&63,e[o++]=128|r>>>6&63,e[o++]=128|63&r);return e}(t)},r.utf8decode=function(t){if(i.nodebuffer)return n.transformTo("nodebuffer",t).toString("utf-8");for(var e=[],r=0,a=(t=n.transformTo(i.uint8array?"uint8array":"array",t)).length;r<a;){var o=u(t,Math.min(r+65536,a));i.uint8array?e.push(l(t.subarray(r,o))):e.push(l(t.slice(r,o))),r=o}return e.join("")}},{"./nodeBuffer":16,"./support":22,"./utils":26}],26:[function(t,e,r){"use strict";function n(t){return t}function i(t,e){for(var r=0;r<t.length;++r)e[r]=255&t.charCodeAt(r);return e}function a(t){var e=65536,n=[],i=t.length,a=r.getTypeOf(t),o=0,s=!0;try{switch(a){case"uint8array":String.fromCharCode.apply(null,new Uint8Array(0));break;case"nodebuffer":String.fromCharCode.apply(null,l(0))}}catch(t){s=!1}if(!s){for(var u="",h=0;h<t.length;h++)u+=String.fromCharCode(t[h]);return u}for(;o<i&&e>1;)try{"array"===a||"nodebuffer"===a?n.push(String.fromCharCode.apply(null,t.slice(o,Math.min(o+e,i)))):n.push(String.fromCharCode.apply(null,t.subarray(o,Math.min(o+e,i)))),o+=e}catch(t){e=Math.floor(e/2)}return n.join("")}function o(t,e){for(var r=0;r<t.length;r++)e[r]=t[r];return e}var s=t("./support"),u=t("./compressions"),l=t("./nodeBuffer");r.string2binary=function(t){for(var e="",r=0;r<t.length;r++)e+=String.fromCharCode(255&t.charCodeAt(r));return e},r.arrayBuffer2Blob=function(t,e){r.checkSupport("blob"),e=e||"application/zip";try{return new Blob([t],{type:e})}catch(r){try{var n=new(window.BlobBuilder||window.WebKitBlobBuilder||window.MozBlobBuilder||window.MSBlobBuilder);return n.append(t),n.getBlob(e)}catch(t){throw new Error("Bug : can't construct the Blob.")}}},r.applyFromCharCode=a;var h={};h.string={string:n,array:function(t){return i(t,new Array(t.length))},arraybuffer:function(t){return h.string.uint8array(t).buffer},uint8array:function(t){return i(t,new Uint8Array(t.length))},nodebuffer:function(t){return i(t,l(t.length))}},h.array={string:a,array:n,arraybuffer:function(t){return new Uint8Array(t).buffer},uint8array:function(t){return new Uint8Array(t)},nodebuffer:function(t){return l(t)}},h.arraybuffer={string:function(t){return a(new Uint8Array(t))},array:function(t){return o(new Uint8Array(t),new Array(t.byteLength))},arraybuffer:n,uint8array:function(t){return new Uint8Array(t)},nodebuffer:function(t){return l(new Uint8Array(t))}},h.uint8array={string:a,array:function(t){return o(t,new Array(t.length))},arraybuffer:function(t){return t.buffer},uint8array:n,nodebuffer:function(t){return l(t)}},h.nodebuffer={string:a,array:function(t){return o(t,new Array(t.length))},arraybuffer:function(t){return h.nodebuffer.uint8array(t).buffer},uint8array:function(t){return o(t,new Uint8Array(t.length))},nodebuffer:n},r.transformTo=function(t,e){if(e||(e=""),!t)return e;r.checkSupport(t);var n=r.getTypeOf(e);return h[n][t](e)},r.getTypeOf=function(t){return"string"==typeof t?"string":"[object Array]"===Object.prototype.toString.call(t)?"array":s.nodebuffer&&l.test(t)?"nodebuffer":s.uint8array&&t instanceof Uint8Array?"uint8array":s.arraybuffer&&t instanceof ArrayBuffer?"arraybuffer":void 0},r.checkSupport=function(t){if(!s[t.toLowerCase()])throw new Error(t+" is not supported by this browser")},r.MAX_VALUE_16BITS=65535,r.MAX_VALUE_32BITS=-1,r.pretty=function(t){var e,r,n="";for(r=0;r<(t||"").length;r++)n+="\\x"+((e=t.charCodeAt(r))<16?"0":"")+e.toString(16).toUpperCase();return n},r.findCompression=function(t){for(var e in u)if(u.hasOwnProperty(e)&&u[e].magic===t)return u[e];return null},r.isRegExp=function(t){return"[object RegExp]"===Object.prototype.toString.call(t)},r.extend=function(){var t,e,r={};for(t=0;t<arguments.length;t++)for(e in arguments[t])arguments[t].hasOwnProperty(e)&&void 0===r[e]&&(r[e]=arguments[t][e]);return r}},{"./compressions":8,"./nodeBuffer":16,"./support":22}],27:[function(t,e,r){"use strict";function n(t,e){this.files=[],this.loadOptions=e,t&&this.load(t)}var i=t("./stringReader"),a=t("./nodeBufferReader"),o=t("./uint8ArrayReader"),s=t("./arrayReader"),u=t("./utils"),l=t("./signature"),h=t("./zipEntry"),f=t("./support");t("./object");n.prototype={checkSignature:function(t){var e=this.reader.readString(4);if(e!==t)throw new Error("Corrupted zip or bug : unexpected signature ("+u.pretty(e)+", expected "+u.pretty(t)+")")},isSignature:function(t,e){var r=this.reader.index;this.reader.setIndex(t);var n=this.reader.readString(4)===e;return this.reader.setIndex(r),n},readBlockEndOfCentral:function(){this.diskNumber=this.reader.readInt(2),this.diskWithCentralDirStart=this.reader.readInt(2),this.centralDirRecordsOnThisDisk=this.reader.readInt(2),this.centralDirRecords=this.reader.readInt(2),this.centralDirSize=this.reader.readInt(4),this.centralDirOffset=this.reader.readInt(4),this.zipCommentLength=this.reader.readInt(2);var t=this.reader.readData(this.zipCommentLength),e=f.uint8array?"uint8array":"array",r=u.transformTo(e,t);this.zipComment=this.loadOptions.decodeFileName(r)},readBlockZip64EndOfCentral:function(){this.zip64EndOfCentralSize=this.reader.readInt(8),this.versionMadeBy=this.reader.readString(2),this.versionNeeded=this.reader.readInt(2),this.diskNumber=this.reader.readInt(4),this.diskWithCentralDirStart=this.reader.readInt(4),this.centralDirRecordsOnThisDisk=this.reader.readInt(8),this.centralDirRecords=this.reader.readInt(8),this.centralDirSize=this.reader.readInt(8),this.centralDirOffset=this.reader.readInt(8),this.zip64ExtensibleData={};for(var t,e,r,n=this.zip64EndOfCentralSize-44;0<n;)t=this.reader.readInt(2),e=this.reader.readInt(4),r=this.reader.readString(e),this.zip64ExtensibleData[t]={id:t,length:e,value:r}},readBlockZip64EndOfCentralLocator:function(){if(this.diskWithZip64CentralDirStart=this.reader.readInt(4),this.relativeOffsetEndOfZip64CentralDir=this.reader.readInt(8),this.disksCount=this.reader.readInt(4),this.disksCount>1)throw new Error("Multi-volumes zip are not supported")},readLocalFiles:function(){var t,e;for(t=0;t<this.files.length;t++)e=this.files[t],this.reader.setIndex(e.localHeaderOffset),this.checkSignature(l.LOCAL_FILE_HEADER),e.readLocalPart(this.reader),e.handleUTF8(),e.processAttributes()},readCentralDir:function(){var t;for(this.reader.setIndex(this.centralDirOffset);this.reader.readString(4)===l.CENTRAL_FILE_HEADER;)(t=new h({zip64:this.zip64},this.loadOptions)).readCentralPart(this.reader),this.files.push(t);if(this.centralDirRecords!==this.files.length&&0!==this.centralDirRecords&&0===this.files.length)throw new Error("Corrupted zip or bug: expected "+this.centralDirRecords+" records in central dir, got "+this.files.length)},readEndOfCentral:function(){var t=this.reader.lastIndexOfSignature(l.CENTRAL_DIRECTORY_END);if(t<0){throw!this.isSignature(0,l.LOCAL_FILE_HEADER)?new Error("Can't find end of central directory : is this a zip file ? If it is, see http://stuk.github.io/jszip/documentation/howto/read_zip.html"):new Error("Corrupted zip : can't find end of central directory")}this.reader.setIndex(t);var e=t;if(this.checkSignature(l.CENTRAL_DIRECTORY_END),this.readBlockEndOfCentral(),this.diskNumber===u.MAX_VALUE_16BITS||this.diskWithCentralDirStart===u.MAX_VALUE_16BITS||this.centralDirRecordsOnThisDisk===u.MAX_VALUE_16BITS||this.centralDirRecords===u.MAX_VALUE_16BITS||this.centralDirSize===u.MAX_VALUE_32BITS||this.centralDirOffset===u.MAX_VALUE_32BITS){if(this.zip64=!0,(t=this.reader.lastIndexOfSignature(l.ZIP64_CENTRAL_DIRECTORY_LOCATOR))<0)throw new Error("Corrupted zip : can't find the ZIP64 end of central directory locator");if(this.reader.setIndex(t),this.checkSignature(l.ZIP64_CENTRAL_DIRECTORY_LOCATOR),this.readBlockZip64EndOfCentralLocator(),!this.isSignature(this.relativeOffsetEndOfZip64CentralDir,l.ZIP64_CENTRAL_DIRECTORY_END)&&(this.relativeOffsetEndOfZip64CentralDir=this.reader.lastIndexOfSignature(l.ZIP64_CENTRAL_DIRECTORY_END),this.relativeOffsetEndOfZip64CentralDir<0))throw new Error("Corrupted zip : can't find the ZIP64 end of central directory");this.reader.setIndex(this.relativeOffsetEndOfZip64CentralDir),this.checkSignature(l.ZIP64_CENTRAL_DIRECTORY_END),this.readBlockZip64EndOfCentral()}var r=this.centralDirOffset+this.centralDirSize;this.zip64&&(r+=20,r+=12+this.zip64EndOfCentralSize);var n=e-r;if(n>0)this.isSignature(e,l.CENTRAL_FILE_HEADER)||(this.reader.zero=n);else if(n<0)throw new Error("Corrupted zip: missing "+Math.abs(n)+" bytes.")},prepareReader:function(t){var e=u.getTypeOf(t);if(u.checkSupport(e),"string"!==e||f.uint8array)if("nodebuffer"===e)this.reader=new a(t);else if(f.uint8array)this.reader=new o(u.transformTo("uint8array",t));else{if(!f.array)throw new Error("Unexpected error: unsupported type '"+e+"'");this.reader=new s(u.transformTo("array",t))}else this.reader=new i(t,this.loadOptions.optimizedBinaryString)},load:function(t){this.prepareReader(t),this.readEndOfCentral(),this.readCentralDir(),this.readLocalFiles()}},e.exports=n},{"./arrayReader":5,"./nodeBufferReader":17,"./object":18,"./signature":19,"./stringReader":20,"./support":22,"./uint8ArrayReader":23,"./utils":26,"./zipEntry":28}],28:[function(t,e,r){"use strict";function n(t,e){this.options=t,this.loadOptions=e}var i=t("./stringReader"),a=t("./utils"),o=t("./compressedObject"),s=t("./object"),u=t("./support");n.prototype={isEncrypted:function(){return 1==(1&this.bitFlag)},useUTF8:function(){return 2048==(2048&this.bitFlag)},prepareCompressedContent:function(t,e,r){return function(){var n=t.index;t.setIndex(e);var i=t.readData(r);return t.setIndex(n),i}},prepareContent:function(t,e,r,n,i){return function(){var t=a.transformTo(n.uncompressInputType,this.getCompressedContent()),e=n.uncompress(t);if(e.length!==i)throw new Error("Bug : uncompressed data size mismatch");return e}},readLocalPart:function(t){var e,r;if(t.skip(22),this.fileNameLength=t.readInt(2),r=t.readInt(2),this.fileName=t.readData(this.fileNameLength),t.skip(r),-1==this.compressedSize||-1==this.uncompressedSize)throw new Error("Bug or corrupted zip : didn't get enough informations from the central directory (compressedSize == -1 || uncompressedSize == -1)");if(null===(e=a.findCompression(this.compressionMethod)))throw new Error("Corrupted zip : compression "+a.pretty(this.compressionMethod)+" unknown (inner file : "+a.transformTo("string",this.fileName)+")");if(this.decompressed=new o,this.decompressed.compressedSize=this.compressedSize,this.decompressed.uncompressedSize=this.uncompressedSize,this.decompressed.crc32=this.crc32,this.decompressed.compressionMethod=this.compressionMethod,this.decompressed.getCompressedContent=this.prepareCompressedContent(t,t.index,this.compressedSize,e),this.decompressed.getContent=this.prepareContent(t,t.index,this.compressedSize,e,this.uncompressedSize),this.loadOptions.checkCRC32&&(this.decompressed=a.transformTo("string",this.decompressed.getContent()),s.crc32(this.decompressed)!==this.crc32))throw new Error("Corrupted zip : CRC32 mismatch")},readCentralPart:function(t){if(this.versionMadeBy=t.readInt(2),this.versionNeeded=t.readInt(2),this.bitFlag=t.readInt(2),this.compressionMethod=t.readString(2),this.date=t.readDate(),this.crc32=t.readInt(4),this.compressedSize=t.readInt(4),this.uncompressedSize=t.readInt(4),this.fileNameLength=t.readInt(2),this.extraFieldsLength=t.readInt(2),this.fileCommentLength=t.readInt(2),this.diskNumberStart=t.readInt(2),this.internalFileAttributes=t.readInt(2),this.externalFileAttributes=t.readInt(4),this.localHeaderOffset=t.readInt(4),this.isEncrypted())throw new Error("Encrypted zip are not supported");this.fileName=t.readData(this.fileNameLength),this.readExtraFields(t),this.parseZIP64ExtraField(t),this.fileComment=t.readData(this.fileCommentLength)},processAttributes:function(){this.unixPermissions=null,this.dosPermissions=null;var t=this.versionMadeBy>>8;this.dir=!!(16&this.externalFileAttributes),0===t&&(this.dosPermissions=63&this.externalFileAttributes),3===t&&(this.unixPermissions=this.externalFileAttributes>>16&65535),this.dir||"/"!==this.fileNameStr.slice(-1)||(this.dir=!0)},parseZIP64ExtraField:function(t){if(this.extraFields[1]){var e=new i(this.extraFields[1].value);this.uncompressedSize===a.MAX_VALUE_32BITS&&(this.uncompressedSize=e.readInt(8)),this.compressedSize===a.MAX_VALUE_32BITS&&(this.compressedSize=e.readInt(8)),this.localHeaderOffset===a.MAX_VALUE_32BITS&&(this.localHeaderOffset=e.readInt(8)),this.diskNumberStart===a.MAX_VALUE_32BITS&&(this.diskNumberStart=e.readInt(4))}},readExtraFields:function(t){var e,r,n,i=t.index;for(this.extraFields=this.extraFields||{};t.index<i+this.extraFieldsLength;)e=t.readInt(2),r=t.readInt(2),n=t.readString(r),this.extraFields[e]={id:e,length:r,value:n}},handleUTF8:function(){var t=u.uint8array?"uint8array":"array";if(this.useUTF8())this.fileNameStr=s.utf8decode(this.fileName),this.fileCommentStr=s.utf8decode(this.fileComment);else{var e=this.findExtraFieldUnicodePath();if(null!==e)this.fileNameStr=e;else{var r=a.transformTo(t,this.fileName);this.fileNameStr=this.loadOptions.decodeFileName(r)}var n=this.findExtraFieldUnicodeComment();if(null!==n)this.fileCommentStr=n;else{var i=a.transformTo(t,this.fileComment);this.fileCommentStr=this.loadOptions.decodeFileName(i)}}},findExtraFieldUnicodePath:function(){var t=this.extraFields[28789];if(t){var e=new i(t.value);return 1!==e.readInt(1)?null:s.crc32(this.fileName)!==e.readInt(4)?null:s.utf8decode(e.readString(t.length-5))}return null},findExtraFieldUnicodeComment:function(){var t=this.extraFields[25461];if(t){var e=new i(t.value);return 1!==e.readInt(1)?null:s.crc32(this.fileComment)!==e.readInt(4)?null:s.utf8decode(e.readString(t.length-5))}return null}},e.exports=n},{"./compressedObject":7,"./object":18,"./stringReader":20,"./support":22,"./utils":26}],29:[function(t,e,r){"use strict";var n={};(0,t("./lib/utils/common").assign)(n,t("./lib/deflate"),t("./lib/inflate"),t("./lib/zlib/constants")),e.exports=n},{"./lib/deflate":30,"./lib/inflate":31,"./lib/utils/common":32,"./lib/zlib/constants":35}],30:[function(t,e,r){"use strict";function n(t){if(!(this instanceof n))return new n(t);this.options=o.assign({level:c,method:p,chunkSize:16384,windowBits:15,memLevel:8,strategy:d,to:""},t||{});var e=this.options;e.raw&&e.windowBits>0?e.windowBits=-e.windowBits:e.gzip&&e.windowBits>0&&e.windowBits<16&&(e.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new l,this.strm.avail_out=0;var r=a.deflateInit2(this.strm,e.level,e.method,e.windowBits,e.memLevel,e.strategy);if(r!==f)throw new Error(u[r]);if(e.header&&a.deflateSetHeader(this.strm,e.header),e.dictionary){var i;if(i="string"==typeof e.dictionary?s.string2buf(e.dictionary):"[object ArrayBuffer]"===h.call(e.dictionary)?new Uint8Array(e.dictionary):e.dictionary,(r=a.deflateSetDictionary(this.strm,i))!==f)throw new Error(u[r]);this._dict_set=!0}}function i(t,e){var r=new n(e);if(r.push(t,!0),r.err)throw r.msg;return r.result}var a=t("./zlib/deflate"),o=t("./utils/common"),s=t("./utils/strings"),u=t("./zlib/messages"),l=t("./zlib/zstream"),h=Object.prototype.toString,f=0,c=-1,d=0,p=8;n.prototype.push=function(t,e){var r,n,i=this.strm,u=this.options.chunkSize;if(this.ended)return!1;n=e===~~e?e:!0===e?4:0,"string"==typeof t?i.input=s.string2buf(t):"[object ArrayBuffer]"===h.call(t)?i.input=new Uint8Array(t):i.input=t,i.next_in=0,i.avail_in=i.input.length;do{if(0===i.avail_out&&(i.output=new o.Buf8(u),i.next_out=0,i.avail_out=u),1!==(r=a.deflate(i,n))&&r!==f)return this.onEnd(r),this.ended=!0,!1;0!==i.avail_out&&(0!==i.avail_in||4!==n&&2!==n)||("string"===this.options.to?this.onData(s.buf2binstring(o.shrinkBuf(i.output,i.next_out))):this.onData(o.shrinkBuf(i.output,i.next_out)))}while((i.avail_in>0||0===i.avail_out)&&1!==r);return 4===n?(r=a.deflateEnd(this.strm),this.onEnd(r),this.ended=!0,r===f):2!==n||(this.onEnd(f),i.avail_out=0,!0)},n.prototype.onData=function(t){this.chunks.push(t)},n.prototype.onEnd=function(t){t===f&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=o.flattenChunks(this.chunks)),this.chunks=[],this.err=t,this.msg=this.strm.msg},r.Deflate=n,r.deflate=i,r.deflateRaw=function(t,e){return e=e||{},e.raw=!0,i(t,e)},r.gzip=function(t,e){return e=e||{},e.gzip=!0,i(t,e)}},{"./utils/common":32,"./utils/strings":33,"./zlib/deflate":37,"./zlib/messages":42,"./zlib/zstream":44}],31:[function(t,e,r){"use strict";function n(t){if(!(this instanceof n))return new n(t);this.options=o.assign({chunkSize:16384,windowBits:0,to:""},t||{});var e=this.options;e.raw&&e.windowBits>=0&&e.windowBits<16&&(e.windowBits=-e.windowBits,0===e.windowBits&&(e.windowBits=-15)),!(e.windowBits>=0&&e.windowBits<16)||t&&t.windowBits||(e.windowBits+=32),e.windowBits>15&&e.windowBits<48&&0==(15&e.windowBits)&&(e.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new h,this.strm.avail_out=0;var r=a.inflateInit2(this.strm,e.windowBits);if(r!==u.Z_OK)throw new Error(l[r]);this.header=new f,a.inflateGetHeader(this.strm,this.header)}function i(t,e){var r=new n(e);if(r.push(t,!0),r.err)throw r.msg;return r.result}var a=t("./zlib/inflate"),o=t("./utils/common"),s=t("./utils/strings"),u=t("./zlib/constants"),l=t("./zlib/messages"),h=t("./zlib/zstream"),f=t("./zlib/gzheader"),c=Object.prototype.toString;n.prototype.push=function(t,e){var r,n,i,l,h,f,d=this.strm,p=this.options.chunkSize,m=this.options.dictionary,_=!1;if(this.ended)return!1;n=e===~~e?e:!0===e?u.Z_FINISH:u.Z_NO_FLUSH,"string"==typeof t?d.input=s.binstring2buf(t):"[object ArrayBuffer]"===c.call(t)?d.input=new Uint8Array(t):d.input=t,d.next_in=0,d.avail_in=d.input.length;do{if(0===d.avail_out&&(d.output=new o.Buf8(p),d.next_out=0,d.avail_out=p),(r=a.inflate(d,u.Z_NO_FLUSH))===u.Z_NEED_DICT&&m&&(f="string"==typeof m?s.string2buf(m):"[object ArrayBuffer]"===c.call(m)?new Uint8Array(m):m,r=a.inflateSetDictionary(this.strm,f)),r===u.Z_BUF_ERROR&&!0===_&&(r=u.Z_OK,_=!1),r!==u.Z_STREAM_END&&r!==u.Z_OK)return this.onEnd(r),this.ended=!0,!1;d.next_out&&(0!==d.avail_out&&r!==u.Z_STREAM_END&&(0!==d.avail_in||n!==u.Z_FINISH&&n!==u.Z_SYNC_FLUSH)||("string"===this.options.to?(i=s.utf8border(d.output,d.next_out),l=d.next_out-i,h=s.buf2string(d.output,i),d.next_out=l,d.avail_out=p-l,l&&o.arraySet(d.output,d.output,i,l,0),this.onData(h)):this.onData(o.shrinkBuf(d.output,d.next_out)))),0===d.avail_in&&0===d.avail_out&&(_=!0)}while((d.avail_in>0||0===d.avail_out)&&r!==u.Z_STREAM_END);return r===u.Z_STREAM_END&&(n=u.Z_FINISH),n===u.Z_FINISH?(r=a.inflateEnd(this.strm),this.onEnd(r),this.ended=!0,r===u.Z_OK):n!==u.Z_SYNC_FLUSH||(this.onEnd(u.Z_OK),d.avail_out=0,!0)},n.prototype.onData=function(t){this.chunks.push(t)},n.prototype.onEnd=function(t){t===u.Z_OK&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=o.flattenChunks(this.chunks)),this.chunks=[],this.err=t,this.msg=this.strm.msg},r.Inflate=n,r.inflate=i,r.inflateRaw=function(t,e){return e=e||{},e.raw=!0,i(t,e)},r.ungzip=i},{"./utils/common":32,"./utils/strings":33,"./zlib/constants":35,"./zlib/gzheader":38,"./zlib/inflate":40,"./zlib/messages":42,"./zlib/zstream":44}],32:[function(t,e,r){"use strict";var n="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Int32Array;r.assign=function(t){for(var e=Array.prototype.slice.call(arguments,1);e.length;){var r=e.shift();if(r){if("object"!=typeof r)throw new TypeError(r+"must be non-object");for(var n in r)r.hasOwnProperty(n)&&(t[n]=r[n])}}return t},r.shrinkBuf=function(t,e){return t.length===e?t:t.subarray?t.subarray(0,e):(t.length=e,t)};var i={arraySet:function(t,e,r,n,i){if(e.subarray&&t.subarray)t.set(e.subarray(r,r+n),i);else for(var a=0;a<n;a++)t[i+a]=e[r+a]},flattenChunks:function(t){var e,r,n,i,a,o;for(n=0,e=0,r=t.length;e<r;e++)n+=t[e].length;for(o=new Uint8Array(n),i=0,e=0,r=t.length;e<r;e++)a=t[e],o.set(a,i),i+=a.length;return o}},a={arraySet:function(t,e,r,n,i){for(var a=0;a<n;a++)t[i+a]=e[r+a]},flattenChunks:function(t){return[].concat.apply([],t)}};r.setTyped=function(t){t?(r.Buf8=Uint8Array,r.Buf16=Uint16Array,r.Buf32=Int32Array,r.assign(r,i)):(r.Buf8=Array,r.Buf16=Array,r.Buf32=Array,r.assign(r,a))},r.setTyped(n)},{}],33:[function(t,e,r){"use strict";function n(t,e){if(e<65537&&(t.subarray&&o||!t.subarray&&a))return String.fromCharCode.apply(null,i.shrinkBuf(t,e));for(var r="",n=0;n<e;n++)r+=String.fromCharCode(t[n]);return r}var i=t("./common"),a=!0,o=!0;try{String.fromCharCode.apply(null,[0])}catch(t){a=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(t){o=!1}for(var s=new i.Buf8(256),u=0;u<256;u++)s[u]=u>=252?6:u>=248?5:u>=240?4:u>=224?3:u>=192?2:1;s[254]=s[254]=1,r.string2buf=function(t){var e,r,n,a,o,s=t.length,u=0;for(a=0;a<s;a++)55296==(64512&(r=t.charCodeAt(a)))&&a+1<s&&56320==(64512&(n=t.charCodeAt(a+1)))&&(r=65536+(r-55296<<10)+(n-56320),a++),u+=r<128?1:r<2048?2:r<65536?3:4;for(e=new i.Buf8(u),o=0,a=0;o<u;a++)55296==(64512&(r=t.charCodeAt(a)))&&a+1<s&&56320==(64512&(n=t.charCodeAt(a+1)))&&(r=65536+(r-55296<<10)+(n-56320),a++),r<128?e[o++]=r:r<2048?(e[o++]=192|r>>>6,e[o++]=128|63&r):r<65536?(e[o++]=224|r>>>12,e[o++]=128|r>>>6&63,e[o++]=128|63&r):(e[o++]=240|r>>>18,e[o++]=128|r>>>12&63,e[o++]=128|r>>>6&63,e[o++]=128|63&r);return e},r.buf2binstring=function(t){return n(t,t.length)},r.binstring2buf=function(t){for(var e=new i.Buf8(t.length),r=0,n=e.length;r<n;r++)e[r]=t.charCodeAt(r);return e},r.buf2string=function(t,e){var r,i,a,o,u=e||t.length,l=new Array(2*u);for(i=0,r=0;r<u;)if((a=t[r++])<128)l[i++]=a;else if((o=s[a])>4)l[i++]=65533,r+=o-1;else{for(a&=2===o?31:3===o?15:7;o>1&&r<u;)a=a<<6|63&t[r++],o--;o>1?l[i++]=65533:a<65536?l[i++]=a:(a-=65536,l[i++]=55296|a>>10&1023,l[i++]=56320|1023&a)}return n(l,i)},r.utf8border=function(t,e){var r;for((e=e||t.length)>t.length&&(e=t.length),r=e-1;r>=0&&128==(192&t[r]);)r--;return r<0?e:0===r?e:r+s[t[r]]>e?r:e}},{"./common":32}],34:[function(t,e,r){"use strict";e.exports=function(t,e,r,n){for(var i=65535&t|0,a=t>>>16&65535|0,o=0;0!==r;){r-=o=r>2e3?2e3:r;do{a=a+(i=i+e[n++]|0)|0}while(--o);i%=65521,a%=65521}return i|a<<16|0}},{}],35:[function(t,e,r){"use strict";e.exports={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8}},{}],36:[function(t,e,r){"use strict";var n=function(){for(var t,e=[],r=0;r<256;r++){t=r;for(var n=0;n<8;n++)t=1&t?3988292384^t>>>1:t>>>1;e[r]=t}return e}();e.exports=function(t,e,r,i){var a=n,o=i+r;t^=-1;for(var s=i;s<o;s++)t=t>>>8^a[255&(t^e[s])];return-1^t}},{}],37:[function(t,e,r){"use strict";function n(t,e){return t.msg=E[e],e}function i(t){return(t<<1)-(t>4?9:0)}function a(t){for(var e=t.length;--e>=0;)t[e]=0}function o(t){var e=t.state,r=e.pending;r>t.avail_out&&(r=t.avail_out),0!==r&&(y.arraySet(t.output,e.pending_buf,e.pending_out,r,t.next_out),t.next_out+=r,e.pending_out+=r,t.total_out+=r,t.avail_out-=r,e.pending-=r,0===e.pending&&(e.pending_out=0))}function s(t,e){v._tr_flush_block(t,t.block_start>=0?t.block_start:-1,t.strstart-t.block_start,e),t.block_start=t.strstart,o(t.strm)}function u(t,e){t.pending_buf[t.pending++]=e}function l(t,e){t.pending_buf[t.pending++]=e>>>8&255,t.pending_buf[t.pending++]=255&e}function h(t,e,r,n){var i=t.avail_in;return i>n&&(i=n),0===i?0:(t.avail_in-=i,y.arraySet(e,t.input,t.next_in,i,r),1===t.state.wrap?t.adler=x(t.adler,e,i,r):2===t.state.wrap&&(t.adler=k(t.adler,e,i,r)),t.next_in+=i,t.total_in+=i,i)}function f(t,e){var r,n,i=t.max_chain_length,a=t.strstart,o=t.prev_length,s=t.nice_match,u=t.strstart>t.w_size-Q?t.strstart-(t.w_size-Q):0,l=t.window,h=t.w_mask,f=t.prev,c=t.strstart+$,d=l[a+o-1],p=l[a+o];t.prev_length>=t.good_match&&(i>>=2),s>t.lookahead&&(s=t.lookahead);do{if(r=e,l[r+o]===p&&l[r+o-1]===d&&l[r]===l[a]&&l[++r]===l[a+1]){a+=2,r++;do{}while(l[++a]===l[++r]&&l[++a]===l[++r]&&l[++a]===l[++r]&&l[++a]===l[++r]&&l[++a]===l[++r]&&l[++a]===l[++r]&&l[++a]===l[++r]&&l[++a]===l[++r]&&a<c);if(n=$-(c-a),a=c-$,n>o){if(t.match_start=e,o=n,n>=s)break;d=l[a+o-1],p=l[a+o]}}}while((e=f[e&h])>u&&0!=--i);return o<=t.lookahead?o:t.lookahead}function c(t){var e,r,n,i,a,o=t.w_size;do{if(i=t.window_size-t.lookahead-t.strstart,t.strstart>=o+(o-Q)){y.arraySet(t.window,t.window,o,o,0),t.match_start-=o,t.strstart-=o,t.block_start-=o,e=r=t.hash_size;do{n=t.head[--e],t.head[e]=n>=o?n-o:0}while(--r);e=r=o;do{n=t.prev[--e],t.prev[e]=n>=o?n-o:0}while(--r);i+=o}if(0===t.strm.avail_in)break;if(r=h(t.strm,t.window,t.strstart+t.lookahead,i),t.lookahead+=r,t.lookahead+t.insert>=q)for(a=t.strstart-t.insert,t.ins_h=t.window[a],t.ins_h=(t.ins_h<<t.hash_shift^t.window[a+1])&t.hash_mask;t.insert&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[a+q-1])&t.hash_mask,t.prev[a&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=a,a++,t.insert--,!(t.lookahead+t.insert<q)););}while(t.lookahead<Q&&0!==t.strm.avail_in)}function d(t,e){for(var r,n;;){if(t.lookahead<Q){if(c(t),t.lookahead<Q&&e===A)return ut;if(0===t.lookahead)break}if(r=0,t.lookahead>=q&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+q-1])&t.hash_mask,r=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),0!==r&&t.strstart-r<=t.w_size-Q&&(t.match_length=f(t,r)),t.match_length>=q)if(n=v._tr_tally(t,t.strstart-t.match_start,t.match_length-q),t.lookahead-=t.match_length,t.match_length<=t.max_lazy_match&&t.lookahead>=q){t.match_length--;do{t.strstart++,t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+q-1])&t.hash_mask,r=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart}while(0!=--t.match_length);t.strstart++}else t.strstart+=t.match_length,t.match_length=0,t.ins_h=t.window[t.strstart],t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+1])&t.hash_mask;else n=v._tr_tally(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++;if(n&&(s(t,!1),0===t.strm.avail_out))return ut}return t.insert=t.strstart<q-1?t.strstart:q-1,e===C?(s(t,!0),0===t.strm.avail_out?ht:ft):t.last_lit&&(s(t,!1),0===t.strm.avail_out)?ut:lt}function p(t,e){for(var r,n,i;;){if(t.lookahead<Q){if(c(t),t.lookahead<Q&&e===A)return ut;if(0===t.lookahead)break}if(r=0,t.lookahead>=q&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+q-1])&t.hash_mask,r=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),t.prev_length=t.match_length,t.prev_match=t.match_start,t.match_length=q-1,0!==r&&t.prev_length<t.max_lazy_match&&t.strstart-r<=t.w_size-Q&&(t.match_length=f(t,r),t.match_length<=5&&(t.strategy===U||t.match_length===q&&t.strstart-t.match_start>4096)&&(t.match_length=q-1)),t.prev_length>=q&&t.match_length<=t.prev_length){i=t.strstart+t.lookahead-q,n=v._tr_tally(t,t.strstart-1-t.prev_match,t.prev_length-q),t.lookahead-=t.prev_length-1,t.prev_length-=2;do{++t.strstart<=i&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+q-1])&t.hash_mask,r=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart)}while(0!=--t.prev_length);if(t.match_available=0,t.match_length=q-1,t.strstart++,n&&(s(t,!1),0===t.strm.avail_out))return ut}else if(t.match_available){if((n=v._tr_tally(t,0,t.window[t.strstart-1]))&&s(t,!1),t.strstart++,t.lookahead--,0===t.strm.avail_out)return ut}else t.match_available=1,t.strstart++,t.lookahead--}return t.match_available&&(n=v._tr_tally(t,0,t.window[t.strstart-1]),t.match_available=0),t.insert=t.strstart<q-1?t.strstart:q-1,e===C?(s(t,!0),0===t.strm.avail_out?ht:ft):t.last_lit&&(s(t,!1),0===t.strm.avail_out)?ut:lt}function m(t,e,r,n,i){this.good_length=t,this.max_lazy=e,this.nice_length=r,this.max_chain=n,this.func=i}function _(t){var e;return t&&t.state?(t.total_in=t.total_out=0,t.data_type=Z,e=t.state,e.pending=0,e.pending_out=0,e.wrap<0&&(e.wrap=-e.wrap),e.status=e.wrap?et:ot,t.adler=2===e.wrap?0:1,e.last_flush=A,v._tr_init(e),B):n(t,T)}function g(t){var e=_(t);return e===B&&function(t){t.window_size=2*t.w_size,a(t.head),t.max_lazy_match=w[t.level].max_lazy,t.good_match=w[t.level].good_length,t.nice_match=w[t.level].nice_length,t.max_chain_length=w[t.level].max_chain,t.strstart=0,t.block_start=0,t.lookahead=0,t.insert=0,t.match_length=t.prev_length=q-1,t.match_available=0,t.ins_h=0}(t.state),e}function b(t,e,r,i,o,s){if(!t)return T;var u=1;if(e===D&&(e=6),i<0?(u=0,i=-i):i>15&&(u=2,i-=16),o<1||o>Y||r!==M||i<8||i>15||e<0||e>9||s<0||s>N)return n(t,T);8===i&&(i=9);var l=new function(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=M,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new y.Buf16(2*K),this.dyn_dtree=new y.Buf16(2*(2*G+1)),this.bl_tree=new y.Buf16(2*(2*X+1)),a(this.dyn_ltree),a(this.dyn_dtree),a(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new y.Buf16(J+1),this.heap=new y.Buf16(2*V+1),a(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new y.Buf16(2*V+1),a(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0};return t.state=l,l.strm=t,l.wrap=u,l.gzhead=null,l.w_bits=i,l.w_size=1<<l.w_bits,l.w_mask=l.w_size-1,l.hash_bits=o+7,l.hash_size=1<<l.hash_bits,l.hash_mask=l.hash_size-1,l.hash_shift=~~((l.hash_bits+q-1)/q),l.window=new y.Buf8(2*l.w_size),l.head=new y.Buf16(l.hash_size),l.prev=new y.Buf16(l.w_size),l.lit_bufsize=1<<o+6,l.pending_buf_size=4*l.lit_bufsize,l.pending_buf=new y.Buf8(l.pending_buf_size),l.d_buf=l.lit_bufsize>>1,l.l_buf=3*l.lit_bufsize,l.level=e,l.strategy=s,l.method=r,g(t)}var w,y=t("../utils/common"),v=t("./trees"),x=t("./adler32"),k=t("./crc32"),E=t("./messages"),A=0,z=1,I=3,C=4,S=5,B=0,R=1,T=-2,O=-3,L=-5,D=-1,U=1,j=2,P=3,N=4,F=0,Z=2,M=8,Y=9,W=15,H=8,V=286,G=30,X=19,K=2*V+1,J=15,q=3,$=258,Q=$+q+1,tt=32,et=42,rt=69,nt=73,it=91,at=103,ot=113,st=666,ut=1,lt=2,ht=3,ft=4,ct=3;w=[new m(0,0,0,0,function(t,e){var r=65535;for(r>t.pending_buf_size-5&&(r=t.pending_buf_size-5);;){if(t.lookahead<=1){if(c(t),0===t.lookahead&&e===A)return ut;if(0===t.lookahead)break}t.strstart+=t.lookahead,t.lookahead=0;var n=t.block_start+r;if((0===t.strstart||t.strstart>=n)&&(t.lookahead=t.strstart-n,t.strstart=n,s(t,!1),0===t.strm.avail_out))return ut;if(t.strstart-t.block_start>=t.w_size-Q&&(s(t,!1),0===t.strm.avail_out))return ut}return t.insert=0,e===C?(s(t,!0),0===t.strm.avail_out?ht:ft):(t.strstart>t.block_start&&(s(t,!1),t.strm.avail_out),ut)}),new m(4,4,8,4,d),new m(4,5,16,8,d),new m(4,6,32,32,d),new m(4,4,16,16,p),new m(8,16,32,32,p),new m(8,16,128,128,p),new m(8,32,128,256,p),new m(32,128,258,1024,p),new m(32,258,258,4096,p)],r.deflateInit=function(t,e){return b(t,e,M,W,H,F)},r.deflateInit2=b,r.deflateReset=g,r.deflateResetKeep=_,r.deflateSetHeader=function(t,e){return t&&t.state?2!==t.state.wrap?T:(t.state.gzhead=e,B):T},r.deflate=function(t,e){var r,h,f,d;if(!t||!t.state||e>S||e<0)return t?n(t,T):T;if(h=t.state,!t.output||!t.input&&0!==t.avail_in||h.status===st&&e!==C)return n(t,0===t.avail_out?L:T);if(h.strm=t,r=h.last_flush,h.last_flush=e,h.status===et)if(2===h.wrap)t.adler=0,u(h,31),u(h,139),u(h,8),h.gzhead?(u(h,(h.gzhead.text?1:0)+(h.gzhead.hcrc?2:0)+(h.gzhead.extra?4:0)+(h.gzhead.name?8:0)+(h.gzhead.comment?16:0)),u(h,255&h.gzhead.time),u(h,h.gzhead.time>>8&255),u(h,h.gzhead.time>>16&255),u(h,h.gzhead.time>>24&255),u(h,9===h.level?2:h.strategy>=j||h.level<2?4:0),u(h,255&h.gzhead.os),h.gzhead.extra&&h.gzhead.extra.length&&(u(h,255&h.gzhead.extra.length),u(h,h.gzhead.extra.length>>8&255)),h.gzhead.hcrc&&(t.adler=k(t.adler,h.pending_buf,h.pending,0)),h.gzindex=0,h.status=rt):(u(h,0),u(h,0),u(h,0),u(h,0),u(h,0),u(h,9===h.level?2:h.strategy>=j||h.level<2?4:0),u(h,ct),h.status=ot);else{var p=M+(h.w_bits-8<<4)<<8;p|=(h.strategy>=j||h.level<2?0:h.level<6?1:6===h.level?2:3)<<6,0!==h.strstart&&(p|=tt),p+=31-p%31,h.status=ot,l(h,p),0!==h.strstart&&(l(h,t.adler>>>16),l(h,65535&t.adler)),t.adler=1}if(h.status===rt)if(h.gzhead.extra){for(f=h.pending;h.gzindex<(65535&h.gzhead.extra.length)&&(h.pending!==h.pending_buf_size||(h.gzhead.hcrc&&h.pending>f&&(t.adler=k(t.adler,h.pending_buf,h.pending-f,f)),o(t),f=h.pending,h.pending!==h.pending_buf_size));)u(h,255&h.gzhead.extra[h.gzindex]),h.gzindex++;h.gzhead.hcrc&&h.pending>f&&(t.adler=k(t.adler,h.pending_buf,h.pending-f,f)),h.gzindex===h.gzhead.extra.length&&(h.gzindex=0,h.status=nt)}else h.status=nt;if(h.status===nt)if(h.gzhead.name){f=h.pending;do{if(h.pending===h.pending_buf_size&&(h.gzhead.hcrc&&h.pending>f&&(t.adler=k(t.adler,h.pending_buf,h.pending-f,f)),o(t),f=h.pending,h.pending===h.pending_buf_size)){d=1;break}d=h.gzindex<h.gzhead.name.length?255&h.gzhead.name.charCodeAt(h.gzindex++):0,u(h,d)}while(0!==d);h.gzhead.hcrc&&h.pending>f&&(t.adler=k(t.adler,h.pending_buf,h.pending-f,f)),0===d&&(h.gzindex=0,h.status=it)}else h.status=it;if(h.status===it)if(h.gzhead.comment){f=h.pending;do{if(h.pending===h.pending_buf_size&&(h.gzhead.hcrc&&h.pending>f&&(t.adler=k(t.adler,h.pending_buf,h.pending-f,f)),o(t),f=h.pending,h.pending===h.pending_buf_size)){d=1;break}d=h.gzindex<h.gzhead.comment.length?255&h.gzhead.comment.charCodeAt(h.gzindex++):0,u(h,d)}while(0!==d);h.gzhead.hcrc&&h.pending>f&&(t.adler=k(t.adler,h.pending_buf,h.pending-f,f)),0===d&&(h.status=at)}else h.status=at;if(h.status===at&&(h.gzhead.hcrc?(h.pending+2>h.pending_buf_size&&o(t),h.pending+2<=h.pending_buf_size&&(u(h,255&t.adler),u(h,t.adler>>8&255),t.adler=0,h.status=ot)):h.status=ot),0!==h.pending){if(o(t),0===t.avail_out)return h.last_flush=-1,B}else if(0===t.avail_in&&i(e)<=i(r)&&e!==C)return n(t,L);if(h.status===st&&0!==t.avail_in)return n(t,L);if(0!==t.avail_in||0!==h.lookahead||e!==A&&h.status!==st){var m=h.strategy===j?function(t,e){for(var r;;){if(0===t.lookahead&&(c(t),0===t.lookahead)){if(e===A)return ut;break}if(t.match_length=0,r=v._tr_tally(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++,r&&(s(t,!1),0===t.strm.avail_out))return ut}return t.insert=0,e===C?(s(t,!0),0===t.strm.avail_out?ht:ft):t.last_lit&&(s(t,!1),0===t.strm.avail_out)?ut:lt}(h,e):h.strategy===P?function(t,e){for(var r,n,i,a,o=t.window;;){if(t.lookahead<=$){if(c(t),t.lookahead<=$&&e===A)return ut;if(0===t.lookahead)break}if(t.match_length=0,t.lookahead>=q&&t.strstart>0&&(i=t.strstart-1,(n=o[i])===o[++i]&&n===o[++i]&&n===o[++i])){a=t.strstart+$;do{}while(n===o[++i]&&n===o[++i]&&n===o[++i]&&n===o[++i]&&n===o[++i]&&n===o[++i]&&n===o[++i]&&n===o[++i]&&i<a);t.match_length=$-(a-i),t.match_length>t.lookahead&&(t.match_length=t.lookahead)}if(t.match_length>=q?(r=v._tr_tally(t,1,t.match_length-q),t.lookahead-=t.match_length,t.strstart+=t.match_length,t.match_length=0):(r=v._tr_tally(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++),r&&(s(t,!1),0===t.strm.avail_out))return ut}return t.insert=0,e===C?(s(t,!0),0===t.strm.avail_out?ht:ft):t.last_lit&&(s(t,!1),0===t.strm.avail_out)?ut:lt}(h,e):w[h.level].func(h,e);if(m!==ht&&m!==ft||(h.status=st),m===ut||m===ht)return 0===t.avail_out&&(h.last_flush=-1),B;if(m===lt&&(e===z?v._tr_align(h):e!==S&&(v._tr_stored_block(h,0,0,!1),e===I&&(a(h.head),0===h.lookahead&&(h.strstart=0,h.block_start=0,h.insert=0))),o(t),0===t.avail_out))return h.last_flush=-1,B}return e!==C?B:h.wrap<=0?R:(2===h.wrap?(u(h,255&t.adler),u(h,t.adler>>8&255),u(h,t.adler>>16&255),u(h,t.adler>>24&255),u(h,255&t.total_in),u(h,t.total_in>>8&255),u(h,t.total_in>>16&255),u(h,t.total_in>>24&255)):(l(h,t.adler>>>16),l(h,65535&t.adler)),o(t),h.wrap>0&&(h.wrap=-h.wrap),0!==h.pending?B:R)},r.deflateEnd=function(t){var e;return t&&t.state?(e=t.state.status)!==et&&e!==rt&&e!==nt&&e!==it&&e!==at&&e!==ot&&e!==st?n(t,T):(t.state=null,e===ot?n(t,O):B):T},r.deflateSetDictionary=function(t,e){var r,n,i,o,s,u,l,h,f=e.length;if(!t||!t.state)return T;if(r=t.state,2===(o=r.wrap)||1===o&&r.status!==et||r.lookahead)return T;for(1===o&&(t.adler=x(t.adler,e,f,0)),r.wrap=0,f>=r.w_size&&(0===o&&(a(r.head),r.strstart=0,r.block_start=0,r.insert=0),h=new y.Buf8(r.w_size),y.arraySet(h,e,f-r.w_size,r.w_size,0),e=h,f=r.w_size),s=t.avail_in,u=t.next_in,l=t.input,t.avail_in=f,t.next_in=0,t.input=e,c(r);r.lookahead>=q;){n=r.strstart,i=r.lookahead-(q-1);do{r.ins_h=(r.ins_h<<r.hash_shift^r.window[n+q-1])&r.hash_mask,r.prev[n&r.w_mask]=r.head[r.ins_h],r.head[r.ins_h]=n,n++}while(--i);r.strstart=n,r.lookahead=q-1,c(r)}return r.strstart+=r.lookahead,r.block_start=r.strstart,r.insert=r.lookahead,r.lookahead=0,r.match_length=r.prev_length=q-1,r.match_available=0,t.next_in=u,t.input=l,t.avail_in=s,r.wrap=o,B},r.deflateInfo="pako deflate (from Nodeca project)"},{"../utils/common":32,"./adler32":34,"./crc32":36,"./messages":42,"./trees":43}],38:[function(t,e,r){"use strict";e.exports=function(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1}},{}],39:[function(t,e,r){"use strict";e.exports=function(t,e){var r,n,i,a,o,s,u,l,h,f,c,d,p,m,_,g,b,w,y,v,x,k,E,A,z;r=t.state,n=t.next_in,A=t.input,i=n+(t.avail_in-5),a=t.next_out,z=t.output,o=a-(e-t.avail_out),s=a+(t.avail_out-257),u=r.dmax,l=r.wsize,h=r.whave,f=r.wnext,c=r.window,d=r.hold,p=r.bits,m=r.lencode,_=r.distcode,g=(1<<r.lenbits)-1,b=(1<<r.distbits)-1;t:do{p<15&&(d+=A[n++]<<p,p+=8,d+=A[n++]<<p,p+=8),w=m[d&g];e:for(;;){if(y=w>>>24,d>>>=y,p-=y,0===(y=w>>>16&255))z[a++]=65535&w;else{if(!(16&y)){if(0==(64&y)){w=m[(65535&w)+(d&(1<<y)-1)];continue e}if(32&y){r.mode=12;break t}t.msg="invalid literal/length code",r.mode=30;break t}v=65535&w,(y&=15)&&(p<y&&(d+=A[n++]<<p,p+=8),v+=d&(1<<y)-1,d>>>=y,p-=y),p<15&&(d+=A[n++]<<p,p+=8,d+=A[n++]<<p,p+=8),w=_[d&b];r:for(;;){if(y=w>>>24,d>>>=y,p-=y,!(16&(y=w>>>16&255))){if(0==(64&y)){w=_[(65535&w)+(d&(1<<y)-1)];continue r}t.msg="invalid distance code",r.mode=30;break t}if(x=65535&w,y&=15,p<y&&(d+=A[n++]<<p,(p+=8)<y&&(d+=A[n++]<<p,p+=8)),(x+=d&(1<<y)-1)>u){t.msg="invalid distance too far back",r.mode=30;break t}if(d>>>=y,p-=y,y=a-o,x>y){if((y=x-y)>h&&r.sane){t.msg="invalid distance too far back",r.mode=30;break t}if(k=0,E=c,0===f){if(k+=l-y,y<v){v-=y;do{z[a++]=c[k++]}while(--y);k=a-x,E=z}}else if(f<y){if(k+=l+f-y,(y-=f)<v){v-=y;do{z[a++]=c[k++]}while(--y);if(k=0,f<v){v-=y=f;do{z[a++]=c[k++]}while(--y);k=a-x,E=z}}}else if(k+=f-y,y<v){v-=y;do{z[a++]=c[k++]}while(--y);k=a-x,E=z}for(;v>2;)z[a++]=E[k++],z[a++]=E[k++],z[a++]=E[k++],v-=3;v&&(z[a++]=E[k++],v>1&&(z[a++]=E[k++]))}else{k=a-x;do{z[a++]=z[k++],z[a++]=z[k++],z[a++]=z[k++],v-=3}while(v>2);v&&(z[a++]=z[k++],v>1&&(z[a++]=z[k++]))}break}}break}}while(n<i&&a<s);n-=v=p>>3,d&=(1<<(p-=v<<3))-1,t.next_in=n,t.next_out=a,t.avail_in=n<i?i-n+5:5-(n-i),t.avail_out=a<s?s-a+257:257-(a-s),r.hold=d,r.bits=p}},{}],40:[function(t,e,r){"use strict";function n(t){return(t>>>24&255)+(t>>>8&65280)+((65280&t)<<8)+((255&t)<<24)}function i(t){var e;return t&&t.state?(e=t.state,t.total_in=t.total_out=e.total=0,t.msg="",e.wrap&&(t.adler=1&e.wrap),e.mode=R,e.last=0,e.havedict=0,e.dmax=32768,e.head=null,e.hold=0,e.bits=0,e.lencode=e.lendyn=new c.Buf32(lt),e.distcode=e.distdyn=new c.Buf32(ht),e.sane=1,e.back=-1,k):z}function a(t){var e;return t&&t.state?(e=t.state,e.wsize=0,e.whave=0,e.wnext=0,i(t)):z}function o(t,e){var r,n;return t&&t.state?(n=t.state,e<0?(r=0,e=-e):(r=1+(e>>4),e<48&&(e&=15)),e&&(e<8||e>15)?z:(null!==n.window&&n.wbits!==e&&(n.window=null),n.wrap=r,n.wbits=e,a(t))):z}function s(t,e){var r,n;return t?(n=new function(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new c.Buf16(320),this.work=new c.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0},t.state=n,n.window=null,(r=o(t,e))!==k&&(t.state=null),r):z}function u(t){if(ct){var e;for(h=new c.Buf32(512),f=new c.Buf32(32),e=0;e<144;)t.lens[e++]=8;for(;e<256;)t.lens[e++]=9;for(;e<280;)t.lens[e++]=7;for(;e<288;)t.lens[e++]=8;for(_(b,t.lens,0,288,h,0,t.work,{bits:9}),e=0;e<32;)t.lens[e++]=5;_(w,t.lens,0,32,f,0,t.work,{bits:5}),ct=!1}t.lencode=h,t.lenbits=9,t.distcode=f,t.distbits=5}function l(t,e,r,n){var i,a=t.state;return null===a.window&&(a.wsize=1<<a.wbits,a.wnext=0,a.whave=0,a.window=new c.Buf8(a.wsize)),n>=a.wsize?(c.arraySet(a.window,e,r-a.wsize,a.wsize,0),a.wnext=0,a.whave=a.wsize):((i=a.wsize-a.wnext)>n&&(i=n),c.arraySet(a.window,e,r-n,i,a.wnext),(n-=i)?(c.arraySet(a.window,e,r-n,n,0),a.wnext=n,a.whave=a.wsize):(a.wnext+=i,a.wnext===a.wsize&&(a.wnext=0),a.whave<a.wsize&&(a.whave+=i))),0}var h,f,c=t("../utils/common"),d=t("./adler32"),p=t("./crc32"),m=t("./inffast"),_=t("./inftrees"),g=0,b=1,w=2,y=4,v=5,x=6,k=0,E=1,A=2,z=-2,I=-3,C=-4,S=-5,B=8,R=1,T=2,O=3,L=4,D=5,U=6,j=7,P=8,N=9,F=10,Z=11,M=12,Y=13,W=14,H=15,V=16,G=17,X=18,K=19,J=20,q=21,$=22,Q=23,tt=24,et=25,rt=26,nt=27,it=28,at=29,ot=30,st=31,ut=32,lt=852,ht=592,ft=15,ct=!0;r.inflateReset=a,r.inflateReset2=o,r.inflateResetKeep=i,r.inflateInit=function(t){return s(t,ft)},r.inflateInit2=s,r.inflate=function(t,e){var r,i,a,o,s,h,f,lt,ht,ft,ct,dt,pt,mt,_t,gt,bt,wt,yt,vt,xt,kt,Et,At,zt=0,It=new c.Buf8(4),Ct=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!t||!t.state||!t.output||!t.input&&0!==t.avail_in)return z;(r=t.state).mode===M&&(r.mode=Y),s=t.next_out,a=t.output,f=t.avail_out,o=t.next_in,i=t.input,h=t.avail_in,lt=r.hold,ht=r.bits,ft=h,ct=f,kt=k;t:for(;;)switch(r.mode){case R:if(0===r.wrap){r.mode=Y;break}for(;ht<16;){if(0===h)break t;h--,lt+=i[o++]<<ht,ht+=8}if(2&r.wrap&&35615===lt){r.check=0,It[0]=255&lt,It[1]=lt>>>8&255,r.check=p(r.check,It,2,0),lt=0,ht=0,r.mode=T;break}if(r.flags=0,r.head&&(r.head.done=!1),!(1&r.wrap)||(((255&lt)<<8)+(lt>>8))%31){t.msg="incorrect header check",r.mode=ot;break}if((15&lt)!==B){t.msg="unknown compression method",r.mode=ot;break}if(lt>>>=4,ht-=4,xt=8+(15&lt),0===r.wbits)r.wbits=xt;else if(xt>r.wbits){t.msg="invalid window size",r.mode=ot;break}r.dmax=1<<xt,t.adler=r.check=1,r.mode=512&lt?F:M,lt=0,ht=0;break;case T:for(;ht<16;){if(0===h)break t;h--,lt+=i[o++]<<ht,ht+=8}if(r.flags=lt,(255&r.flags)!==B){t.msg="unknown compression method",r.mode=ot;break}if(57344&r.flags){t.msg="unknown header flags set",r.mode=ot;break}r.head&&(r.head.text=lt>>8&1),512&r.flags&&(It[0]=255&lt,It[1]=lt>>>8&255,r.check=p(r.check,It,2,0)),lt=0,ht=0,r.mode=O;case O:for(;ht<32;){if(0===h)break t;h--,lt+=i[o++]<<ht,ht+=8}r.head&&(r.head.time=lt),512&r.flags&&(It[0]=255&lt,It[1]=lt>>>8&255,It[2]=lt>>>16&255,It[3]=lt>>>24&255,r.check=p(r.check,It,4,0)),lt=0,ht=0,r.mode=L;case L:for(;ht<16;){if(0===h)break t;h--,lt+=i[o++]<<ht,ht+=8}r.head&&(r.head.xflags=255&lt,r.head.os=lt>>8),512&r.flags&&(It[0]=255&lt,It[1]=lt>>>8&255,r.check=p(r.check,It,2,0)),lt=0,ht=0,r.mode=D;case D:if(1024&r.flags){for(;ht<16;){if(0===h)break t;h--,lt+=i[o++]<<ht,ht+=8}r.length=lt,r.head&&(r.head.extra_len=lt),512&r.flags&&(It[0]=255&lt,It[1]=lt>>>8&255,r.check=p(r.check,It,2,0)),lt=0,ht=0}else r.head&&(r.head.extra=null);r.mode=U;case U:if(1024&r.flags&&((dt=r.length)>h&&(dt=h),dt&&(r.head&&(xt=r.head.extra_len-r.length,r.head.extra||(r.head.extra=new Array(r.head.extra_len)),c.arraySet(r.head.extra,i,o,dt,xt)),512&r.flags&&(r.check=p(r.check,i,dt,o)),h-=dt,o+=dt,r.length-=dt),r.length))break t;r.length=0,r.mode=j;case j:if(2048&r.flags){if(0===h)break t;dt=0;do{xt=i[o+dt++],r.head&&xt&&r.length<65536&&(r.head.name+=String.fromCharCode(xt))}while(xt&&dt<h);if(512&r.flags&&(r.check=p(r.check,i,dt,o)),h-=dt,o+=dt,xt)break t}else r.head&&(r.head.name=null);r.length=0,r.mode=P;case P:if(4096&r.flags){if(0===h)break t;dt=0;do{xt=i[o+dt++],r.head&&xt&&r.length<65536&&(r.head.comment+=String.fromCharCode(xt))}while(xt&&dt<h);if(512&r.flags&&(r.check=p(r.check,i,dt,o)),h-=dt,o+=dt,xt)break t}else r.head&&(r.head.comment=null);r.mode=N;case N:if(512&r.flags){for(;ht<16;){if(0===h)break t;h--,lt+=i[o++]<<ht,ht+=8}if(lt!==(65535&r.check)){t.msg="header crc mismatch",r.mode=ot;break}lt=0,ht=0}r.head&&(r.head.hcrc=r.flags>>9&1,r.head.done=!0),t.adler=r.check=0,r.mode=M;break;case F:for(;ht<32;){if(0===h)break t;h--,lt+=i[o++]<<ht,ht+=8}t.adler=r.check=n(lt),lt=0,ht=0,r.mode=Z;case Z:if(0===r.havedict)return t.next_out=s,t.avail_out=f,t.next_in=o,t.avail_in=h,r.hold=lt,r.bits=ht,A;t.adler=r.check=1,r.mode=M;case M:if(e===v||e===x)break t;case Y:if(r.last){lt>>>=7&ht,ht-=7&ht,r.mode=nt;break}for(;ht<3;){if(0===h)break t;h--,lt+=i[o++]<<ht,ht+=8}switch(r.last=1&lt,lt>>>=1,ht-=1,3&lt){case 0:r.mode=W;break;case 1:if(u(r),r.mode=J,e===x){lt>>>=2,ht-=2;break t}break;case 2:r.mode=G;break;case 3:t.msg="invalid block type",r.mode=ot}lt>>>=2,ht-=2;break;case W:for(lt>>>=7&ht,ht-=7&ht;ht<32;){if(0===h)break t;h--,lt+=i[o++]<<ht,ht+=8}if((65535&lt)!=(lt>>>16^65535)){t.msg="invalid stored block lengths",r.mode=ot;break}if(r.length=65535&lt,lt=0,ht=0,r.mode=H,e===x)break t;case H:r.mode=V;case V:if(dt=r.length){if(dt>h&&(dt=h),dt>f&&(dt=f),0===dt)break t;c.arraySet(a,i,o,dt,s),h-=dt,o+=dt,f-=dt,s+=dt,r.length-=dt;break}r.mode=M;break;case G:for(;ht<14;){if(0===h)break t;h--,lt+=i[o++]<<ht,ht+=8}if(r.nlen=257+(31&lt),lt>>>=5,ht-=5,r.ndist=1+(31&lt),lt>>>=5,ht-=5,r.ncode=4+(15&lt),lt>>>=4,ht-=4,r.nlen>286||r.ndist>30){t.msg="too many length or distance symbols",r.mode=ot;break}r.have=0,r.mode=X;case X:for(;r.have<r.ncode;){for(;ht<3;){if(0===h)break t;h--,lt+=i[o++]<<ht,ht+=8}r.lens[Ct[r.have++]]=7&lt,lt>>>=3,ht-=3}for(;r.have<19;)r.lens[Ct[r.have++]]=0;if(r.lencode=r.lendyn,r.lenbits=7,Et={bits:r.lenbits},kt=_(g,r.lens,0,19,r.lencode,0,r.work,Et),r.lenbits=Et.bits,kt){t.msg="invalid code lengths set",r.mode=ot;break}r.have=0,r.mode=K;case K:for(;r.have<r.nlen+r.ndist;){for(;zt=r.lencode[lt&(1<<r.lenbits)-1],_t=zt>>>24,gt=zt>>>16&255,bt=65535&zt,!(_t<=ht);){if(0===h)break t;h--,lt+=i[o++]<<ht,ht+=8}if(bt<16)lt>>>=_t,ht-=_t,r.lens[r.have++]=bt;else{if(16===bt){for(At=_t+2;ht<At;){if(0===h)break t;h--,lt+=i[o++]<<ht,ht+=8}if(lt>>>=_t,ht-=_t,0===r.have){t.msg="invalid bit length repeat",r.mode=ot;break}xt=r.lens[r.have-1],dt=3+(3&lt),lt>>>=2,ht-=2}else if(17===bt){for(At=_t+3;ht<At;){if(0===h)break t;h--,lt+=i[o++]<<ht,ht+=8}ht-=_t,xt=0,dt=3+(7&(lt>>>=_t)),lt>>>=3,ht-=3}else{for(At=_t+7;ht<At;){if(0===h)break t;h--,lt+=i[o++]<<ht,ht+=8}ht-=_t,xt=0,dt=11+(127&(lt>>>=_t)),lt>>>=7,ht-=7}if(r.have+dt>r.nlen+r.ndist){t.msg="invalid bit length repeat",r.mode=ot;break}for(;dt--;)r.lens[r.have++]=xt}}if(r.mode===ot)break;if(0===r.lens[256]){t.msg="invalid code -- missing end-of-block",r.mode=ot;break}if(r.lenbits=9,Et={bits:r.lenbits},kt=_(b,r.lens,0,r.nlen,r.lencode,0,r.work,Et),r.lenbits=Et.bits,kt){t.msg="invalid literal/lengths set",r.mode=ot;break}if(r.distbits=6,r.distcode=r.distdyn,Et={bits:r.distbits},kt=_(w,r.lens,r.nlen,r.ndist,r.distcode,0,r.work,Et),r.distbits=Et.bits,kt){t.msg="invalid distances set",r.mode=ot;break}if(r.mode=J,e===x)break t;case J:r.mode=q;case q:if(h>=6&&f>=258){t.next_out=s,t.avail_out=f,t.next_in=o,t.avail_in=h,r.hold=lt,r.bits=ht,m(t,ct),s=t.next_out,a=t.output,f=t.avail_out,o=t.next_in,i=t.input,h=t.avail_in,lt=r.hold,ht=r.bits,r.mode===M&&(r.back=-1);break}for(r.back=0;zt=r.lencode[lt&(1<<r.lenbits)-1],_t=zt>>>24,gt=zt>>>16&255,bt=65535&zt,!(_t<=ht);){if(0===h)break t;h--,lt+=i[o++]<<ht,ht+=8}if(gt&&0==(240&gt)){for(wt=_t,yt=gt,vt=bt;zt=r.lencode[vt+((lt&(1<<wt+yt)-1)>>wt)],_t=zt>>>24,gt=zt>>>16&255,bt=65535&zt,!(wt+_t<=ht);){if(0===h)break t;h--,lt+=i[o++]<<ht,ht+=8}lt>>>=wt,ht-=wt,r.back+=wt}if(lt>>>=_t,ht-=_t,r.back+=_t,r.length=bt,0===gt){r.mode=rt;break}if(32&gt){r.back=-1,r.mode=M;break}if(64&gt){t.msg="invalid literal/length code",r.mode=ot;break}r.extra=15&gt,r.mode=$;case $:if(r.extra){for(At=r.extra;ht<At;){if(0===h)break t;h--,lt+=i[o++]<<ht,ht+=8}r.length+=lt&(1<<r.extra)-1,lt>>>=r.extra,ht-=r.extra,r.back+=r.extra}r.was=r.length,r.mode=Q;case Q:for(;zt=r.distcode[lt&(1<<r.distbits)-1],_t=zt>>>24,gt=zt>>>16&255,bt=65535&zt,!(_t<=ht);){if(0===h)break t;h--,lt+=i[o++]<<ht,ht+=8}if(0==(240&gt)){for(wt=_t,yt=gt,vt=bt;zt=r.distcode[vt+((lt&(1<<wt+yt)-1)>>wt)],_t=zt>>>24,gt=zt>>>16&255,bt=65535&zt,!(wt+_t<=ht);){if(0===h)break t;h--,lt+=i[o++]<<ht,ht+=8}lt>>>=wt,ht-=wt,r.back+=wt}if(lt>>>=_t,ht-=_t,r.back+=_t,64&gt){t.msg="invalid distance code",r.mode=ot;break}r.offset=bt,r.extra=15&gt,r.mode=tt;case tt:if(r.extra){for(At=r.extra;ht<At;){if(0===h)break t;h--,lt+=i[o++]<<ht,ht+=8}r.offset+=lt&(1<<r.extra)-1,lt>>>=r.extra,ht-=r.extra,r.back+=r.extra}if(r.offset>r.dmax){t.msg="invalid distance too far back",r.mode=ot;break}r.mode=et;case et:if(0===f)break t;if(dt=ct-f,r.offset>dt){if((dt=r.offset-dt)>r.whave&&r.sane){t.msg="invalid distance too far back",r.mode=ot;break}dt>r.wnext?(dt-=r.wnext,pt=r.wsize-dt):pt=r.wnext-dt,dt>r.length&&(dt=r.length),mt=r.window}else mt=a,pt=s-r.offset,dt=r.length;dt>f&&(dt=f),f-=dt,r.length-=dt;do{a[s++]=mt[pt++]}while(--dt);0===r.length&&(r.mode=q);break;case rt:if(0===f)break t;a[s++]=r.length,f--,r.mode=q;break;case nt:if(r.wrap){for(;ht<32;){if(0===h)break t;h--,lt|=i[o++]<<ht,ht+=8}if(ct-=f,t.total_out+=ct,r.total+=ct,ct&&(t.adler=r.check=r.flags?p(r.check,a,ct,s-ct):d(r.check,a,ct,s-ct)),ct=f,(r.flags?lt:n(lt))!==r.check){t.msg="incorrect data check",r.mode=ot;break}lt=0,ht=0}r.mode=it;case it:if(r.wrap&&r.flags){for(;ht<32;){if(0===h)break t;h--,lt+=i[o++]<<ht,ht+=8}if(lt!==(4294967295&r.total)){t.msg="incorrect length check",r.mode=ot;break}lt=0,ht=0}r.mode=at;case at:kt=E;break t;case ot:kt=I;break t;case st:return C;case ut:default:return z}return t.next_out=s,t.avail_out=f,t.next_in=o,t.avail_in=h,r.hold=lt,r.bits=ht,(r.wsize||ct!==t.avail_out&&r.mode<ot&&(r.mode<nt||e!==y))&&l(t,t.output,t.next_out,ct-t.avail_out)?(r.mode=st,C):(ft-=t.avail_in,ct-=t.avail_out,t.total_in+=ft,t.total_out+=ct,r.total+=ct,r.wrap&&ct&&(t.adler=r.check=r.flags?p(r.check,a,ct,t.next_out-ct):d(r.check,a,ct,t.next_out-ct)),t.data_type=r.bits+(r.last?64:0)+(r.mode===M?128:0)+(r.mode===J||r.mode===H?256:0),(0===ft&&0===ct||e===y)&&kt===k&&(kt=S),kt)},r.inflateEnd=function(t){if(!t||!t.state)return z;var e=t.state;return e.window&&(e.window=null),t.state=null,k},r.inflateGetHeader=function(t,e){var r;return t&&t.state?0==(2&(r=t.state).wrap)?z:(r.head=e,e.done=!1,k):z},r.inflateSetDictionary=function(t,e){var r,n,i=e.length;return t&&t.state?0!==(r=t.state).wrap&&r.mode!==Z?z:r.mode===Z&&(n=1,(n=d(n,e,i,0))!==r.check)?I:l(t,e,i,i)?(r.mode=st,C):(r.havedict=1,k):z},r.inflateInfo="pako inflate (from Nodeca project)"},{"../utils/common":32,"./adler32":34,"./crc32":36,"./inffast":39,"./inftrees":41}],41:[function(t,e,r){"use strict";var n=t("../utils/common"),i=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],a=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],o=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],s=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64];e.exports=function(t,e,r,u,l,h,f,c){var d,p,m,_,g,b,w,y,v,x=c.bits,k=0,E=0,A=0,z=0,I=0,C=0,S=0,B=0,R=0,T=0,O=null,L=0,D=new n.Buf16(16),U=new n.Buf16(16),j=null,P=0;for(k=0;k<=15;k++)D[k]=0;for(E=0;E<u;E++)D[e[r+E]]++;for(I=x,z=15;z>=1&&0===D[z];z--);if(I>z&&(I=z),0===z)return l[h++]=20971520,l[h++]=20971520,c.bits=1,0;for(A=1;A<z&&0===D[A];A++);for(I<A&&(I=A),B=1,k=1;k<=15;k++)if(B<<=1,(B-=D[k])<0)return-1;if(B>0&&(0===t||1!==z))return-1;for(U[1]=0,k=1;k<15;k++)U[k+1]=U[k]+D[k];for(E=0;E<u;E++)0!==e[r+E]&&(f[U[e[r+E]]++]=E);if(0===t?(O=j=f,b=19):1===t?(O=i,L-=257,j=a,P-=257,b=256):(O=o,j=s,b=-1),T=0,E=0,k=A,g=h,C=I,S=0,m=-1,R=1<<I,_=R-1,1===t&&R>852||2===t&&R>592)return 1;for(;;){0,w=k-S,f[E]<b?(y=0,v=f[E]):f[E]>b?(y=j[P+f[E]],v=O[L+f[E]]):(y=96,v=0),d=1<<k-S,A=p=1<<C;do{l[g+(T>>S)+(p-=d)]=w<<24|y<<16|v|0}while(0!==p);for(d=1<<k-1;T&d;)d>>=1;if(0!==d?(T&=d-1,T+=d):T=0,E++,0==--D[k]){if(k===z)break;k=e[r+f[E]]}if(k>I&&(T&_)!==m){for(0===S&&(S=I),g+=A,B=1<<(C=k-S);C+S<z&&!((B-=D[C+S])<=0);)C++,B<<=1;if(R+=1<<C,1===t&&R>852||2===t&&R>592)return 1;l[m=T&_]=I<<24|C<<16|g-h|0}}return 0!==T&&(l[g+T]=k-S<<24|64<<16|0),c.bits=I,0}},{"../utils/common":32}],42:[function(t,e,r){"use strict";e.exports={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"}},{}],43:[function(t,e,r){"use strict";function n(t){for(var e=t.length;--e>=0;)t[e]=0}function i(t,e,r,n,i){this.static_tree=t,this.extra_bits=e,this.extra_base=r,this.elems=n,this.max_length=i,this.has_stree=t&&t.length}function a(t,e){this.dyn_tree=t,this.max_code=0,this.stat_desc=e}function o(t){return t<256?X[t]:X[256+(t>>>7)]}function s(t,e){t.pending_buf[t.pending++]=255&e,t.pending_buf[t.pending++]=e>>>8&255}function u(t,e,r){t.bi_valid>U-r?(t.bi_buf|=e<<t.bi_valid&65535,s(t,t.bi_buf),t.bi_buf=e>>U-t.bi_valid,t.bi_valid+=r-U):(t.bi_buf|=e<<t.bi_valid&65535,t.bi_valid+=r)}function l(t,e,r){u(t,r[2*e],r[2*e+1])}function h(t,e){var r=0;do{r|=1&t,t>>>=1,r<<=1}while(--e>0);return r>>>1}function f(t,e,r){var n,i,a=new Array(D+1),o=0;for(n=1;n<=D;n++)a[n]=o=o+r[n-1]<<1;for(i=0;i<=e;i++){var s=t[2*i+1];0!==s&&(t[2*i]=h(a[s]++,s))}}function c(t){var e;for(e=0;e<R;e++)t.dyn_ltree[2*e]=0;for(e=0;e<T;e++)t.dyn_dtree[2*e]=0;for(e=0;e<O;e++)t.bl_tree[2*e]=0;t.dyn_ltree[2*P]=1,t.opt_len=t.static_len=0,t.last_lit=t.matches=0}function d(t){t.bi_valid>8?s(t,t.bi_buf):t.bi_valid>0&&(t.pending_buf[t.pending++]=t.bi_buf),t.bi_buf=0,t.bi_valid=0}function p(t,e,r,n){var i=2*e,a=2*r;return t[i]<t[a]||t[i]===t[a]&&n[e]<=n[r]}function m(t,e,r){for(var n=t.heap[r],i=r<<1;i<=t.heap_len&&(i<t.heap_len&&p(e,t.heap[i+1],t.heap[i],t.depth)&&i++,!p(e,n,t.heap[i],t.depth));)t.heap[r]=t.heap[i],r=i,i<<=1;t.heap[r]=n}function _(t,e,r){var n,i,a,s,h=0;if(0!==t.last_lit)do{n=t.pending_buf[t.d_buf+2*h]<<8|t.pending_buf[t.d_buf+2*h+1],i=t.pending_buf[t.l_buf+h],h++,0===n?l(t,i,e):(l(t,(a=K[i])+B+1,e),0!==(s=M[a])&&u(t,i-=J[a],s),l(t,a=o(--n),r),0!==(s=Y[a])&&u(t,n-=q[a],s))}while(h<t.last_lit);l(t,P,e)}function g(t,e){var r,n,i,a=e.dyn_tree,o=e.stat_desc.static_tree,s=e.stat_desc.has_stree,u=e.stat_desc.elems,l=-1;for(t.heap_len=0,t.heap_max=L,r=0;r<u;r++)0!==a[2*r]?(t.heap[++t.heap_len]=l=r,t.depth[r]=0):a[2*r+1]=0;for(;t.heap_len<2;)a[2*(i=t.heap[++t.heap_len]=l<2?++l:0)]=1,t.depth[i]=0,t.opt_len--,s&&(t.static_len-=o[2*i+1]);for(e.max_code=l,r=t.heap_len>>1;r>=1;r--)m(t,a,r);i=u;do{r=t.heap[1],t.heap[1]=t.heap[t.heap_len--],m(t,a,1),n=t.heap[1],t.heap[--t.heap_max]=r,t.heap[--t.heap_max]=n,a[2*i]=a[2*r]+a[2*n],t.depth[i]=(t.depth[r]>=t.depth[n]?t.depth[r]:t.depth[n])+1,a[2*r+1]=a[2*n+1]=i,t.heap[1]=i++,m(t,a,1)}while(t.heap_len>=2);t.heap[--t.heap_max]=t.heap[1],function(t,e){var r,n,i,a,o,s,u=e.dyn_tree,l=e.max_code,h=e.stat_desc.static_tree,f=e.stat_desc.has_stree,c=e.stat_desc.extra_bits,d=e.stat_desc.extra_base,p=e.stat_desc.max_length,m=0;for(a=0;a<=D;a++)t.bl_count[a]=0;for(u[2*t.heap[t.heap_max]+1]=0,r=t.heap_max+1;r<L;r++)(a=u[2*u[2*(n=t.heap[r])+1]+1]+1)>p&&(a=p,m++),u[2*n+1]=a,n>l||(t.bl_count[a]++,o=0,n>=d&&(o=c[n-d]),s=u[2*n],t.opt_len+=s*(a+o),f&&(t.static_len+=s*(h[2*n+1]+o)));if(0!==m){do{for(a=p-1;0===t.bl_count[a];)a--;t.bl_count[a]--,t.bl_count[a+1]+=2,t.bl_count[p]--,m-=2}while(m>0);for(a=p;0!==a;a--)for(n=t.bl_count[a];0!==n;)(i=t.heap[--r])>l||(u[2*i+1]!==a&&(t.opt_len+=(a-u[2*i+1])*u[2*i],u[2*i+1]=a),n--)}}(t,e),f(a,l,t.bl_count)}function b(t,e,r){var n,i,a=-1,o=e[1],s=0,u=7,l=4;for(0===o&&(u=138,l=3),e[2*(r+1)+1]=65535,n=0;n<=r;n++)i=o,o=e[2*(n+1)+1],++s<u&&i===o||(s<l?t.bl_tree[2*i]+=s:0!==i?(i!==a&&t.bl_tree[2*i]++,t.bl_tree[2*N]++):s<=10?t.bl_tree[2*F]++:t.bl_tree[2*Z]++,s=0,a=i,0===o?(u=138,l=3):i===o?(u=6,l=3):(u=7,l=4))}function w(t,e,r){var n,i,a=-1,o=e[1],s=0,h=7,f=4;for(0===o&&(h=138,f=3),n=0;n<=r;n++)if(i=o,o=e[2*(n+1)+1],!(++s<h&&i===o)){if(s<f)do{l(t,i,t.bl_tree)}while(0!=--s);else 0!==i?(i!==a&&(l(t,i,t.bl_tree),s--),l(t,N,t.bl_tree),u(t,s-3,2)):s<=10?(l(t,F,t.bl_tree),u(t,s-3,3)):(l(t,Z,t.bl_tree),u(t,s-11,7));s=0,a=i,0===o?(h=138,f=3):i===o?(h=6,f=3):(h=7,f=4)}}function y(t,e,r,n){u(t,(z<<1)+(n?1:0),3),function(t,e,r,n){d(t),n&&(s(t,r),s(t,~r)),v.arraySet(t.pending_buf,t.window,e,r,t.pending),t.pending+=r}(t,e,r,!0)}var v=t("../utils/common"),x=4,k=0,E=1,A=2,z=0,I=1,C=2,S=29,B=256,R=B+1+S,T=30,O=19,L=2*R+1,D=15,U=16,j=7,P=256,N=16,F=17,Z=18,M=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],Y=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],W=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],H=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],V=new Array(2*(R+2));n(V);var G=new Array(2*T);n(G);var X=new Array(512);n(X);var K=new Array(256);n(K);var J=new Array(S);n(J);var q=new Array(T);n(q);var $,Q,tt,et=!1;r._tr_init=function(t){et||(function(){var t,e,r,n,a,o=new Array(D+1);for(r=0,n=0;n<S-1;n++)for(J[n]=r,t=0;t<1<<M[n];t++)K[r++]=n;for(K[r-1]=n,a=0,n=0;n<16;n++)for(q[n]=a,t=0;t<1<<Y[n];t++)X[a++]=n;for(a>>=7;n<T;n++)for(q[n]=a<<7,t=0;t<1<<Y[n]-7;t++)X[256+a++]=n;for(e=0;e<=D;e++)o[e]=0;for(t=0;t<=143;)V[2*t+1]=8,t++,o[8]++;for(;t<=255;)V[2*t+1]=9,t++,o[9]++;for(;t<=279;)V[2*t+1]=7,t++,o[7]++;for(;t<=287;)V[2*t+1]=8,t++,o[8]++;for(f(V,R+1,o),t=0;t<T;t++)G[2*t+1]=5,G[2*t]=h(t,5);$=new i(V,M,B+1,R,D),Q=new i(G,Y,0,T,D),tt=new i(new Array(0),W,0,O,j)}(),et=!0),t.l_desc=new a(t.dyn_ltree,$),t.d_desc=new a(t.dyn_dtree,Q),t.bl_desc=new a(t.bl_tree,tt),t.bi_buf=0,t.bi_valid=0,c(t)},r._tr_stored_block=y,r._tr_flush_block=function(t,e,r,n){var i,a,o=0;t.level>0?(t.strm.data_type===A&&(t.strm.data_type=function(t){var e,r=4093624447;for(e=0;e<=31;e++,r>>>=1)if(1&r&&0!==t.dyn_ltree[2*e])return k;if(0!==t.dyn_ltree[18]||0!==t.dyn_ltree[20]||0!==t.dyn_ltree[26])return E;for(e=32;e<B;e++)if(0!==t.dyn_ltree[2*e])return E;return k}(t)),g(t,t.l_desc),g(t,t.d_desc),o=function(t){var e;for(b(t,t.dyn_ltree,t.l_desc.max_code),b(t,t.dyn_dtree,t.d_desc.max_code),g(t,t.bl_desc),e=O-1;e>=3&&0===t.bl_tree[2*H[e]+1];e--);return t.opt_len+=3*(e+1)+5+5+4,e}(t),i=t.opt_len+3+7>>>3,(a=t.static_len+3+7>>>3)<=i&&(i=a)):i=a=r+5,r+4<=i&&-1!==e?y(t,e,r,n):t.strategy===x||a===i?(u(t,(I<<1)+(n?1:0),3),_(t,V,G)):(u(t,(C<<1)+(n?1:0),3),function(t,e,r,n){var i;for(u(t,e-257,5),u(t,r-1,5),u(t,n-4,4),i=0;i<n;i++)u(t,t.bl_tree[2*H[i]+1],3);w(t,t.dyn_ltree,e-1),w(t,t.dyn_dtree,r-1)}(t,t.l_desc.max_code+1,t.d_desc.max_code+1,o+1),_(t,t.dyn_ltree,t.dyn_dtree)),c(t),n&&d(t)},r._tr_tally=function(t,e,r){return t.pending_buf[t.d_buf+2*t.last_lit]=e>>>8&255,t.pending_buf[t.d_buf+2*t.last_lit+1]=255&e,t.pending_buf[t.l_buf+t.last_lit]=255&r,t.last_lit++,0===e?t.dyn_ltree[2*r]++:(t.matches++,e--,t.dyn_ltree[2*(K[r]+B+1)]++,t.dyn_dtree[2*o(e)]++),t.last_lit===t.lit_bufsize-1},r._tr_align=function(t){u(t,I<<1,3),l(t,P,V),function(t){16===t.bi_valid?(s(t,t.bi_buf),t.bi_buf=0,t.bi_valid=0):t.bi_valid>=8&&(t.pending_buf[t.pending++]=255&t.bi_buf,t.bi_buf>>=8,t.bi_valid-=8)}(t)}},{"../utils/common":32}],44:[function(t,e,r){"use strict";e.exports=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}},{}],45:[function(t,e,r){function n(t){return h[t]}function i(t){if("string"==typeof t)return t;if(null==t)return"";if(function(t){return"symbol"==typeof t||function(t){return!!t&&"object"==typeof t}(t)&&f.call(t)==s}(t))return c?p.call(t):"";var e=t+"";return"0"==e&&1/t==-o?"-0":e}var a=t("lodash._root"),o=1/0,s="[object Symbol]",u=/[&<>"'`]/g,l=RegExp(u.source),h={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","`":"&#96;"},f=Object.prototype.toString,c=a.Symbol,d=c?c.prototype:void 0,p=c?d.toString:void 0;e.exports=function(t){return(t=i(t))&&l.test(t)?t.replace(u,n):t}},{"lodash._root":46}],46:[function(t,e,r){(function(t){function n(t){return t&&t.Object===Object?t:null}var i={function:!0,object:!0},a=i[typeof r]&&r&&!r.nodeType?r:void 0,o=i[typeof e]&&e&&!e.nodeType?e:void 0,s=n(a&&o&&"object"==typeof t&&t),u=n(i[typeof self]&&self),l=n(i[typeof window]&&window),h=n(i[typeof this]&&this),f=s||l!==(h&&h.window)&&l||u||h||Function("return this")();e.exports=f}).call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],47:[function(t,e,r){function n(t,e,r,s,p){if(!function(t){var e=typeof t;return!!t&&("object"==e||"function"==e)}(t))return t;var m=i(e)&&(l(e)||f(e)),_=m?void 0:c(e);return o(_||e,function(o,c){if(_&&(o=e[c=o]),function(t){return!!t&&"object"==typeof t}(o))s||(s=[]),p||(p=[]),function(t,e,r,n,o,s,c){var p=s.length,m=e[r];for(;p--;)if(s[p]==m)return void(t[r]=c[p]);var _=t[r],g=o?o(_,m,r,t,e):void 0,b=void 0===g;b&&(g=m,i(m)&&(l(m)||f(m))?g=l(_)?_:i(_)?a(_):[]:h(m)||u(m)?g=u(_)?d(_):h(_)?_:{}:b=!1);s.push(m),c.push(g),b?t[r]=n(g,m,o,s,c):(g==g?g!==_:_==_)&&(t[r]=g)}(t,e,c,n,r,s,p);else{var g=t[c],b=r?r(g,o,c,t,e):void 0,w=void 0===b;w&&(b=o),void 0===b&&(!m||c in t)||!w&&(b==b?b===g:g!=g)||(t[c]=b)}}),t}function i(t){return null!=t&&function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=p}(m(t))}var a=t("lodash._arraycopy"),o=t("lodash._arrayeach"),s=t("lodash._createassigner"),u=t("lodash.isarguments"),l=t("lodash.isarray"),h=t("lodash.isplainobject"),f=t("lodash.istypedarray"),c=t("lodash.keys"),d=t("lodash.toplainobject"),p=9007199254740991,m=function(t){return function(e){return null==e?void 0:e[t]}}("length"),_=s(n);e.exports=_},{"lodash._arraycopy":48,"lodash._arrayeach":49,"lodash._createassigner":50,"lodash.isarguments":55,"lodash.isarray":56,"lodash.isplainobject":57,"lodash.istypedarray":59,"lodash.keys":60,"lodash.toplainobject":62}],48:[function(t,e,r){e.exports=function(t,e){var r=-1,n=t.length;for(e||(e=Array(n));++r<n;)e[r]=t[r];return e}},{}],49:[function(t,e,r){e.exports=function(t,e){for(var r=-1,n=t.length;++r<n&&!1!==e(t[r],r,t););return t}},{}],50:[function(t,e,r){var n=t("lodash._bindcallback"),i=t("lodash._isiterateecall"),a=t("lodash.restparam");e.exports=function(t){return a(function(e,r){var a=-1,o=null==e?0:r.length,s=o>2?r[o-2]:void 0,u=o>2?r[2]:void 0,l=o>1?r[o-1]:void 0;for("function"==typeof s?(s=n(s,l,5),o-=2):o-=(s="function"==typeof l?l:void 0)?1:0,u&&i(r[0],r[1],u)&&(s=o<3?void 0:s,o=1);++a<o;){var h=r[a];h&&t(e,h,s)}return e})}},{"lodash._bindcallback":51,"lodash._isiterateecall":52,"lodash.restparam":53}],51:[function(t,e,r){function n(t){return t}e.exports=function(t,e,r){if("function"!=typeof t)return n;if(void 0===e)return t;switch(r){case 1:return function(r){return t.call(e,r)};case 3:return function(r,n,i){return t.call(e,r,n,i)};case 4:return function(r,n,i,a){return t.call(e,r,n,i,a)};case 5:return function(r,n,i,a,o){return t.call(e,r,n,i,a,o)}}return function(){return t.apply(e,arguments)}}},{}],52:[function(t,e,r){function n(t){return null!=t&&function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=a}(o(t))}var i=/^\d+$/,a=9007199254740991,o=function(t){return function(e){return null==e?void 0:e[t]}}("length");e.exports=function(t,e,r){if(!function(t){var e=typeof r;return!!r&&("object"==e||"function"==e)}())return!1;var o=typeof e;if("number"==o?n(r)&&function(t,e){return t="number"==typeof t||i.test(t)?+t:-1,e=null==e?a:e,t>-1&&t%1==0&&t<e}(e,r.length):"string"==o&&e in r){var s=r[e];return t==t?t===s:s!=s}return!1}},{}],53:[function(t,e,r){var n="Expected a function",i=Math.max;e.exports=function(t,e){if("function"!=typeof t)throw new TypeError(n);return e=i(void 0===e?t.length-1:+e||0,0),function(){for(var r=arguments,n=-1,a=i(r.length-e,0),o=Array(a);++n<a;)o[n]=r[e+n];switch(e){case 0:return t.call(this,o);case 1:return t.call(this,r[0],o);case 2:return t.call(this,r[0],r[1],o)}var s=Array(e+1);for(n=-1;++n<e;)s[n]=r[n];return s[e]=o,t.apply(this,s)}}},{}],54:[function(t,e,r){var n="[object Function]",i=/^\[object .+?Constructor\]$/,a=Object.prototype,o=Function.prototype.toString,s=a.hasOwnProperty,u=a.toString,l=RegExp("^"+o.call(s).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");e.exports=function(t,e){var r=null==t?void 0:t[e];return function(t){return null!=t&&(function(t){return function(t){var e=typeof t;return!!t&&("object"==e||"function"==e)}(t)&&u.call(t)==n}(t)?l.test(o.call(t)):function(t){return!!t&&"object"==typeof t}(t)&&i.test(t))}(r)?r:void 0}},{}],55:[function(t,e,r){var n=9007199254740991,i="[object Arguments]",a="[object Function]",o="[object GeneratorFunction]",s=Object.prototype,u=s.hasOwnProperty,l=s.toString,h=s.propertyIsEnumerable,f=function(t){return function(e){return null==e?void 0:e[t]}}("length");e.exports=function(t){return function(t){return function(t){return!!t&&"object"==typeof t}(t)&&function(t){return null!=t&&function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=n}(f(t))&&!function(t){var e=function(t){var e=typeof t;return!!t&&("object"==e||"function"==e)}(t)?l.call(t):"";return e==a||e==o}(t)}(t)}(t)&&u.call(t,"callee")&&(!h.call(t,"callee")||l.call(t)==i)}},{}],56:[function(t,e,r){function n(t){return!!t&&"object"==typeof t}var i="[object Function]",a=/^\[object .+?Constructor\]$/,o=Object.prototype,s=Function.prototype.toString,u=o.hasOwnProperty,l=o.toString,h=RegExp("^"+s.call(u).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),f=9007199254740991,c=function(t,e){var r=null==t?void 0:t[e];return function(t){return null!=t&&(function(t){return function(t){var e=typeof t;return!!t&&("object"==e||"function"==e)}(t)&&l.call(t)==i}(t)?h.test(s.call(t)):n(t)&&a.test(t))}(r)?r:void 0}(Array,"isArray")||function(t){return n(t)&&function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=f}(t.length)&&"[object Array]"==l.call(t)};e.exports=c},{}],57:[function(t,e,r){var n=t("lodash._basefor"),i=t("lodash.isarguments"),a=t("lodash.keysin"),o="[object Object]",s=Object.prototype,u=s.hasOwnProperty,l=s.toString;e.exports=function(t){var e;if(!function(t){return!!t&&"object"==typeof t}(t)||l.call(t)!=o||i(t)||!u.call(t,"constructor")&&"function"==typeof(e=t.constructor)&&!(e instanceof e))return!1;var r;return n(t,function(t,e){r=e},a),void 0===r||u.call(t,r)}},{"lodash._basefor":58,"lodash.isarguments":55,"lodash.keysin":61}],58:[function(t,e,r){var n=function(t){return function(e,r,n){for(var i=-1,a=Object(e),o=n(e),s=o.length;s--;){var u=o[t?s:++i];if(!1===r(a[u],u,a))break}return e}}();e.exports=n},{}],59:[function(t,e,r){var n=9007199254740991,i={};i["[object Float32Array]"]=i["[object Float64Array]"]=i["[object Int8Array]"]=i["[object Int16Array]"]=i["[object Int32Array]"]=i["[object Uint8Array]"]=i["[object Uint8ClampedArray]"]=i["[object Uint16Array]"]=i["[object Uint32Array]"]=!0,i["[object Arguments]"]=i["[object Array]"]=i["[object ArrayBuffer]"]=i["[object Boolean]"]=i["[object DataView]"]=i["[object Date]"]=i["[object Error]"]=i["[object Function]"]=i["[object Map]"]=i["[object Number]"]=i["[object Object]"]=i["[object RegExp]"]=i["[object Set]"]=i["[object String]"]=i["[object WeakMap]"]=!1;var a=Object.prototype.toString;e.exports=function(t){return function(t){return!!t&&"object"==typeof t}(t)&&function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=n}(t.length)&&!!i[a.call(t)]}},{}],60:[function(t,e,r){function n(t,e){return t="number"==typeof t||h.test(t)?+t:-1,e=null==e?d:e,t>-1&&t%1==0&&t<e}function i(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=d}function a(t){for(var e=function(t){if(null==t)return[];o(t)||(t=Object(t));var e=t.length;e=e&&i(e)&&(l(t)||u(t))&&e||0;var r=t.constructor,a=-1,s="function"==typeof r&&r.prototype===t,h=Array(e),c=e>0;for(;++a<e;)h[a]=a+"";for(var d in t)c&&n(d,e)||"constructor"==d&&(s||!f.call(t,d))||h.push(d);return h}(t),r=e.length,a=r&&t.length,s=!!a&&i(a)&&(l(t)||u(t)),h=-1,c=[];++h<r;){var d=e[h];(s&&n(d,a)||f.call(t,d))&&c.push(d)}return c}function o(t){var e=typeof t;return!!t&&("object"==e||"function"==e)}var s=t("lodash._getnative"),u=t("lodash.isarguments"),l=t("lodash.isarray"),h=/^\d+$/,f=Object.prototype.hasOwnProperty,c=s(Object,"keys"),d=9007199254740991,p=function(t){return function(e){return null==e?void 0:e[t]}}("length"),m=c?function(t){var e=null==t?void 0:t.constructor;return"function"==typeof e&&e.prototype===t||"function"!=typeof t&&function(t){return null!=t&&i(p(t))}(t)?a(t):o(t)?c(t):[]}:a;e.exports=m},{"lodash._getnative":54,"lodash.isarguments":55,"lodash.isarray":56}],61:[function(t,e,r){function n(t,e){return t="number"==typeof t||o.test(t)?+t:-1,e=null==e?u:e,t>-1&&t%1==0&&t<e}var i=t("lodash.isarguments"),a=t("lodash.isarray"),o=/^\d+$/,s=Object.prototype.hasOwnProperty,u=9007199254740991;e.exports=function(t){if(null==t)return[];(function(e){var r=typeof t;return!!t&&("object"==r||"function"==r)})()||(t=Object(t));var e=t.length;e=e&&"number"==typeof e&&e>-1&&e%1==0&&e<=u&&(a(t)||i(t))&&e||0;for(var r=t.constructor,o=-1,l="function"==typeof r&&r.prototype===t,h=Array(e),f=e>0;++o<e;)h[o]=o+"";for(var c in t)f&&n(c,e)||"constructor"==c&&(l||!s.call(t,c))||h.push(c);return h}},{"lodash.isarguments":55,"lodash.isarray":56}],62:[function(t,e,r){var n=t("lodash._basecopy"),i=t("lodash.keysin");e.exports=function(t){return n(t,i(t))}},{"lodash._basecopy":63,"lodash.keysin":61}],63:[function(t,e,r){e.exports=function(t,e,r){r||(r={});for(var n=-1,i=e.length;++n<i;){var a=e[n];r[a]=t[a]}return r}},{}],64:[function(t,e,r){var n,i;n=t("jszip"),i=t("./internal"),e.exports={asBlob:function(t,e){var r;return r=new n,i.addFiles(r,t,e),i.generateDocument(r)}}},{"./internal":65,jszip:14}],65:[function(t,e,r){(function(r,n){var i,a,o;i=t("./templates/document"),a=t("./utils"),o={merge:t("lodash.merge")},e.exports={generateDocument:function(t){var e;if(e=t.generate({type:"arraybuffer"}),r.Blob)return new Blob([e],{type:"application/vnd.openxmlformats-officedocument.wordprocessingml.document"});if(r.Buffer)return new n(new Uint8Array(e));throw new Error("Neither Blob nor Buffer are accessible in this environment. Consider adding Blob.js shim")},renderDocumentFile:function(t){var e;return null==t&&(t={}),e=o.merge({margins:{top:1440,right:1440,bottom:1440,left:1440,header:720,footer:720,gutter:0}},function(){switch(t.orientation){case"landscape":return{height:12240,width:15840,orient:"landscape"};default:return{width:12240,height:15840,orient:"portrait"}}}(),{margins:t.margins}),i(e)},addFiles:function(t,e,r){return t.file("[Content_Types].xml",n("PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9InllcyI/Pgo8VHlwZXMgeG1sbnM9Imh0dHA6Ly9zY2hlbWFzLm9wZW54bWxmb3JtYXRzLm9yZy9wYWNrYWdlLzIwMDYvY29udGVudC10eXBlcyI+CiAgPERlZmF1bHQgRXh0ZW5zaW9uPSJyZWxzIiBDb250ZW50VHlwZT0KICAgICJhcHBsaWNhdGlvbi92bmQub3BlbnhtbGZvcm1hdHMtcGFja2FnZS5yZWxhdGlvbnNoaXBzK3htbCIgLz4KICA8T3ZlcnJpZGUgUGFydE5hbWU9Ii93b3JkL2RvY3VtZW50LnhtbCIgQ29udGVudFR5cGU9CiAgICAiYXBwbGljYXRpb24vdm5kLm9wZW54bWxmb3JtYXRzLW9mZmljZWRvY3VtZW50LndvcmRwcm9jZXNzaW5nbWwuZG9jdW1lbnQubWFpbit4bWwiLz4KICA8T3ZlcnJpZGUgUGFydE5hbWU9Ii93b3JkL2FmY2h1bmsubWh0IiBDb250ZW50VHlwZT0ibWVzc2FnZS9yZmM4MjIiLz4KPC9UeXBlcz4K","base64")),t.folder("_rels").file(".rels",n("PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9InllcyI/Pgo8UmVsYXRpb25zaGlwcyB4bWxucz0iaHR0cDovL3NjaGVtYXMub3BlbnhtbGZvcm1hdHMub3JnL3BhY2thZ2UvMjAwNi9yZWxhdGlvbnNoaXBzIj4KICA8UmVsYXRpb25zaGlwCiAgICAgIFR5cGU9Imh0dHA6Ly9zY2hlbWFzLm9wZW54bWxmb3JtYXRzLm9yZy9vZmZpY2VEb2N1bWVudC8yMDA2L3JlbGF0aW9uc2hpcHMvb2ZmaWNlRG9jdW1lbnQiCiAgICAgIFRhcmdldD0iL3dvcmQvZG9jdW1lbnQueG1sIiBJZD0iUjA5YzgzZmFmYzA2NzQ4OGUiIC8+CjwvUmVsYXRpb25zaGlwcz4K","base64")),t.folder("word").file("document.xml",this.renderDocumentFile(r)).file("afchunk.mht",a.getMHTdocument(e)).folder("_rels").file("document.xml.rels",n("PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9InllcyI/Pgo8UmVsYXRpb25zaGlwcyB4bWxucz0iaHR0cDovL3NjaGVtYXMub3BlbnhtbGZvcm1hdHMub3JnL3BhY2thZ2UvMjAwNi9yZWxhdGlvbnNoaXBzIj4KICA8UmVsYXRpb25zaGlwIFR5cGU9Imh0dHA6Ly9zY2hlbWFzLm9wZW54bWxmb3JtYXRzLm9yZy9vZmZpY2VEb2N1bWVudC8yMDA2L3JlbGF0aW9uc2hpcHMvYUZDaHVuayIKICAgIFRhcmdldD0iL3dvcmQvYWZjaHVuay5taHQiIElkPSJodG1sQ2h1bmsiIC8+CjwvUmVsYXRpb25zaGlwcz4K","base64"))}}}).call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:{},t("buffer").Buffer)},{"./templates/document":66,"./utils":69,buffer:1,"lodash.merge":47}],66:[function(_dereq_,module,exports){var _={escape:_dereq_("lodash.escape")};module.exports=function(obj){var __t,__p="",__j=Array.prototype.join,print=function(){__p+=__j.call(arguments,"")};with(obj||{})__p+='<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n<w:document\n  xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main"\n  xmlns:m="http://schemas.openxmlformats.org/officeDocument/2006/math"\n  xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships"\n  xmlns:wp="http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing"\n  xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main"\n  xmlns:ns6="http://schemas.openxmlformats.org/schemaLibrary/2006/main"\n  xmlns:c="http://schemas.openxmlformats.org/drawingml/2006/chart"\n  xmlns:ns8="http://schemas.openxmlformats.org/drawingml/2006/chartDrawing"\n  xmlns:dgm="http://schemas.openxmlformats.org/drawingml/2006/diagram"\n  xmlns:pic="http://schemas.openxmlformats.org/drawingml/2006/picture"\n  xmlns:ns11="http://schemas.openxmlformats.org/drawingml/2006/spreadsheetDrawing"\n  xmlns:dsp="http://schemas.microsoft.com/office/drawing/2008/diagram"\n  xmlns:ns13="urn:schemas-microsoft-com:office:excel"\n  xmlns:o="urn:schemas-microsoft-com:office:office"\n  xmlns:v="urn:schemas-microsoft-com:vml"\n  xmlns:w10="urn:schemas-microsoft-com:office:word"\n  xmlns:ns17="urn:schemas-microsoft-com:office:powerpoint"\n  xmlns:odx="http://opendope.org/xpaths"\n  xmlns:odc="http://opendope.org/conditions"\n  xmlns:odq="http://opendope.org/questions"\n  xmlns:odi="http://opendope.org/components"\n  xmlns:odgm="http://opendope.org/SmartArt/DataHierarchy"\n  xmlns:ns24="http://schemas.openxmlformats.org/officeDocument/2006/bibliography"\n  xmlns:ns25="http://schemas.openxmlformats.org/drawingml/2006/compatibility"\n  xmlns:ns26="http://schemas.openxmlformats.org/drawingml/2006/lockedCanvas">\n  <w:body>\n    <w:altChunk r:id="htmlChunk" />\n    <w:sectPr>\n      <w:pgSz w:w="'+(null==(__t=width)?"":__t)+'" w:h="'+(null==(__t=height)?"":__t)+'" w:orient="'+(null==(__t=orient)?"":__t)+'" />\n      <w:pgMar w:top="'+(null==(__t=margins.top)?"":__t)+'"\n               w:right="'+(null==(__t=margins.right)?"":__t)+'"\n               w:bottom="'+(null==(__t=margins.bottom)?"":__t)+'"\n               w:left="'+(null==(__t=margins.left)?"":__t)+'"\n               w:header="'+(null==(__t=margins.header)?"":__t)+'"\n               w:footer="'+(null==(__t=margins.footer)?"":__t)+'"\n               w:gutter="'+(null==(__t=margins.gutter)?"":__t)+'"/>\n    </w:sectPr>\n  </w:body>\n</w:document>\n';return __p}},{"lodash.escape":45}],67:[function(_dereq_,module,exports){var _={escape:_dereq_("lodash.escape")};module.exports=function(obj){var __t,__p="",__j=Array.prototype.join,print=function(){__p+=__j.call(arguments,"")};with(obj||{})__p+='MIME-Version: 1.0\nContent-Type: multipart/related;\n    type="text/html";\n    boundary="----=mhtDocumentPart"\n\n\n------=mhtDocumentPart\nContent-Type: text/html;\n    charset="utf-8"\nContent-Transfer-Encoding: quoted-printable\nContent-Location: file:///C:/fake/document.html\n\n'+(null==(__t=htmlSource)?"":__t)+"\n\n"+(null==(__t=contentParts)?"":__t)+"\n\n------=mhtDocumentPart--\n";return __p}},{"lodash.escape":45}],68:[function(_dereq_,module,exports){var _={escape:_dereq_("lodash.escape")};module.exports=function(obj){var __t,__p="",__j=Array.prototype.join,print=function(){__p+=__j.call(arguments,"")};with(obj||{})__p+="------=mhtDocumentPart\nContent-Type: "+(null==(__t=contentType)?"":__t)+"\nContent-Transfer-Encoding: "+(null==(__t=contentEncoding)?"":__t)+"\nContent-Location: "+(null==(__t=contentLocation)?"":__t)+"\n\n"+(null==(__t=encodedContent)?"":__t)+"\n";return __p}},{"lodash.escape":45}],69:[function(t,e,r){var n,i;n=t("./templates/mht_document"),i=t("./templates/mht_part"),e.exports={getMHTdocument:function(t){var e,r;return r=this._prepareImageParts(t),t=r.htmlSource,e=r.imageContentParts,t=t.replace(/\=/g,"=3D"),n({htmlSource:t,contentParts:e.join("\n")})},_prepareImageParts:function(t){var e,r,n;if(e=[],n=/"data:(\w+\/\w+);(\w+),(\S+)"/g,r=function(t,r,n,a){var o,s,u;return u=e.length,s=r.split("/")[1],o="file:///C:/fake/image"+u+"."+s,e.push(i({contentType:r,contentEncoding:n,contentLocation:o,encodedContent:a})),'"'+o+'"'},"string"==typeof t)return/<img/g.test(t)?(t=t.replace(n,r),{htmlSource:t,imageContentParts:e}):{htmlSource:t,imageContentParts:e};throw new Error("Not a valid source provided!")}}},{"./templates/mht_document":67,"./templates/mht_part":68}]},{},[64])(64)});
//# sourceMappingURL=/sm/074cc9f0eaa7eebc8919f806f59cc3c0303247f285edb5a77238866e58c015f6.map