<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rich Text Editor with Save Functionality</title>
    <link rel="stylesheet" href="style.css"> <!-- Include your CSS file here -->
</head>
<body>
    <div class="ribbon">
        <div class="ribbon-inner">
            <img src="logo.png" alt="Lucid Noter Logo" class="logo">
            <h1>VSSKit Noter</h1>
            <span id="exit-button" onclick="closeAndSpinIcon()" class="close-icon"></span>
        </div>
    </div>

    <div class="dropdown">
        <button class="btn" title="File">
            <img src="saveicons/file.png" alt="Custom Icon" class="custom-icon">
        </button>
        
        <div class="dropdown-menu" id="myDropdown">
            <a href="#" id="new-btn" class="dropdown-item" title="New Doc">
                <img src="saveicons/New.png" alt="Custom Icon" style="width: 25px; height: 25px;">
            </a>
            <a href="#" class="dropdown-item" title="Save as Ms Word" onclick="saveAsDOCX()">
                <img src="saveicons/word.png" alt="Custom Icon" style="width: 25px; height: 25px;">
            </a>
            <a href="#" class="dropdown-item" title="Save as PDF" id="pdf-download-button">
                <img src="saveicons/pdf.png" alt="Custom Icon" style="width: 25px; height: 25px;">
            </a>
            <a href="#" class="dropdown-item" title="Save as TXT" id="txt-btn">
                <img src="saveicons/coding.png" alt="Custom Icon" style="width: 25px; height: 25px;">
            </a>
        </div>
    </div>

    <div id="VSSKit" style="margin-bottom: 20px;"></div>

    <script src="saveAs.js"></script>
    <script src="editor.js"></script>
    <script src="jspdf.umd.min.js"></script>
    <script src="froala_editor.pkgd.min.js"></script>
    <script src="html2pdf.bundle.min.js"></script>
    <script src="jquery-3.5.1.min.js"></script>
    <script src="bootstrap.bundle.min.js"></script>

    <script>
        // Initialize Froala Editor
        var editor = new FroalaEditor('#VSSKit');

        // Function to close the application and spin the close icon simultaneously
        function closeAndSpinIcon() {
            var closeIcon = document.getElementById('exit-button');
            closeIcon.classList.add('spin'); // Add the spin animation class to the close icon

            // Close the application after a brief delay (adjust as needed)
            setTimeout(function() {
                closeApp(); // Call the closeApp function after the spinning animation starts
            }, 250); // Adjust the timeout as needed to synchronize with the spinning animation duration
        }

        // Function to close the application
        function closeApp() {
            if (confirm("Are you sure you want to close the application?")) {
                window.close(); // Close the window
                // Navigate to the launcher page
                window.location.href = '../index.html';
            }
        }

        // Function to handle dropdown menu interactions
        document.addEventListener('DOMContentLoaded', function() {
            var dropdownBtn = document.querySelector('.dropdown .btn');
            var dropdownMenu = document.querySelector('.dropdown-menu');

            dropdownBtn.addEventListener('click', function() {
                dropdownMenu.classList.toggle('show');
            });

            document.addEventListener('click', function(event) {
                if (!dropdownBtn.contains(event.target) && !dropdownMenu.contains(event.target)) {
                    dropdownMenu.classList.remove('show');
                }
            });
        });

        // Save as DOCX functionality
        function saveAsDOCX() {
            var editorContent = document.querySelector('.fr-view').innerHTML;
            Export2Doc(editorContent, 'document');
        }

        // Export to DOC function
        function Export2Doc(content, filename = '') {
            var preHtml = "<html xmlns:o='urn:schemas-microsoft-com:office:office' xmlns:w='urn:schemas-microsoft-com:office:word' xmlns='http://www.w3.org/TR/REC-html40'><head><meta charset='utf-8'><title>Export HTML To Doc</title></head><body>";
            var postHtml = "</body></html>";
            var html = preHtml + content + postHtml;
            var blob = new Blob(['\ufeff', html], { type: 'application/msword' });

            filename = filename ? filename + '.doc' : 'document.doc';

            var downloadLink = document.createElement("a");
            document.body.appendChild(downloadLink);

            if (navigator.msSaveOrOpenBlob) {
                navigator.msSaveOrOpenBlob(blob, filename);
            } else {
                var url = URL.createObjectURL(blob);
                downloadLink.href = url;
                downloadLink.download = filename;
                downloadLink.click();
                URL.revokeObjectURL(url);
            }

            document.body.removeChild(downloadLink);
        }

        // Generate and download PDF
        function generatePDF() {
            const element = document.querySelector('.fr-view');
            html2pdf().from(element).save();
        }

        document.getElementById('pdf-download-button').addEventListener('click', generatePDF);

        // Save as TXT functionality
        const txtBtn = document.getElementById('txt-btn');
        txtBtn.addEventListener("click", () => {
            const editorContent = editor.html.get();
            const textOnly = stripHtmlTags(editorContent);
            const filename = 'document';
            const blob = new Blob([textOnly], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const downloadLink = document.createElement("a");
            downloadLink.href = url;
            downloadLink.download = filename + ".txt";
            downloadLink.click();
            URL.revokeObjectURL(url);
        });

        // Helper function to remove HTML tags from given text/html string
        function stripHtmlTags(str) {
            var tempDiv = document.createElement("div");
            tempDiv.innerHTML = str;
            return tempDiv.textContent || tempDiv.innerText;
        }

        document.addEventListener("DOMContentLoaded", function() {
            const newBtn = document.getElementById("new-btn");
            const editor = new FroalaEditor('#VSSKit');

            newBtn.addEventListener("click", function(event) {
                event.preventDefault();
                editor.html.set('');
            });
        });
    </script>
</body>
</html>
