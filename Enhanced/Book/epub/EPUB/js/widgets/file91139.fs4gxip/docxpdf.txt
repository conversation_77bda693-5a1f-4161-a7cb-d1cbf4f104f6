 document.addEventListener("DOMContentLoaded", function() {
        // Get the button element
        const newBtn = document.getElementById("new-btn");

        // Initialize the Froala Editor instance
        const editor = new FroalaEditor('#VSSKit');

        // Add click event listener to the button
        newBtn.addEventListener("click", function(event) {
            event.preventDefault(); // Prevent the default link behavior

            // Prompt the user to enter a name for the new document
            const docName = prompt("Enter a name for the new document:");
            if (docName !== null) { // Check if the user entered a name
                // Clear the content of the editor
                editor.html.set('');
                
                // Save the document as PDF with the given name
                saveAsPDF(docName);
                
                // Save the document as DOCX with the given name
                saveAsDOCX(docName);
            }
        });
    });

    // Function to save as PDF
    function saveAsPDF(filename) {
        var content = document.getElementById('VSSKit').innerHTML;
        if (filename !== null) {
            html2pdf().from(content).save(filename + '.pdf');
        }
    }

    // Function to save as DOCX
    function saveAsDOCX(filename) {
        var editorContent = document.querySelector('.fr-view').innerHTML;
        Export2Doc(editorContent, filename);
    }

    // Export to DOC function
    function Export2Doc(content, filename = '') {
        var preHtml = "<html xmlns:o='urn:schemas-microsoft-com:office:office' xmlns:w='urn:schemas-microsoft-com:office:word' xmlns='http://www.w3.org/TR/REC-html40'><head><meta charset='utf-8'><title>Export HTML To Doc</title></head><body>";
        var postHtml = "</body></html>";
        var html = preHtml + content + postHtml;
        var absoluteContent = content.replace(/<img\s+src=["']([^"']+)["']/gi, function(match, url) {
            var a = document.createElement('a');
            a.href = url;
            return '<img src="' + a.href + '"';
        });

        var blob = new Blob(['\ufeff', html], {
            type: 'application/msword'
        });

        var url = 'data:application/vnd.ms-word;charset=utf-8,' + encodeURIComponent(html);

        filename = filename ? filename + '.doc' : 'document.doc';

        var downloadLink = document.createElement("a");

        document.body.appendChild(downloadLink);

        if (navigator.msSaveOrOpenBlob) {
            navigator.msSaveOrOpenBlob(blob, filename);
        } else {
            downloadLink.href = url;
            downloadLink.download = filename;
            downloadLink.click();
        }

        document.body.removeChild(downloadLink);
    }
</script>