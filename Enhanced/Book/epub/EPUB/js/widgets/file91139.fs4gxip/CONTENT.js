 document.addEventListener("DOMContentLoaded", function() {
            // Get the button element
            const newBtn = document.getElementById("new-btn");

            // Get the content editor element
            const editor = document.getElementById("VSSKit");

            // Add click event listener to the button
            newBtn.addEventListener("click", function(event) {
                event.preventDefault(); // Prevent the default button behavior

                // Clear the content of the document within the editor
                editor.textContent = "";
            });
        });

        <div class="dropdown">
        <div id="VSSKit" class="fr-box fr-basic">
        <div class="fr-wrapper">
            <div id="document-content"></div>
        </div>
    </div>

    .fr-box.fr-basic .fr-wrapper {
            background: #ffffff;
            border: 1px solid #CCCCCC;
            border-bottom-color: #efefef;
            top: 0;
            left: 0;
        }
        
        /* Adjust the styles for the document content element */
        #document-content {
            width: 100%; /* Adjust the width as needed */
            height: 300px; /* Adjust the height as needed */
            font-size: 16px; /* Adjust the font size as needed */
            font-family: Arial, sans-serif; /* Adjust the font family as needed */
            color: #333333; /* Adjust the text color as needed */
            background-color: #f9f9f9; /* Adjust the background color as needed */
            border: 1px solid #cccccc; /* Adjust the border as needed */
            padding: 10px; /* Adjust the padding as needed */
            
            margin-bottom: 20px; /* Adjust the margin as needed */
        }
