
<!doctype html>
    <html class="h5p-iframe">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1"> 
        <script>H5PIntegration = {"ajax":{"setFinished":"","contentUserData":""},"ajaxPath":"/h5p/ajax?action=","contents":{"cid-*********":{"displayOptions":{"copy":false,"copyright":false,"embed":false,"export":false,"frame":false,"icon":false},"fullScreen":"1","jsonContent":"{\"iconType\":\"image\",\"icon\":\"plus\",\"color\":\"#981d99\",\"hotspots\":[{\"position\":{\"x\":6.521739130434782,\"y\":29.21591482646792},\"alwaysFullscreen\":false,\"content\":[{\"params\":{\"text\":\"<h3>The sun heats the ground as shortwave radiation (SWR)</h3>\\n\"},\"library\":\"H5P.Text 1.1\",\"metadata\":{\"contentType\":\"Text\",\"license\":\"U\",\"title\":\"Untitled Text\",\"authors\":[],\"changes\":[]},\"subContentId\":\"03a43393-96aa-4257-920d-3cf0fec86189\"}],\"header\":\"Sun\"},{\"position\":{\"x\":6.750572082379863,\"y\":66.47534659037069},\"alwaysFullscreen\":false,\"content\":[{\"params\":{\"text\":\"<h3>The ground absorbs the heat and reflects it back as long wave radiation (LWR)</h3>\\n\"},\"library\":\"H5P.Text 1.1\",\"metadata\":{\"contentType\":\"Text\",\"license\":\"U\",\"title\":\"Untitled Text\",\"authors\":[],\"changes\":[]},\"subContentId\":\"276d34ea-afd3-4512-b33a-85fcbc3902e4\"}],\"header\":\"Evaporation\"},{\"position\":{\"x\":21.28146453089245,\"y\":41.07874258618589},\"alwaysFullscreen\":false,\"content\":[{\"params\":{\"text\":\"<h3>The air above the ground is heated from below and powerful convection currents are set, the air is forced rise and it cools</h3>\\n\"},\"library\":\"H5P.Text 1.1\",\"metadata\":{\"contentType\":\"Text\",\"license\":\"U\",\"title\":\"Untitled Text\",\"authors\":[],\"changes\":[]},\"subContentId\":\"d24adf6d-3182-42dd-96af-d602ec087620\"}],\"header\":\"Rapid Cooling\"},{\"position\":{\"x\":59.49656750572082,\"y\":19.356412284450666},\"alwaysFullscreen\":false,\"content\":[{\"params\":{\"text\":\"<h3>Saturated clouds are formed, leading to the precipitaion (converctional rainfall) </h3>\\n\"},\"library\":\"H5P.Text 1.1\",\"metadata\":{\"contentType\":\"Text\",\"license\":\"U\",\"title\":\"Untitled Text\",\"authors\":[],\"changes\":[]},\"subContentId\":\"8cde7f01-e0f2-4f61-8a1a-011596e646d5\"}],\"header\":\"Saturated Clouds\"}],\"hotspotNumberLabel\":\"Hotspot #num\",\"closeButtonLabel\":\"Close\",\"disableScaling\":false,\"image\":{\"path\":\"index/images/image-dhNtP7Y8.png\",\"mime\":\"image/png\",\"copyright\":{\"license\":\"U\"},\"width\":4096,\"height\":2400},\"iconImage\":{\"path\":\"index/images/image-thGIcixm.png\",\"mime\":\"image/png\",\"copyright\":{\"license\":\"U\"},\"width\":512,\"height\":512}}","library":"H5P.ImageHotspots 1.10","metadata":{"license":"U","title":"Convectional Rainfall","defaultLanguage":"en"},"scripts":["/h5p/libraries/H5P.Text-1.1/scripts/text.js?version=1.1.17","/h5p/libraries/H5P.Transition-1.0/transition.js?version=1.0.4","/h5p/libraries/H5P.ImageHotspots-1.10/scripts/image-hotspots.js?version=1.10.7","/h5p/libraries/H5P.ImageHotspots-1.10/scripts/hotspot.js?version=1.10.7","/h5p/libraries/H5P.ImageHotspots-1.10/scripts/popup.js?version=1.10.7"],"styles":["/h5p/libraries/H5P.Text-1.1/styles/text.css?version=1.1.17","/h5p/libraries/FontAwesome-4.5/h5p-font-awesome.min.css?version=4.5.4","/h5p/libraries/H5P.ImageHotspots-1.10/styles/image-hotspots.css?version=1.10.7"],"url":"*********","exportUrl":"/h5p/download/*********"}},"core":{"scripts":["/h5p/core/js/jquery.js?version=1.24-master","/h5p/core/js/h5p.js?version=1.24-master","/h5p/core/js/h5p-event-dispatcher.js?version=1.24-master","/h5p/core/js/h5p-x-api-event.js?version=1.24-master","/h5p/core/js/h5p-x-api.js?version=1.24-master","/h5p/core/js/h5p-content-type.js?version=1.24-master","/h5p/core/js/h5p-confirmation-dialog.js?version=1.24-master","/h5p/core/js/h5p-action-bar.js?version=1.24-master","/h5p/core/js/request-queue.js?version=1.24-master"],"styles":["/h5p/core/styles/h5p.css?version=1.24-master","/h5p/core/styles/h5p-confirmation-dialog.css?version=1.24-master","/h5p/core/styles/h5p-core-button.css?version=1.24-master"]},"l10n":{"H5P":{"fullscreen":"Fullscreen","disableFullscreen":"Disable fullscreen","download":"Download","copyrights":"Rights of use","embed":"Embed","size":"Size","showAdvanced":"Show advanced","hideAdvanced":"Hide advanced","advancedHelp":"Include this script on your website if you want dynamic sizing of the embedded content:","copyrightInformation":"Rights of use","close":"Close","title":"Title","author":"Author","year":"Year","source":"Source","license":"License","thumbnail":"Thumbnail","noCopyrights":"No copyright information available for this content.","reuse":"Reuse","reuseContent":"Reuse Content","reuseDescription":"Reuse this content.","downloadDescription":"Download this content as a H5P file.","copyrightsDescription":"View copyright information for this content.","embedDescription":"View the embed code for this content.","h5pDescription":"Visit H5P.org to check out more cool content.","contentChanged":"This content has changed since you last used it.","startingOver":"You'll be starting over.","by":"by","showMore":"Show more","showLess":"Show less","subLevel":"Sublevel","confirmDialogHeader":"Confirm action","confirmDialogBody":"Please confirm that you wish to proceed. This action is not reversible.","cancelLabel":"Cancel","confirmLabel":"Confirm","licenseU":"Undisclosed","licenseCCBY":"Attribution","licenseCCBYSA":"Attribution-ShareAlike","licenseCCBYND":"Attribution-NoDerivs","licenseCCBYNC":"Attribution-NonCommercial","licenseCCBYNCSA":"Attribution-NonCommercial-ShareAlike","licenseCCBYNCND":"Attribution-NonCommercial-NoDerivs","licenseCC40":"4.0 International","licenseCC30":"3.0 Unported","licenseCC25":"2.5 Generic","licenseCC20":"2.0 Generic","licenseCC10":"1.0 Generic","licenseGPL":"General Public License","licenseV3":"Version 3","licenseV2":"Version 2","licenseV1":"Version 1","licensePD":"Public Domain","licenseCC010":"CC0 1.0 Universal (CC0 1.0) Public Domain Dedication","licensePDM":"Public Domain Mark","licenseC":"Copyright","contentType":"Content Type","licenseExtras":"License Extras","changes":"Changelog","contentCopied":"Content is copied to the clipboard","connectionLost":"Connection lost. Results will be stored and sent when you regain connection.","connectionReestablished":"Connection reestablished.","resubmitScores":"Attempting to submit stored results.","offlineDialogHeader":"Your connection to the server was lost","offlineDialogBody":"We were unable to send information about your completion of this task. Please check your internet connection.","offlineDialogRetryMessage":"Retrying in :num....","offlineDialogRetryButtonLabel":"Retry now","offlineSuccessfulSubmit":"Successfully submitted results.","mainTitle":"Sharing <strong>:title</strong>","editInfoTitle":"Edit info for <strong>:title</strong>","cancel":"Cancel","back":"Back","next":"Next","reviewInfo":"Review info","share":"Share","saveChanges":"Save changes","registerOnHub":"Register on the H5P Hub","updateRegistrationOnHub":"Save account settings","requiredInfo":"Required Info","optionalInfo":"Optional Info","reviewAndShare":"Review & Share","reviewAndSave":"Review & Save","shared":"Shared","currentStep":"Step :step of :total","sharingNote":"All content details can be edited after sharing","licenseDescription":"Select a license for your content","licenseVersion":"License Version","licenseVersionDescription":"Select a license version","disciplineLabel":"Disciplines","disciplineDescription":"You can select multiple disciplines","disciplineLimitReachedMessage":"You can select up to :numDisciplines disciplines","discipline":{"searchPlaceholder":"Type to search for disciplines","in":"in","dropdownButton":"Dropdown button"},"removeChip":"Remove :chip from the list","keywordsPlaceholder":"Add keywords","keywords":"Keywords","keywordsDescription":"You can add multiple keywords separated by commas. Press \"Enter\" or \"Add\" to confirm keywords","altText":"Alt text","reviewMessage":"Please review the info below before you share","subContentWarning":"Sub-content (images, questions etc.) will be shared under :license unless otherwise specified in the authoring tool","disciplines":"Disciplines","shortDescription":"Short description","longDescription":"Long description","icon":"Icon","screenshots":"Screenshots","helpChoosingLicense":"Help me choose a license","shareFailed":"Share failed.","editingFailed":"Editing failed.","shareTryAgain":"Something went wrong, please try to share again.","pleaseWait":"Please wait...","language":"Language","level":"Level","shortDescriptionPlaceholder":"Short description of your content","longDescriptionPlaceholder":"Long description of your content","description":"Description","iconDescription":"640x480px. If not selected content will use category icon","screenshotsDescription":"Add up to five screenshots of your content","submitted":"Submitted!","isNowSubmitted":"Is now submitted to H5P Hub","changeHasBeenSubmitted":"A change has been submited for","contentAvailable":"Your content will normally be available in the Hub within one business day.","contentUpdateSoon":"Your content will update soon","contentLicenseTitle":"Content License Info","licenseDialogDescription":"Click on a specific license to get info about proper usage","publisherFieldTitle":"Publisher","publisherFieldDescription":"This will display as the \"Publisher name\" on shared content","emailAddress":"Email Address","publisherDescription":"Publisher description","publisherDescriptionText":"This will be displayed under \"Publisher info\" on shared content","contactPerson":"Contact Person","phone":"Phone","address":"Address","city":"City","zip":"Zip","country":"Country","logoUploadText":"Organization logo or avatar","acceptTerms":"I accept the <a href=':url' target='_blank'>terms of use</a>","successfullyRegistred":"You have successfully registered an account on the H5P Hub","successfullyRegistredDescription":"You account details can be changed","successfullyUpdated":"Your H5P Hub account settings have successfully been changed","accountDetailsLinkText":"here","registrationTitle":"H5P Hub Registration","registrationFailed":"An error occurred","registrationFailedDescription":"We were not able to create an account at this point. Something went wrong. Try again later.","maxLength":":length is the maximum number of characters","keywordExists":"Keyword already exists!","licenseDetails":"License details","remove":"Remove","removeImage":"Remove image","cancelPublishConfirmationDialogTitle":"Cancel sharing","cancelPublishConfirmationDialogDescription":"Are you sure you want to cancel the sharing process?","cancelPublishConfirmationDialogCancelButtonText":"No","cancelPublishConfirmationDialogConfirmButtonText":"Yes","add":"Add","age":"Typical age","ageDescription":"The target audience of this content. Possible input formats separated by commas: \"1,34-45,-50,59-\".","invalidAge":"Invalid input format for Typical age. Possible input formats separated by commas: \"1, 34-45, -50, -59-\".","contactPersonDescription":"H5P will reach out to the contact person in case there are any issues with the content shared by the publisher. The contact person\"s name or other information will not be published or shared with third parties","emailAddressDescription":"The email address will be used by H5P to reach out to the publisher in case of any issues with the content or in case the publisher needs to recover their account. It will not be published or shared with any third parties","copyrightWarning":"Copyrighted material cannot be shared in the H5P Content Hub. If the content is licensed with a OER friendly license like Creative Commons, please choose the appropriate license. If not this content cannot be shared.","keywordsExits":"Keywords already exists!","someKeywordsExits":"Some of these keywords already exist"}},"postUserStatistics":false,"saveFreq":false,"url":".","hubIsEnabled":true,"fullscreenDisabled":0,"user":{"name":"Firstname Surname","id":"1"},"baseUrl":".","libraryUrl":""};

        if (new URLSearchParams(window.location.search).get('embed') == 'true') {
            H5PIntegration.contents['cid-' + '*********'].displayOptions.embed = false;
        } else {
            H5PIntegration.contents['cid-' + '*********'].embedCode = '<iframe src="' + window.location.protocol + "//" + window.location.host + window.location.pathname + '?embed=true' + '" width=":w" height=":h" frameborder="0" allowfullscreen="allowfullscreen"></iframe>';
            H5PIntegration.contents['cid-' + '*********'].resizeCode = '';
        }
            
        /*!@license js/jquery.js by Joubel and other contributors, licensed under GNU GENERAL PUBLIC LICENSE Version 3*/
!function(e,t){"use strict";"object"==typeof module&&"object"==typeof module.exports?module.exports=e.document?t(e,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return t(e)}:t(e)}("undefined"!=typeof window?window:this,function(H,e){"use strict";function g(e){return"function"==typeof e&&"number"!=typeof e.nodeType}function m(e){return null!=e&&e===e.window}var t=[],n=Object.getPrototypeOf,s=t.slice,v=t.flat?function(e){return t.flat.call(e)}:function(e){return t.concat.apply([],e)},c=t.push,o=t.indexOf,i={},r=i.toString,y=i.hasOwnProperty,a=y.toString,u=a.call(Object),b={},w=H.document,l={type:!0,src:!0,nonce:!0,noModule:!0};function P(e,t,n){var i,o,r=(n=n||w).createElement("script");if(r.text=e,t)for(i in l)(o=t[i]||t.getAttribute&&t.getAttribute(i))&&r.setAttribute(i,o);n.head.appendChild(r).parentNode.removeChild(r)}function h(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?i[r.call(e)]||"object":typeof e}var d="3.5.1",C=function(e,t){return new C.fn.init(e,t)};function p(e){var t=!!e&&"length"in e&&e.length,n=h(e);return!g(e)&&!m(e)&&("array"===n||0===t||"number"==typeof t&&0<t&&t-1 in e)}C.fn=C.prototype={jquery:d,constructor:C,length:0,toArray:function(){return s.call(this)},get:function(e){return null==e?s.call(this):e<0?this[e+this.length]:this[e]},pushStack:function(e){e=C.merge(this.constructor(),e);return e.prevObject=this,e},each:function(e){return C.each(this,e)},map:function(n){return this.pushStack(C.map(this,function(e,t){return n.call(e,t,e)}))},slice:function(){return this.pushStack(s.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(C.grep(this,function(e,t){return(t+1)%2}))},odd:function(){return this.pushStack(C.grep(this,function(e,t){return t%2}))},eq:function(e){var t=this.length,e=+e+(e<0?t:0);return this.pushStack(0<=e&&e<t?[this[e]]:[])},end:function(){return this.prevObject||this.constructor()},push:c,sort:t.sort,splice:t.splice},C.extend=C.fn.extend=function(){var e,t,n,i,o,r=arguments[0]||{},a=1,s=arguments.length,c=!1;for("boolean"==typeof r&&(c=r,r=arguments[a]||{},a++),"object"==typeof r||g(r)||(r={}),a===s&&(r=this,a--);a<s;a++)if(null!=(e=arguments[a]))for(t in e)n=e[t],"__proto__"!==t&&r!==n&&(c&&n&&(C.isPlainObject(n)||(i=Array.isArray(n)))?(o=r[t],o=i&&!Array.isArray(o)?[]:i||C.isPlainObject(o)?o:{},i=!1,r[t]=C.extend(c,o,n)):void 0!==n&&(r[t]=n));return r},C.extend({expando:"jQuery"+(d+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isPlainObject:function(e){return!(!e||"[object Object]"!==r.call(e)||(e=n(e))&&("function"!=typeof(e=y.call(e,"constructor")&&e.constructor)||a.call(e)!==u))},isEmptyObject:function(e){for(var t in e)return!1;return!0},globalEval:function(e,t,n){P(e,{nonce:t&&t.nonce},n)},each:function(e,t){var n,i=0;if(p(e))for(n=e.length;i<n&&!1!==t.call(e[i],i,e[i]);i++);else for(i in e)if(!1===t.call(e[i],i,e[i]))break;return e},makeArray:function(e,t){t=t||[];return null!=e&&(p(Object(e))?C.merge(t,"string"==typeof e?[e]:e):c.call(t,e)),t},inArray:function(e,t,n){return null==t?-1:o.call(t,e,n)},merge:function(e,t){for(var n=+t.length,i=0,o=e.length;i<n;i++)e[o++]=t[i];return e.length=o,e},grep:function(e,t,n){for(var i=[],o=0,r=e.length,a=!n;o<r;o++)!t(e[o],o)!=a&&i.push(e[o]);return i},map:function(e,t,n){var i,o,r=0,a=[];if(p(e))for(i=e.length;r<i;r++)null!=(o=t(e[r],r,n))&&a.push(o);else for(r in e)null!=(o=t(e[r],r,n))&&a.push(o);return v(a)},guid:1,support:b}),"function"==typeof Symbol&&(C.fn[Symbol.iterator]=t[Symbol.iterator]),C.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){i["[object "+t+"]"]=t.toLowerCase()});var f=function(n){function d(e,t){return e="0x"+e.slice(1)-65536,t||(e<0?String.fromCharCode(65536+e):String.fromCharCode(e>>10|55296,1023&e|56320))}function p(e,t){return t?"\0"===e?"�":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e}function i(){H()}var e,f,P,r,o,h,g,m,x,c,u,H,w,a,C,v,s,l,y,T="sizzle"+ +new Date,b=n.document,E=0,S=0,I=ce(),k=ce(),D=ce(),A=ce(),j=function(e,t){return e===t&&(u=!0),0},N={}.hasOwnProperty,t=[],L=t.pop,q=t.push,O=t.push,F=t.slice,$=function(e,t){for(var n=0,i=e.length;n<i;n++)if(e[n]===t)return n;return-1},M="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",B="[\\x20\\t\\r\\n\\f]",R="(?:\\\\[\\da-fA-F]{1,6}"+B+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",z="\\["+B+"*("+R+")(?:"+B+"*([*^$|!~]?=)"+B+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+R+"))|)"+B+"*\\]",Q=":("+R+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+z+")*)|.*)\\)|)",U=new RegExp(B+"+","g"),V=new RegExp("^"+B+"+|((?:^|[^\\\\])(?:\\\\.)*)"+B+"+$","g"),X=new RegExp("^"+B+"*,"+B+"*"),W=new RegExp("^"+B+"*([>+~]|"+B+")"+B+"*"),_=new RegExp(B+"|>"),Y=new RegExp(Q),G=new RegExp("^"+R+"$"),J={ID:new RegExp("^#("+R+")"),CLASS:new RegExp("^\\.("+R+")"),TAG:new RegExp("^("+R+"|[*])"),ATTR:new RegExp("^"+z),PSEUDO:new RegExp("^"+Q),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+B+"*(even|odd|(([+-]|)(\\d*)n|)"+B+"*(?:([+-]|)"+B+"*(\\d+)|))"+B+"*\\)|)","i"),bool:new RegExp("^(?:"+M+")$","i"),needsContext:new RegExp("^"+B+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+B+"*((?:-\\d)?\\d*)"+B+"*\\)|)(?=[^-]|$)","i")},K=/HTML$/i,Z=/^(?:input|select|textarea|button)$/i,ee=/^h\d$/i,te=/^[^{]+\{\s*\[native \w/,ne=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,ie=/[+~]/,oe=new RegExp("\\\\[\\da-fA-F]{1,6}"+B+"?|\\\\([^\\r\\n\\f])","g"),re=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\0-\x1f\x7f-\uFFFF\w-]/g,ae=ye(function(e){return!0===e.disabled&&"fieldset"===e.nodeName.toLowerCase()},{dir:"parentNode",next:"legend"});try{O.apply(t=F.call(b.childNodes),b.childNodes),t[b.childNodes.length].nodeType}catch(e){O={apply:t.length?function(e,t){q.apply(e,F.call(t))}:function(e,t){for(var n=e.length,i=0;e[n++]=t[i++];);e.length=n-1}}}function se(e,t,n,i){var o,r,a,s,c,u,l=t&&t.ownerDocument,d=t?t.nodeType:9;if(n=n||[],"string"!=typeof e||!e||1!==d&&9!==d&&11!==d)return n;if(!i&&(H(t),t=t||w,C)){if(11!==d&&(s=ne.exec(e)))if(u=s[1]){if(9===d){if(!(r=t.getElementById(u)))return n;if(r.id===u)return n.push(r),n}else if(l&&(r=l.getElementById(u))&&y(t,r)&&r.id===u)return n.push(r),n}else{if(s[2])return O.apply(n,t.getElementsByTagName(e)),n;if((u=s[3])&&f.getElementsByClassName&&t.getElementsByClassName)return O.apply(n,t.getElementsByClassName(u)),n}if(f.qsa&&!A[e+" "]&&(!v||!v.test(e))&&(1!==d||"object"!==t.nodeName.toLowerCase())){if(u=e,l=t,1===d&&(_.test(e)||W.test(e))){for((l=ie.test(e)&&ge(t.parentNode)||t)===t&&f.scope||((a=t.getAttribute("id"))?a=a.replace(re,p):t.setAttribute("id",a=T)),o=(c=h(e)).length;o--;)c[o]=(a?"#"+a:":scope")+" "+ve(c[o]);u=c.join(",")}try{return O.apply(n,l.querySelectorAll(u)),n}catch(t){A(e,!0)}finally{a===T&&t.removeAttribute("id")}}}return m(e.replace(V,"$1"),t,n,i)}function ce(){var i=[];return function e(t,n){return i.push(t+" ")>P.cacheLength&&delete e[i.shift()],e[t+" "]=n}}function ue(e){return e[T]=!0,e}function le(e){var t=w.createElement("fieldset");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t)}}function de(e,t){for(var n=e.split("|"),i=n.length;i--;)P.attrHandle[n[i]]=t}function pe(e,t){var n=t&&e,i=n&&1===e.nodeType&&1===t.nodeType&&e.sourceIndex-t.sourceIndex;if(i)return i;if(n)for(;n=n.nextSibling;)if(n===t)return-1;return e?1:-1}function fe(t){return function(e){return"form"in e?e.parentNode&&!1===e.disabled?"label"in e?"label"in e.parentNode?e.parentNode.disabled===t:e.disabled===t:e.isDisabled===t||e.isDisabled!==!t&&ae(e)===t:e.disabled===t:"label"in e&&e.disabled===t}}function he(a){return ue(function(r){return r=+r,ue(function(e,t){for(var n,i=a([],e.length,r),o=i.length;o--;)e[n=i[o]]&&(e[n]=!(t[n]=e[n]))})})}function ge(e){return e&&void 0!==e.getElementsByTagName&&e}for(e in f=se.support={},o=se.isXML=function(e){var t=e.namespaceURI,e=(e.ownerDocument||e).documentElement;return!K.test(t||e&&e.nodeName||"HTML")},H=se.setDocument=function(e){var t,e=e?e.ownerDocument||e:b;return e!=w&&9===e.nodeType&&e.documentElement&&(a=(w=e).documentElement,C=!o(w),b!=w&&(t=w.defaultView)&&t.top!==t&&(t.addEventListener?t.addEventListener("unload",i,!1):t.attachEvent&&t.attachEvent("onunload",i)),f.scope=le(function(e){return a.appendChild(e).appendChild(w.createElement("div")),void 0!==e.querySelectorAll&&!e.querySelectorAll(":scope fieldset div").length}),f.attributes=le(function(e){return e.className="i",!e.getAttribute("className")}),f.getElementsByTagName=le(function(e){return e.appendChild(w.createComment("")),!e.getElementsByTagName("*").length}),f.getElementsByClassName=te.test(w.getElementsByClassName),f.getById=le(function(e){return a.appendChild(e).id=T,!w.getElementsByName||!w.getElementsByName(T).length}),f.getById?(P.filter.ID=function(e){var t=e.replace(oe,d);return function(e){return e.getAttribute("id")===t}},P.find.ID=function(e,t){if(void 0!==t.getElementById&&C){e=t.getElementById(e);return e?[e]:[]}}):(P.filter.ID=function(e){var t=e.replace(oe,d);return function(e){e=void 0!==e.getAttributeNode&&e.getAttributeNode("id");return e&&e.value===t}},P.find.ID=function(e,t){if(void 0!==t.getElementById&&C){var n,i,o,r=t.getElementById(e);if(r){if((n=r.getAttributeNode("id"))&&n.value===e)return[r];for(o=t.getElementsByName(e),i=0;r=o[i++];)if((n=r.getAttributeNode("id"))&&n.value===e)return[r]}return[]}}),P.find.TAG=f.getElementsByTagName?function(e,t){return void 0!==t.getElementsByTagName?t.getElementsByTagName(e):f.qsa?t.querySelectorAll(e):void 0}:function(e,t){var n,i=[],o=0,r=t.getElementsByTagName(e);if("*"!==e)return r;for(;n=r[o++];)1===n.nodeType&&i.push(n);return i},P.find.CLASS=f.getElementsByClassName&&function(e,t){if(void 0!==t.getElementsByClassName&&C)return t.getElementsByClassName(e)},s=[],v=[],(f.qsa=te.test(w.querySelectorAll))&&(le(function(e){var t;a.appendChild(e).innerHTML="<a id='"+T+"'></a><select id='"+T+"-\r\\' msallowcapture=''><option selected=''></option></select>",e.querySelectorAll("[msallowcapture^='']").length&&v.push("[*^$]="+B+"*(?:''|\"\")"),e.querySelectorAll("[selected]").length||v.push("\\["+B+"*(?:value|"+M+")"),e.querySelectorAll("[id~="+T+"-]").length||v.push("~="),(t=w.createElement("input")).setAttribute("name",""),e.appendChild(t),e.querySelectorAll("[name='']").length||v.push("\\["+B+"*name"+B+"*="+B+"*(?:''|\"\")"),e.querySelectorAll(":checked").length||v.push(":checked"),e.querySelectorAll("a#"+T+"+*").length||v.push(".#.+[+~]"),e.querySelectorAll("\\\f"),v.push("[\\r\\n\\f]")}),le(function(e){e.innerHTML="<a href='' disabled='disabled'></a><select disabled='disabled'><option/></select>";var t=w.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),e.querySelectorAll("[name=d]").length&&v.push("name"+B+"*[*^$|!~]?="),2!==e.querySelectorAll(":enabled").length&&v.push(":enabled",":disabled"),a.appendChild(e).disabled=!0,2!==e.querySelectorAll(":disabled").length&&v.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),v.push(",.*:")})),(f.matchesSelector=te.test(l=a.matches||a.webkitMatchesSelector||a.mozMatchesSelector||a.oMatchesSelector||a.msMatchesSelector))&&le(function(e){f.disconnectedMatch=l.call(e,"*"),l.call(e,"[s!='']:x"),s.push("!=",Q)}),v=v.length&&new RegExp(v.join("|")),s=s.length&&new RegExp(s.join("|")),t=te.test(a.compareDocumentPosition),y=t||te.test(a.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,t=t&&t.parentNode;return e===t||!(!t||1!==t.nodeType||!(n.contains?n.contains(t):e.compareDocumentPosition&&16&e.compareDocumentPosition(t)))}:function(e,t){if(t)for(;t=t.parentNode;)if(t===e)return!0;return!1},j=t?function(e,t){return e===t?(u=!0,0):(n=!e.compareDocumentPosition-!t.compareDocumentPosition)||(1&(n=(e.ownerDocument||e)==(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!f.sortDetached&&t.compareDocumentPosition(e)===n?e==w||e.ownerDocument==b&&y(b,e)?-1:t==w||t.ownerDocument==b&&y(b,t)?1:c?$(c,e)-$(c,t):0:4&n?-1:1);var n}:function(e,t){if(e===t)return u=!0,0;var n,i=0,o=e.parentNode,r=t.parentNode,a=[e],s=[t];if(!o||!r)return e==w?-1:t==w?1:o?-1:r?1:c?$(c,e)-$(c,t):0;if(o===r)return pe(e,t);for(n=e;n=n.parentNode;)a.unshift(n);for(n=t;n=n.parentNode;)s.unshift(n);for(;a[i]===s[i];)i++;return i?pe(a[i],s[i]):a[i]==b?-1:s[i]==b?1:0}),w},se.matches=function(e,t){return se(e,null,null,t)},se.matchesSelector=function(e,t){if(H(e),f.matchesSelector&&C&&!A[t+" "]&&(!s||!s.test(t))&&(!v||!v.test(t)))try{var n=l.call(e,t);if(n||f.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch(e){A(t,!0)}return 0<se(t,w,null,[e]).length},se.contains=function(e,t){return(e.ownerDocument||e)!=w&&H(e),y(e,t)},se.attr=function(e,t){(e.ownerDocument||e)!=w&&H(e);var n=P.attrHandle[t.toLowerCase()],n=n&&N.call(P.attrHandle,t.toLowerCase())?n(e,t,!C):void 0;return void 0!==n?n:f.attributes||!C?e.getAttribute(t):(n=e.getAttributeNode(t))&&n.specified?n.value:null},se.escape=function(e){return(e+"").replace(re,p)},se.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},se.uniqueSort=function(e){var t,n=[],i=0,o=0;if(u=!f.detectDuplicates,c=!f.sortStable&&e.slice(0),e.sort(j),u){for(;t=e[o++];)t===e[o]&&(i=n.push(o));for(;i--;)e.splice(n[i],1)}return c=null,e},r=se.getText=function(e){var t,n="",i=0,o=e.nodeType;if(o){if(1===o||9===o||11===o){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=r(e)}else if(3===o||4===o)return e.nodeValue}else for(;t=e[i++];)n+=r(t);return n},(P=se.selectors={cacheLength:50,createPseudo:ue,match:J,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(oe,d),e[3]=(e[3]||e[4]||e[5]||"").replace(oe,d),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||se.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&se.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return J.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&Y.test(n)&&(t=h(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(oe,d).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=I[e+" "];return t||(t=new RegExp("(^|"+B+")"+e+"("+B+"|$)"))&&I(e,function(e){return t.test("string"==typeof e.className&&e.className||void 0!==e.getAttribute&&e.getAttribute("class")||"")})},ATTR:function(t,n,i){return function(e){e=se.attr(e,t);return null==e?"!="===n:!n||(e+="","="===n?e===i:"!="===n?e!==i:"^="===n?i&&0===e.indexOf(i):"*="===n?i&&-1<e.indexOf(i):"$="===n?i&&e.slice(-i.length)===i:"~="===n?-1<(" "+e.replace(U," ")+" ").indexOf(i):"|="===n&&(e===i||e.slice(0,i.length+1)===i+"-"))}},CHILD:function(h,e,t,g,m){var v="nth"!==h.slice(0,3),y="last"!==h.slice(-4),b="of-type"===e;return 1===g&&0===m?function(e){return!!e.parentNode}:function(e,t,n){var i,o,r,a,s,c,u=v!=y?"nextSibling":"previousSibling",l=e.parentNode,d=b&&e.nodeName.toLowerCase(),p=!n&&!b,f=!1;if(l){if(v){for(;u;){for(a=e;a=a[u];)if(b?a.nodeName.toLowerCase()===d:1===a.nodeType)return!1;c=u="only"===h&&!c&&"nextSibling"}return!0}if(c=[y?l.firstChild:l.lastChild],y&&p){for(f=(s=(i=(o=(r=(a=l)[T]||(a[T]={}))[a.uniqueID]||(r[a.uniqueID]={}))[h]||[])[0]===E&&i[1])&&i[2],a=s&&l.childNodes[s];a=++s&&a&&a[u]||(f=s=0)||c.pop();)if(1===a.nodeType&&++f&&a===e){o[h]=[E,s,f];break}}else if(!1===(f=p?s=(i=(o=(r=(a=e)[T]||(a[T]={}))[a.uniqueID]||(r[a.uniqueID]={}))[h]||[])[0]===E&&i[1]:f))for(;(a=++s&&a&&a[u]||(f=s=0)||c.pop())&&((b?a.nodeName.toLowerCase()!==d:1!==a.nodeType)||!++f||(p&&((o=(r=a[T]||(a[T]={}))[a.uniqueID]||(r[a.uniqueID]={}))[h]=[E,f]),a!==e)););return(f-=m)===g||f%g==0&&0<=f/g}}},PSEUDO:function(e,r){var t,a=P.pseudos[e]||P.setFilters[e.toLowerCase()]||se.error("unsupported pseudo: "+e);return a[T]?a(r):1<a.length?(t=[e,e,"",r],P.setFilters.hasOwnProperty(e.toLowerCase())?ue(function(e,t){for(var n,i=a(e,r),o=i.length;o--;)e[n=$(e,i[o])]=!(t[n]=i[o])}):function(e){return a(e,0,t)}):a}},pseudos:{not:ue(function(e){var i=[],o=[],s=g(e.replace(V,"$1"));return s[T]?ue(function(e,t,n,i){for(var o,r=s(e,null,i,[]),a=e.length;a--;)(o=r[a])&&(e[a]=!(t[a]=o))}):function(e,t,n){return i[0]=e,s(i,null,n,o),i[0]=null,!o.pop()}}),has:ue(function(t){return function(e){return 0<se(t,e).length}}),contains:ue(function(t){return t=t.replace(oe,d),function(e){return-1<(e.textContent||r(e)).indexOf(t)}}),lang:ue(function(n){return G.test(n||"")||se.error("unsupported lang: "+n),n=n.replace(oe,d).toLowerCase(),function(e){var t;do{if(t=C?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(t=t.toLowerCase())===n||0===t.indexOf(n+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}}),target:function(e){var t=n.location&&n.location.hash;return t&&t.slice(1)===e.id},root:function(e){return e===a},focus:function(e){return e===w.activeElement&&(!w.hasFocus||w.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:fe(!1),disabled:fe(!0),checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!P.pseudos.empty(e)},header:function(e){return ee.test(e.nodeName)},input:function(e){return Z.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(e=e.getAttribute("type"))||"text"===e.toLowerCase())},first:he(function(){return[0]}),last:he(function(e,t){return[t-1]}),eq:he(function(e,t,n){return[n<0?n+t:n]}),even:he(function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e}),odd:he(function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e}),lt:he(function(e,t,n){for(var i=n<0?n+t:t<n?t:n;0<=--i;)e.push(i);return e}),gt:he(function(e,t,n){for(var i=n<0?n+t:n;++i<t;)e.push(i);return e})}}).pseudos.nth=P.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})P.pseudos[e]=function(t){return function(e){return"input"===e.nodeName.toLowerCase()&&e.type===t}}(e);for(e in{submit:!0,reset:!0})P.pseudos[e]=function(n){return function(e){var t=e.nodeName.toLowerCase();return("input"===t||"button"===t)&&e.type===n}}(e);function me(){}function ve(e){for(var t=0,n=e.length,i="";t<n;t++)i+=e[t].value;return i}function ye(a,e,t){var s=e.dir,c=e.next,u=c||s,l=t&&"parentNode"===u,d=S++;return e.first?function(e,t,n){for(;e=e[s];)if(1===e.nodeType||l)return a(e,t,n);return!1}:function(e,t,n){var i,o,r=[E,d];if(n){for(;e=e[s];)if((1===e.nodeType||l)&&a(e,t,n))return!0}else for(;e=e[s];)if(1===e.nodeType||l)if(i=(o=e[T]||(e[T]={}))[e.uniqueID]||(o[e.uniqueID]={}),c&&c===e.nodeName.toLowerCase())e=e[s]||e;else{if((o=i[u])&&o[0]===E&&o[1]===d)return r[2]=o[2];if((i[u]=r)[2]=a(e,t,n))return!0}return!1}}function be(o){return 1<o.length?function(e,t,n){for(var i=o.length;i--;)if(!o[i](e,t,n))return!1;return!0}:o[0]}function Pe(e,t,n,i,o){for(var r,a=[],s=0,c=e.length,u=null!=t;s<c;s++)(r=e[s])&&(n&&!n(r,i,o)||(a.push(r),u&&t.push(s)));return a}function xe(e){for(var i,t,n,o=e.length,r=P.relative[e[0].type],a=r||P.relative[" "],s=r?1:0,c=ye(function(e){return e===i},a,!0),u=ye(function(e){return-1<$(i,e)},a,!0),l=[function(e,t,n){n=!r&&(n||t!==x)||((i=t).nodeType?c:u)(e,t,n);return i=null,n}];s<o;s++)if(t=P.relative[e[s].type])l=[ye(be(l),t)];else{if((t=P.filter[e[s].type].apply(null,e[s].matches))[T]){for(n=++s;n<o&&!P.relative[e[n].type];n++);return function e(f,h,g,m,v,t){return m&&!m[T]&&(m=e(m)),v&&!v[T]&&(v=e(v,t)),ue(function(e,t,n,i){var o,r,a,s=[],c=[],u=t.length,l=e||function(e,t,n){for(var i=0,o=t.length;i<o;i++)se(e,t[i],n);return n}(h||"*",n.nodeType?[n]:n,[]),d=!f||!e&&h?l:Pe(l,s,f,n,i),p=g?v||(e?f:u||m)?[]:t:d;if(g&&g(d,p,n,i),m)for(o=Pe(p,c),m(o,[],n,i),r=o.length;r--;)(a=o[r])&&(p[c[r]]=!(d[c[r]]=a));if(e){if(v||f){if(v){for(o=[],r=p.length;r--;)(a=p[r])&&o.push(d[r]=a);v(null,p=[],o,i)}for(r=p.length;r--;)(a=p[r])&&-1<(o=v?$(e,a):s[r])&&(e[o]=!(t[o]=a))}}else p=Pe(p===t?p.splice(u,p.length):p),v?v(null,t,p,i):O.apply(t,p)})}(1<s&&be(l),1<s&&ve(e.slice(0,s-1).concat({value:" "===e[s-2].type?"*":""})).replace(V,"$1"),t,s<n&&xe(e.slice(s,n)),n<o&&xe(e=e.slice(n)),n<o&&ve(e))}l.push(t)}return be(l)}return me.prototype=P.filters=P.pseudos,P.setFilters=new me,h=se.tokenize=function(e,t){var n,i,o,r,a,s,c,u=k[e+" "];if(u)return t?0:u.slice(0);for(a=e,s=[],c=P.preFilter;a;){for(r in n&&!(i=X.exec(a))||(i&&(a=a.slice(i[0].length)||a),s.push(o=[])),n=!1,(i=W.exec(a))&&(n=i.shift(),o.push({value:n,type:i[0].replace(V," ")}),a=a.slice(n.length)),P.filter)!(i=J[r].exec(a))||c[r]&&!(i=c[r](i))||(n=i.shift(),o.push({value:n,type:r,matches:i}),a=a.slice(n.length));if(!n)break}return t?a.length:a?se.error(e):k(e,s).slice(0)},g=se.compile=function(e,t){var n,m,v,y,b,i,o=[],r=[],a=D[e+" "];if(!a){for(n=(t=t||h(e)).length;n--;)((a=xe(t[n]))[T]?o:r).push(a);(a=D(e,(y=0<(v=o).length,b=0<(m=r).length,i=function(e,t,n,i,o){var r,a,s,c=0,u="0",l=e&&[],d=[],p=x,f=e||b&&P.find.TAG("*",o),h=E+=null==p?1:Math.random()||.1,g=f.length;for(o&&(x=t==w||t||o);u!==g&&null!=(r=f[u]);u++){if(b&&r){for(a=0,t||r.ownerDocument==w||(H(r),n=!C);s=m[a++];)if(s(r,t||w,n)){i.push(r);break}o&&(E=h)}y&&((r=!s&&r)&&c--,e&&l.push(r))}if(c+=u,y&&u!==c){for(a=0;s=v[a++];)s(l,d,t,n);if(e){if(0<c)for(;u--;)l[u]||d[u]||(d[u]=L.call(i));d=Pe(d)}O.apply(i,d),o&&!e&&0<d.length&&1<c+v.length&&se.uniqueSort(i)}return o&&(E=h,x=p),l},y?ue(i):i))).selector=e}return a},m=se.select=function(e,t,n,i){var o,r,a,s,c,u="function"==typeof e&&e,l=!i&&h(e=u.selector||e);if(n=n||[],1===l.length){if(2<(r=l[0]=l[0].slice(0)).length&&"ID"===(a=r[0]).type&&9===t.nodeType&&C&&P.relative[r[1].type]){if(!(t=(P.find.ID(a.matches[0].replace(oe,d),t)||[])[0]))return n;u&&(t=t.parentNode),e=e.slice(r.shift().value.length)}for(o=J.needsContext.test(e)?0:r.length;o--&&(a=r[o],!P.relative[s=a.type]);)if((c=P.find[s])&&(i=c(a.matches[0].replace(oe,d),ie.test(r[0].type)&&ge(t.parentNode)||t))){if(r.splice(o,1),!(e=i.length&&ve(r)))return O.apply(n,i),n;break}}return(u||g(e,l))(i,t,!C,n,!t||ie.test(e)&&ge(t.parentNode)||t),n},f.sortStable=T.split("").sort(j).join("")===T,f.detectDuplicates=!!u,H(),f.sortDetached=le(function(e){return 1&e.compareDocumentPosition(w.createElement("fieldset"))}),le(function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")})||de("type|href|height|width",function(e,t,n){if(!n)return e.getAttribute(t,"type"===t.toLowerCase()?1:2)}),f.attributes&&le(function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")})||de("value",function(e,t,n){if(!n&&"input"===e.nodeName.toLowerCase())return e.defaultValue}),le(function(e){return null==e.getAttribute("disabled")})||de(M,function(e,t,n){if(!n)return!0===e[t]?t.toLowerCase():(t=e.getAttributeNode(t))&&t.specified?t.value:null}),se}(H);C.find=f,C.expr=f.selectors,C.expr[":"]=C.expr.pseudos,C.uniqueSort=C.unique=f.uniqueSort,C.text=f.getText,C.isXMLDoc=f.isXML,C.contains=f.contains,C.escapeSelector=f.escape;function x(e,t,n){for(var i=[],o=void 0!==n;(e=e[t])&&9!==e.nodeType;)if(1===e.nodeType){if(o&&C(e).is(n))break;i.push(e)}return i}function T(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n}var E=C.expr.match.needsContext;function S(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}var I=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function k(e,n,i){return g(n)?C.grep(e,function(e,t){return!!n.call(e,t,e)!==i}):n.nodeType?C.grep(e,function(e){return e===n!==i}):"string"!=typeof n?C.grep(e,function(e){return-1<o.call(n,e)!==i}):C.filter(n,e,i)}C.filter=function(e,t,n){var i=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===i.nodeType?C.find.matchesSelector(i,e)?[i]:[]:C.find.matches(e,C.grep(t,function(e){return 1===e.nodeType}))},C.fn.extend({find:function(e){var t,n,i=this.length,o=this;if("string"!=typeof e)return this.pushStack(C(e).filter(function(){for(t=0;t<i;t++)if(C.contains(o[t],this))return!0}));for(n=this.pushStack([]),t=0;t<i;t++)C.find(e,o[t],n);return 1<i?C.uniqueSort(n):n},filter:function(e){return this.pushStack(k(this,e||[],!1))},not:function(e){return this.pushStack(k(this,e||[],!0))},is:function(e){return!!k(this,"string"==typeof e&&E.test(e)?C(e):e||[],!1).length}});var D=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(C.fn.init=function(e,t,n){if(!e)return this;if(n=n||A,"string"!=typeof e)return e.nodeType?(this[0]=e,this.length=1,this):g(e)?void 0!==n.ready?n.ready(e):e(C):C.makeArray(e,this);if(!(i="<"===e[0]&&">"===e[e.length-1]&&3<=e.length?[null,e,null]:D.exec(e))||!i[1]&&t)return(!t||t.jquery?t||n:this.constructor(t)).find(e);if(i[1]){if(t=t instanceof C?t[0]:t,C.merge(this,C.parseHTML(i[1],t&&t.nodeType?t.ownerDocument||t:w,!0)),I.test(i[1])&&C.isPlainObject(t))for(var i in t)g(this[i])?this[i](t[i]):this.attr(i,t[i]);return this}return(e=w.getElementById(i[2]))&&(this[0]=e,this.length=1),this}).prototype=C.fn;var A=C(w),j=/^(?:parents|prev(?:Until|All))/,N={children:!0,contents:!0,next:!0,prev:!0};function L(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e}C.fn.extend({has:function(e){var t=C(e,this),n=t.length;return this.filter(function(){for(var e=0;e<n;e++)if(C.contains(this,t[e]))return!0})},closest:function(e,t){var n,i=0,o=this.length,r=[],a="string"!=typeof e&&C(e);if(!E.test(e))for(;i<o;i++)for(n=this[i];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(a?-1<a.index(n):1===n.nodeType&&C.find.matchesSelector(n,e))){r.push(n);break}return this.pushStack(1<r.length?C.uniqueSort(r):r)},index:function(e){return e?"string"==typeof e?o.call(C(e),this[0]):o.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(C.uniqueSort(C.merge(this.get(),C(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),C.each({parent:function(e){e=e.parentNode;return e&&11!==e.nodeType?e:null},parents:function(e){return x(e,"parentNode")},parentsUntil:function(e,t,n){return x(e,"parentNode",n)},next:function(e){return L(e,"nextSibling")},prev:function(e){return L(e,"previousSibling")},nextAll:function(e){return x(e,"nextSibling")},prevAll:function(e){return x(e,"previousSibling")},nextUntil:function(e,t,n){return x(e,"nextSibling",n)},prevUntil:function(e,t,n){return x(e,"previousSibling",n)},siblings:function(e){return T((e.parentNode||{}).firstChild,e)},children:function(e){return T(e.firstChild)},contents:function(e){return null!=e.contentDocument&&n(e.contentDocument)?e.contentDocument:(S(e,"template")&&(e=e.content||e),C.merge([],e.childNodes))}},function(i,o){C.fn[i]=function(e,t){var n=C.map(this,o,e);return(t="Until"!==i.slice(-5)?e:t)&&"string"==typeof t&&(n=C.filter(t,n)),1<this.length&&(N[i]||C.uniqueSort(n),j.test(i)&&n.reverse()),this.pushStack(n)}});var q=/[^\x20\t\r\n\f]+/g;function O(e){return e}function F(e){throw e}function $(e,t,n,i){var o;try{e&&g(o=e.promise)?o.call(e).done(t).fail(n):e&&g(o=e.then)?o.call(e,t,n):t.apply(void 0,[e].slice(i))}catch(e){n.apply(void 0,[e])}}C.Callbacks=function(i){var e,n;i="string"==typeof i?(e=i,n={},C.each(e.match(q)||[],function(e,t){n[t]=!0}),n):C.extend({},i);function o(){for(s=s||i.once,a=r=!0;u.length;l=-1)for(t=u.shift();++l<c.length;)!1===c[l].apply(t[0],t[1])&&i.stopOnFalse&&(l=c.length,t=!1);i.memory||(t=!1),r=!1,s&&(c=t?[]:"")}var r,t,a,s,c=[],u=[],l=-1,d={add:function(){return c&&(t&&!r&&(l=c.length-1,u.push(t)),function n(e){C.each(e,function(e,t){g(t)?i.unique&&d.has(t)||c.push(t):t&&t.length&&"string"!==h(t)&&n(t)})}(arguments),t&&!r&&o()),this},remove:function(){return C.each(arguments,function(e,t){for(var n;-1<(n=C.inArray(t,c,n));)c.splice(n,1),n<=l&&l--}),this},has:function(e){return e?-1<C.inArray(e,c):0<c.length},empty:function(){return c=c&&[],this},disable:function(){return s=u=[],c=t="",this},disabled:function(){return!c},lock:function(){return s=u=[],t||r||(c=t=""),this},locked:function(){return!!s},fireWith:function(e,t){return s||(t=[e,(t=t||[]).slice?t.slice():t],u.push(t),r||o()),this},fire:function(){return d.fireWith(this,arguments),this},fired:function(){return!!a}};return d},C.extend({Deferred:function(e){var r=[["notify","progress",C.Callbacks("memory"),C.Callbacks("memory"),2],["resolve","done",C.Callbacks("once memory"),C.Callbacks("once memory"),0,"resolved"],["reject","fail",C.Callbacks("once memory"),C.Callbacks("once memory"),1,"rejected"]],o="pending",a={state:function(){return o},always:function(){return s.done(arguments).fail(arguments),this},catch:function(e){return a.then(null,e)},pipe:function(){var o=arguments;return C.Deferred(function(i){C.each(r,function(e,t){var n=g(o[t[4]])&&o[t[4]];s[t[1]](function(){var e=n&&n.apply(this,arguments);e&&g(e.promise)?e.promise().progress(i.notify).done(i.resolve).fail(i.reject):i[t[0]+"With"](this,n?[e]:arguments)})}),o=null}).promise()},then:function(t,n,i){var c=0;function u(o,r,a,s){return function(){function e(){var e,t;if(!(o<c)){if((e=a.apply(n,i))===r.promise())throw new TypeError("Thenable self-resolution");t=e&&("object"==typeof e||"function"==typeof e)&&e.then,g(t)?s?t.call(e,u(c,r,O,s),u(c,r,F,s)):(c++,t.call(e,u(c,r,O,s),u(c,r,F,s),u(c,r,O,r.notifyWith))):(a!==O&&(n=void 0,i=[e]),(s||r.resolveWith)(n,i))}}var n=this,i=arguments,t=s?e:function(){try{e()}catch(e){C.Deferred.exceptionHook&&C.Deferred.exceptionHook(e,t.stackTrace),c<=o+1&&(a!==F&&(n=void 0,i=[e]),r.rejectWith(n,i))}};o?t():(C.Deferred.getStackHook&&(t.stackTrace=C.Deferred.getStackHook()),H.setTimeout(t))}}return C.Deferred(function(e){r[0][3].add(u(0,e,g(i)?i:O,e.notifyWith)),r[1][3].add(u(0,e,g(t)?t:O)),r[2][3].add(u(0,e,g(n)?n:F))}).promise()},promise:function(e){return null!=e?C.extend(e,a):a}},s={};return C.each(r,function(e,t){var n=t[2],i=t[5];a[t[1]]=n.add,i&&n.add(function(){o=i},r[3-e][2].disable,r[3-e][3].disable,r[0][2].lock,r[0][3].lock),n.add(t[3].fire),s[t[0]]=function(){return s[t[0]+"With"](this===s?void 0:this,arguments),this},s[t[0]+"With"]=n.fireWith}),a.promise(s),e&&e.call(s,s),s},when:function(e){function t(t){return function(e){o[t]=this,r[t]=1<arguments.length?s.call(arguments):e,--n||a.resolveWith(o,r)}}var n=arguments.length,i=n,o=Array(i),r=s.call(arguments),a=C.Deferred();if(n<=1&&($(e,a.done(t(i)).resolve,a.reject,!n),"pending"===a.state()||g(r[i]&&r[i].then)))return a.then();for(;i--;)$(r[i],t(i),a.reject);return a.promise()}});var M=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;C.Deferred.exceptionHook=function(e,t){H.console&&H.console.warn&&e&&M.test(e.name)&&H.console.warn("jQuery.Deferred exception: "+e.message,e.stack,t)},C.readyException=function(e){H.setTimeout(function(){throw e})};var B=C.Deferred();function R(){w.removeEventListener("DOMContentLoaded",R),H.removeEventListener("load",R),C.ready()}C.fn.ready=function(e){return B.then(e).catch(function(e){C.readyException(e)}),this},C.extend({isReady:!1,readyWait:1,ready:function(e){(!0===e?--C.readyWait:C.isReady)||(C.isReady=!0)!==e&&0<--C.readyWait||B.resolveWith(w,[C])}}),C.ready.then=B.then,"complete"===w.readyState||"loading"!==w.readyState&&!w.documentElement.doScroll?H.setTimeout(C.ready):(w.addEventListener("DOMContentLoaded",R),H.addEventListener("load",R));function z(e,t,n,i,o,r,a){var s=0,c=e.length,u=null==n;if("object"===h(n))for(s in o=!0,n)z(e,t,s,n[s],!0,r,a);else if(void 0!==i&&(o=!0,g(i)||(a=!0),t=u?a?(t.call(e,i),null):(u=t,function(e,t,n){return u.call(C(e),n)}):t))for(;s<c;s++)t(e[s],n,a?i:i.call(e[s],s,t(e[s],n)));return o?e:u?t.call(e):c?t(e[0],n):r}var Q=/^-ms-/,U=/-([a-z])/g;function V(e,t){return t.toUpperCase()}function X(e){return e.replace(Q,"ms-").replace(U,V)}function W(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType}function _(){this.expando=C.expando+_.uid++}_.uid=1,_.prototype={cache:function(e){var t=e[this.expando];return t||(t={},W(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,n){var i,o=this.cache(e);if("string"==typeof t)o[X(t)]=n;else for(i in t)o[X(i)]=t[i];return o},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][X(t)]},access:function(e,t,n){return void 0===t||t&&"string"==typeof t&&void 0===n?this.get(e,t):(this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,i=e[this.expando];if(void 0!==i){if(void 0!==t){n=(t=Array.isArray(t)?t.map(X):(t=X(t))in i?[t]:t.match(q)||[]).length;for(;n--;)delete i[t[n]]}void 0!==t&&!C.isEmptyObject(i)||(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){e=e[this.expando];return void 0!==e&&!C.isEmptyObject(e)}};var Y=new _,G=new _,J=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,K=/[A-Z]/g;function Z(e,t,n){var i,o;if(void 0===n&&1===e.nodeType)if(i="data-"+t.replace(K,"-$&").toLowerCase(),"string"==typeof(n=e.getAttribute(i))){try{n="true"===(o=n)||"false"!==o&&("null"===o?null:o===+o+""?+o:J.test(o)?JSON.parse(o):o)}catch(e){}G.set(e,t,n)}else n=void 0;return n}C.extend({hasData:function(e){return G.hasData(e)||Y.hasData(e)},data:function(e,t,n){return G.access(e,t,n)},removeData:function(e,t){G.remove(e,t)},_data:function(e,t,n){return Y.access(e,t,n)},_removeData:function(e,t){Y.remove(e,t)}}),C.fn.extend({data:function(n,e){var t,i,o,r=this[0],a=r&&r.attributes;if(void 0!==n)return"object"==typeof n?this.each(function(){G.set(this,n)}):z(this,function(e){var t;return r&&void 0===e?void 0!==(t=G.get(r,n))||void 0!==(t=Z(r,n))?t:void 0:void this.each(function(){G.set(this,n,e)})},null,e,1<arguments.length,null,!0);if(this.length&&(o=G.get(r),1===r.nodeType&&!Y.get(r,"hasDataAttrs"))){for(t=a.length;t--;)a[t]&&0===(i=a[t].name).indexOf("data-")&&(i=X(i.slice(5)),Z(r,i,o[i]));Y.set(r,"hasDataAttrs",!0)}return o},removeData:function(e){return this.each(function(){G.remove(this,e)})}}),C.extend({queue:function(e,t,n){var i;if(e)return i=Y.get(e,t=(t||"fx")+"queue"),n&&(!i||Array.isArray(n)?i=Y.access(e,t,C.makeArray(n)):i.push(n)),i||[]},dequeue:function(e,t){t=t||"fx";var n=C.queue(e,t),i=n.length,o=n.shift(),r=C._queueHooks(e,t);"inprogress"===o&&(o=n.shift(),i--),o&&("fx"===t&&n.unshift("inprogress"),delete r.stop,o.call(e,function(){C.dequeue(e,t)},r)),!i&&r&&r.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return Y.get(e,n)||Y.access(e,n,{empty:C.Callbacks("once memory").add(function(){Y.remove(e,[t+"queue",n])})})}}),C.fn.extend({queue:function(t,n){var e=2;return"string"!=typeof t&&(n=t,t="fx",e--),arguments.length<e?C.queue(this[0],t):void 0===n?this:this.each(function(){var e=C.queue(this,t,n);C._queueHooks(this,t),"fx"===t&&"inprogress"!==e[0]&&C.dequeue(this,t)})},dequeue:function(e){return this.each(function(){C.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){function n(){--o||r.resolveWith(a,[a])}var i,o=1,r=C.Deferred(),a=this,s=this.length;for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";s--;)(i=Y.get(a[s],e+"queueHooks"))&&i.empty&&(o++,i.empty.add(n));return n(),r.promise(t)}});var ee=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,te=new RegExp("^(?:([+-])=|)("+ee+")([a-z%]*)$","i"),ne=["Top","Right","Bottom","Left"],ie=w.documentElement,oe=function(e){return C.contains(e.ownerDocument,e)},re={composed:!0};ie.getRootNode&&(oe=function(e){return C.contains(e.ownerDocument,e)||e.getRootNode(re)===e.ownerDocument});function ae(e,t){return"none"===(e=t||e).style.display||""===e.style.display&&oe(e)&&"none"===C.css(e,"display")}function se(e,t,n,i){var o,r,a=20,s=i?function(){return i.cur()}:function(){return C.css(e,t,"")},c=s(),u=n&&n[3]||(C.cssNumber[t]?"":"px"),l=e.nodeType&&(C.cssNumber[t]||"px"!==u&&+c)&&te.exec(C.css(e,t));if(l&&l[3]!==u){for(u=u||l[3],l=+(c/=2)||1;a--;)C.style(e,t,l+u),(1-r)*(1-(r=s()/c||.5))<=0&&(a=0),l/=r;C.style(e,t,(l*=2)+u),n=n||[]}return n&&(l=+l||+c||0,o=n[1]?l+(n[1]+1)*n[2]:+n[2],i&&(i.unit=u,i.start=l,i.end=o)),o}var ce={};function ue(e,t){for(var n,i,o,r,a,s,c=[],u=0,l=e.length;u<l;u++)(i=e[u]).style&&(n=i.style.display,t?("none"===n&&(c[u]=Y.get(i,"display")||null,c[u]||(i.style.display="")),""===i.style.display&&ae(i)&&(c[u]=(s=r=o=void 0,r=i.ownerDocument,a=i.nodeName,(s=ce[a])||(o=r.body.appendChild(r.createElement(a)),s=C.css(o,"display"),o.parentNode.removeChild(o),ce[a]=s="none"===s?"block":s)))):"none"!==n&&(c[u]="none",Y.set(i,"display",n)));for(u=0;u<l;u++)null!=c[u]&&(e[u].style.display=c[u]);return e}C.fn.extend({show:function(){return ue(this,!0)},hide:function(){return ue(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){ae(this)?C(this).show():C(this).hide()})}});var le=/^(?:checkbox|radio)$/i,de=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,pe=/^$|^module$|\/(?:java|ecma)script/i,d=w.createDocumentFragment().appendChild(w.createElement("div"));(f=w.createElement("input")).setAttribute("type","radio"),f.setAttribute("checked","checked"),f.setAttribute("name","t"),d.appendChild(f),b.checkClone=d.cloneNode(!0).cloneNode(!0).lastChild.checked,d.innerHTML="<textarea>x</textarea>",b.noCloneChecked=!!d.cloneNode(!0).lastChild.defaultValue,d.innerHTML="<option></option>",b.option=!!d.lastChild;var fe={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function he(e,t){var n=void 0!==e.getElementsByTagName?e.getElementsByTagName(t||"*"):void 0!==e.querySelectorAll?e.querySelectorAll(t||"*"):[];return void 0===t||t&&S(e,t)?C.merge([e],n):n}function ge(e,t){for(var n=0,i=e.length;n<i;n++)Y.set(e[n],"globalEval",!t||Y.get(t[n],"globalEval"))}fe.tbody=fe.tfoot=fe.colgroup=fe.caption=fe.thead,fe.th=fe.td,b.option||(fe.optgroup=fe.option=[1,"<select multiple='multiple'>","</select>"]);var me=/<|&#?\w+;/;function ve(e,t,n,i,o){for(var r,a,s,c,u,l=t.createDocumentFragment(),d=[],p=0,f=e.length;p<f;p++)if((r=e[p])||0===r)if("object"===h(r))C.merge(d,r.nodeType?[r]:r);else if(me.test(r)){for(a=a||l.appendChild(t.createElement("div")),s=(de.exec(r)||["",""])[1].toLowerCase(),s=fe[s]||fe._default,a.innerHTML=s[1]+C.htmlPrefilter(r)+s[2],u=s[0];u--;)a=a.lastChild;C.merge(d,a.childNodes),(a=l.firstChild).textContent=""}else d.push(t.createTextNode(r));for(l.textContent="",p=0;r=d[p++];)if(i&&-1<C.inArray(r,i))o&&o.push(r);else if(c=oe(r),a=he(l.appendChild(r),"script"),c&&ge(a),n)for(u=0;r=a[u++];)pe.test(r.type||"")&&n.push(r);return l}var ye=/^key/,be=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,Pe=/^([^.]*)(?:\.(.+)|)/;function xe(){return!0}function He(){return!1}function we(e,t){return e===function(){try{return w.activeElement}catch(e){}}()==("focus"===t)}function Ce(e,t,n,i,o,r){var a,s;if("object"==typeof t){for(s in"string"!=typeof n&&(i=i||n,n=void 0),t)Ce(e,s,n,i,t[s],r);return e}if(null==i&&null==o?(o=n,i=n=void 0):null==o&&("string"==typeof n?(o=i,i=void 0):(o=i,i=n,n=void 0)),!1===o)o=He;else if(!o)return e;return 1===r&&(a=o,(o=function(e){return C().off(e),a.apply(this,arguments)}).guid=a.guid||(a.guid=C.guid++)),e.each(function(){C.event.add(this,t,o,i,n)})}function Te(e,o,r){r?(Y.set(e,o,!1),C.event.add(e,o,{namespace:!1,handler:function(e){var t,n,i=Y.get(this,o);if(1&e.isTrigger&&this[o]){if(i.length)(C.event.special[o]||{}).delegateType&&e.stopPropagation();else if(i=s.call(arguments),Y.set(this,o,i),t=r(this,o),this[o](),i!==(n=Y.get(this,o))||t?Y.set(this,o,!1):n={},i!==n)return e.stopImmediatePropagation(),e.preventDefault(),n.value}else i.length&&(Y.set(this,o,{value:C.event.trigger(C.extend(i[0],C.Event.prototype),i.slice(1),this)}),e.stopImmediatePropagation())}})):void 0===Y.get(e,o)&&C.event.add(e,o,xe)}C.event={global:{},add:function(t,e,n,i,o){var r,a,s,c,u,l,d,p,f,h=Y.get(t);if(W(t))for(n.handler&&(n=(r=n).handler,o=r.selector),o&&C.find.matchesSelector(ie,o),n.guid||(n.guid=C.guid++),(s=h.events)||(s=h.events=Object.create(null)),(a=h.handle)||(a=h.handle=function(e){return void 0!==C&&C.event.triggered!==e.type?C.event.dispatch.apply(t,arguments):void 0}),c=(e=(e||"").match(q)||[""]).length;c--;)d=f=(u=Pe.exec(e[c])||[])[1],p=(u[2]||"").split(".").sort(),d&&(l=C.event.special[d]||{},d=(o?l.delegateType:l.bindType)||d,l=C.event.special[d]||{},u=C.extend({type:d,origType:f,data:i,handler:n,guid:n.guid,selector:o,needsContext:o&&C.expr.match.needsContext.test(o),namespace:p.join(".")},r),(f=s[d])||((f=s[d]=[]).delegateCount=0,l.setup&&!1!==l.setup.call(t,i,p,a)||t.addEventListener&&t.addEventListener(d,a)),l.add&&(l.add.call(t,u),u.handler.guid||(u.handler.guid=n.guid)),o?f.splice(f.delegateCount++,0,u):f.push(u),C.event.global[d]=!0)},remove:function(e,t,n,i,o){var r,a,s,c,u,l,d,p,f,h,g,m=Y.hasData(e)&&Y.get(e);if(m&&(c=m.events)){for(u=(t=(t||"").match(q)||[""]).length;u--;)if(f=g=(s=Pe.exec(t[u])||[])[1],h=(s[2]||"").split(".").sort(),f){for(d=C.event.special[f]||{},p=c[f=(i?d.delegateType:d.bindType)||f]||[],s=s[2]&&new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"),a=r=p.length;r--;)l=p[r],!o&&g!==l.origType||n&&n.guid!==l.guid||s&&!s.test(l.namespace)||i&&i!==l.selector&&("**"!==i||!l.selector)||(p.splice(r,1),l.selector&&p.delegateCount--,d.remove&&d.remove.call(e,l));a&&!p.length&&(d.teardown&&!1!==d.teardown.call(e,h,m.handle)||C.removeEvent(e,f,m.handle),delete c[f])}else for(f in c)C.event.remove(e,f+t[u],n,i,!0);C.isEmptyObject(c)&&Y.remove(e,"handle events")}},dispatch:function(e){var t,n,i,o,r,a=new Array(arguments.length),s=C.event.fix(e),c=(Y.get(this,"events")||Object.create(null))[s.type]||[],e=C.event.special[s.type]||{};for(a[0]=s,t=1;t<arguments.length;t++)a[t]=arguments[t];if(s.delegateTarget=this,!e.preDispatch||!1!==e.preDispatch.call(this,s)){for(r=C.event.handlers.call(this,s,c),t=0;(i=r[t++])&&!s.isPropagationStopped();)for(s.currentTarget=i.elem,n=0;(o=i.handlers[n++])&&!s.isImmediatePropagationStopped();)s.rnamespace&&!1!==o.namespace&&!s.rnamespace.test(o.namespace)||(s.handleObj=o,s.data=o.data,void 0!==(o=((C.event.special[o.origType]||{}).handle||o.handler).apply(i.elem,a))&&!1===(s.result=o)&&(s.preventDefault(),s.stopPropagation()));return e.postDispatch&&e.postDispatch.call(this,s),s.result}},handlers:function(e,t){var n,i,o,r,a,s=[],c=t.delegateCount,u=e.target;if(c&&u.nodeType&&!("click"===e.type&&1<=e.button))for(;u!==this;u=u.parentNode||this)if(1===u.nodeType&&("click"!==e.type||!0!==u.disabled)){for(r=[],a={},n=0;n<c;n++)void 0===a[o=(i=t[n]).selector+" "]&&(a[o]=i.needsContext?-1<C(o,this).index(u):C.find(o,this,null,[u]).length),a[o]&&r.push(i);r.length&&s.push({elem:u,handlers:r})}return u=this,c<t.length&&s.push({elem:u,handlers:t.slice(c)}),s},addProp:function(t,e){Object.defineProperty(C.Event.prototype,t,{enumerable:!0,configurable:!0,get:g(e)?function(){if(this.originalEvent)return e(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[t]},set:function(e){Object.defineProperty(this,t,{enumerable:!0,configurable:!0,writable:!0,value:e})}})},fix:function(e){return e[C.expando]?e:new C.Event(e)},special:{load:{noBubble:!0},click:{setup:function(e){e=this||e;return le.test(e.type)&&e.click&&S(e,"input")&&Te(e,"click",xe),!1},trigger:function(e){e=this||e;return le.test(e.type)&&e.click&&S(e,"input")&&Te(e,"click"),!0},_default:function(e){e=e.target;return le.test(e.type)&&e.click&&S(e,"input")&&Y.get(e,"click")||S(e,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},C.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)},C.Event=function(e,t){if(!(this instanceof C.Event))return new C.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?xe:He,this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,t&&C.extend(this,t),this.timeStamp=e&&e.timeStamp||Date.now(),this[C.expando]=!0},C.Event.prototype={constructor:C.Event,isDefaultPrevented:He,isPropagationStopped:He,isImmediatePropagationStopped:He,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=xe,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=xe,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=xe,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},C.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:function(e){var t=e.button;return null==e.which&&ye.test(e.type)?null!=e.charCode?e.charCode:e.keyCode:!e.which&&void 0!==t&&be.test(e.type)?1&t?1:2&t?3:4&t?2:0:e.which}},C.event.addProp),C.each({focus:"focusin",blur:"focusout"},function(e,t){C.event.special[e]={setup:function(){return Te(this,e,we),!1},trigger:function(){return Te(this,e),!0},delegateType:t}}),C.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,o){C.event.special[e]={delegateType:o,bindType:o,handle:function(e){var t,n=e.relatedTarget,i=e.handleObj;return n&&(n===this||C.contains(this,n))||(e.type=i.origType,t=i.handler.apply(this,arguments),e.type=o),t}}}),C.fn.extend({on:function(e,t,n,i){return Ce(this,e,t,n,i)},one:function(e,t,n,i){return Ce(this,e,t,n,i,1)},off:function(e,t,n){var i,o;if(e&&e.preventDefault&&e.handleObj)return i=e.handleObj,C(e.delegateTarget).off(i.namespace?i.origType+"."+i.namespace:i.origType,i.selector,i.handler),this;if("object"!=typeof e)return!1!==t&&"function"!=typeof t||(n=t,t=void 0),!1===n&&(n=He),this.each(function(){C.event.remove(this,e,n,t)});for(o in e)this.off(o,t,e[o]);return this}});var Ee=/<script|<style|<link/i,Se=/checked\s*(?:[^=]|=\s*.checked.)/i,Ie=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;function ke(e,t){return S(e,"table")&&S(11!==t.nodeType?t:t.firstChild,"tr")&&C(e).children("tbody")[0]||e}function De(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function Ae(e){return"true/"===(e.type||"").slice(0,5)?e.type=e.type.slice(5):e.removeAttribute("type"),e}function je(e,t){var n,i,o,r;if(1===t.nodeType){if(Y.hasData(e)&&(r=Y.get(e).events))for(o in Y.remove(t,"handle events"),r)for(n=0,i=r[o].length;n<i;n++)C.event.add(t,o,r[o][n]);G.hasData(e)&&(e=G.access(e),e=C.extend({},e),G.set(t,e))}}function Ne(n,i,o,r){i=v(i);var e,t,a,s,c,u,l=0,d=n.length,p=d-1,f=i[0],h=g(f);if(h||1<d&&"string"==typeof f&&!b.checkClone&&Se.test(f))return n.each(function(e){var t=n.eq(e);h&&(i[0]=f.call(this,e,t.html())),Ne(t,i,o,r)});if(d&&(t=(e=ve(i,n[0].ownerDocument,!1,n,r)).firstChild,1===e.childNodes.length&&(e=t),t||r)){for(s=(a=C.map(he(e,"script"),De)).length;l<d;l++)c=e,l!==p&&(c=C.clone(c,!0,!0),s&&C.merge(a,he(c,"script"))),o.call(n[l],c,l);if(s)for(u=a[a.length-1].ownerDocument,C.map(a,Ae),l=0;l<s;l++)c=a[l],pe.test(c.type||"")&&!Y.access(c,"globalEval")&&C.contains(u,c)&&(c.src&&"module"!==(c.type||"").toLowerCase()?C._evalUrl&&!c.noModule&&C._evalUrl(c.src,{nonce:c.nonce||c.getAttribute("nonce")},u):P(c.textContent.replace(Ie,""),c,u))}return n}function Le(e,t,n){for(var i,o=t?C.filter(t,e):e,r=0;null!=(i=o[r]);r++)n||1!==i.nodeType||C.cleanData(he(i)),i.parentNode&&(n&&oe(i)&&ge(he(i,"script")),i.parentNode.removeChild(i));return e}C.extend({htmlPrefilter:function(e){return e},clone:function(e,t,n){var i,o,r,a,s,c,u,l=e.cloneNode(!0),d=oe(e);if(!(b.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||C.isXMLDoc(e)))for(a=he(l),i=0,o=(r=he(e)).length;i<o;i++)s=r[i],"input"===(u=(c=a[i]).nodeName.toLowerCase())&&le.test(s.type)?c.checked=s.checked:"input"!==u&&"textarea"!==u||(c.defaultValue=s.defaultValue);if(t)if(n)for(r=r||he(e),a=a||he(l),i=0,o=r.length;i<o;i++)je(r[i],a[i]);else je(e,l);return 0<(a=he(l,"script")).length&&ge(a,!d&&he(e,"script")),l},cleanData:function(e){for(var t,n,i,o=C.event.special,r=0;void 0!==(n=e[r]);r++)if(W(n)){if(t=n[Y.expando]){if(t.events)for(i in t.events)o[i]?C.event.remove(n,i):C.removeEvent(n,i,t.handle);n[Y.expando]=void 0}n[G.expando]&&(n[G.expando]=void 0)}}}),C.fn.extend({detach:function(e){return Le(this,e,!0)},remove:function(e){return Le(this,e)},text:function(e){return z(this,function(e){return void 0===e?C.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=e)})},null,e,arguments.length)},append:function(){return Ne(this,arguments,function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||ke(this,e).appendChild(e)})},prepend:function(){return Ne(this,arguments,function(e){var t;1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(t=ke(this,e)).insertBefore(e,t.firstChild)})},before:function(){return Ne(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return Ne(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(C.cleanData(he(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return C.clone(this,e,t)})},html:function(e){return z(this,function(e){var t=this[0]||{},n=0,i=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;if("string"==typeof e&&!Ee.test(e)&&!fe[(de.exec(e)||["",""])[1].toLowerCase()]){e=C.htmlPrefilter(e);try{for(;n<i;n++)1===(t=this[n]||{}).nodeType&&(C.cleanData(he(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var n=[];return Ne(this,arguments,function(e){var t=this.parentNode;C.inArray(this,n)<0&&(C.cleanData(he(this)),t&&t.replaceChild(e,this))},n)}}),C.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,a){C.fn[e]=function(e){for(var t,n=[],i=C(e),o=i.length-1,r=0;r<=o;r++)t=r===o?this:this.clone(!0),C(i[r])[a](t),c.apply(n,t.get());return this.pushStack(n)}});function qe(e){var t=e.ownerDocument.defaultView;return(t=!t||!t.opener?H:t).getComputedStyle(e)}function Oe(e,t,n){var i,o={};for(i in t)o[i]=e.style[i],e.style[i]=t[i];for(i in n=n.call(e),t)e.style[i]=o[i];return n}var Fe,$e,Me,Be,Re,ze,Qe,Ue,Ve=new RegExp("^("+ee+")(?!px)[a-z%]+$","i"),Xe=new RegExp(ne.join("|"),"i");function We(e,t,n){var i,o,r=e.style;return(n=n||qe(e))&&(""!==(o=n.getPropertyValue(t)||n[t])||oe(e)||(o=C.style(e,t)),!b.pixelBoxStyles()&&Ve.test(o)&&Xe.test(t)&&(i=r.width,e=r.minWidth,t=r.maxWidth,r.minWidth=r.maxWidth=r.width=o,o=n.width,r.width=i,r.minWidth=e,r.maxWidth=t)),void 0!==o?o+"":o}function _e(e,t){return{get:function(){if(!e())return(this.get=t).apply(this,arguments);delete this.get}}}function Ye(){var e;Ue&&(Qe.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",Ue.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",ie.appendChild(Qe).appendChild(Ue),e=H.getComputedStyle(Ue),Fe="1%"!==e.top,ze=12===Ge(e.marginLeft),Ue.style.right="60%",Be=36===Ge(e.right),$e=36===Ge(e.width),Ue.style.position="absolute",Me=12===Ge(Ue.offsetWidth/3),ie.removeChild(Qe),Ue=null)}function Ge(e){return Math.round(parseFloat(e))}Qe=w.createElement("div"),(Ue=w.createElement("div")).style&&(Ue.style.backgroundClip="content-box",Ue.cloneNode(!0).style.backgroundClip="",b.clearCloneStyle="content-box"===Ue.style.backgroundClip,C.extend(b,{boxSizingReliable:function(){return Ye(),$e},pixelBoxStyles:function(){return Ye(),Be},pixelPosition:function(){return Ye(),Fe},reliableMarginLeft:function(){return Ye(),ze},scrollboxSize:function(){return Ye(),Me},reliableTrDimensions:function(){var e,t,n;return null==Re&&(e=w.createElement("table"),n=w.createElement("tr"),t=w.createElement("div"),e.style.cssText="position:absolute;left:-11111px",n.style.height="1px",t.style.height="9px",ie.appendChild(e).appendChild(n).appendChild(t),n=H.getComputedStyle(n),Re=3<parseInt(n.height),ie.removeChild(e)),Re}}));var Je=["Webkit","Moz","ms"],Ke=w.createElement("div").style,Ze={};function et(e){return C.cssProps[e]||Ze[e]||(e in Ke?e:Ze[e]=function(e){for(var t=e[0].toUpperCase()+e.slice(1),n=Je.length;n--;)if((e=Je[n]+t)in Ke)return e}(e)||e)}var tt=/^(none|table(?!-c[ea]).+)/,nt=/^--/,it={position:"absolute",visibility:"hidden",display:"block"},ot={letterSpacing:"0",fontWeight:"400"};function rt(e,t,n){var i=te.exec(t);return i?Math.max(0,i[2]-(n||0))+(i[3]||"px"):t}function at(e,t,n,i,o,r){var a="width"===t?1:0,s=0,c=0;if(n===(i?"border":"content"))return 0;for(;a<4;a+=2)"margin"===n&&(c+=C.css(e,n+ne[a],!0,o)),i?("content"===n&&(c-=C.css(e,"padding"+ne[a],!0,o)),"margin"!==n&&(c-=C.css(e,"border"+ne[a]+"Width",!0,o))):(c+=C.css(e,"padding"+ne[a],!0,o),"padding"!==n?c+=C.css(e,"border"+ne[a]+"Width",!0,o):s+=C.css(e,"border"+ne[a]+"Width",!0,o));return!i&&0<=r&&(c+=Math.max(0,Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-r-c-s-.5))||0),c}function st(e,t,n){var i=qe(e),o=(!b.boxSizingReliable()||n)&&"border-box"===C.css(e,"boxSizing",!1,i),r=o,a=We(e,t,i),s="offset"+t[0].toUpperCase()+t.slice(1);if(Ve.test(a)){if(!n)return a;a="auto"}return(!b.boxSizingReliable()&&o||!b.reliableTrDimensions()&&S(e,"tr")||"auto"===a||!parseFloat(a)&&"inline"===C.css(e,"display",!1,i))&&e.getClientRects().length&&(o="border-box"===C.css(e,"boxSizing",!1,i),(r=s in e)&&(a=e[s])),(a=parseFloat(a)||0)+at(e,t,n||(o?"border":"content"),r,i,a)+"px"}function ct(e,t,n,i,o){return new ct.prototype.init(e,t,n,i,o)}C.extend({cssHooks:{opacity:{get:function(e,t){if(t){e=We(e,"opacity");return""===e?"1":e}}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{},style:function(e,t,n,i){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var o,r,a,s=X(t),c=nt.test(t),u=e.style;if(c||(t=et(s)),a=C.cssHooks[t]||C.cssHooks[s],void 0===n)return a&&"get"in a&&void 0!==(o=a.get(e,!1,i))?o:u[t];"string"==(r=typeof n)&&(o=te.exec(n))&&o[1]&&(n=se(e,t,o),r="number"),null!=n&&n==n&&("number"!==r||c||(n+=o&&o[3]||(C.cssNumber[s]?"":"px")),b.clearCloneStyle||""!==n||0!==t.indexOf("background")||(u[t]="inherit"),a&&"set"in a&&void 0===(n=a.set(e,n,i))||(c?u.setProperty(t,n):u[t]=n))}},css:function(e,t,n,i){var o,r=X(t);return nt.test(t)||(t=et(r)),"normal"===(o=void 0===(o=(r=C.cssHooks[t]||C.cssHooks[r])&&"get"in r?r.get(e,!0,n):o)?We(e,t,i):o)&&t in ot&&(o=ot[t]),""===n||n?(t=parseFloat(o),!0===n||isFinite(t)?t||0:o):o}}),C.each(["height","width"],function(e,s){C.cssHooks[s]={get:function(e,t,n){if(t)return!tt.test(C.css(e,"display"))||e.getClientRects().length&&e.getBoundingClientRect().width?st(e,s,n):Oe(e,it,function(){return st(e,s,n)})},set:function(e,t,n){var i,o=qe(e),r=!b.scrollboxSize()&&"absolute"===o.position,a=(r||n)&&"border-box"===C.css(e,"boxSizing",!1,o),n=n?at(e,s,n,a,o):0;return a&&r&&(n-=Math.ceil(e["offset"+s[0].toUpperCase()+s.slice(1)]-parseFloat(o[s])-at(e,s,"border",!1,o)-.5)),n&&(i=te.exec(t))&&"px"!==(i[3]||"px")&&(e.style[s]=t,t=C.css(e,s)),rt(0,t,n)}}}),C.cssHooks.marginLeft=_e(b.reliableMarginLeft,function(e,t){if(t)return(parseFloat(We(e,"marginLeft"))||e.getBoundingClientRect().left-Oe(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}))+"px"}),C.each({margin:"",padding:"",border:"Width"},function(o,r){C.cssHooks[o+r]={expand:function(e){for(var t=0,n={},i="string"==typeof e?e.split(" "):[e];t<4;t++)n[o+ne[t]+r]=i[t]||i[t-2]||i[0];return n}},"margin"!==o&&(C.cssHooks[o+r].set=rt)}),C.fn.extend({css:function(e,t){return z(this,function(e,t,n){var i,o,r={},a=0;if(Array.isArray(t)){for(i=qe(e),o=t.length;a<o;a++)r[t[a]]=C.css(e,t[a],!1,i);return r}return void 0!==n?C.style(e,t,n):C.css(e,t)},e,t,1<arguments.length)}}),((C.Tween=ct).prototype={constructor:ct,init:function(e,t,n,i,o,r){this.elem=e,this.prop=n,this.easing=o||C.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=i,this.unit=r||(C.cssNumber[n]?"":"px")},cur:function(){var e=ct.propHooks[this.prop];return(e&&e.get?e:ct.propHooks._default).get(this)},run:function(e){var t,n=ct.propHooks[this.prop];return this.options.duration?this.pos=t=C.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),(n&&n.set?n:ct.propHooks._default).set(this),this}}).init.prototype=ct.prototype,(ct.propHooks={_default:{get:function(e){return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(e=C.css(e.elem,e.prop,""))&&"auto"!==e?e:0},set:function(e){C.fx.step[e.prop]?C.fx.step[e.prop](e):1!==e.elem.nodeType||!C.cssHooks[e.prop]&&null==e.elem.style[et(e.prop)]?e.elem[e.prop]=e.now:C.style(e.elem,e.prop,e.now+e.unit)}}}).scrollTop=ct.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},C.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"},C.fx=ct.prototype.init,C.fx.step={};var ut,lt,dt=/^(?:toggle|show|hide)$/,pt=/queueHooks$/;function ft(){lt&&(!1===w.hidden&&H.requestAnimationFrame?H.requestAnimationFrame(ft):H.setTimeout(ft,C.fx.interval),C.fx.tick())}function ht(){return H.setTimeout(function(){ut=void 0}),ut=Date.now()}function gt(e,t){var n,i=0,o={height:e};for(t=t?1:0;i<4;i+=2-t)o["margin"+(n=ne[i])]=o["padding"+n]=e;return t&&(o.opacity=o.width=e),o}function mt(e,t,n){for(var i,o=(vt.tweeners[t]||[]).concat(vt.tweeners["*"]),r=0,a=o.length;r<a;r++)if(i=o[r].call(n,t,e))return i}function vt(o,e,t){var n,r,i=0,a=vt.prefilters.length,s=C.Deferred().always(function(){delete c.elem}),c=function(){if(r)return!1;for(var e=ut||ht(),e=Math.max(0,u.startTime+u.duration-e),t=1-(e/u.duration||0),n=0,i=u.tweens.length;n<i;n++)u.tweens[n].run(t);return s.notifyWith(o,[u,t,e]),t<1&&i?e:(i||s.notifyWith(o,[u,1,0]),s.resolveWith(o,[u]),!1)},u=s.promise({elem:o,props:C.extend({},e),opts:C.extend(!0,{specialEasing:{},easing:C.easing._default},t),originalProperties:e,originalOptions:t,startTime:ut||ht(),duration:t.duration,tweens:[],createTween:function(e,t){e=C.Tween(o,u.opts,e,t,u.opts.specialEasing[e]||u.opts.easing);return u.tweens.push(e),e},stop:function(e){var t=0,n=e?u.tweens.length:0;if(r)return this;for(r=!0;t<n;t++)u.tweens[t].run(1);return e?(s.notifyWith(o,[u,1,0]),s.resolveWith(o,[u,e])):s.rejectWith(o,[u,e]),this}}),l=u.props;for(function(e,t){var n,i,o,r,a;for(n in e)if(o=t[i=X(n)],r=e[n],Array.isArray(r)&&(o=r[1],r=e[n]=r[0]),n!==i&&(e[i]=r,delete e[n]),(a=C.cssHooks[i])&&"expand"in a)for(n in r=a.expand(r),delete e[i],r)n in e||(e[n]=r[n],t[n]=o);else t[i]=o}(l,u.opts.specialEasing);i<a;i++)if(n=vt.prefilters[i].call(u,o,l,u.opts))return g(n.stop)&&(C._queueHooks(u.elem,u.opts.queue).stop=n.stop.bind(n)),n;return C.map(l,mt,u),g(u.opts.start)&&u.opts.start.call(o,u),u.progress(u.opts.progress).done(u.opts.done,u.opts.complete).fail(u.opts.fail).always(u.opts.always),C.fx.timer(C.extend(c,{elem:o,anim:u,queue:u.opts.queue})),u}C.Animation=C.extend(vt,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return se(n.elem,e,te.exec(t),n),n}]},tweener:function(e,t){for(var n,i=0,o=(e=g(e)?(t=e,["*"]):e.match(q)).length;i<o;i++)n=e[i],vt.tweeners[n]=vt.tweeners[n]||[],vt.tweeners[n].unshift(t)},prefilters:[function(e,t,n){var i,o,r,a,s,c,u,l="width"in t||"height"in t,d=this,p={},f=e.style,h=e.nodeType&&ae(e),g=Y.get(e,"fxshow");for(i in n.queue||(null==(a=C._queueHooks(e,"fx")).unqueued&&(a.unqueued=0,s=a.empty.fire,a.empty.fire=function(){a.unqueued||s()}),a.unqueued++,d.always(function(){d.always(function(){a.unqueued--,C.queue(e,"fx").length||a.empty.fire()})})),t)if(o=t[i],dt.test(o)){if(delete t[i],r=r||"toggle"===o,o===(h?"hide":"show")){if("show"!==o||!g||void 0===g[i])continue;h=!0}p[i]=g&&g[i]||C.style(e,i)}if((c=!C.isEmptyObject(t))||!C.isEmptyObject(p))for(i in l&&1===e.nodeType&&(n.overflow=[f.overflow,f.overflowX,f.overflowY],null==(u=g&&g.display)&&(u=Y.get(e,"display")),"none"===(l=C.css(e,"display"))&&(u?l=u:(ue([e],!0),u=e.style.display||u,l=C.css(e,"display"),ue([e]))),("inline"===l||"inline-block"===l&&null!=u)&&"none"===C.css(e,"float")&&(c||(d.done(function(){f.display=u}),null==u&&(l=f.display,u="none"===l?"":l)),f.display="inline-block")),n.overflow&&(f.overflow="hidden",d.always(function(){f.overflow=n.overflow[0],f.overflowX=n.overflow[1],f.overflowY=n.overflow[2]})),c=!1,p)c||(g?"hidden"in g&&(h=g.hidden):g=Y.access(e,"fxshow",{display:u}),r&&(g.hidden=!h),h&&ue([e],!0),d.done(function(){for(i in h||ue([e]),Y.remove(e,"fxshow"),p)C.style(e,i,p[i])})),c=mt(h?g[i]:0,i,d),i in g||(g[i]=c.start,h&&(c.end=c.start,c.start=0))}],prefilter:function(e,t){t?vt.prefilters.unshift(e):vt.prefilters.push(e)}}),C.speed=function(e,t,n){var i=e&&"object"==typeof e?C.extend({},e):{complete:n||!n&&t||g(e)&&e,duration:e,easing:n&&t||t&&!g(t)&&t};return C.fx.off?i.duration=0:"number"!=typeof i.duration&&(i.duration in C.fx.speeds?i.duration=C.fx.speeds[i.duration]:i.duration=C.fx.speeds._default),null!=i.queue&&!0!==i.queue||(i.queue="fx"),i.old=i.complete,i.complete=function(){g(i.old)&&i.old.call(this),i.queue&&C.dequeue(this,i.queue)},i},C.fn.extend({fadeTo:function(e,t,n,i){return this.filter(ae).css("opacity",0).show().end().animate({opacity:t},e,n,i)},animate:function(t,e,n,i){function o(){var e=vt(this,C.extend({},t),a);(r||Y.get(this,"finish"))&&e.stop(!0)}var r=C.isEmptyObject(t),a=C.speed(e,n,i);return o.finish=o,r||!1===a.queue?this.each(o):this.queue(a.queue,o)},stop:function(o,e,r){function a(e){var t=e.stop;delete e.stop,t(r)}return"string"!=typeof o&&(r=e,e=o,o=void 0),e&&this.queue(o||"fx",[]),this.each(function(){var e=!0,t=null!=o&&o+"queueHooks",n=C.timers,i=Y.get(this);if(t)i[t]&&i[t].stop&&a(i[t]);else for(t in i)i[t]&&i[t].stop&&pt.test(t)&&a(i[t]);for(t=n.length;t--;)n[t].elem!==this||null!=o&&n[t].queue!==o||(n[t].anim.stop(r),e=!1,n.splice(t,1));!e&&r||C.dequeue(this,o)})},finish:function(a){return!1!==a&&(a=a||"fx"),this.each(function(){var e,t=Y.get(this),n=t[a+"queue"],i=t[a+"queueHooks"],o=C.timers,r=n?n.length:0;for(t.finish=!0,C.queue(this,a,[]),i&&i.stop&&i.stop.call(this,!0),e=o.length;e--;)o[e].elem===this&&o[e].queue===a&&(o[e].anim.stop(!0),o.splice(e,1));for(e=0;e<r;e++)n[e]&&n[e].finish&&n[e].finish.call(this);delete t.finish})}}),C.each(["toggle","show","hide"],function(e,i){var o=C.fn[i];C.fn[i]=function(e,t,n){return null==e||"boolean"==typeof e?o.apply(this,arguments):this.animate(gt(i,!0),e,t,n)}}),C.each({slideDown:gt("show"),slideUp:gt("hide"),slideToggle:gt("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,i){C.fn[e]=function(e,t,n){return this.animate(i,e,t,n)}}),C.timers=[],C.fx.tick=function(){var e,t=0,n=C.timers;for(ut=Date.now();t<n.length;t++)(e=n[t])()||n[t]!==e||n.splice(t--,1);n.length||C.fx.stop(),ut=void 0},C.fx.timer=function(e){C.timers.push(e),C.fx.start()},C.fx.interval=13,C.fx.start=function(){lt||(lt=!0,ft())},C.fx.stop=function(){lt=null},C.fx.speeds={slow:600,fast:200,_default:400},C.fn.delay=function(i,e){return i=C.fx&&C.fx.speeds[i]||i,this.queue(e=e||"fx",function(e,t){var n=H.setTimeout(e,i);t.stop=function(){H.clearTimeout(n)}})},d=w.createElement("input"),ee=w.createElement("select").appendChild(w.createElement("option")),d.type="checkbox",b.checkOn=""!==d.value,b.optSelected=ee.selected,(d=w.createElement("input")).value="t",d.type="radio",b.radioValue="t"===d.value;var yt,bt=C.expr.attrHandle;C.fn.extend({attr:function(e,t){return z(this,C.attr,e,t,1<arguments.length)},removeAttr:function(e){return this.each(function(){C.removeAttr(this,e)})}}),C.extend({attr:function(e,t,n){var i,o,r=e.nodeType;if(3!==r&&8!==r&&2!==r)return void 0===e.getAttribute?C.prop(e,t,n):(1===r&&C.isXMLDoc(e)||(o=C.attrHooks[t.toLowerCase()]||(C.expr.match.bool.test(t)?yt:void 0)),void 0!==n?null===n?void C.removeAttr(e,t):o&&"set"in o&&void 0!==(i=o.set(e,n,t))?i:(e.setAttribute(t,n+""),n):!(o&&"get"in o&&null!==(i=o.get(e,t)))&&null==(i=C.find.attr(e,t))?void 0:i)},attrHooks:{type:{set:function(e,t){if(!b.radioValue&&"radio"===t&&S(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}},removeAttr:function(e,t){var n,i=0,o=t&&t.match(q);if(o&&1===e.nodeType)for(;n=o[i++];)e.removeAttribute(n)}}),yt={set:function(e,t,n){return!1===t?C.removeAttr(e,n):e.setAttribute(n,n),n}},C.each(C.expr.match.bool.source.match(/\w+/g),function(e,t){var a=bt[t]||C.find.attr;bt[t]=function(e,t,n){var i,o,r=t.toLowerCase();return n||(o=bt[r],bt[r]=i,i=null!=a(e,t,n)?r:null,bt[r]=o),i}});var Pt=/^(?:input|select|textarea|button)$/i,xt=/^(?:a|area)$/i;function Ht(e){return(e.match(q)||[]).join(" ")}function wt(e){return e.getAttribute&&e.getAttribute("class")||""}function Ct(e){return Array.isArray(e)?e:"string"==typeof e&&e.match(q)||[]}C.fn.extend({prop:function(e,t){return z(this,C.prop,e,t,1<arguments.length)},removeProp:function(e){return this.each(function(){delete this[C.propFix[e]||e]})}}),C.extend({prop:function(e,t,n){var i,o,r=e.nodeType;if(3!==r&&8!==r&&2!==r)return 1===r&&C.isXMLDoc(e)||(t=C.propFix[t]||t,o=C.propHooks[t]),void 0!==n?o&&"set"in o&&void 0!==(i=o.set(e,n,t))?i:e[t]=n:o&&"get"in o&&null!==(i=o.get(e,t))?i:e[t]},propHooks:{tabIndex:{get:function(e){var t=C.find.attr(e,"tabindex");return t?parseInt(t,10):Pt.test(e.nodeName)||xt.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),b.optSelected||(C.propHooks.selected={get:function(e){e=e.parentNode;return e&&e.parentNode&&e.parentNode.selectedIndex,null},set:function(e){e=e.parentNode;e&&(e.selectedIndex,e.parentNode&&e.parentNode.selectedIndex)}}),C.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){C.propFix[this.toLowerCase()]=this}),C.fn.extend({addClass:function(t){var e,n,i,o,r,a,s=0;if(g(t))return this.each(function(e){C(this).addClass(t.call(this,e,wt(this)))});if((e=Ct(t)).length)for(;n=this[s++];)if(a=wt(n),i=1===n.nodeType&&" "+Ht(a)+" "){for(r=0;o=e[r++];)i.indexOf(" "+o+" ")<0&&(i+=o+" ");a!==(a=Ht(i))&&n.setAttribute("class",a)}return this},removeClass:function(t){var e,n,i,o,r,a,s=0;if(g(t))return this.each(function(e){C(this).removeClass(t.call(this,e,wt(this)))});if(!arguments.length)return this.attr("class","");if((e=Ct(t)).length)for(;n=this[s++];)if(a=wt(n),i=1===n.nodeType&&" "+Ht(a)+" "){for(r=0;o=e[r++];)for(;-1<i.indexOf(" "+o+" ");)i=i.replace(" "+o+" "," ");a!==(a=Ht(i))&&n.setAttribute("class",a)}return this},toggleClass:function(o,t){var r=typeof o,a="string"==r||Array.isArray(o);return"boolean"==typeof t&&a?t?this.addClass(o):this.removeClass(o):g(o)?this.each(function(e){C(this).toggleClass(o.call(this,e,wt(this),t),t)}):this.each(function(){var e,t,n,i;if(a)for(t=0,n=C(this),i=Ct(o);e=i[t++];)n.hasClass(e)?n.removeClass(e):n.addClass(e);else void 0!==o&&"boolean"!=r||((e=wt(this))&&Y.set(this,"__className__",e),this.setAttribute&&this.setAttribute("class",!e&&!1!==o&&Y.get(this,"__className__")||""))})},hasClass:function(e){for(var t,n=0,i=" "+e+" ";t=this[n++];)if(1===t.nodeType&&-1<(" "+Ht(wt(t))+" ").indexOf(i))return!0;return!1}});var Tt=/\r/g;C.fn.extend({val:function(t){var n,e,i,o=this[0];return arguments.length?(i=g(t),this.each(function(e){1===this.nodeType&&(null==(e=i?t.call(this,e,C(this).val()):t)?e="":"number"==typeof e?e+="":Array.isArray(e)&&(e=C.map(e,function(e){return null==e?"":e+""})),(n=C.valHooks[this.type]||C.valHooks[this.nodeName.toLowerCase()])&&"set"in n&&void 0!==n.set(this,e,"value")||(this.value=e))})):o?(n=C.valHooks[o.type]||C.valHooks[o.nodeName.toLowerCase()])&&"get"in n&&void 0!==(e=n.get(o,"value"))?e:"string"==typeof(e=o.value)?e.replace(Tt,""):null==e?"":e:void 0}}),C.extend({valHooks:{option:{get:function(e){var t=C.find.attr(e,"value");return null!=t?t:Ht(C.text(e))}},select:{get:function(e){for(var t,n=e.options,i=e.selectedIndex,o="select-one"===e.type,r=o?null:[],a=o?i+1:n.length,s=i<0?a:o?i:0;s<a;s++)if(((t=n[s]).selected||s===i)&&!t.disabled&&(!t.parentNode.disabled||!S(t.parentNode,"optgroup"))){if(t=C(t).val(),o)return t;r.push(t)}return r},set:function(e,t){for(var n,i,o=e.options,r=C.makeArray(t),a=o.length;a--;)((i=o[a]).selected=-1<C.inArray(C.valHooks.option.get(i),r))&&(n=!0);return n||(e.selectedIndex=-1),r}}}}),C.each(["radio","checkbox"],function(){C.valHooks[this]={set:function(e,t){if(Array.isArray(t))return e.checked=-1<C.inArray(C(e).val(),t)}},b.checkOn||(C.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})}),b.focusin="onfocusin"in H;function Et(e){e.stopPropagation()}var St=/^(?:focusinfocus|focusoutblur)$/;C.extend(C.event,{trigger:function(e,t,n,i){var o,r,a,s,c,u,l,d=[n||w],p=y.call(e,"type")?e.type:e,f=y.call(e,"namespace")?e.namespace.split("."):[],h=l=r=n=n||w;if(3!==n.nodeType&&8!==n.nodeType&&!St.test(p+C.event.triggered)&&(-1<p.indexOf(".")&&(p=(f=p.split(".")).shift(),f.sort()),s=p.indexOf(":")<0&&"on"+p,(e=e[C.expando]?e:new C.Event(p,"object"==typeof e&&e)).isTrigger=i?2:3,e.namespace=f.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+f.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=n),t=null==t?[e]:C.makeArray(t,[e]),u=C.event.special[p]||{},i||!u.trigger||!1!==u.trigger.apply(n,t))){if(!i&&!u.noBubble&&!m(n)){for(a=u.delegateType||p,St.test(a+p)||(h=h.parentNode);h;h=h.parentNode)d.push(h),r=h;r===(n.ownerDocument||w)&&d.push(r.defaultView||r.parentWindow||H)}for(o=0;(h=d[o++])&&!e.isPropagationStopped();)l=h,e.type=1<o?a:u.bindType||p,(c=(Y.get(h,"events")||Object.create(null))[e.type]&&Y.get(h,"handle"))&&c.apply(h,t),(c=s&&h[s])&&c.apply&&W(h)&&(e.result=c.apply(h,t),!1===e.result&&e.preventDefault());return e.type=p,i||e.isDefaultPrevented()||u._default&&!1!==u._default.apply(d.pop(),t)||!W(n)||s&&g(n[p])&&!m(n)&&((r=n[s])&&(n[s]=null),C.event.triggered=p,e.isPropagationStopped()&&l.addEventListener(p,Et),n[p](),e.isPropagationStopped()&&l.removeEventListener(p,Et),C.event.triggered=void 0,r&&(n[s]=r)),e.result}},simulate:function(e,t,n){e=C.extend(new C.Event,n,{type:e,isSimulated:!0});C.event.trigger(e,null,t)}}),C.fn.extend({trigger:function(e,t){return this.each(function(){C.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];if(n)return C.event.trigger(e,t,n,!0)}}),b.focusin||C.each({focus:"focusin",blur:"focusout"},function(n,i){function o(e){C.event.simulate(i,e.target,C.event.fix(e))}C.event.special[i]={setup:function(){var e=this.ownerDocument||this.document||this,t=Y.access(e,i);t||e.addEventListener(n,o,!0),Y.access(e,i,(t||0)+1)},teardown:function(){var e=this.ownerDocument||this.document||this,t=Y.access(e,i)-1;t?Y.access(e,i,t):(e.removeEventListener(n,o,!0),Y.remove(e,i))}}});var It=H.location,kt={guid:Date.now()},Dt=/\?/;C.parseXML=function(e){var t;if(!e||"string"!=typeof e)return null;try{t=(new H.DOMParser).parseFromString(e,"text/xml")}catch(e){t=void 0}return t&&!t.getElementsByTagName("parsererror").length||C.error("Invalid XML: "+e),t};var At=/\[\]$/,jt=/\r?\n/g,Nt=/^(?:submit|button|image|reset|file)$/i,Lt=/^(?:input|select|textarea|keygen)/i;C.param=function(e,t){function n(e,t){t=g(t)?t():t,o[o.length]=encodeURIComponent(e)+"="+encodeURIComponent(null==t?"":t)}var i,o=[];if(null==e)return"";if(Array.isArray(e)||e.jquery&&!C.isPlainObject(e))C.each(e,function(){n(this.name,this.value)});else for(i in e)!function n(i,e,o,r){if(Array.isArray(e))C.each(e,function(e,t){o||At.test(i)?r(i,t):n(i+"["+("object"==typeof t&&null!=t?e:"")+"]",t,o,r)});else if(o||"object"!==h(e))r(i,e);else for(var t in e)n(i+"["+t+"]",e[t],o,r)}(i,e[i],t,n);return o.join("&")},C.fn.extend({serialize:function(){return C.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=C.prop(this,"elements");return e?C.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!C(this).is(":disabled")&&Lt.test(this.nodeName)&&!Nt.test(e)&&(this.checked||!le.test(e))}).map(function(e,t){var n=C(this).val();return null==n?null:Array.isArray(n)?C.map(n,function(e){return{name:t.name,value:e.replace(jt,"\r\n")}}):{name:t.name,value:n.replace(jt,"\r\n")}}).get()}});var qt=/%20/g,Ot=/#.*$/,Ft=/([?&])_=[^&]*/,$t=/^(.*?):[ \t]*([^\r\n]*)$/gm,Mt=/^(?:GET|HEAD)$/,Bt=/^\/\//,Rt={},zt={},Qt="*/".concat("*"),Ut=w.createElement("a");function Vt(r){return function(e,t){"string"!=typeof e&&(t=e,e="*");var n,i=0,o=e.toLowerCase().match(q)||[];if(g(t))for(;n=o[i++];)"+"===n[0]?(n=n.slice(1)||"*",(r[n]=r[n]||[]).unshift(t)):(r[n]=r[n]||[]).push(t)}}function Xt(t,i,o,r){var a={},s=t===zt;function c(e){var n;return a[e]=!0,C.each(t[e]||[],function(e,t){t=t(i,o,r);return"string"!=typeof t||s||a[t]?s?!(n=t):void 0:(i.dataTypes.unshift(t),c(t),!1)}),n}return c(i.dataTypes[0])||!a["*"]&&c("*")}function Wt(e,t){var n,i,o=C.ajaxSettings.flatOptions||{};for(n in t)void 0!==t[n]&&((o[n]?e:i=i||{})[n]=t[n]);return i&&C.extend(!0,e,i),e}Ut.href=It.href,C.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:It.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(It.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Qt,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":C.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?Wt(Wt(e,C.ajaxSettings),t):Wt(C.ajaxSettings,e)},ajaxPrefilter:Vt(Rt),ajaxTransport:Vt(zt),ajax:function(e,t){"object"==typeof e&&(t=e,e=void 0);var c,u,l,n,d,p,f,i,o,h=C.ajaxSetup({},t=t||{}),g=h.context||h,m=h.context&&(g.nodeType||g.jquery)?C(g):C.event,v=C.Deferred(),y=C.Callbacks("once memory"),b=h.statusCode||{},r={},a={},s="canceled",P={readyState:0,getResponseHeader:function(e){var t;if(p){if(!n)for(n={};t=$t.exec(l);)n[t[1].toLowerCase()+" "]=(n[t[1].toLowerCase()+" "]||[]).concat(t[2]);t=n[e.toLowerCase()+" "]}return null==t?null:t.join(", ")},getAllResponseHeaders:function(){return p?l:null},setRequestHeader:function(e,t){return null==p&&(e=a[e.toLowerCase()]=a[e.toLowerCase()]||e,r[e]=t),this},overrideMimeType:function(e){return null==p&&(h.mimeType=e),this},statusCode:function(e){if(e)if(p)P.always(e[P.status]);else for(var t in e)b[t]=[b[t],e[t]];return this},abort:function(e){e=e||s;return c&&c.abort(e),x(0,e),this}};if(v.promise(P),h.url=((e||h.url||It.href)+"").replace(Bt,It.protocol+"//"),h.type=t.method||t.type||h.method||h.type,h.dataTypes=(h.dataType||"*").toLowerCase().match(q)||[""],null==h.crossDomain){o=w.createElement("a");try{o.href=h.url,o.href=o.href,h.crossDomain=Ut.protocol+"//"+Ut.host!=o.protocol+"//"+o.host}catch(e){h.crossDomain=!0}}if(h.data&&h.processData&&"string"!=typeof h.data&&(h.data=C.param(h.data,h.traditional)),Xt(Rt,h,t,P),p)return P;for(i in(f=C.event&&h.global)&&0==C.active++&&C.event.trigger("ajaxStart"),h.type=h.type.toUpperCase(),h.hasContent=!Mt.test(h.type),u=h.url.replace(Ot,""),h.hasContent?h.data&&h.processData&&0===(h.contentType||"").indexOf("application/x-www-form-urlencoded")&&(h.data=h.data.replace(qt,"+")):(o=h.url.slice(u.length),h.data&&(h.processData||"string"==typeof h.data)&&(u+=(Dt.test(u)?"&":"?")+h.data,delete h.data),!1===h.cache&&(u=u.replace(Ft,"$1"),o=(Dt.test(u)?"&":"?")+"_="+kt.guid+++o),h.url=u+o),h.ifModified&&(C.lastModified[u]&&P.setRequestHeader("If-Modified-Since",C.lastModified[u]),C.etag[u]&&P.setRequestHeader("If-None-Match",C.etag[u])),(h.data&&h.hasContent&&!1!==h.contentType||t.contentType)&&P.setRequestHeader("Content-Type",h.contentType),P.setRequestHeader("Accept",h.dataTypes[0]&&h.accepts[h.dataTypes[0]]?h.accepts[h.dataTypes[0]]+("*"!==h.dataTypes[0]?", "+Qt+"; q=0.01":""):h.accepts["*"]),h.headers)P.setRequestHeader(i,h.headers[i]);if(h.beforeSend&&(!1===h.beforeSend.call(g,P,h)||p))return P.abort();if(s="abort",y.add(h.complete),P.done(h.success),P.fail(h.error),c=Xt(zt,h,t,P)){if(P.readyState=1,f&&m.trigger("ajaxSend",[P,h]),p)return P;h.async&&0<h.timeout&&(d=H.setTimeout(function(){P.abort("timeout")},h.timeout));try{p=!1,c.send(r,x)}catch(e){if(p)throw e;x(-1,e)}}else x(-1,"No Transport");function x(e,t,n,i){var o,r,a,s=t;p||(p=!0,d&&H.clearTimeout(d),c=void 0,l=i||"",P.readyState=0<e?4:0,i=200<=e&&e<300||304===e,n&&(a=function(e,t,n){for(var i,o,r,a,s=e.contents,c=e.dataTypes;"*"===c[0];)c.shift(),void 0===i&&(i=e.mimeType||t.getResponseHeader("Content-Type"));if(i)for(o in s)if(s[o]&&s[o].test(i)){c.unshift(o);break}if(c[0]in n)r=c[0];else{for(o in n){if(!c[0]||e.converters[o+" "+c[0]]){r=o;break}a=a||o}r=r||a}if(r)return r!==c[0]&&c.unshift(r),n[r]}(h,P,n)),!i&&-1<C.inArray("script",h.dataTypes)&&(h.converters["text script"]=function(){}),a=function(e,t,n,i){var o,r,a,s,c,u={},l=e.dataTypes.slice();if(l[1])for(a in e.converters)u[a.toLowerCase()]=e.converters[a];for(r=l.shift();r;)if(e.responseFields[r]&&(n[e.responseFields[r]]=t),!c&&i&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),c=r,r=l.shift())if("*"===r)r=c;else if("*"!==c&&c!==r){if(!(a=u[c+" "+r]||u["* "+r]))for(o in u)if((s=o.split(" "))[1]===r&&(a=u[c+" "+s[0]]||u["* "+s[0]])){!0===a?a=u[o]:!0!==u[o]&&(r=s[0],l.unshift(s[1]));break}if(!0!==a)if(a&&e.throws)t=a(t);else try{t=a(t)}catch(e){return{state:"parsererror",error:a?e:"No conversion from "+c+" to "+r}}}return{state:"success",data:t}}(h,a,P,i),i?(h.ifModified&&((n=P.getResponseHeader("Last-Modified"))&&(C.lastModified[u]=n),(n=P.getResponseHeader("etag"))&&(C.etag[u]=n)),204===e||"HEAD"===h.type?s="nocontent":304===e?s="notmodified":(s=a.state,o=a.data,i=!(r=a.error))):(r=s,!e&&s||(s="error",e<0&&(e=0))),P.status=e,P.statusText=(t||s)+"",i?v.resolveWith(g,[o,s,P]):v.rejectWith(g,[P,s,r]),P.statusCode(b),b=void 0,f&&m.trigger(i?"ajaxSuccess":"ajaxError",[P,h,i?o:r]),y.fireWith(g,[P,s]),f&&(m.trigger("ajaxComplete",[P,h]),--C.active||C.event.trigger("ajaxStop")))}return P},getJSON:function(e,t,n){return C.get(e,t,n,"json")},getScript:function(e,t){return C.get(e,void 0,t,"script")}}),C.each(["get","post"],function(e,o){C[o]=function(e,t,n,i){return g(t)&&(i=i||n,n=t,t=void 0),C.ajax(C.extend({url:e,type:o,dataType:i,data:t,success:n},C.isPlainObject(e)&&e))}}),C.ajaxPrefilter(function(e){for(var t in e.headers)"content-type"===t.toLowerCase()&&(e.contentType=e.headers[t]||"")}),C._evalUrl=function(e,t,n){return C.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(e){C.globalEval(e,t,n)}})},C.fn.extend({wrapAll:function(e){return this[0]&&(g(e)&&(e=e.call(this[0])),e=C(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&e.insertBefore(this[0]),e.map(function(){for(var e=this;e.firstElementChild;)e=e.firstElementChild;return e}).append(this)),this},wrapInner:function(n){return g(n)?this.each(function(e){C(this).wrapInner(n.call(this,e))}):this.each(function(){var e=C(this),t=e.contents();t.length?t.wrapAll(n):e.append(n)})},wrap:function(t){var n=g(t);return this.each(function(e){C(this).wrapAll(n?t.call(this,e):t)})},unwrap:function(e){return this.parent(e).not("body").each(function(){C(this).replaceWith(this.childNodes)}),this}}),C.expr.pseudos.hidden=function(e){return!C.expr.pseudos.visible(e)},C.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},C.ajaxSettings.xhr=function(){try{return new H.XMLHttpRequest}catch(e){}};var _t={0:200,1223:204},Yt=C.ajaxSettings.xhr();b.cors=!!Yt&&"withCredentials"in Yt,b.ajax=Yt=!!Yt,C.ajaxTransport(function(o){var r,a;if(b.cors||Yt&&!o.crossDomain)return{send:function(e,t){var n,i=o.xhr();if(i.open(o.type,o.url,o.async,o.username,o.password),o.xhrFields)for(n in o.xhrFields)i[n]=o.xhrFields[n];for(n in o.mimeType&&i.overrideMimeType&&i.overrideMimeType(o.mimeType),o.crossDomain||e["X-Requested-With"]||(e["X-Requested-With"]="XMLHttpRequest"),e)i.setRequestHeader(n,e[n]);r=function(e){return function(){r&&(r=a=i.onload=i.onerror=i.onabort=i.ontimeout=i.onreadystatechange=null,"abort"===e?i.abort():"error"===e?"number"!=typeof i.status?t(0,"error"):t(i.status,i.statusText):t(_t[i.status]||i.status,i.statusText,"text"!==(i.responseType||"text")||"string"!=typeof i.responseText?{binary:i.response}:{text:i.responseText},i.getAllResponseHeaders()))}},i.onload=r(),a=i.onerror=i.ontimeout=r("error"),void 0!==i.onabort?i.onabort=a:i.onreadystatechange=function(){4===i.readyState&&H.setTimeout(function(){r&&a()})},r=r("abort");try{i.send(o.hasContent&&o.data||null)}catch(e){if(r)throw e}},abort:function(){r&&r()}}}),C.ajaxPrefilter(function(e){e.crossDomain&&(e.contents.script=!1)}),C.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return C.globalEval(e),e}}}),C.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET")}),C.ajaxTransport("script",function(n){var i,o;if(n.crossDomain||n.scriptAttrs)return{send:function(e,t){i=C("<script>").attr(n.scriptAttrs||{}).prop({charset:n.scriptCharset,src:n.url}).on("load error",o=function(e){i.remove(),o=null,e&&t("error"===e.type?404:200,e.type)}),w.head.appendChild(i[0])},abort:function(){o&&o()}}});var Gt=[],Jt=/(=)\?(?=&|$)|\?\?/;C.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=Gt.pop()||C.expando+"_"+kt.guid++;return this[e]=!0,e}}),C.ajaxPrefilter("json jsonp",function(e,t,n){var i,o,r,a=!1!==e.jsonp&&(Jt.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&Jt.test(e.data)&&"data");if(a||"jsonp"===e.dataTypes[0])return i=e.jsonpCallback=g(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,a?e[a]=e[a].replace(Jt,"$1"+i):!1!==e.jsonp&&(e.url+=(Dt.test(e.url)?"&":"?")+e.jsonp+"="+i),e.converters["script json"]=function(){return r||C.error(i+" was not called"),r[0]},e.dataTypes[0]="json",o=H[i],H[i]=function(){r=arguments},n.always(function(){void 0===o?C(H).removeProp(i):H[i]=o,e[i]&&(e.jsonpCallback=t.jsonpCallback,Gt.push(i)),r&&g(o)&&o(r[0]),r=o=void 0}),"script"}),b.createHTMLDocument=((d=w.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===d.childNodes.length),C.parseHTML=function(e,t,n){return"string"!=typeof e?[]:("boolean"==typeof t&&(n=t,t=!1),t||(b.createHTMLDocument?((i=(t=w.implementation.createHTMLDocument("")).createElement("base")).href=w.location.href,t.head.appendChild(i)):t=w),i=!n&&[],(n=I.exec(e))?[t.createElement(n[1])]:(n=ve([e],t,i),i&&i.length&&C(i).remove(),C.merge([],n.childNodes)));var i},C.fn.load=function(e,t,n){var i,o,r,a=this,s=e.indexOf(" ");return-1<s&&(i=Ht(e.slice(s)),e=e.slice(0,s)),g(t)?(n=t,t=void 0):t&&"object"==typeof t&&(o="POST"),0<a.length&&C.ajax({url:e,type:o||"GET",dataType:"html",data:t}).done(function(e){r=arguments,a.html(i?C("<div>").append(C.parseHTML(e)).find(i):e)}).always(n&&function(e,t){a.each(function(){n.apply(this,r||[e.responseText,t,e])})}),this},C.expr.pseudos.animated=function(t){return C.grep(C.timers,function(e){return t===e.elem}).length},C.offset={setOffset:function(e,t,n){var i,o,r,a,s=C.css(e,"position"),c=C(e),u={};"static"===s&&(e.style.position="relative"),r=c.offset(),i=C.css(e,"top"),a=C.css(e,"left"),a=("absolute"===s||"fixed"===s)&&-1<(i+a).indexOf("auto")?(o=(s=c.position()).top,s.left):(o=parseFloat(i)||0,parseFloat(a)||0),null!=(t=g(t)?t.call(e,n,C.extend({},r)):t).top&&(u.top=t.top-r.top+o),null!=t.left&&(u.left=t.left-r.left+a),"using"in t?t.using.call(e,u):("number"==typeof u.top&&(u.top+="px"),"number"==typeof u.left&&(u.left+="px"),c.css(u))}},C.fn.extend({offset:function(t){if(arguments.length)return void 0===t?this:this.each(function(e){C.offset.setOffset(this,t,e)});var e,n=this[0];return n?n.getClientRects().length?(e=n.getBoundingClientRect(),n=n.ownerDocument.defaultView,{top:e.top+n.pageYOffset,left:e.left+n.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var e,t,n,i=this[0],o={top:0,left:0};if("fixed"===C.css(i,"position"))t=i.getBoundingClientRect();else{for(t=this.offset(),n=i.ownerDocument,e=i.offsetParent||n.documentElement;e&&(e===n.body||e===n.documentElement)&&"static"===C.css(e,"position");)e=e.parentNode;e&&e!==i&&1===e.nodeType&&((o=C(e).offset()).top+=C.css(e,"borderTopWidth",!0),o.left+=C.css(e,"borderLeftWidth",!0))}return{top:t.top-o.top-C.css(i,"marginTop",!0),left:t.left-o.left-C.css(i,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent;e&&"static"===C.css(e,"position");)e=e.offsetParent;return e||ie})}}),C.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(t,o){var r="pageYOffset"===o;C.fn[t]=function(e){return z(this,function(e,t,n){var i;return m(e)?i=e:9===e.nodeType&&(i=e.defaultView),void 0===n?i?i[o]:e[t]:void(i?i.scrollTo(r?i.pageXOffset:n,r?n:i.pageYOffset):e[t]=n)},t,e,arguments.length)}}),C.each(["top","left"],function(e,n){C.cssHooks[n]=_e(b.pixelPosition,function(e,t){if(t)return t=We(e,n),Ve.test(t)?C(e).position()[n]+"px":t})}),C.each({Height:"height",Width:"width"},function(a,s){C.each({padding:"inner"+a,content:s,"":"outer"+a},function(i,r){C.fn[r]=function(e,t){var n=arguments.length&&(i||"boolean"!=typeof e),o=i||(!0===e||!0===t?"margin":"border");return z(this,function(e,t,n){var i;return m(e)?0===r.indexOf("outer")?e["inner"+a]:e.document.documentElement["client"+a]:9===e.nodeType?(i=e.documentElement,Math.max(e.body["scroll"+a],i["scroll"+a],e.body["offset"+a],i["offset"+a],i["client"+a])):void 0===n?C.css(e,t,o):C.style(e,t,n,o)},s,n?e:void 0,n)}})}),C.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){C.fn[t]=function(e){return this.on(t,e)}}),C.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,i){return this.on(t,e,n,i)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)},hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)}}),C.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(e,n){C.fn[n]=function(e,t){return 0<arguments.length?this.on(n,null,e,t):this.trigger(n)}});var Kt=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g;C.proxy=function(e,t){var n,i;if("string"==typeof t&&(i=e[t],t=e,e=i),g(e))return n=s.call(arguments,2),(i=function(){return e.apply(t||this,n.concat(s.call(arguments)))}).guid=e.guid=e.guid||C.guid++,i},C.holdReady=function(e){e?C.readyWait++:C.ready(!0)},C.isArray=Array.isArray,C.parseJSON=JSON.parse,C.nodeName=S,C.isFunction=g,C.isWindow=m,C.camelCase=X,C.type=h,C.now=Date.now,C.isNumeric=function(e){var t=C.type(e);return("number"===t||"string"===t)&&!isNaN(e-parseFloat(e))},C.trim=function(e){return null==e?"":(e+"").replace(Kt,"")},"function"==typeof define&&define.amd&&define("jquery",[],function(){return C});var Zt=H.jQuery,en=H.$;return C.noConflict=function(e){return H.$===C&&(H.$=en),e&&H.jQuery===C&&(H.jQuery=Zt),C},void 0===e&&(H.jQuery=H.$=C),C});var H5P=window.H5P=window.H5P||{};H5P.jQuery=jQuery.noConflict(!0),H5P.jQuery.fn.__originalLoad=H5P.jQuery.load,H5P.jQuery.fn.load=function(e,t,n){if("function"!=typeof e)return H5P.jQuery.fn.__originalLoad.apply(this,arguments);{console.warn("You are using a deprecated H5P library. Please upgrade!");let e=Array.prototype.slice.call(arguments);return e.unshift("load"),H5P.jQuery.fn.on.apply(this,e)}},(H5P=window.H5P=window.H5P||{}).isFramed=window.self!==window.parent,H5P.$window=H5P.jQuery(window),H5P.instances=[],document.documentElement.requestFullScreen?H5P.fullScreenBrowserPrefix="":document.documentElement.webkitRequestFullScreen?(H5P.safariBrowser=navigator.userAgent.match(/version\/([.\d]+)/i),H5P.safariBrowser=null===H5P.safariBrowser?0:parseInt(H5P.safariBrowser[1]),(0===H5P.safariBrowser||6<H5P.safariBrowser)&&(H5P.fullScreenBrowserPrefix="webkit")):document.documentElement.mozRequestFullScreen?H5P.fullScreenBrowserPrefix="moz":document.documentElement.msRequestFullscreen&&(H5P.fullScreenBrowserPrefix="ms"),H5P.opened={},H5P.init=function(e){void 0===H5P.$body&&(H5P.$body=H5P.jQuery(document.body)),void 0===H5P.fullscreenSupported&&(H5P.fullscreenSupported=!(H5PIntegration.fullscreenDisabled||H5P.fullscreenDisabled||H5P.isFramed&&!1!==H5P.externalEmbed&&!(document.fullscreenEnabled||document.webkitFullscreenEnabled||document.mozFullScreenEnabled))),void 0===H5P.canHasFullScreen&&(H5P.canHasFullScreen=H5P.fullscreenSupported),H5P.jQuery(".h5p-content:not(.h5p-initialized)",e).each(function(){var t=H5P.jQuery(this).addClass("h5p-initialized"),n=H5P.jQuery('<div class="h5p-container"></div>').appendTo(t),o=t.data("content-id"),r=H5PIntegration.contents["cid-"+o];if(void 0===r)return H5P.error("No data for content id "+o+". Perhaps the library is gone?");var a={library:r.library,params:JSON.parse(r.jsonContent),metadata:r.metadata};H5P.getUserData(o,"state",function(e,t){var i;t?a.userDatas={state:t}:null===t&&H5PIntegration.saveFreq&&(delete r.contentUserData,i=new H5P.Dialog("content-user-data-reset","Data Reset","<p>"+H5P.t("contentChanged")+"</p><p>"+H5P.t("startingOver")+'</p><div class="h5p-dialog-ok-button" tabIndex="0" role="button">OK</div>',n),H5P.jQuery(i).on("dialog-opened",function(e,t){function n(e){"click"!==e.type&&32!==e.which||(i.close(),H5P.deleteUserData(o,"state",0))}t.find(".h5p-dialog-ok-button").click(n).keypress(n),H5P.trigger(s,"resize")}).on("dialog-closed",function(){H5P.trigger(s,"resize")}),i.open())});var s=H5P.newRunnable(a,o,n,!0,{standalone:!0});H5P.offlineRequestQueue=new H5P.OfflineRequestQueue({instance:s}),1==r.fullScreen&&H5P.fullscreenSupported&&H5P.jQuery('<div class="h5p-content-controls"><div role="button" tabindex="0" class="h5p-enable-fullscreen" aria-label="'+H5P.t("fullscreen")+'" title="'+H5P.t("fullscreen")+'"></div></div>').prependTo(n).children().click(function(){H5P.fullScreen(n,s)}).keydown(function(e){if(32===e.which||13===e.which)return H5P.fullScreen(n,s),!1});var e,i,c,u,l,d,p,f=r.displayOptions,h=!1;f.frame&&(f.copyright&&((e=H5P.getCopyrights(s,a.params,o,a.metadata))||(f.copyright=!1)),f=new H5P.ActionBar(f),i=f.getDOMElement(),f.on("reuse",function(){H5P.openReuseDialog(i,r,a,s,o),s.triggerXAPI("accessed-reuse")}),f.on("copyrights",function(){new H5P.Dialog("copyrights",H5P.t("copyrightInformation"),e,n).open(!0),s.triggerXAPI("accessed-copyright")}),f.on("embed",function(){H5P.openEmbedDialog(i,r.embedCode,r.resizeCode,{width:t.width(),height:t.height()},s),s.triggerXAPI("accessed-embed")}),f.hasActions()&&(h=!0,i.insertAfter(n))),t.addClass(h?"h5p-frame":"h5p-no-frame"),H5P.opened[o]=new Date,H5P.on(s,"finish",function(e){void 0!==e.data&&H5P.setFinished(o,e.data.score,e.data.maxScore,e.data.time)}),H5P.on(s,"xAPI",H5P.xAPICompletedListener),!1!==H5PIntegration.saveFreq&&(s.getCurrentState instanceof Function||"function"==typeof s.getCurrentState)&&(u=function(){var e=s.getCurrentState();void 0!==e&&H5P.setUserData(o,"state",e,{deleteOnChange:!0}),H5PIntegration.saveFreq&&(c=setTimeout(u,1e3*H5PIntegration.saveFreq))},H5PIntegration.saveFreq&&(c=setTimeout(u,1e3*H5PIntegration.saveFreq)),H5P.on(s,"xAPI",function(e){e=e.getVerb();"completed"!==e&&"progressed"!==e||(clearTimeout(c),c=setTimeout(u,3e3))})),H5P.isFramed&&(!1===H5P.externalEmbed?(d=window.frameElement,H5P.on(s,"resize",function(){clearTimeout(l),l=setTimeout(function(){var e;window.parent.H5P.isFullscreen||(e=d.parentElement.style.height,d.parentElement.style.height=d.parentElement.clientHeight+"px",d.getBoundingClientRect(),d.style.height="1px",d.style.height=d.contentDocument.body.scrollHeight+"px",d.parentElement.style.height=e)},1)})):H5P.communicator&&(p=!1,H5P.communicator.on("ready",function(){H5P.communicator.send("hello")}),H5P.communicator.on("hello",function(){p=!0,document.body.style.height="auto",document.body.style.overflow="hidden",H5P.trigger(s,"resize")}),H5P.communicator.on("resizePrepared",function(){H5P.communicator.send("resize",{scrollHeight:document.body.scrollHeight})}),H5P.communicator.on("resize",function(){H5P.trigger(s,"resize")}),H5P.on(s,"resize",function(){H5P.isFullscreen||(clearTimeout(l),l=setTimeout(function(){p?H5P.communicator.send("prepareResize",{scrollHeight:document.body.scrollHeight,clientHeight:document.body.clientHeight}):H5P.communicator.send("hello")},0))}))),H5P.isFramed&&!1!==H5P.externalEmbed||H5P.jQuery(window.parent).resize(function(){window.parent.H5P.isFullscreen,H5P.trigger(s,"resize")}),H5P.instances.push(s),H5P.trigger(s,"resize"),t.addClass("using-mouse"),t.on("mousedown keydown keyup",function(e){t.toggleClass("using-mouse","mousedown"===e.type)}),H5P.externalDispatcher&&H5P.externalDispatcher.trigger("initialized")}),H5P.jQuery("iframe.h5p-iframe:not(.h5p-initialized)",e).each(function(){var e=H5P.jQuery(this).addClass("h5p-initialized").data("content-id"),t=H5PIntegration.contents["cid-"+e],t=t&&t.metadata&&t.metadata.defaultLanguage?t.metadata.defaultLanguage:"en";this.contentDocument.open(),this.contentDocument.write('<!doctype html><html class="h5p-iframe" lang="'+t+'"><head>'+H5P.getHeadTags(e)+'</head><body><div class="h5p-content" data-content-id="'+e+'"/></body></html>'),this.contentDocument.close()})},H5P.getHeadTags=function(e){function t(e){for(var t="",n=0;n<e.length;n++)t+='<link rel="stylesheet" href="'+e[n]+'">';return t}function n(e){for(var t="",n=0;n<e.length;n++)t+='<script src="'+e[n]+'"><\/script>';return t}return'<base target="_parent">'+t(H5PIntegration.core.styles)+t(H5PIntegration.contents["cid-"+e].styles)+n(H5PIntegration.core.scripts)+n(H5PIntegration.contents["cid-"+e].scripts)+"<script>H5PIntegration = window.parent.H5PIntegration; var H5P = H5P || {}; H5P.externalEmbed = false;<\/script>"},H5P.communicator=window.postMessage&&window.addEventListener?new function(){var n={};window.addEventListener("message",function(e){window.parent===e.source&&"h5p"===e.data.context&&void 0!==n[e.data.action]&&n[e.data.action](e.data)},!1),this.on=function(e,t){n[e]=t},this.send=function(e,t){(t=void 0===t?{}:t).context="h5p",t.action=e,window.parent.postMessage(t,"*")}}:void 0,H5P.semiFullScreen=function(e,t,n,i){H5P.fullScreen(e,t,n,i,!0)},H5P.fullScreen=function(e,t,n,i,o){if(void 0===H5P.exitFullScreen){if(H5P.isFramed&&!1===H5P.externalEmbed)return window.parent.H5P.fullScreen(e,t,n,H5P.$body.get(),o),H5P.isFullscreen=!0,H5P.exitFullScreen=function(){window.parent.H5P.exitFullScreen()},void H5P.on(t,"exitFullScreen",function(){H5P.isFullscreen=!1,H5P.exitFullScreen=void 0});var r,a,s,c=e;void 0===i?r=H5P.$body:(a=(r=H5P.jQuery(i)).add(e.get()),i="#h5p-iframe-"+e.parent().data("content-id"),e=(s=H5P.jQuery(i)).parent()),a=e.add(H5P.$body).add(a);function u(e){a.addClass(e),void 0!==s&&s.css("height","")}function l(){H5P.trigger(t,"resize"),H5P.trigger(t,"focus"),H5P.trigger(t,"enterFullScreen")}function d(e){H5P.isFullscreen=!1,a.removeClass(e),H5P.trigger(t,"resize"),H5P.trigger(t,"focus"),(H5P.exitFullScreen=void 0)!==n&&n(),H5P.trigger(t,"exitFullScreen")}if(H5P.isFullscreen=!0,void 0===H5P.fullScreenBrowserPrefix||!0===o){if(!H5P.isFramed){u("h5p-semi-fullscreen");var p,f,h=H5P.jQuery('<div role="button" tabindex="0" class="h5p-disable-fullscreen" title="'+H5P.t("disableFullscreen")+'" aria-label="'+H5P.t("disableFullscreen")+'"></div>').appendTo(c.find(".h5p-content-controls")),g=H5P.exitFullScreen=function(){p?f.content=p:v.removeChild(f),h.remove(),r.unbind("keyup",m),d("h5p-semi-fullscreen")},m=function(e){27===e.keyCode&&g()};h.click(g),r.keyup(m);for(var v,y=document.getElementsByTagName("meta"),b=0;b<y.length;b++)if("viewport"===y[b].name){f=y[b],p=f.content;break}p||((f=document.createElement("meta")).name="viewport"),f.content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0",p||(v=document.getElementsByTagName("head")[0]).appendChild(f),l()}}else{u("h5p-fullscreen");var P,x="ms"===H5P.fullScreenBrowserPrefix?"MSFullscreenChange":H5P.fullScreenBrowserPrefix+"fullscreenchange";document.addEventListener(x,function(){return void 0===P?(P=!1,void l()):(d("h5p-fullscreen"),void document.removeEventListener(x,arguments.callee,!1))}),""===H5P.fullScreenBrowserPrefix?e[0].requestFullScreen():(o="ms"===H5P.fullScreenBrowserPrefix?"msRequestFullscreen":H5P.fullScreenBrowserPrefix+"RequestFullScreen",c="webkit"===H5P.fullScreenBrowserPrefix&&0===H5P.safariBrowser?Element.ALLOW_KEYBOARD_INPUT:void 0,e[0][o](c)),H5P.exitFullScreen=function(){""===H5P.fullScreenBrowserPrefix?document.exitFullscreen():"moz"===H5P.fullScreenBrowserPrefix?document.mozCancelFullScreen():document[H5P.fullScreenBrowserPrefix+"ExitFullscreen"]()}}}},function(){H5P.addQueryParameter=function(e,t){let n,i;const o=e.split("?");return n=o[1]?(i=o[1].split("#"),o[0]+"?"+i[0]+"&"):(i=o[0].split("#"),i[0]+"?"),n+=t,i[1]&&(n+="#"+i[1]),n},H5P.setSource=function(e,t,n){let i=t.path;t=H5P.getCrossOrigin(t);t?(e.crossOrigin=t,H5PIntegration.crossoriginCacheBuster&&(i=H5P.addQueryParameter(i,H5PIntegration.crossoriginCacheBuster))):e.removeAttribute("crossorigin"),e.src=H5P.getPath(i,n)};function o(e){return e.match(/^[a-z0-9]+:\/\//i)}H5P.getCrossOrigin=function(e){return"object"!=typeof e?H5PIntegration.crossorigin&&H5PIntegration.crossoriginRegex&&e.match(H5PIntegration.crossoriginRegex)?H5PIntegration.crossorigin:null:H5PIntegration.crossorigin&&!o(e.path)?H5PIntegration.crossorigin:void 0},H5P.getPath=function(e,t){if(o(e))return e;var n,i="#tmp"===e.substr(-4,4);if(void 0===t||i){if(void 0===window.H5PEditor)return;n=H5PEditor.filesPath}else n=(n=void 0!==H5PIntegration.contents&&H5PIntegration.contents["cid-"+t]?H5PIntegration.contents["cid-"+t].contentUrl:n)||H5PIntegration.url+"/content/"+t;return(n=!o(n)?window.location.protocol+"//"+window.location.host+n:n)+"/"+e}}(),H5P.getContentPath=function(e){return H5PIntegration.url+"/content/"+e},H5P.classFromName=function(e){e=e.split(".");return this[e[e.length-1]]},H5P.newRunnable=function(t,e,n,i,o){var r,a;try{a=(s=t.library.split(" ",2))[0],r=s[1].split(".",2)}catch(e){return H5P.error("Invalid library string: "+t.library)}if(t.params instanceof Object!=!0||t.params instanceof Array==!0)return H5P.error("Invalid library params for: "+t.library),H5P.error(t.params);try{for(var s=s[0].split("."),c=window,u=0;u<s.length;u++)c=c[s[u]];if("function"!=typeof c)throw null}catch(e){return H5P.error("Unable to find constructor for: "+t.library)}void 0===o&&(o={}),t.subContentId&&(o.subContentId=t.subContentId),t.userDatas&&t.userDatas.state&&H5PIntegration.saveFreq&&(o.previousState=t.userDatas.state),t.metadata&&(o.metadata=t.metadata);var l,d=o.standalone||!1;return c.prototype=H5P.jQuery.extend({},H5P.ContentType(d).prototype,c.prototype),void 0===(l=-1<H5P.jQuery.inArray(t.library,["H5P.CoursePresentation 1.0","H5P.CoursePresentation 1.1","H5P.CoursePresentation 1.2","H5P.CoursePresentation 1.3"])?new c(t.params,e):new c(t.params,e,o)).$&&(l.$=H5P.jQuery(l)),void 0===l.contentId&&(l.contentId=e),void 0===l.subContentId&&t.subContentId&&(l.subContentId=t.subContentId),void 0===l.parent&&o&&o.parent&&(l.parent=o.parent),void 0===l.libraryInfo&&(l.libraryInfo={versionedName:t.library,versionedNameNoSpaces:a+"-"+r[0]+"."+r[1],machineName:a,majorVersion:r[0],minorVersion:r[1]}),void 0!==n&&(n.toggleClass("h5p-standalone",d),l.attach(n),H5P.trigger(l,"domChanged",{$target:n,library:a,key:"newLibrary"},{bubbles:!0,external:!0}),void 0!==i&&i||H5P.trigger(l,"resize")),l},H5P.error=function(e){void 0!==window.console&&void 0!==console.error&&console.error(e.stack||e)},H5P.t=function(e,t,n){if(void 0===n&&(n="H5P"),void 0===H5PIntegration.l10n[n])return'[Missing translation namespace "'+n+'"]';if(void 0===H5PIntegration.l10n[n][e])return'[Missing translation "'+e+'" in "'+n+'"]';var i=H5PIntegration.l10n[n][e];if(void 0!==t)for(var o in t)i=i.replace(o,t[o]);return i},H5P.Dialog=function(e,t,n,i){var o=this,r=H5P.jQuery('<div class="h5p-popup-dialog h5p-'+e+'-dialog" role="dialog" tabindex="-1">                              <div class="h5p-inner">                                <h2>'+t+'</h2>                                <div class="h5p-scroll-content">'+n+'</div>                                <div class="h5p-close" role="button" tabindex="0" aria-label="'+H5P.t("close")+'" title="'+H5P.t("close")+'"></div>                              </div>                            </div>').insertAfter(i).click(function(e){e&&e.originalEvent&&e.originalEvent.preventClosing||o.close()}).children(".h5p-inner").click(function(e){e.originalEvent.preventClosing=!0}).find(".h5p-close").click(function(){o.close()}).keypress(function(e){if(13===e.which||32===e.which)return o.close(),!1}).end().find("a").click(function(e){e.stopPropagation()}).end().end();o.open=function(e){e&&r.css("height","100%"),setTimeout(function(){r.addClass("h5p-open"),H5P.jQuery(o).trigger("dialog-opened",[r]),r.focus()},1)},o.close=function(){r.removeClass("h5p-open"),setTimeout(function(){r.remove(),H5P.jQuery(o).trigger("dialog-closed",[r]),i.attr("tabindex","-1"),i.focus()},200)}},H5P.getCopyrights=function(e,t,n,i){var o;if(void 0!==e.getCopyrights)try{o=e.getCopyrights()}catch(e){}void 0===o&&(o=new H5P.ContentCopyrights,H5P.findCopyrights(o,t,n));e=H5P.buildMetadataCopyrights(i,e.libraryInfo.machineName);return void 0!==e&&o.addMediaInFront(e),o=void 0!==o?o.toString():o},H5P.findCopyrights=function(r,e,t,n){var i,o,a,s;for(o in n&&(n.params=e,c(n,n.machineName,t)),e)e.hasOwnProperty(o)&&("overrideSettings"!==o?((a=e[o])&&a.library&&"string"==typeof a.library?i=a.library.split(" ")[0]:a&&a.library&&"object"==typeof a.library&&(i=a.library.library&&"string"==typeof a.library.library?a.library.library.split(" ")[0]:i),a instanceof Array?H5P.findCopyrights(r,a,t):a instanceof Object&&(c(a,i,t),void 0===a.copyright||void 0===a.copyright.license||void 0===a.path||void 0===a.mime?H5P.findCopyrights(r,a,t):(s=new H5P.MediaCopyright(a.copyright),void 0!==a.width&&void 0!==a.height&&s.setThumbnail(new H5P.Thumbnail(H5P.getPath(a.path,t),a.width,a.height)),r.addMedia(s)))):(console.warn("The semantics field 'overrideSettings' is DEPRECATED and should not be used."),console.warn(e)));function c(e,t,n){if(e.metadata){const o=H5P.buildMetadataCopyrights(e.metadata,t);var i;void 0!==o&&(e.params&&"Image"===e.params.contentName&&e.params.file&&(i=e.params.file.path,t=e.params.file.width,e=e.params.file.height,o.setThumbnail(new H5P.Thumbnail(H5P.getPath(i,n),t,e))),r.addMedia(o))}}},H5P.buildMetadataCopyrights=function(e){if(e&&void 0!==e.license&&"U"!==e.license){e={contentType:e.contentType,title:e.title,author:e.authors&&0<e.authors.length?e.authors.map(function(e){return e.role?e.name+" ("+e.role+")":e.name}).join(", "):void 0,source:e.source,year:e.yearFrom?e.yearFrom+(e.yearTo?"-"+e.yearTo:""):void 0,license:e.license,version:e.licenseVersion,licenseExtras:e.licenseExtras,changes:e.changes&&0<e.changes.length?e.changes.map(function(e){return e.log+(e.author?", "+e.author:"")+(e.date?", "+e.date:"")}).join(" / "):void 0};return new H5P.MediaCopyright(e)}},H5P.openReuseDialog=function(e,n,i,o,r){let t="";n.displayOptions.export&&(t+='<button type="button" class="h5p-big-button h5p-download-button"><div class="h5p-button-title">Download as an .h5p file</div><div class="h5p-button-description">.h5p files may be uploaded to any web-site where H5P content may be created.</div></button>'),n.displayOptions.export&&n.displayOptions.copy&&(t+='<div class="h5p-horizontal-line-text"><span>or</span></div>'),n.displayOptions.copy&&(t+='<button type="button" class="h5p-big-button h5p-copy-button"><div class="h5p-button-title">Copy content</div><div class="h5p-button-description">Copied content may be pasted anywhere this content type is supported on this website.</div></button>');const a=new H5P.Dialog("reuse",H5P.t("reuseContent"),t,e);H5P.jQuery(a).on("dialog-opened",function(e,t){H5P.jQuery('<a href="https://h5p.org/node/442225" target="_blank">More Info</a>').click(function(e){e.stopPropagation()}).appendTo(t.find("h2")),t.find(".h5p-download-button").click(function(){window.location.href=n.exportUrl,o.triggerXAPI("downloaded"),a.close()}),t.find(".h5p-copy-button").click(function(){const e=new H5P.ClipboardItem(i);e.contentId=r,H5P.setClipboard(e),o.triggerXAPI("copied"),a.close(),H5P.attachToastTo(H5P.jQuery(".h5p-content:first")[0],H5P.t("contentCopied"),{position:{horizontal:"centered",vertical:"centered",noOverflowX:!0}})}),H5P.trigger(o,"resize")}).on("dialog-closed",function(){H5P.trigger(o,"resize")}),a.open()},H5P.openEmbedDialog=function(e,t,n,u,l){var d=t+n,e=new H5P.Dialog("embed",H5P.t("embed"),'<textarea class="h5p-embed-code-container" autocorrect="off" autocapitalize="off" spellcheck="false"></textarea>'+H5P.t("size")+': <input type="text" value="'+Math.ceil(u.width)+'" class="h5p-embed-size"/> × <input type="text" value="'+Math.ceil(u.height)+'" class="h5p-embed-size"/> px<br/><div role="button" tabindex="0" class="h5p-expander">'+H5P.t("showAdvanced")+'</div><div class="h5p-expander-content"><p>'+H5P.t("advancedHelp")+'</p><textarea class="h5p-embed-code-container" autocorrect="off" autocapitalize="off" spellcheck="false">'+n+"</textarea></div>",e);H5P.jQuery(e).on("dialog-opened",function(e,n){function i(){H5P.trigger(l,"resize")}function t(e,t){return e=parseFloat(e.val()),isNaN(e)?t:Math.ceil(e)}function o(){n.find(".h5p-embed-code-container:first").val(d.replace(":w",t(a,u.width)).replace(":h",t(s,u.height)))}var r=n.find(".h5p-inner").find(".h5p-scroll-content"),a=(r.outerHeight(),r.innerHeight(),n.find(".h5p-embed-size:eq(0)")),s=n.find(".h5p-embed-size:eq(1)");a.change(o),s.change(o),o(),n.find(".h5p-embed-code-container").each(function(){H5P.jQuery(this).css("height",this.scrollHeight+"px").focus(function(){H5P.jQuery(this).select()})}),n.find(".h5p-embed-code-container").eq(0).select(),i();function c(){var e=H5P.jQuery(this),t=e.next();t.is(":visible")?(e.removeClass("h5p-open").text(H5P.t("showAdvanced")).attr("aria-expanded","true"),t.hide()):(e.addClass("h5p-open").text(H5P.t("hideAdvanced")).attr("aria-expanded","false"),t.show()),n.find(".h5p-embed-code-container").each(function(){H5P.jQuery(this).css("height",this.scrollHeight+"px")}),i()}n.find(".h5p-expander").click(c).keypress(function(e){if(32===e.keyCode)return c.apply(this),!1})}).on("dialog-closed",function(){H5P.trigger(l,"resize")}),e.open()},H5P.attachToastTo=function(t,e,n){if(void 0===t||void 0===e)return;function i(e){-1===o(e).indexOf(t)&&(clearTimeout(c),r())}const o=function(e){var t=e.composedPath&&e.composedPath()||e.path,e=e.target;return null!=t?t.indexOf(window)<0?t.concat(window):t:e===window?[window]:[e].concat(function e(t,n){n=n||[];t=t.parentNode;return t?e(t,n.concat(t)):n}(e),window)},r=function(){document.removeEventListener("click",i),a.parentNode&&a.parentNode.removeChild(a)};(n=n||{}).style=n.style||"h5p-toast",n.duration=n.duration||3e3;const a=document.createElement("div");a.setAttribute("id",n.style),a.classList.add("h5p-toast-disabled"),a.classList.add(n.style);const s=document.createElement("span");s.innerHTML=e,a.appendChild(s),document.body.appendChild(a);e=function(e,t,n){(n=n||{}).offsetHorizontal=n.offsetHorizontal||0,n.offsetVertical=n.offsetVertical||0;var i=t.getBoundingClientRect(),o=e.getBoundingClientRect();let r=0,a=0;switch(n.horizontal){case"before":r=o.left-i.width-n.offsetHorizontal;break;case"after":r=o.left+o.width+n.offsetHorizontal;break;case"left":r=o.left+n.offsetHorizontal;break;case"right":r=o.left+o.width-i.width-n.offsetHorizontal;break;default:r=o.left+o.width/2-i.width/2+n.offsetHorizontal}switch(n.vertical){case"above":a=o.top-i.height-n.offsetVertical;break;case"below":a=o.top+o.height+n.offsetVertical;break;case"top":a=o.top+n.offsetVertical;break;case"bottom":a=o.top+o.height-i.height-n.offsetVertical;break;case"centered":a=o.top+o.height/2-i.height/2+n.offsetVertical;break;default:a=o.top+o.height+n.offsetVertical}const s=document.body;e=s.getBoundingClientRect();return(n.noOverflowLeft||n.noOverflowX)&&r<e.x&&(r=e.x),(n.noOverflowRight||n.noOverflowX)&&r+i.width>e.x+e.width&&(r=e.x+e.width-i.width),(n.noOverflowTop||n.noOverflowY)&&a<e.y&&(a=e.y),(n.noOverflowBottom||n.noOverflowY)&&a+i.height>e.y+e.height&&(r=e.y+e.height-i.height),{left:r,top:a}}(t,a,n.position);a.style.left=Math.round(e.left)+"px",a.style.top=Math.round(e.top)+"px",a.classList.remove("h5p-toast-disabled");const c=setTimeout(r,n.duration);document.addEventListener("click",i)},H5P.ContentCopyrights=function(){var n,i=[],o=[];this.setLabel=function(e){n=e},this.addMedia=function(e){void 0!==e&&i.push(e)},this.addMediaInFront=function(e){void 0!==e&&i.unshift(e)},this.addContent=function(e){void 0!==e&&o.push(e)},this.toString=function(){for(var e="",t=0;t<i.length;t++)e+=i[t];for(t=0;t<o.length;t++)e+=o[t];return e=""!==e?'<div class="h5p-content-copyrights">'+(e=void 0!==n?"<h3>"+n+"</h3>"+e:e)+"</div>":e}},H5P.MediaCopyright=function(e,t,n,i){function o(e){return void 0===t||void 0===t[e]?H5P.t(e):t[e]}function r(e,t){var n,i=H5P.copyrightLicenses[e],o="";"PD"===e&&t||(o+=i.hasOwnProperty("label")?i.label:i),(n=i.versions&&(t=!i.versions.default||t&&i.versions[t]?t:i.versions.default)&&i.versions[t]?i.versions[t]:n)&&(o&&(o+=" "),o+=n.hasOwnProperty("label")?n.label:n),i.hasOwnProperty("link")?r=i.link.replace(":version",i.linkVersions?i.linkVersions[t]:t):n&&i.hasOwnProperty("link")&&(r=n.link),r&&(o='<a href="'+r+'" target="_blank">'+o+"</a>");var r="";return"PD"!==e&&"C"!==e&&(r+=e),t&&"CC0 1.0"!==t&&(r&&"GNU GPL"!==e&&(r+=" "),r+=t),r&&(o+=" ("+r+")"),"C"===e&&(o+=" &copy;"),o}var a,s=new H5P.DefinitionList;if(void 0!==e){for(var c in i)i.hasOwnProperty(c)&&(e[c]=i[c]);void 0===n&&(n=["contentType","title","license","author","year","source","licenseExtras","changes"]);for(var u=0;u<n.length;u++){var l,d=n[u];void 0!==e[d]&&""!==e[d]&&(l=e[d],"license"===d&&(l=r(e.license,e.version)),"source"===d&&(l=l?'<a href="'+l+'" target="_blank">'+l+"</a>":void 0),s.add(new H5P.Field(o(d),l)))}}this.setThumbnail=function(e){a=e},this.undisclosed=function(){if(1===s.size()){var e=s.get(0);if(e.getLabel()===o("license")&&e.getValue()===r("U"))return!0}return!1},this.toString=function(){var e="";return this.undisclosed()?e:(void 0!==a&&(e+=a),""!==(e+=s)?'<div class="h5p-media-copyright">'+e+"</div>":e)}},H5P.Thumbnail=function(e,t,n){var i;void 0!==t&&(i=Math.round(t/n*100)),this.toString=function(){return'<img src="'+e+'" alt="'+H5P.t("thumbnail")+'" class="h5p-thumbnail" height="100"'+(void 0===i?"":' width="'+i+'"')+"/>"}},H5P.Field=function(e,t){this.getLabel=function(){return e},this.getValue=function(){return t}},H5P.DefinitionList=function(){var i=[];this.add=function(e){i.push(e)},this.size=function(){return i.length},this.get=function(e){return i[e]},this.toString=function(){for(var e="",t=0;t<i.length;t++){var n=i[t];e+="<dt>"+n.getLabel()+"</dt><dd>"+n.getValue()+"</dd>"}return""===e?e:'<dl class="h5p-definition-list">'+e+"</dl>"}},H5P.Coords=function(e,t,n,i){return this instanceof H5P.Coords?(this.x=0,this.y=0,this.w=1,this.h=1,"object"==typeof e?(this.x=e.x,this.y=e.y,this.w=e.w,this.h=e.h):(void 0!==e&&(this.x=e),void 0!==t&&(this.y=t),void 0!==n&&(this.w=n),void 0!==i&&(this.h=i)),this):new H5P.Coords(e,t,n,i)},H5P.libraryFromString=function(e){e=/(.+)\s(\d+)\.(\d+)$/g.exec(e);return null!==e&&{machineName:e[1],majorVersion:parseInt(e[2]),minorVersion:parseInt(e[3])}},H5P.getLibraryPath=function(e){return void 0!==H5PIntegration.urlLibraries?H5PIntegration.urlLibraries+"/"+e:H5PIntegration.url+"/libraries/"+e},H5P.cloneObject=function(e,t){var n,i=e instanceof Array?[]:{};for(n in e)e.hasOwnProperty(n)&&(void 0!==t&&t&&"object"==typeof e[n]?i[n]=H5P.cloneObject(e[n],t):i[n]=e[n]);return i},H5P.trim=function(e){return e.replace(/^\s+|\s+$/g,"")},H5P.jsLoaded=function(e){return H5PIntegration.loadedJs=H5PIntegration.loadedJs||[],-1!==H5P.jQuery.inArray(e,H5PIntegration.loadedJs)},H5P.cssLoaded=function(e){return H5PIntegration.loadedCss=H5PIntegration.loadedCss||[],-1!==H5P.jQuery.inArray(e,H5PIntegration.loadedCss)},H5P.shuffleArray=function(e){if(e instanceof Array){var t,n,i,o=e.length;if(0===o)return!1;for(;--o;)t=Math.floor(Math.random()*(o+1)),n=e[o],i=e[t],e[o]=i,e[t]=n;return e}},H5P.setFinished=function(e,t,n,i){if(("number"==typeof t||t instanceof Number)&&!0===H5PIntegration.postUserStatistics){function o(e){return Math.round(e.getTime()/1e3)}const r={contentId:e,score:t,maxScore:n,opened:o(H5P.opened[e]),finished:o(new Date),time:i};H5P.jQuery.post(H5PIntegration.ajax.setFinished,r).fail(function(){H5P.offlineRequestQueue.add(H5PIntegration.ajax.setFinished,r)})}},Array.prototype.indexOf||(Array.prototype.indexOf=function(e){for(var t=0;t<this.length;t++)if(this[t]===e)return t;return-1}),void 0===String.prototype.trim&&(String.prototype.trim=function(){return H5P.trim(this)}),H5P.trigger=function(e,t,n,i){void 0!==e.trigger?e.trigger(t,n,i):void 0!==e.$&&void 0!==e.$.trigger&&e.$.trigger(t)},H5P.on=function(e,t,n){void 0!==e.on?e.on(t,n):void 0!==e.$&&void 0!==e.$.on&&e.$.on(t,n)},H5P.createUUID=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})},H5P.createTitle=function(e,t){if(!e)return"";void 0===t&&(t=60);e=H5P.jQuery("<div></div>").text(e.replace(/(<([^>]+)>)/gi,"")).text();return e=e.length>t?e.substr(0,t-3)+"...":e},function(c){function s(e,t,n,i,o,r,a,s){void 0!==H5PIntegration.user?(s={url:H5PIntegration.ajax.contentUserData.replace(":contentId",e).replace(":dataType",t).replace(":subContentId",n||0),dataType:"json",async:void 0===s||s},void 0!==o?(s.type="POST",s.data={data:null===o?0:o,preload:r?1:0,invalidate:a?1:0}):s.type="GET",void 0!==i&&(s.error=function(e,t){i(t)},s.success=function(e){e.success?!1!==e.data&&void 0!==e.data?i(void 0,e.data):i():i(e.message)}),c.ajax(s)):i("Not signed in.")}H5P.getUserData=function(e,n,i,o){o=o||0,H5PIntegration.contents=H5PIntegration.contents||{};var r=H5PIntegration.contents["cid-"+e]||{},a=r.contentUserData;if(a&&a[o]&&void 0!==a[o][n])if("RESET"!==a[o][n])try{i(void 0,JSON.parse(a[o][n]))}catch(e){i(e)}else i(void 0,null);else s(e,n,o,function(e,t){if(e||void 0===t)i(e,t);else{void 0===r.contentUserData&&(r.contentUserData=a={}),void 0===a[o]&&(a[o]={}),a[o][n]=t;try{i(void 0,JSON.parse(t))}catch(e){i(e)}}})},H5P.setUserData=function(e,t,n,i){var o=H5P.jQuery.extend(!0,{},{subContentId:0,preloaded:!0,deleteOnChange:!1,async:!0},i);try{n=JSON.stringify(n)}catch(e){return void(o.errorCallback&&o.errorCallback(e))}i=H5PIntegration.contents["cid-"+e];(i=void 0===i?H5PIntegration.contents["cid-"+e]={}:i).contentUserData||(i.contentUserData={});i=i.contentUserData;void 0===i[o.subContentId]&&(i[o.subContentId]={}),n!==i[o.subContentId][t]&&(i[o.subContentId][t]=n,s(e,t,o.subContentId,function(e){o.errorCallback&&e&&o.errorCallback(e)},n,o.preloaded,o.deleteOnChange,o.async))},H5P.deleteUserData=function(e,t,n){n=n||0;var i=H5PIntegration.contents["cid-"+e].contentUserData;i&&i[n]&&i[n][t]&&delete i[n][t],s(e,t,n,void 0,null)},H5P.getContentForInstance=function(e){e="cid-"+e;return H5PIntegration&&H5PIntegration.contents&&H5PIntegration.contents[e]?H5PIntegration.contents[e]:void 0},H5P.ClipboardItem=function(e,t,n){var i=this;t||(t="action",e={action:e}),i.specific=e,t&&e[t]&&(i.generic=t),n&&(i.from=n),window.H5PEditor&&H5PEditor.contentId&&(i.contentId=H5PEditor.contentId),i.specific.width||i.specific.height||!i.generic||(n=i.specific[i.generic]).params.file&&n.params.file.width&&n.params.file.height&&(i.width=20,i.height=n.params.file.height/n.params.file.width*i.width)},H5P.clipboardify=function(e){e instanceof H5P.ClipboardItem||(e=new H5P.ClipboardItem(e)),H5P.setClipboard(e)},H5P.getClipboard=function(){return e()},H5P.setClipboard=function(e){localStorage.setItem("h5pClipboard",JSON.stringify(e)),H5P.externalDispatcher.trigger("datainclipboard",{reset:!1})},H5P.getLibraryConfig=function(e){return H5PIntegration.libraryConfig&&H5PIntegration.libraryConfig[e]?H5PIntegration.libraryConfig[e]:{}};var e=function(){var t=localStorage.getItem("h5pClipboard");if(t){try{t=JSON.parse(t)}catch(e){return void console.error("Unable to parse JSON from clipboard.",e)}return i(t.specific,function(e){return"#tmp"===e.substr(-4,4)||!t.contentId||e.match(/^https?:\/\//i)?e:H5PEditor.contentId?"../"+t.contentId+"/"+e:(H5PEditor.contentRelUrl||"../content/")+t.contentId+"/"+e}),t.generic&&(t.generic=t.specific[t.generic]),t}},i=function(e,t){for(var n in e)e.hasOwnProperty(n)&&e[n]instanceof Object&&(void 0!==(n=e[n]).path&&void 0!==n.mime?n.path=t(n.path):(void 0!==n.library&&void 0!==n.subContentId&&delete n.subContentId,i(n,t)))};c(document).ready(function(){window.addEventListener("storage",function(e){"h5pClipboard"===e.key&&H5P.externalDispatcher.trigger("datainclipboard",{reset:null===e.newValue})});var o,e,t={default:"4.0","4.0":H5P.t("licenseCC40"),"3.0":H5P.t("licenseCC30"),2.5:H5P.t("licenseCC25"),"2.0":H5P.t("licenseCC20"),"1.0":H5P.t("licenseCC10")};H5P.copyrightLicenses={U:H5P.t("licenseU"),"CC BY":{label:H5P.t("licenseCCBY"),link:"http://creativecommons.org/licenses/by/:version",versions:t},"CC BY-SA":{label:H5P.t("licenseCCBYSA"),link:"http://creativecommons.org/licenses/by-sa/:version",versions:t},"CC BY-ND":{label:H5P.t("licenseCCBYND"),link:"http://creativecommons.org/licenses/by-nd/:version",versions:t},"CC BY-NC":{label:H5P.t("licenseCCBYNC"),link:"http://creativecommons.org/licenses/by-nc/:version",versions:t},"CC BY-NC-SA":{label:H5P.t("licenseCCBYNCSA"),link:"http://creativecommons.org/licenses/by-nc-sa/:version",versions:t},"CC BY-NC-ND":{label:H5P.t("licenseCCBYNCND"),link:"http://creativecommons.org/licenses/by-nc-nd/:version",versions:t},"CC0 1.0":{label:H5P.t("licenseCC010"),link:"https://creativecommons.org/publicdomain/zero/1.0/"},"GNU GPL":{label:H5P.t("licenseGPL"),link:"http://www.gnu.org/licenses/gpl-:version-standalone.html",linkVersions:{v3:"3.0",v2:"2.0",v1:"1.0"},versions:{default:"v3",v3:H5P.t("licenseV3"),v2:H5P.t("licenseV2"),v1:H5P.t("licenseV1")}},PD:{label:H5P.t("licensePD"),versions:{"CC0 1.0":{label:H5P.t("licenseCC010"),link:"https://creativecommons.org/publicdomain/zero/1.0/"},"CC PDM":{label:H5P.t("licensePDM"),link:"https://creativecommons.org/publicdomain/mark/1.0/"}}},"ODC PDDL":'<a href="http://opendatacommons.org/licenses/pddl/1.0/" target="_blank">Public Domain Dedication and Licence</a>',"CC PDM":{label:H5P.t("licensePDM"),link:"https://creativecommons.org/publicdomain/mark/1.0/"},C:H5P.t("licenseC")},H5P.isFramed&&!1===H5P.externalEmbed&&H5P.externalDispatcher.on("*",function(e){window.parent.H5P.externalDispatcher.trigger.call(this,e)}),H5P.preventInit||H5P.init(document.body),!1!==H5PIntegration.saveFreq&&(o=0,e=function(){var e=(new Date).getTime();if(250<e-o){o=e;for(var t=0;t<H5P.instances.length;t++){var n,i=H5P.instances[t];!(i.getCurrentState instanceof Function||"function"==typeof i.getCurrentState)||void 0!==(n=i.getCurrentState())&&H5P.setUserData(i.contentId,"state",n,{deleteOnChange:!0,async:!1})}}},H5P.$window.one("beforeunload unload",function(){H5P.$window.off("pagehide beforeunload unload"),e()}),H5P.$window.on("pagehide",e))})}(H5P.jQuery),(H5P=window.H5P=window.H5P||{}).Event=function(e,t,n){this.type=e,this.data=t;var i=!1,o=!1,r=!1;!0===(n=void 0===n?{}:n).bubbles&&(i=!0),!0===n.external&&(o=!0),this.preventBubbling=function(){i=!1},this.getBubbles=function(){return i},this.scheduleForExternal=function(){return!(!o||r)&&(r=!0)}},H5P.EventDispatcher=function(){var o=this,a={};function i(e,t){if(void 0!==a[e])for(var n=a[e].slice(),i=0;i<n.length;i++){var o=n[i],r=o.thisArg||this;o.listener.call(r,t)}}this.on=function(e,t,n){if("function"!=typeof t)throw TypeError("listener must be a function");o.trigger("newListener",{type:e,listener:t});n={listener:t,thisArg:n};a[e]?a[e].push(n):a[e]=[n]},this.once=function(e,t,n){if(!(t instanceof Function))throw TypeError("listener must be a function");var i=function(e){o.off(e.type,i),t.call(this,e)};o.on(e,i,n)},this.off=function(e,t){if(void 0!==t&&!(t instanceof Function))throw TypeError("listener must be a function");if(void 0!==a[e]){if(void 0===t)return delete a[e],void o.trigger("removeListener",e);for(var n=0;n<a[e].length;n++)if(a[e][n].listener===t){a[e].splice(n,1),o.trigger("removeListener",e,{listener:t});break}a[e].length||delete a[e]}},this.trigger=function(e,t,n){void 0!==e&&(e instanceof String||"string"==typeof e?e=new H5P.Event(e,t,n):void 0!==t&&(e.data=t),t=e.scheduleForExternal(),i.call(this,e.type,e),i.call(this,"*",e),e.getBubbles()&&o.parent instanceof H5P.EventDispatcher&&(o.parent.trigger instanceof Function||"function"==typeof o.parent.trigger)&&o.parent.trigger(e),t&&H5P.externalDispatcher.trigger.call(this,e))}},(H5P=window.H5P=window.H5P||{}).XAPIEvent=function(){H5P.Event.call(this,"xAPI",{statement:{}},{bubbles:!0,external:!0})},H5P.XAPIEvent.prototype=Object.create(H5P.Event.prototype),H5P.XAPIEvent.prototype.constructor=H5P.XAPIEvent,H5P.XAPIEvent.prototype.setScoredResult=function(e,t,n,i,o){this.data.statement.result={},void 0!==e&&(void 0===t?this.data.statement.result.score={raw:e}:(this.data.statement.result.score={min:0,max:t,raw:e},0<t&&(this.data.statement.result.score.scaled=Math.round(e/t*1e4)/1e4))),this.data.statement.result.completion=void 0===i?"completed"===this.getVerb()||"answered"===this.getVerb():i,void 0!==o&&(this.data.statement.result.success=o),n&&n.activityStartTime&&(n=Math.round((Date.now()-n.activityStartTime)/10)/100,this.data.statement.result.duration="PT"+n+"S")},H5P.XAPIEvent.prototype.setVerb=function(e){-1!==H5P.jQuery.inArray(e,H5P.XAPIEvent.allowedXAPIVerbs)?this.data.statement.verb={id:"http://adlnet.gov/expapi/verbs/"+e,display:{"en-US":e}}:void 0!==e.id&&(this.data.statement.verb=e)},H5P.XAPIEvent.prototype.getVerb=function(e){var t=this.data.statement;return"verb"in t?!0===e?t.verb:t.verb.id.slice(31):null},H5P.XAPIEvent.prototype.setObject=function(e){e.contentId?(this.data.statement.object={id:this.getContentXAPIId(e),objectType:"Activity",definition:{extensions:{"http://h5p.org/x-api/h5p-local-content-id":e.contentId}}},e.subContentId?(this.data.statement.object.definition.extensions["http://h5p.org/x-api/h5p-subContentId"]=e.subContentId,"function"==typeof e.getTitle&&(this.data.statement.object.definition.name={"en-US":e.getTitle()})):(e=H5P.getContentForInstance(e.contentId))&&e.metadata&&e.metadata.title&&(this.data.statement.object.definition.name={"en-US":H5P.createTitle(e.metadata.title)})):this.data.statement.object={definition:{}}},H5P.XAPIEvent.prototype.setContext=function(e){e.parent&&(e.parent.contentId||e.parent.subContentId)&&(this.data.statement.context={contextActivities:{parent:[{id:this.getContentXAPIId(e.parent),objectType:"Activity"}]}}),e.libraryInfo&&(void 0===this.data.statement.context&&(this.data.statement.context={contextActivities:{}}),this.data.statement.context.contextActivities.category=[{id:"http://h5p.org/libraries/"+e.libraryInfo.versionedNameNoSpaces,objectType:"Activity"}])},H5P.XAPIEvent.prototype.setActor=function(){if(void 0!==H5PIntegration.user)this.data.statement.actor={name:H5PIntegration.user.name,mbox:"mailto:"+H5PIntegration.user.mail,objectType:"Agent"};else{var t;try{localStorage.H5PUserUUID?t=localStorage.H5PUserUUID:(t=H5P.createUUID(),localStorage.H5PUserUUID=t)}catch(e){t="not-trackable-"+H5P.createUUID()}this.data.statement.actor={account:{name:t,homePage:H5PIntegration.siteUrl},objectType:"Agent"}}},H5P.XAPIEvent.prototype.getMaxScore=function(){return this.getVerifiedStatementValue(["result","score","max"])},H5P.XAPIEvent.prototype.getScore=function(){return this.getVerifiedStatementValue(["result","score","raw"])},H5P.XAPIEvent.prototype.getContentXAPIId=function(e){var t;return e.contentId&&H5PIntegration&&H5PIntegration.contents&&H5PIntegration.contents["cid-"+e.contentId]&&(t=H5PIntegration.contents["cid-"+e.contentId].url,e.subContentId&&(t+="?subContentId="+e.subContentId)),t},H5P.XAPIEvent.prototype.isFromChild=function(){var e=this.getVerifiedStatementValue(["context","contextActivities","parent",0,"id"]);return!e||-1===e.indexOf("subContentId")},H5P.XAPIEvent.prototype.getVerifiedStatementValue=function(e){for(var t=this.data.statement,n=0;n<e.length;n++){if(void 0===t[e[n]])return null;t=t[e[n]]}return t},H5P.XAPIEvent.allowedXAPIVerbs=["answered","asked","attempted","attended","commented","completed","exited","experienced","failed","imported","initialized","interacted","launched","mastered","passed","preferred","progressed","registered","responded","resumed","scored","shared","suspended","terminated","voided","downloaded","copied","accessed-reuse","accessed-embed","accessed-copyright"],(H5P=window.H5P=window.H5P||{}).externalDispatcher=new H5P.EventDispatcher,H5P.EventDispatcher.prototype.triggerXAPI=function(e,t){this.trigger(this.createXAPIEventTemplate(e,t))},H5P.EventDispatcher.prototype.createXAPIEventTemplate=function(e,t){var n=new H5P.XAPIEvent;if(n.setActor(),n.setVerb(e),void 0!==t)for(var i in t)n.data.statement[i]=t[i];return"object"in n.data.statement||n.setObject(this),"context"in n.data.statement||n.setContext(this),n},H5P.EventDispatcher.prototype.triggerXAPICompleted=function(e,t,n){this.triggerXAPIScored(e,t,"completed",!0,n)},H5P.EventDispatcher.prototype.triggerXAPIScored=function(e,t,n,i,o){n=this.createXAPIEventTemplate(n);n.setScoredResult(e,t,this,i,o),this.trigger(n)},H5P.EventDispatcher.prototype.setActivityStarted=function(){void 0===this.activityStartTime&&(void 0!==this.contentId&&void 0!==H5PIntegration.contents&&void 0!==H5PIntegration.contents["cid-"+this.contentId]&&this.triggerXAPI("attempted"),this.activityStartTime=Date.now())},H5P.xAPICompletedListener=function(e){var t,n;"completed"!==e.getVerb()&&"answered"!==e.getVerb()||e.getVerifiedStatementValue(["context","contextActivities","parent"])||(t=e.getScore(),n=e.getMaxScore(),e=e.getVerifiedStatementValue(["object","definition","extensions","http://h5p.org/x-api/h5p-local-content-id"]),H5P.setFinished(e,t,n))},/*!@license js/h5p-content-type.js by Joubel and other contributors, licensed under GNU GENERAL PUBLIC LICENSE Version 3*/
H5P.ContentType=function(e){function t(){}return(t.prototype=new H5P.EventDispatcher).isRoot=function(){return e},t.prototype.getLibraryFilePath=function(e){return H5P.getLibraryPath(this.libraryInfo.versionedNameNoSpaces)+"/"+e},t},/*!@license js/h5p-confirmation-dialog.js by Joubel and other contributors, licensed under GNU GENERAL PUBLIC LICENSE Version 3*/
H5P.ConfirmationDialog=function(w){"use strict";function e(n){w.call(this);var i=this;H5P.ConfirmationDialog.uniqueId+=1;var e=H5P.ConfirmationDialog.uniqueId;function t(e){i.hide(),i.trigger("confirmed"),e.preventDefault()}function o(e){i.hide(),i.trigger("canceled"),e.preventDefault()}function r(e,t){e.focus(),t.preventDefault()}(n=n||{}).headerText=n.headerText||H5P.t("confirmDialogHeader"),n.dialogText=n.dialogText||H5P.t("confirmDialogBody"),n.cancelText=n.cancelText||H5P.t("cancelLabel"),n.confirmText=n.confirmText||H5P.t("confirmLabel");var a=32,s=8,c=!1,u=document.createElement("div");u.classList.add("h5p-confirmation-dialog-background","hidden","hiding");var l=document.createElement("div");l.classList.add("h5p-confirmation-dialog-popup","hidden"),n.classes&&n.classes.forEach(function(e){l.classList.add(e)}),l.setAttribute("role","dialog"),l.setAttribute("aria-labelledby","h5p-confirmation-dialog-dialog-text-"+e),u.appendChild(l),l.addEventListener("keydown",function(e){27===e.which&&o(e)});var d=document.createElement("div");d.classList.add("h5p-confirmation-dialog-header"),l.appendChild(d);var p=document.createElement("div");p.classList.add("h5p-confirmation-dialog-header-text"),p.innerHTML=n.headerText,d.appendChild(p);d=document.createElement("div");d.classList.add("h5p-confirmation-dialog-body"),l.appendChild(d);p=document.createElement("div");p.classList.add("h5p-confirmation-dialog-text"),p.innerHTML=n.dialogText,p.id="h5p-confirmation-dialog-dialog-text-"+e,d.appendChild(p);p=document.createElement("div");p.classList.add("h5p-confirmation-dialog-buttons"),d.appendChild(p);var f=document.createElement("button");f.classList.add("h5p-core-cancel-button"),f.textContent=n.cancelText;var h=document.createElement("button");h.classList.add("h5p-core-button"),h.classList.add("h5p-confirmation-dialog-confirm-button"),h.textContent=n.confirmText;var g,m,d=document.createElement("button");d.classList.add("h5p-confirmation-dialog-exit"),d.setAttribute("aria-hidden","true"),d.tabIndex=-1,d.title=n.cancelText,f.addEventListener("click",o),f.addEventListener("keydown",function(e){32===e.which?o(e):9===e.which&&e.shiftKey&&r(h,e)}),n.hideCancel?p.classList.add("center"):p.appendChild(f),h.addEventListener("click",t),h.addEventListener("keydown",function(e){32===e.which?t(e):9!==e.which||e.shiftKey||r(n.hideCancel?h:f,e)}),p.appendChild(h),d.addEventListener("click",o),d.addEventListener("keydown",function(e){32===e.which&&o(e)}),n.hideExit||l.appendChild(d);var v,y=[],b=[];this.appendTo=function(e){return g=e,this};var P=function(e){u.contains(e.target)||(e.preventDefault(),h.focus())},x=function(e){for(var t=[],n=e.parentNode.children,i=0;i<n.length;i+=1)t[i]=!!n[i].getAttribute("aria-hidden"),n[i]!==e&&n[i].setAttribute("aria-hidden",!0);return t},H=function(e,t){for(var n=e.parentNode.children,i=0;i<n.length;i+=1)n[i]===e||t[i]||n[i].removeAttribute("aria-hidden")};this.show=function(e){var t;return v=document.activeElement,g.appendChild(u),(m=g.parentNode||g).addEventListener("focus",P,!0),y=x(g),b=x(u),u.classList.remove("hidden"),t=e,e=parseInt(l.style.top,10),(e=(e=(e=void 0!==t?t:e)||0)+l.offsetHeight>g.offsetHeight?g.offsetHeight-l.offsetHeight-s:e)-a<=0&&(e=a+s,c=!0),l.style.top=e+"px",setTimeout(function(){l.classList.remove("hidden"),u.classList.remove("hiding"),setTimeout(function(){var e;h.focus(),c&&n.instance&&(e=parseInt(l.offsetHeight,10)+a+2*s,i.setViewPortMinimumHeight(e),n.instance.trigger("resize"),c=!1)},100)},0),this},this.hide=function(){return u.classList.add("hiding"),l.classList.add("hidden"),m.removeAttribute("aria-hidden"),m.removeEventListener("focus",P,!0),n.skipRestoreFocus||v.focus(),H(g,y),H(u,b),setTimeout(function(){u.classList.add("hidden"),g.removeChild(u),i.setViewPortMinimumHeight(null)},100),this},this.getElement=function(){return l},this.getPreviouslyFocused=function(){return v},this.setViewPortMinimumHeight=function(e){(document.querySelector(".h5p-container")||document.body).style.minHeight="number"==typeof e?e+"px":e}}return(e.prototype=Object.create(w.prototype)).constructor=e}(H5P.EventDispatcher),H5P.ConfirmationDialog.uniqueId=-1,/*!@license js/h5p-action-bar.js by Joubel and other contributors, licensed under GNU GENERAL PUBLIC LICENSE Version 3*/
H5P.ActionBar=function(n){"use strict";function e(e){n.call(this);function t(e,t){function n(){i.trigger(e)}H5P.jQuery("<li/>",{class:"h5p-button h5p-noselect h5p-"+(t||e),role:"button",tabindex:0,title:H5P.t(e+"Description"),html:H5P.t(e),on:{click:n,keypress:function(e){32===e.which&&(n(),e.preventDefault())}},appendTo:r}),o=!0}var i=this,o=!1,r=H5P.jQuery('<ul class="h5p-actions"></ul>');(e.export||e.copy)&&t("reuse","export"),e.copyright&&t("copyrights"),e.embed&&t("embed"),e.icon&&(H5P.jQuery('<li><a class="h5p-link" href="http://h5p.org" target="_blank" title="'+H5P.t("h5pDescription")+'"></a></li>').appendTo(r),o=!0),i.getDOMElement=function(){return r},i.hasActions=function(){return o}}return(e.prototype=Object.create(n.prototype)).constructor=e}((H5P.jQuery,H5P.EventDispatcher)),/*!@license js/request-queue.js by Joubel and other contributors, licensed under GNU GENERAL PUBLIC LICENSE Version 3*/
H5P.RequestQueue=function(n,t){function e(e){t.call(this),this.processingQueue=!1,this.showToast=(e=e||{}).showToast,this.itemName="requestQueue"}return e.prototype.add=function(e,t){if(!window.localStorage)return!1;let n=this.getStoredRequests();return n=n||[],n.push({url:e,data:t}),window.localStorage.setItem(this.itemName,JSON.stringify(n)),this.trigger("requestQueued",{storedStatements:n,processingQueue:this.processingQueue}),!0},e.prototype.getStoredRequests=function(){if(!window.localStorage)return!1;var e=window.localStorage.getItem(this.itemName);return e?JSON.parse(e):[]},e.prototype.clearQueue=function(){return!!window.localStorage&&(window.localStorage.removeItem(this.itemName),!0)},e.prototype.resumeQueue=function(){if(!H5PIntegration||!window.navigator||!window.localStorage)return!1;if(this.processingQueue)return!1;var e=this.getStoredRequests(),t=e.length;return this.clearQueue(),t?(this.processingQueue=!0,this.processQueue(e)):this.trigger("emptiedQueue",e),!0},e.prototype.processQueue=function(e){var t;e.length&&(this.trigger("processingQueue"),t=e.shift(),n.post(t.url,t.data).fail(this.onQueuedRequestFail.bind(this,t)).always(this.onQueuedRequestProcessed.bind(this,e)))},e.prototype.onQueuedRequestFail=function(e){window.navigator.onLine||this.add(e.url,e.data)},e.prototype.onQueuedRequestProcessed=function(e){e.length?this.processQueue(e):(this.processingQueue=!1,e=this.getStoredRequests(),this.trigger("queueEmptied",e))},e.prototype.displayToastMessage=function(e,t,n){(this.showToast||t)&&(n=H5P.jQuery.extend(!0,{},{position:{horizontal:"centered",vertical:"centered",noOverflowX:!0}},n),H5P.attachToastTo(H5P.jQuery(".h5p-content:first")[0],e,n))},e}(H5P.jQuery,H5P.EventDispatcher),H5P.OfflineRequestQueue=function(P,x){return function(e){const n=new P;n.clearQueue();let i=null;const o=[10,20,40,60,120,300,600];let r=-1,t=null,a=!1,s=!1,c=!1;e=e.instance;const u=new x({headerText:H5P.t("offlineDialogHeader"),dialogText:H5P.t("offlineDialogBody"),confirmText:H5P.t("offlineDialogRetryButtonLabel"),hideCancel:!0,hideExit:!0,classes:["offline"],instance:e,skipRestoreFocus:!0}),l=u.getElement(),d=document.createElement("div");d.classList.add("count-down"),d.innerHTML=H5P.t("offlineDialogRetryMessage").replace(":num",'<span class="count-down-num">0</span>'),l.querySelector(".h5p-confirmation-dialog-text").appendChild(d);const p=d.querySelector(".count-down-num"),f=document.createElement("div");f.classList.add("throbber-wrapper");const h=document.createElement("div");h.classList.add("sending-requests-throbber"),f.appendChild(h),n.on("requestQueued",function(e){if(!e.data||!e.data.processingQueue){if(!a){const t=document.body.querySelector(".h5p-content");if(!t)return;u.appendTo(t),t.appendChild(f),a=!0}y()}}.bind(this)),n.on("queueEmptied",function(e){e.data&&e.data.length?y(!0):(clearInterval(t),g(!1),r=-1,s&&(u.hide(),s=!1),n.displayToastMessage(H5P.t("offlineSuccessfulSubmit"),!0,{position:{vertical:"top",offsetVertical:"100"}}))}.bind(this)),u.on("confirmed",function(){s=!1,setTimeout(function(){m()},100)}.bind(this)),window.addEventListener("online",function(){m()}.bind(this)),window.addEventListener("message",function(e){window.parent===e.source&&"h5p"===e.data.context&&"queueRequest"===e.data.action&&this.add(e.data.url,e.data.data)}.bind(this));const g=function(e){c=!c,void 0!==e&&(c=e),c&&s&&(u.hide(),s=!1),c?f.classList.add("show"):f.classList.remove("show")},m=function(){clearInterval(t),g(!0),n.resumeQueue()},v=function(){r+=1,r>=o.length&&(r=o.length-1)},y=function(e){s||(g(!1),s||(e?setTimeout(function(){u.show(0)},100):u.show(0)),s=!0,i=(new Date).getTime(),v(),clearInterval(t),t=setInterval(b,100))},b=function(){var e=(new Date).getTime(),e=Math.floor((e-i)/1e3);const t=o[r]-e;p.textContent=t.toString(),t<=0&&m()};this.add=function(e,t){if(window.navigator.onLine)return!1;n.add(e,t)}}}(H5P.RequestQueue,H5P.ConfirmationDialog),(H5P=H5P||{}).Text=function(e){this.text=void 0===e.text?"<em>New text</em>":e.text},H5P.Text.prototype.attach=function(e){e.addClass("h5p-text").html(this.text)},(H5P=H5P||{}).Transition=function(){Transition={transitionEndEventNames:{WebkitTransition:"webkitTransitionEnd",transition:"transitionend",MozTransition:"transitionend",OTransition:"oTransitionEnd",msTransition:"MSTransitionEnd"},cache:[],getVendorPropertyName:function(e){if(void 0!==Transition.cache[e])return Transition.cache[e];var t=document.createElement("div");if(e in t.style)Transition.cache[e]=e;else{var n=["Moz","Webkit","O","ms"],i=e.charAt(0).toUpperCase()+e.substr(1);if(e in t.style)Transition.cache[e]=e;else for(var o=0;o<n.length;++o){var r=n[o]+i;if(r in t.style){Transition.cache[e]=r;break}}}return Transition.cache[e]},getTransitionEndEventName:function(){return Transition.transitionEndEventNames[Transition.getVendorPropertyName("transition")]||void 0},onTransitionEnd:function(e,t,n){n=n||1e3,Transition.transitionEndEventName=Transition.transitionEndEventName||Transition.getTransitionEndEventName();function i(){o||(e.off(Transition.transitionEndEventName,t),o=!0,clearTimeout(r),t())}var o=!1,r=setTimeout(function(){i()},n);e.on(Transition.transitionEndEventName,function(){i()})}};function i(e,t){var n;t>=e.length||(n=e[t],H5P.Transition.onTransitionEnd(n.$element,function(){n.end&&n.end(),!0!==n.break&&i(e,t+1)},n.timeout||void 0))}return Transition.sequence=function(e){i(e,0)},Transition}(H5P.jQuery),/*!@license H5P.ImageHotspots-1.10/scripts/image-hotspots.js by Joubel licensed under MIT*/
H5P.ImageHotspots=function(s,n){function c(e,t){n.call(this),this.options=s.extend(!0,{},{image:null,hotspots:[],hotspotNumberLabel:"Hotspot #num",closeButtonLabel:"Close",iconType:"icon",icon:"plus",disableScaling:!0},e),this.id=t,this.isSmallDevice=!1,this.massageAttributeOutput=function(e){e=(new DOMParser).parseFromString(e,"text/html");const t=document.createElement("div");return t.innerHTML=e.documentElement.textContent,t.textContent||t.innerText||""}}return((c.prototype=Object.create(n.prototype)).constructor=c).prototype.attach=function(e){var t=this;if(t.$container=e,null!==this.options.image&&void 0!==this.options.image){!1===/(iPad|iPhone|iPod)/g.test(navigator.userAgent)&&e.addClass("not-an-ios-device"),e.addClass("h5p-image-hotspots"),this.$hotspotContainer=s("<div/>",{class:"h5p-image-hotspots-container"}),this.options.image&&this.options.image.path&&(this.$image=s("<img/>",{class:"h5p-image-hotspots-background",src:H5P.getPath(this.options.image.path,this.id)}).appendTo(this.$hotspotContainer),this.options.backgroundImageAltText?this.$image.attr("alt",this.massageAttributeOutput(this.options.backgroundImageAltText)):this.$image.attr("aria-hidden",!0));function n(){return t.isSmallDevice}var i=this.options.hotspots.length;this.hotspots=[],this.options.hotspots.sort(function(e,t){var n=e.position&&e.position.x&&e.position.y,i=t.position&&t.position.x&&t.position.y;return n?i?e.position.y!==t.position.y?e.position.y<t.position.y?-1:1:e.position.x<t.position.x?-1:1:-1:1});for(var o=0;o<i;o++)try{var r=new c.Hotspot(this.options.hotspots[o],this.options,this.id,n,t);r.appendTo(this.$hotspotContainer);var a=this.options.hotspots[o].header||this.options.hotspotNumberLabel.replace("#num",(o+1).toString());r.setTitle(a),this.hotspots.push(r)}catch(e){H5P.error(e)}this.$hotspotContainer.appendTo(e),this.on("resize",t.resize,t),this.on("enterFullScreen",function(){t.fullscreenButton.tabIndex=-1,setTimeout(function(){t.trigger("resize"),t.toggleTrapFocus(!0)})}),this.on("exitFullScreen",function(){t.fullscreenButton.tabIndex=0,t.trigger("resize",{forceImageHeight:!0}),t.toggleTrapFocus(!1)}),t.resize()}else e.append('<div class="background-image-missing">Missing required background image</div>')},c.prototype.setShowingPopup=function(e){this.$container.toggleClass("showing-popup",e)},c.prototype.toggleTrapFocus=function(e){this.hotspots.length<1||(e?(this.hotspots[0].focus(),1<this.hotspots.length&&(this.hotspots[this.hotspots.length-1].setTrapFocusTo(this.hotspots[0]),this.hotspots[0].setTrapFocusTo(this.hotspots[this.hotspots.length-1],!0))):(this.hotspots[this.hotspots.length-1].releaseTrapFocus(),this.hotspots[0].releaseTrapFocus()))},c.prototype.resize=function(e){var t,n,i,o,r,a;null!==this.options.image&&((t=this).fullscreenButton=document.querySelector(".h5p-enable-fullscreen"),n=t.$container.width(),i=t.$container.height(),a=n,o=Math.floor(a/t.options.image.width*t.options.image.height),r=e&&e.data&&e.data.forceImageHeight,(e=e&&e.data&&e.data.decreaseSize)||t.$container.css("width",""),this.isRoot()&&H5P.isFullscreen&&(!r&&i<o&&(o=i,a=Math.floor(o/t.options.image.height*t.options.image.width)),t.$container.is(".h5p-semi-fullscreen")&&(t.$container.css("width",""),e||(t.$hotspotContainer.css("width","10px"),t.$image.css("width","10px"),setTimeout(function(){t.trigger("resize",{decreaseSize:!0})},200)),(e=s(window.frameElement))&&(a=e.parent().width(),t.$container.css("width",a+"px")))),t.$image.css({width:a+"px",height:o+"px"}),t.initialWidth||(t.initialWidth=t.$container.width()),t.fontSize=this.options.disableScaling?24:Math.max(24,a/t.initialWidth*24),t.$hotspotContainer.css({width:a+"px",height:o+"px",fontSize:t.fontSize+"px"}),t.isSmallDevice=n/parseFloat(s("body").css("font-size"))<40)},c.prototype.pause=function(){this.hotspots.forEach(function(e){e.pause&&e.pause()})},c}(H5P.jQuery,H5P.EventDispatcher),/*!@license H5P.ImageHotspots-1.10/scripts/hotspot.js by Joubel licensed under MIT*/
function(s,c){c.Hotspot=function(e,t,n,i,o){var r=this;this.config=e,this.visible=!1,this.id=n,this.isSmallDeviceCB=i,this.options=t,this.parent=o;i=void 0!==t.iconImage&&"image"===t.iconType;if(void 0===this.config.content||0===this.config.content.length)throw new Error("Missing content configuration for hotspot. Please fix in editor.");this.$element=s(i?"<img/>":"<button/>",{class:"h5p-image-hotspot "+(i?"":"h5p-image-hotspot-"+t.icon)+(e.position.legacyPositioning?" legacy-positioning":""),role:"button",tabindex:0,"aria-haspopup":!0,src:i?H5P.getPath(t.iconImage.path,this.id):void 0,click:function(){return r.loadingPopup||(r.visible?r.hidePopup():r.showPopup(!0)),!1},keydown:function(e){if(32===e.which||13===e.which)return r.loadingPopup||(r.visible?r.hidePopup():r.showPopup(!0),e.stopPropagation()),!1}}),this.$element.css({top:this.config.position.y+"%",left:this.config.position.x+"%",color:t.color,backgroundColor:t.backgroundColor||""}),o.on("resize",function(){r.popup&&r.actionInstances.forEach(function(e){void 0!==e.trigger&&setTimeout(function(){e.trigger("resize")},1)})})},c.Hotspot.prototype.appendTo=function(e){this.$container=e,this.$element.appendTo(e)},c.Hotspot.prototype.showPopup=function(e){var n=this,i=s("<div/>",{class:"h5p-image-hotspot-popup-body"});n.loadingPopup=!0,this.parent.setShowingPopup(!0),this.actionInstances=[];var o=[];this.config.content.forEach(function(e){var t=s("<div>",{class:"h5p-image-hotspot-popup-body-fraction",appendTo:i});"H5P.Audio"===e.library.split(" ")[0]&&"transparent"===e.params.playerMode&&(e.params.autoplay=!0);e=H5P.newRunnable(e,n.id);n.actionInstances.push(e),"H5P.Image"!==e.libraryInfo.machineName&&"H5P.Video"!==e.libraryInfo.machineName||o.push(e),e.attach(t),"H5P.Audio"===e.libraryInfo.machineName&&(e.audio&&"full"===e.params.playerMode&&window.chrome?e.audio.style.height="54px":e.$audioButton&&"transparent"===e.params.playerMode&&e.$audioButton.css({height:0,padding:0})),n.parent.fullscreenButton&&(n.parent.fullscreenButton.tabIndex=-1)});function r(){n.toggleHotspotsTabindex(!0),n.visible=!0,n.popup.show(e),n.$element.addClass("active"),n.actionInstances.forEach(function(e){e.trigger("resize")})}var a,t="h5p-video";o.length?1===n.actionInstances.length&&"H5P.Image"===n.actionInstances[0].libraryInfo.machineName&&(t="h5p-image"):t="h5p-text",n.popup=new c.Popup(n.$container,i,n.config.position.x,n.config.position.y,n.$element.outerWidth(),n.config.header,t,n.config.alwaysFullscreen||n.isSmallDeviceCB(),n.options,n.config.position.legacyPositioning),n.parent.on("resize",function(){n.visible&&n.popup.resize()}),n.popup.on("closed",function(e){n.hidePopup(),e.data&&e.data.refocus&&n.focus()}),n.popup.on("finishedLoading",function(){n.loadingPopup=!1}),o.length?(a=0,o.forEach(function(e){function t(){clearTimeout(n),e.off("loaded",t),(a+=1)>=o.length&&setTimeout(function(){r()},100)}var n=setTimeout(t,1e3);e.on("loaded",t,{unloaded:e,timeout:n}),e.trigger("resize")})):setTimeout(function(){r()},100),s("body").children().on("click.h5p-image-hotspot-popup",function(e){var t=s(e.target);!n.visible||t.hasClass("h5p-enable-fullscreen")||t.hasClass("h5p-disable-fullscreen")||"h5p-image-hotspots-overlay"!==e.target.id||n.hidePopup()})},c.Hotspot.prototype.toggleHotspotsTabindex=function(e){this.$container.find(".h5p-image-hotspot").attr("tabindex",e?"-1":"0").attr("aria-hidden",!!e||"")},c.Hotspot.prototype.hidePopup=function(){this.popup&&(s("body").children().off("click.h5p-image-hotspot-popup"),this.pause(),this.popup.hide(),this.$element.removeClass("active"),this.visible=!1,this.popup=void 0,this.toggleHotspotsTabindex()),this.parent.setShowingPopup(!1),this.parent.fullscreenButton&&(this.parent.fullscreenButton.tabIndex=0)},c.Hotspot.prototype.focus=function(){this.$element.focus()},c.Hotspot.prototype.setTrapFocusTo=function(t,n){this.$element.on("keydown.trapfocus",function(e){if(9===e.which&&(n?e.shiftKey:!e.shiftKey))return t.focus(),e.stopPropagation(),!1})},c.Hotspot.prototype.releaseTrapFocus=function(){this.$element.off("keydown.trapfocus")},c.Hotspot.prototype.setTitle=function(e){this.$element.attr("title",e),this.$element.attr("aria-label",e)},c.Hotspot.prototype.pause=function(){this.actionInstances&&this.actionInstances.forEach(function(e){e.audio&&(e.audio.pause instanceof Function||"function"==typeof e.audio.pause)&&e.audio.pause()})}}(H5P.jQuery,H5P.ImageHotspots),/*!@license H5P.ImageHotspots-1.10/scripts/popup.js by Joubel licensed under MIT*/
function(g,e,m){e.Popup=function(e,t,n,o,i,r,a,s,c,u){m.call(this);var l=this;this.$container=e;var d,e=this.$container.width();this.$container.height();i=i/e*100;var p=0,f=0,h=!1;s?(f=100,a+=" fullscreen-popup"):(p=(h=50<n)?0:n+i+1.55,f=h?n-i-1.55:100-p),this.$popupBackground=g("<div/>",{class:"h5p-image-hotspots-overlay",id:"h5p-image-hotspots-overlay"}),this.$popup=g("<div/>",{class:"h5p-image-hotspot-popup "+a,tabindex:"0",role:"dialog","aria-modal":"true","aria-labelledby":r?"h5p-image-hotspot-popup-header":void 0}).css({left:(h?"":"-")+"100%",width:f+"%"}).appendTo(this.$popupBackground),this.$popupContent=g("<div/>",{class:"h5p-image-hotspot-popup-content",on:{scroll:function(){g(this).addClass("has-scrolled")}}}),r&&(this.$popupHeader=g("<div/>",{class:"h5p-image-hotspot-popup-header",id:"h5p-image-hotspot-popup-header",html:r,tabindex:"-1","aria-hidden":"true"}),this.$popupContent.append(this.$popupHeader),this.$popup.addClass("h5p-image-hotspot-has-header")),t.appendTo(this.$popupContent),this.$popupContent.appendTo(this.$popup),this.$closeButton=g("<button>",{class:"h5p-image-hotspot-close-popup-button","aria-label":c.closeButtonLabel,title:c.closeButtonLabel}).click(function(){l.trigger("closed")}).keydown(function(e){if(32===e.which||13===e.which)return l.trigger("closed",{refocus:!0}),!1}).appendTo(this.$popup),r||l.$popupContent.addClass("h5p-image-hotspot-popup-content-no-header"),s||(this.$pointer=g("<div/>",{class:"h5p-image-hotspot-popup-pointer to-the-"+(h?"left":"right")+(u?" legacy-positioning":"")}).css({top:o+"%"}).appendTo(this.$popupBackground)),this.$popupBackground.appendTo(this.$container),l.resize=function(){var e,t,n,i;s||(l.$popup.css({maxHeight:"",height:""}),l.$popupContent.css({height:""}),d=this.$container.height(),e=l.$popupContent.outerHeight(),t=l.$popup.outerHeight(),(n=e<d)&&(l.$popup.css({maxHeight:"auto",height:"auto"}),i=Math.max(0,o/100*t-e/2),l.$popup.css({top:(i=t<i+e?t-e:i)/t*100+"%"})),l.$popupContent.css({height:n?"":"100%",overflow:n?"":"auto"}).toggleClass("overflowing",!n),l.$popup.toggleClass("popup-overflowing",!n))},l.show=function(e){s||(l.resize(),l.$pointer.css({left:h?f+"%":p+"%"})),l.$popup.css({left:p+"%"}),l.$popupBackground.addClass("visible"),l.$popup.focus(),H5P.Transition.onTransitionEnd(l.$popup,function(){e&&(l.$popupHeader||l.$closeButton).focus(),l.$pointer&&l.$pointer.addClass("visible"),l.trigger("finishedLoading")},300)},l.hide=function(){l.$popupBackground.remove()}},e.Popup.prototype=Object.create(m.prototype),e.Popup.prototype.constructor=e.Popup}(H5P.jQuery,H5P.ImageHotspots,H5P.EventDispatcher);const realH5PGetPath=H5P.getPath;H5P.getPath=function(e,t){return e.startsWith("data:")?e:realH5PGetPath(e,t)}; var furtherH5PInlineResources={"H5P.ImageHotspots-1.10/close.svg":"data:image/svg+xml;base64,PHN2ZyB2ZXJzaW9uPSIxLjIiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIG92ZXJmbG93PSJ2aXNpYmxlIiBwcmVzZXJ2ZUFzcGVjdFJhdGlvPSJub25lIiB2aWV3Qm94PSIwIDAgMjQgMjQiIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMyI+PGc+PHBhdGggeG1sbnM6ZGVmYXVsdD0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGQ9Ik0xOSA2LjQxTDE3LjU5IDUgMTIgMTAuNTkgNi40MSA1IDUgNi40MSAxMC41OSAxMiA1IDE3LjU5IDYuNDEgMTkgMTIgMTMuNDEgMTcuNTkgMTkgMTkgMTcuNTkgMTMuNDEgMTJ6IiBzdHlsZT0iZmlsbDogcmdiKDAsIDAsIDApOyIgdmVjdG9yLWVmZmVjdD0ibm9uLXNjYWxpbmctc3Ryb2tlIi8+PC9nPjwvc3ZnPg=="};!function(){H5P.ContentType=function(t){function e(){}return(e.prototype=new H5P.EventDispatcher).isRoot=function(){return t},e.prototype.getLibraryFilePath=function(t){return furtherH5PInlineResources[this.libraryInfo.versionedNameNoSpaces+"/"+t]||t},e};var i=HTMLScriptElement.prototype.setAttribute;HTMLScriptElement.prototype.setAttribute=function(t,e){if("src"===t){if(!(e instanceof String)){if(!e.toString)return void i.call(this,t,e);e=e.toString()}var r=window.location.href.substr(0,window.location.href.lastIndexOf("/")),r=(e=e.startsWith(r)?e.substr(r.length+1):e).match(/^.\/libraries\/([^?]+)\??.*$/);r&&(r=r[1],furtherH5PInlineResources[r]&&(e=furtherH5PInlineResources[r]))}i.call(this,t,e)},Object.defineProperty(HTMLScriptElement.prototype,"src",{set(t){this.setAttribute("src",t)}});var n=HTMLLinkElement.prototype.setAttribute;HTMLLinkElement.prototype.setAttribute=function(t,e){if("href"===t){if(!(e instanceof String)){if(!e.toString)return void i.call(this,t,e);e=e.toString()}var r=window.location.href.substr(0,window.location.href.lastIndexOf("/")),r=(e=e.startsWith(r)?e.substr(r.length+1):e).match(/^.\/libraries\/([^?]+)\??.*$/);r&&(r=r[1],furtherH5PInlineResources[r]&&(e=furtherH5PInlineResources[r]))}n.call(this,t,e)},Object.defineProperty(HTMLLinkElement.prototype,"src",{set(t){this.setAttribute("src",t)}})}();H5P.getPath=function(t,n){return t};</script>
        <style>/*!@license styles/h5p.css by Joubel and other contributors, licensed under GNU GENERAL PUBLIC LICENSE Version 3*/@font-face{font-family:h5p;src:url(data:font/woff;base64,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) format('woff');font-weight:400;font-style:normal}@font-face{font-family:h5p-hub-publish;src:url(data:font/woff;base64,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) format('woff');font-weight:400;font-style:normal;font-display:block}html.h5p-iframe,html.h5p-iframe>body{font-family:Sans-Serif;width:100%;height:100%;margin:0;padding:0}.h5p-fullscreen,.h5p-semi-fullscreen,html.h5p-iframe .h5p-container{overflow:hidden}.h5p-content{position:relative;background:#fefefe;border:1px solid #eee;border-bottom:none;box-sizing:border-box;-moz-box-sizing:border-box}.h5p-noselect{-khtml-user-select:none;-ms-user-select:none;-moz-user-select:none;-webkit-user-select:none;user-select:none}html.h5p-iframe .h5p-content{font-size:16px;line-height:1.5em;width:100%;height:auto}html.h5p-iframe .h5p-fullscreen .h5p-content,html.h5p-iframe .h5p-semi-fullscreen .h5p-content{height:100%}.h5p-content.h5p-no-frame,.h5p-fullscreen .h5p-content,.h5p-semi-fullscreen .h5p-content{border:0}.h5p-container{position:relative;z-index:1}.h5p-iframe-wrapper.h5p-fullscreen{background-color:#000}body.h5p-semi-fullscreen{position:fixed;width:100%;height:100%}.h5p-container.h5p-semi-fullscreen{position:fixed;top:0;left:0;z-index:101;width:100%;height:100%;background-color:#fff}.h5p-content-controls{margin:0;position:absolute;right:0;top:0;z-index:3}.h5p-fullscreen .h5p-content-controls{display:none}.h5p-content-controls>a:link,.h5p-content-controls>a:visited,a.h5p-disable-fullscreen:link,a.h5p-disable-fullscreen:visited{color:#e5eef6}.h5p-enable-fullscreen:before{font-family:H5P;content:"\e88c"}.h5p-disable-fullscreen:before{font-family:H5P;content:"\e891"}.h5p-disable-fullscreen,.h5p-enable-fullscreen{cursor:pointer;color:#eee;background:#000;background:rgba(0,0,0,.3);line-height:.975em;font-size:2em;width:1.125em;height:1em;text-indent:.04em}.h5p-disable-fullscreen{line-height:.925em;width:1.1em;height:.9em}.h5p-disable-fullscreen:focus,.h5p-enable-fullscreen:focus{outline-style:solid;outline-width:1px;outline-offset:.25em}.h5p-disable-fullscreen:hover,.h5p-enable-fullscreen:hover{background:rgba(0,0,0,.5)}.h5p-semi-fullscreen .h5p-enable-fullscreen{display:none}div.h5p-fullscreen{width:100%;height:100%}.h5p-iframe-wrapper{width:auto;height:auto}.h5p-fullscreen .h5p-iframe-wrapper,.h5p-semi-fullscreen .h5p-iframe-wrapper{width:100%;height:100%}.h5p-iframe-wrapper.h5p-semi-fullscreen{width:auto;height:auto;background:#000;position:fixed;top:0;left:0;right:0;bottom:0;z-index:100001}.h5p-iframe-wrapper.h5p-semi-fullscreen .buttons{position:absolute;top:0;right:0;z-index:20}.h5p-iframe-wrapper iframe.h5p-iframe{width:10px;min-width:100%;height:100%;z-index:10;overflow:hidden;border:0;display:block}.h5p-content ul.h5p-actions{box-sizing:border-box;-moz-box-sizing:border-box;overflow:hidden;list-style:none;padding:0 10px;margin:0;height:25px;font-size:12px;background:#fafafa;border-top:1px solid #eee;border-bottom:1px solid #eee;clear:both;font-family:Sans-Serif}.h5p-fullscreen .h5p-actions,.h5p-semi-fullscreen .h5p-actions{display:none}.h5p-actions>.h5p-button{float:left;cursor:pointer;margin:0 .5em 0 0;background:0 0;padding:0 .75em 0 .25em;vertical-align:top;color:#707070;text-decoration:none;outline:0;line-height:22px}.h5p-actions>.h5p-button:hover{color:#333}.h5p-actions .h5p-link:active,.h5p-actions .h5p-link:focus,.h5p-actions>.h5p-button:active,.h5p-actions>.h5p-button:focus{color:#666}.h5p-actions .h5p-link:focus,.h5p-actions>.h5p-button:focus{outline-style:solid;outline-width:thin;outline-offset:-2px;outline-color:#9ecaed}.h5p-actions>.h5p-button:before{font-family:H5P;font-size:20px;line-height:23px;vertical-align:bottom;padding-right:0}.h5p-actions>.h5p-button.h5p-export:before{content:"\e90b"}.h5p-actions>.h5p-button.h5p-copyrights:before{content:"\e88f"}.h5p-actions>.h5p-button.h5p-embed:before{content:"\e892"}.h5p-actions .h5p-link{float:right;margin-right:0;font-size:2em;line-height:23px;overflow:hidden;color:#999;text-decoration:none;outline:0}.h5p-actions .h5p-link:before{font-family:H5P;content:"\e88e";vertical-align:bottom}.h5p-actions>li{margin:0;list-style:none}.h5p-popup-dialog{position:absolute;top:0;left:0;width:100%;min-height:100%;z-index:100;padding:2em;box-sizing:border-box;-moz-box-sizing:border-box;opacity:0;-webkit-transition:opacity .2s;-moz-transition:opacity .2s;-o-transition:opacity .2s;transition:opacity .2s;background:#000;background:rgba(0,0,0,.75)}.h5p-popup-dialog.h5p-open{opacity:1}.h5p-popup-dialog .h5p-inner{box-sizing:border-box;-moz-box-sizing:border-box;background:#fff;height:100%;max-height:100%;position:relative}.h5p-popup-dialog .h5p-inner>h2{position:absolute;box-sizing:border-box;-moz-box-sizing:border-box;width:100%;margin:0;background:#eee;display:block;color:#656565;font-size:1.25em;padding:.325em .5em .25em;line-height:1.25em;border-bottom:1px solid #ccc;z-index:2}.h5p-popup-dialog .h5p-inner>h2>a{font-size:12px;margin-left:1em}.h5p-content-user-data-reset-dialog .h5p-inner,.h5p-embed-dialog .h5p-inner,.h5p-reuse-dialog .h5p-inner{min-width:316px;max-width:400px;left:50%;top:50%;transform:translateX(-50%)}.h5p-embed-dialog .h5p-embed-code-container,.h5p-embed-size{resize:none;outline:0;width:100%;padding:.375em .5em .25em;margin:0;overflow:hidden;border:1px solid #ccc;box-shadow:0 1px 2px 0 #d0d0d0 inset;font-size:.875em;letter-spacing:.065em;font-family:sans-serif;white-space:pre;line-height:1.5em;height:2.0714em;background:#f5f5f5;box-sizing:border-box;-moz-box-sizing:border-box}.h5p-embed-dialog .h5p-embed-code-container:focus{height:5em}.h5p-embed-size{width:3.5em;text-align:right;margin:.5em 0;line-height:2em}.h5p-popup-dialog .h5p-scroll-content{border-top:2.25em solid transparent;padding:1em;box-sizing:border-box;-moz-box-sizing:border-box;color:#555;z-index:1}.h5p-popup-dialog.h5p-open .h5p-scroll-content{overflow:auto;overflow-x:hidden;overflow-y:auto;height:100%}.h5p-popup-dialog .h5p-scroll-content::-webkit-scrollbar{width:8px}.h5p-popup-dialog .h5p-scroll-content::-webkit-scrollbar-track{background:#e0e0e0}.h5p-popup-dialog .h5p-scroll-content::-webkit-scrollbar-thumb{box-shadow:0 0 10px #000 inset;border-radius:4px}.h5p-popup-dialog .h5p-close{cursor:pointer;font-size:2em;position:absolute;right:0;top:0;width:1.125em;height:1.125em;line-height:1.125em;color:#656565;cursor:pointer;text-indent:-.065em;z-index:3}.h5p-popup-dialog .h5p-close:after{font-family:H5P;content:"\e894"}.h5p-popup-dialog .h5p-close:focus:after,.h5p-popup-dialog .h5p-close:hover:after{color:#454545}.h5p-popup-dialog .h5p-close:active:after{color:#252525}.h5p-poopup-dialog h2{margin:.25em 0 .5em}.h5p-popup-dialog h3{margin:.75em 0 .25em}.h5p-popup-dialog dl{margin:.25em 0 .75em}.h5p-popup-dialog dt{float:left;margin:0 .75em 0 0}.h5p-popup-dialog dt:after{content:':'}.h5p-popup-dialog dd{margin:0}.h5p-expander{cursor:pointer;font-size:1.125em;margin:.5em 0 0;display:inline-block}.h5p-expander:before{content:"+";width:1em;display:inline-block;font-weight:700}.h5p-expander.h5p-open:before{content:"-";text-indent:.125em}.h5p-expander:focus,.h5p-expander:hover{color:#303030}.h5p-expander:active{color:#202020}.h5p-expander-content{display:none}.h5p-expander-content p{margin:.5em 0}.h5p-content-copyrights{border-left:.25em solid #d0d0d0;margin-left:.25em;padding-left:.25em}.h5p-throbber{background:url(data:image/gif;base64,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) 10px center no-repeat;padding-left:38px;min-height:30px;line-height:30px}.h5p-dialog-ok-button{cursor:default;float:right;outline:0;border:2px solid #ccc;padding:.25em .75em .125em;background:#eee}.h5p-dialog-ok-button:focus,.h5p-dialog-ok-button:hover{background:#fafafa}.h5p-dialog-ok-button:active{background:#efe}.h5p-big-button{line-height:1.25;display:block;position:relative;cursor:pointer;width:100%;padding:1em 1em 1em 3.75em;text-align:left;border:1px solid #dedede;background:linear-gradient(#fff,#f1f1f2);border-radius:.25em}.h5p-big-button:before{font-family:h5p;content:"\e893";line-height:1;font-size:3em;color:#2747f7;position:absolute;left:.125em;top:.125em}.h5p-copy-button:before{content:"\e905"}.h5p-big-button:hover{border:1px solid #2747f7;background:#eff1fe}.h5p-big-button:active{border:1px solid #dedede;background:#dfe4fe}.h5p-button-title{color:#2747f7;font-size:15px;font-weight:700;margin-bottom:.5em}.h5p-button-description{color:#757575}.h5p-horizontal-line-text{border-top:1px solid #dadada;line-height:1;color:#474747;text-align:center;position:relative;margin:1.25em 0}.h5p-horizontal-line-text>span{background:#fff;padding:.5em;position:absolute;top:-1em;left:50%;transform:translateX(-50%)}.h5p-toast{font-size:.75em;background-color:rgba(0,0,0,.9);color:#fff;z-index:110;position:absolute;padding:0 .5em;line-height:2;border-radius:4px;white-space:nowrap;pointer-events:none;top:0;opacity:1;visibility:visible;transition:opacity 1s}.h5p-toast-disabled{opacity:0;visibility:hidden}.h5p-content code,.h5peditor code{color:#3d3d3d;background:#e0e0e0;border-radius:2px;padding:0 5px}.h5p-content pre>code,.h5peditor pre>code{background-color:#fafafa;padding:5px;display:block;line-height:normal;border:1px solid #c7c7c7;border-left-width:4px;max-width:100%;white-space:pre;overflow:auto}.h5peditor-semi-fullscreen{width:100%;height:100%;position:fixed;top:0;left:0;right:0;bottom:0;z-index:101}iframe.h5peditor-semi-fullscreen{background:#fff;z-index:100001}.h5p-content.using-mouse :not(textarea):focus{outline:0!important}.h5p-content-hub-button:before{font-family:h5p;margin-right:.5em;font-size:.7em;line-height:1}.h5p-content-hub-button.unpublish:before{content:"\e916"}.h5p-content-hub-button.sync:before,.h5p-content-hub-button.waiting:before{content:"\e917"}.h5p-content-hub-button.waiting:before{display:inline-block;animation:rotate 2s linear infinite}@keyframes rotate{to{transform:rotate(360deg)}}
/*!@license styles/h5p-confirmation-dialog.css by Joubel and other contributors, licensed under GNU GENERAL PUBLIC LICENSE Version 3*/.h5p-confirmation-dialog-background{position:fixed;height:100%;width:100%;left:0;top:0;background:rgba(44,44,44,.9);opacity:1;visibility:visible;-webkit-transition:opacity .1s,linear 0s,visibility 0s linear 0s;transition:opacity .1s linear 0s,visibility 0s linear 0s;z-index:201}.h5p-confirmation-dialog-background.hidden{display:none}.h5p-confirmation-dialog-background.hiding{opacity:0;visibility:hidden;-webkit-transition:opacity .1s,linear 0s,visibility 0s linear .1s;transition:opacity .1s linear 0s,visibility 0s linear .1s}.h5p-confirmation-dialog-popup:focus{outline:0}.h5p-confirmation-dialog-popup{position:absolute;display:flex;flex-direction:column;justify-content:center;box-sizing:border-box;max-width:35em;min-width:25em;top:2em;left:50%;-webkit-transform:translate(-50%,0);-ms-transform:translate(-50%,0);transform:translate(-50%,0);color:#555;box-shadow:0 0 6px 6px rgba(10,10,10,.3);-webkit-transition:transform .1s ease-in;transition:transform .1s ease-in}.h5p-confirmation-dialog-popup.hidden{-webkit-transform:translate(-50%,50%);-ms-transform:translate(-50%,50%);transform:translate(-50%,50%)}.h5p-confirmation-dialog-header{padding:1.5em;background:#fff;color:#356593}.h5p-confirmation-dialog-header-text{font-size:1.25em}.h5p-confirmation-dialog-body{background:#fafbfc;border-top:solid 1px #dde0e9;padding:1.25em 1.5em}.h5p-confirmation-dialog-text{margin-bottom:1.5em}.h5p-confirmation-dialog-buttons{float:right}button.h5p-confirmation-dialog-exit,button.h5p-confirmation-dialog-exit:link,button.h5p-confirmation-dialog-exit:visited{position:absolute;background:0 0;border:none;font-size:2.5em;top:-.9em;right:-1.15em;color:#fff;cursor:pointer;text-decoration:none}button.h5p-confirmation-dialog-exit:focus,button.h5p-confirmation-dialog-exit:hover{color:#e4ecf5}.h5p-confirmation-dialog-exit:before{font-family:H5P;content:"\e890"}.h5p-core-button.h5p-confirmation-dialog-confirm-button{padding-left:.75em;margin-bottom:0}.h5p-core-button.h5p-confirmation-dialog-confirm-button:before{content:"\e601";margin-top:-6px;display:inline-block}.h5p-confirmation-dialog-popup.offline .h5p-confirmation-dialog-buttons{float:none;text-align:center}.h5p-confirmation-dialog-popup.offline .count-down{font-family:Arial;margin-top:.15em;color:#000}.h5p-confirmation-dialog-popup.offline .h5p-confirmation-dialog-confirm-button:before{content:"\e90b";font-weight:400;vertical-align:text-bottom}.throbber-wrapper{display:none;position:absolute;height:100%;width:100%;top:0;left:0;z-index:1;background:rgba(44,44,44,.9)}.throbber-wrapper.show{display:block}.throbber-wrapper .throbber-container{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%)}.throbber-wrapper .sending-requests-throbber{position:absolute;top:7em;left:50%;transform:translateX(-50%)}.throbber-wrapper .sending-requests-throbber:before{display:block;font-family:H5P;content:"\e90b";color:#fff;font-size:10em;animation:request-throbber 1.5s infinite linear}@keyframes request-throbber{from{transform:rotate(0)}to{transform:rotate(359deg)}}
/*!@license styles/h5p-core-button.css by Joubel and other contributors, licensed under GNU GENERAL PUBLIC LICENSE Version 3*/button.h5p-core-button,button.h5p-core-button:link,button.h5p-core-button:visited{font-family:"Open Sans",sans-serif;font-weight:600;font-size:1em;line-height:1.2;padding:.5em 1.25em;border-radius:2em;background:#2579c6;color:#fff;cursor:pointer;border:none;box-shadow:none;outline:0;display:inline-block;text-align:center;text-shadow:none;vertical-align:baseline;text-decoration:none;-webkit-transition:initial;transition:initial}button.h5p-core-button:focus{background:#1f67a8}button.h5p-core-button:hover{background:rgba(31,103,168,.83)}button.h5p-core-button:active{background:#104888}button.h5p-core-button:before{font-family:H5P;padding-right:.15em;font-size:1.5em;vertical-align:middle;line-height:.7}button.h5p-core-cancel-button,button.h5p-core-cancel-button:link,button.h5p-core-cancel-button:visited{border:none;background:0 0;color:#a00;margin-right:1em;font-size:1em;text-decoration:none;cursor:pointer}button.h5p-core-cancel-button:focus,button.h5p-core-cancel-button:hover{background:0 0;border:none;color:#e40000}
/*!@license H5P.Text-1.1/styles/text.css by Joubel licensed under unknown license*/.h5p-text li,.h5p-text ul{padding-left:0}.h5p-text ul li{list-style-type:circle;margin-left:1.5em;padding-left:0}.h5p-text ol li{list-style-type:decimal;margin-left:1.5em;padding-left:0}.h5p-text.h5p-frame{margin:1em}
/*!@license FontAwesome-4.5/h5p-font-awesome.min.css by Dave Gandy licensed under MIT*//*!
 *  Font Awesome 4.5.0 by @davegandy - http://fontawesome.io - @fontawesome
 *  License - http://fontawesome.io/license (Font: SIL OFL 1.1, CSS: MIT License)
 */@font-face{font-family:H5PFontAwesome4;src:url(data:font/woff;base64,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) format('woff');font-weight:400;font-style:normal}.fa{display:inline-block;font:normal normal normal 14px/1 H5PFontAwesome4;font-size:inherit;text-rendering:auto;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.fa-lg{font-size:1.33333333em;line-height:.75em;vertical-align:-15%}.fa-2x{font-size:2em}.fa-3x{font-size:3em}.fa-4x{font-size:4em}.fa-5x{font-size:5em}.fa-fw{width:1.28571429em;text-align:center}.fa-ul{padding-left:0;margin-left:2.14285714em;list-style-type:none}.fa-ul>li{position:relative}.fa-li{position:absolute;left:-2.14285714em;width:2.14285714em;top:.14285714em;text-align:center}.fa-li.fa-lg{left:-1.85714286em}.fa-border{padding:.2em .25em .15em;border:solid .08em #eee;border-radius:.1em}.fa-pull-left{float:left}.fa-pull-right{float:right}.fa.fa-pull-left{margin-right:.3em}.fa.fa-pull-right{margin-left:.3em}.pull-right{float:right}.pull-left{float:left}.fa.pull-left{margin-right:.3em}.fa.pull-right{margin-left:.3em}.fa-spin{-webkit-animation:fa-spin 2s infinite linear;animation:fa-spin 2s infinite linear}.fa-pulse{-webkit-animation:fa-spin 1s infinite steps(8);animation:fa-spin 1s infinite steps(8)}@-webkit-keyframes fa-spin{0%{-webkit-transform:rotate(0);transform:rotate(0)}100%{-webkit-transform:rotate(359deg);transform:rotate(359deg)}}@keyframes fa-spin{0%{-webkit-transform:rotate(0);transform:rotate(0)}100%{-webkit-transform:rotate(359deg);transform:rotate(359deg)}}.fa-rotate-90{-webkit-transform:rotate(90deg);-ms-transform:rotate(90deg);transform:rotate(90deg)}.fa-rotate-180{-webkit-transform:rotate(180deg);-ms-transform:rotate(180deg);transform:rotate(180deg)}.fa-rotate-270{-webkit-transform:rotate(270deg);-ms-transform:rotate(270deg);transform:rotate(270deg)}.fa-flip-horizontal{-webkit-transform:scale(-1,1);-ms-transform:scale(-1,1);transform:scale(-1,1)}.fa-flip-vertical{-webkit-transform:scale(1,-1);-ms-transform:scale(1,-1);transform:scale(1,-1)}:root .fa-flip-horizontal,:root .fa-flip-vertical,:root .fa-rotate-180,:root .fa-rotate-270,:root .fa-rotate-90{filter:none}.fa-stack{position:relative;display:inline-block;width:2em;height:2em;line-height:2em;vertical-align:middle}.fa-stack-1x,.fa-stack-2x{position:absolute;left:0;width:100%;text-align:center}.fa-stack-1x{line-height:inherit}.fa-stack-2x{font-size:2em}.fa-inverse{color:#fff}.fa-glass:before{content:"\f000"}.fa-music:before{content:"\f001"}.fa-search:before{content:"\f002"}.fa-envelope-o:before{content:"\f003"}.fa-heart:before{content:"\f004"}.fa-star:before{content:"\f005"}.fa-star-o:before{content:"\f006"}.fa-user:before{content:"\f007"}.fa-film:before{content:"\f008"}.fa-th-large:before{content:"\f009"}.fa-th:before{content:"\f00a"}.fa-th-list:before{content:"\f00b"}.fa-check:before{content:"\f00c"}.fa-close:before,.fa-remove:before,.fa-times:before{content:"\f00d"}.fa-search-plus:before{content:"\f00e"}.fa-search-minus:before{content:"\f010"}.fa-power-off:before{content:"\f011"}.fa-signal:before{content:"\f012"}.fa-cog:before,.fa-gear:before{content:"\f013"}.fa-trash-o:before{content:"\f014"}.fa-home:before{content:"\f015"}.fa-file-o:before{content:"\f016"}.fa-clock-o:before{content:"\f017"}.fa-road:before{content:"\f018"}.fa-download:before{content:"\f019"}.fa-arrow-circle-o-down:before{content:"\f01a"}.fa-arrow-circle-o-up:before{content:"\f01b"}.fa-inbox:before{content:"\f01c"}.fa-play-circle-o:before{content:"\f01d"}.fa-repeat:before,.fa-rotate-right:before{content:"\f01e"}.fa-refresh:before{content:"\f021"}.fa-list-alt:before{content:"\f022"}.fa-lock:before{content:"\f023"}.fa-flag:before{content:"\f024"}.fa-headphones:before{content:"\f025"}.fa-volume-off:before{content:"\f026"}.fa-volume-down:before{content:"\f027"}.fa-volume-up:before{content:"\f028"}.fa-qrcode:before{content:"\f029"}.fa-barcode:before{content:"\f02a"}.fa-tag:before{content:"\f02b"}.fa-tags:before{content:"\f02c"}.fa-book:before{content:"\f02d"}.fa-bookmark:before{content:"\f02e"}.fa-print:before{content:"\f02f"}.fa-camera:before{content:"\f030"}.fa-font:before{content:"\f031"}.fa-bold:before{content:"\f032"}.fa-italic:before{content:"\f033"}.fa-text-height:before{content:"\f034"}.fa-text-width:before{content:"\f035"}.fa-align-left:before{content:"\f036"}.fa-align-center:before{content:"\f037"}.fa-align-right:before{content:"\f038"}.fa-align-justify:before{content:"\f039"}.fa-list:before{content:"\f03a"}.fa-dedent:before,.fa-outdent:before{content:"\f03b"}.fa-indent:before{content:"\f03c"}.fa-video-camera:before{content:"\f03d"}.fa-image:before,.fa-photo:before,.fa-picture-o:before{content:"\f03e"}.fa-pencil:before{content:"\f040"}.fa-map-marker:before{content:"\f041"}.fa-adjust:before{content:"\f042"}.fa-tint:before{content:"\f043"}.fa-edit:before,.fa-pencil-square-o:before{content:"\f044"}.fa-share-square-o:before{content:"\f045"}.fa-check-square-o:before{content:"\f046"}.fa-arrows:before{content:"\f047"}.fa-step-backward:before{content:"\f048"}.fa-fast-backward:before{content:"\f049"}.fa-backward:before{content:"\f04a"}.fa-play:before{content:"\f04b"}.fa-pause:before{content:"\f04c"}.fa-stop:before{content:"\f04d"}.fa-forward:before{content:"\f04e"}.fa-fast-forward:before{content:"\f050"}.fa-step-forward:before{content:"\f051"}.fa-eject:before{content:"\f052"}.fa-chevron-left:before{content:"\f053"}.fa-chevron-right:before{content:"\f054"}.fa-plus-circle:before{content:"\f055"}.fa-minus-circle:before{content:"\f056"}.fa-times-circle:before{content:"\f057"}.fa-check-circle:before{content:"\f058"}.fa-question-circle:before{content:"\f059"}.fa-info-circle:before{content:"\f05a"}.fa-crosshairs:before{content:"\f05b"}.fa-times-circle-o:before{content:"\f05c"}.fa-check-circle-o:before{content:"\f05d"}.fa-ban:before{content:"\f05e"}.fa-arrow-left:before{content:"\f060"}.fa-arrow-right:before{content:"\f061"}.fa-arrow-up:before{content:"\f062"}.fa-arrow-down:before{content:"\f063"}.fa-mail-forward:before,.fa-share:before{content:"\f064"}.fa-expand:before{content:"\f065"}.fa-compress:before{content:"\f066"}.fa-plus:before{content:"\f067"}.fa-minus:before{content:"\f068"}.fa-asterisk:before{content:"\f069"}.fa-exclamation-circle:before{content:"\f06a"}.fa-gift:before{content:"\f06b"}.fa-leaf:before{content:"\f06c"}.fa-fire:before{content:"\f06d"}.fa-eye:before{content:"\f06e"}.fa-eye-slash:before{content:"\f070"}.fa-exclamation-triangle:before,.fa-warning:before{content:"\f071"}.fa-plane:before{content:"\f072"}.fa-calendar:before{content:"\f073"}.fa-random:before{content:"\f074"}.fa-comment:before{content:"\f075"}.fa-magnet:before{content:"\f076"}.fa-chevron-up:before{content:"\f077"}.fa-chevron-down:before{content:"\f078"}.fa-retweet:before{content:"\f079"}.fa-shopping-cart:before{content:"\f07a"}.fa-folder:before{content:"\f07b"}.fa-folder-open:before{content:"\f07c"}.fa-arrows-v:before{content:"\f07d"}.fa-arrows-h:before{content:"\f07e"}.fa-bar-chart-o:before,.fa-bar-chart:before{content:"\f080"}.fa-twitter-square:before{content:"\f081"}.fa-facebook-square:before{content:"\f082"}.fa-camera-retro:before{content:"\f083"}.fa-key:before{content:"\f084"}.fa-cogs:before,.fa-gears:before{content:"\f085"}.fa-comments:before{content:"\f086"}.fa-thumbs-o-up:before{content:"\f087"}.fa-thumbs-o-down:before{content:"\f088"}.fa-star-half:before{content:"\f089"}.fa-heart-o:before{content:"\f08a"}.fa-sign-out:before{content:"\f08b"}.fa-linkedin-square:before{content:"\f08c"}.fa-thumb-tack:before{content:"\f08d"}.fa-external-link:before{content:"\f08e"}.fa-sign-in:before{content:"\f090"}.fa-trophy:before{content:"\f091"}.fa-github-square:before{content:"\f092"}.fa-upload:before{content:"\f093"}.fa-lemon-o:before{content:"\f094"}.fa-phone:before{content:"\f095"}.fa-square-o:before{content:"\f096"}.fa-bookmark-o:before{content:"\f097"}.fa-phone-square:before{content:"\f098"}.fa-twitter:before{content:"\f099"}.fa-facebook-f:before,.fa-facebook:before{content:"\f09a"}.fa-github:before{content:"\f09b"}.fa-unlock:before{content:"\f09c"}.fa-credit-card:before{content:"\f09d"}.fa-feed:before,.fa-rss:before{content:"\f09e"}.fa-hdd-o:before{content:"\f0a0"}.fa-bullhorn:before{content:"\f0a1"}.fa-bell:before{content:"\f0f3"}.fa-certificate:before{content:"\f0a3"}.fa-hand-o-right:before{content:"\f0a4"}.fa-hand-o-left:before{content:"\f0a5"}.fa-hand-o-up:before{content:"\f0a6"}.fa-hand-o-down:before{content:"\f0a7"}.fa-arrow-circle-left:before{content:"\f0a8"}.fa-arrow-circle-right:before{content:"\f0a9"}.fa-arrow-circle-up:before{content:"\f0aa"}.fa-arrow-circle-down:before{content:"\f0ab"}.fa-globe:before{content:"\f0ac"}.fa-wrench:before{content:"\f0ad"}.fa-tasks:before{content:"\f0ae"}.fa-filter:before{content:"\f0b0"}.fa-briefcase:before{content:"\f0b1"}.fa-arrows-alt:before{content:"\f0b2"}.fa-group:before,.fa-users:before{content:"\f0c0"}.fa-chain:before,.fa-link:before{content:"\f0c1"}.fa-cloud:before{content:"\f0c2"}.fa-flask:before{content:"\f0c3"}.fa-cut:before,.fa-scissors:before{content:"\f0c4"}.fa-copy:before,.fa-files-o:before{content:"\f0c5"}.fa-paperclip:before{content:"\f0c6"}.fa-floppy-o:before,.fa-save:before{content:"\f0c7"}.fa-square:before{content:"\f0c8"}.fa-bars:before,.fa-navicon:before,.fa-reorder:before{content:"\f0c9"}.fa-list-ul:before{content:"\f0ca"}.fa-list-ol:before{content:"\f0cb"}.fa-strikethrough:before{content:"\f0cc"}.fa-underline:before{content:"\f0cd"}.fa-table:before{content:"\f0ce"}.fa-magic:before{content:"\f0d0"}.fa-truck:before{content:"\f0d1"}.fa-pinterest:before{content:"\f0d2"}.fa-pinterest-square:before{content:"\f0d3"}.fa-google-plus-square:before{content:"\f0d4"}.fa-google-plus:before{content:"\f0d5"}.fa-money:before{content:"\f0d6"}.fa-caret-down:before{content:"\f0d7"}.fa-caret-up:before{content:"\f0d8"}.fa-caret-left:before{content:"\f0d9"}.fa-caret-right:before{content:"\f0da"}.fa-columns:before{content:"\f0db"}.fa-sort:before,.fa-unsorted:before{content:"\f0dc"}.fa-sort-desc:before,.fa-sort-down:before{content:"\f0dd"}.fa-sort-asc:before,.fa-sort-up:before{content:"\f0de"}.fa-envelope:before{content:"\f0e0"}.fa-linkedin:before{content:"\f0e1"}.fa-rotate-left:before,.fa-undo:before{content:"\f0e2"}.fa-gavel:before,.fa-legal:before{content:"\f0e3"}.fa-dashboard:before,.fa-tachometer:before{content:"\f0e4"}.fa-comment-o:before{content:"\f0e5"}.fa-comments-o:before{content:"\f0e6"}.fa-bolt:before,.fa-flash:before{content:"\f0e7"}.fa-sitemap:before{content:"\f0e8"}.fa-umbrella:before{content:"\f0e9"}.fa-clipboard:before,.fa-paste:before{content:"\f0ea"}.fa-lightbulb-o:before{content:"\f0eb"}.fa-exchange:before{content:"\f0ec"}.fa-cloud-download:before{content:"\f0ed"}.fa-cloud-upload:before{content:"\f0ee"}.fa-user-md:before{content:"\f0f0"}.fa-stethoscope:before{content:"\f0f1"}.fa-suitcase:before{content:"\f0f2"}.fa-bell-o:before{content:"\f0a2"}.fa-coffee:before{content:"\f0f4"}.fa-cutlery:before{content:"\f0f5"}.fa-file-text-o:before{content:"\f0f6"}.fa-building-o:before{content:"\f0f7"}.fa-hospital-o:before{content:"\f0f8"}.fa-ambulance:before{content:"\f0f9"}.fa-medkit:before{content:"\f0fa"}.fa-fighter-jet:before{content:"\f0fb"}.fa-beer:before{content:"\f0fc"}.fa-h-square:before{content:"\f0fd"}.fa-plus-square:before{content:"\f0fe"}.fa-angle-double-left:before{content:"\f100"}.fa-angle-double-right:before{content:"\f101"}.fa-angle-double-up:before{content:"\f102"}.fa-angle-double-down:before{content:"\f103"}.fa-angle-left:before{content:"\f104"}.fa-angle-right:before{content:"\f105"}.fa-angle-up:before{content:"\f106"}.fa-angle-down:before{content:"\f107"}.fa-desktop:before{content:"\f108"}.fa-laptop:before{content:"\f109"}.fa-tablet:before{content:"\f10a"}.fa-mobile-phone:before,.fa-mobile:before{content:"\f10b"}.fa-circle-o:before{content:"\f10c"}.fa-quote-left:before{content:"\f10d"}.fa-quote-right:before{content:"\f10e"}.fa-spinner:before{content:"\f110"}.fa-circle:before{content:"\f111"}.fa-mail-reply:before,.fa-reply:before{content:"\f112"}.fa-github-alt:before{content:"\f113"}.fa-folder-o:before{content:"\f114"}.fa-folder-open-o:before{content:"\f115"}.fa-smile-o:before{content:"\f118"}.fa-frown-o:before{content:"\f119"}.fa-meh-o:before{content:"\f11a"}.fa-gamepad:before{content:"\f11b"}.fa-keyboard-o:before{content:"\f11c"}.fa-flag-o:before{content:"\f11d"}.fa-flag-checkered:before{content:"\f11e"}.fa-terminal:before{content:"\f120"}.fa-code:before{content:"\f121"}.fa-mail-reply-all:before,.fa-reply-all:before{content:"\f122"}.fa-star-half-empty:before,.fa-star-half-full:before,.fa-star-half-o:before{content:"\f123"}.fa-location-arrow:before{content:"\f124"}.fa-crop:before{content:"\f125"}.fa-code-fork:before{content:"\f126"}.fa-chain-broken:before,.fa-unlink:before{content:"\f127"}.fa-question:before{content:"\f128"}.fa-info:before{content:"\f129"}.fa-exclamation:before{content:"\f12a"}.fa-superscript:before{content:"\f12b"}.fa-subscript:before{content:"\f12c"}.fa-eraser:before{content:"\f12d"}.fa-puzzle-piece:before{content:"\f12e"}.fa-microphone:before{content:"\f130"}.fa-microphone-slash:before{content:"\f131"}.fa-shield:before{content:"\f132"}.fa-calendar-o:before{content:"\f133"}.fa-fire-extinguisher:before{content:"\f134"}.fa-rocket:before{content:"\f135"}.fa-maxcdn:before{content:"\f136"}.fa-chevron-circle-left:before{content:"\f137"}.fa-chevron-circle-right:before{content:"\f138"}.fa-chevron-circle-up:before{content:"\f139"}.fa-chevron-circle-down:before{content:"\f13a"}.fa-html5:before{content:"\f13b"}.fa-css3:before{content:"\f13c"}.fa-anchor:before{content:"\f13d"}.fa-unlock-alt:before{content:"\f13e"}.fa-bullseye:before{content:"\f140"}.fa-ellipsis-h:before{content:"\f141"}.fa-ellipsis-v:before{content:"\f142"}.fa-rss-square:before{content:"\f143"}.fa-play-circle:before{content:"\f144"}.fa-ticket:before{content:"\f145"}.fa-minus-square:before{content:"\f146"}.fa-minus-square-o:before{content:"\f147"}.fa-level-up:before{content:"\f148"}.fa-level-down:before{content:"\f149"}.fa-check-square:before{content:"\f14a"}.fa-pencil-square:before{content:"\f14b"}.fa-external-link-square:before{content:"\f14c"}.fa-share-square:before{content:"\f14d"}.fa-compass:before{content:"\f14e"}.fa-caret-square-o-down:before,.fa-toggle-down:before{content:"\f150"}.fa-caret-square-o-up:before,.fa-toggle-up:before{content:"\f151"}.fa-caret-square-o-right:before,.fa-toggle-right:before{content:"\f152"}.fa-eur:before,.fa-euro:before{content:"\f153"}.fa-gbp:before{content:"\f154"}.fa-dollar:before,.fa-usd:before{content:"\f155"}.fa-inr:before,.fa-rupee:before{content:"\f156"}.fa-cny:before,.fa-jpy:before,.fa-rmb:before,.fa-yen:before{content:"\f157"}.fa-rouble:before,.fa-rub:before,.fa-ruble:before{content:"\f158"}.fa-krw:before,.fa-won:before{content:"\f159"}.fa-bitcoin:before,.fa-btc:before{content:"\f15a"}.fa-file:before{content:"\f15b"}.fa-file-text:before{content:"\f15c"}.fa-sort-alpha-asc:before{content:"\f15d"}.fa-sort-alpha-desc:before{content:"\f15e"}.fa-sort-amount-asc:before{content:"\f160"}.fa-sort-amount-desc:before{content:"\f161"}.fa-sort-numeric-asc:before{content:"\f162"}.fa-sort-numeric-desc:before{content:"\f163"}.fa-thumbs-up:before{content:"\f164"}.fa-thumbs-down:before{content:"\f165"}.fa-youtube-square:before{content:"\f166"}.fa-youtube:before{content:"\f167"}.fa-xing:before{content:"\f168"}.fa-xing-square:before{content:"\f169"}.fa-youtube-play:before{content:"\f16a"}.fa-dropbox:before{content:"\f16b"}.fa-stack-overflow:before{content:"\f16c"}.fa-instagram:before{content:"\f16d"}.fa-flickr:before{content:"\f16e"}.fa-adn:before{content:"\f170"}.fa-bitbucket:before{content:"\f171"}.fa-bitbucket-square:before{content:"\f172"}.fa-tumblr:before{content:"\f173"}.fa-tumblr-square:before{content:"\f174"}.fa-long-arrow-down:before{content:"\f175"}.fa-long-arrow-up:before{content:"\f176"}.fa-long-arrow-left:before{content:"\f177"}.fa-long-arrow-right:before{content:"\f178"}.fa-apple:before{content:"\f179"}.fa-windows:before{content:"\f17a"}.fa-android:before{content:"\f17b"}.fa-linux:before{content:"\f17c"}.fa-dribbble:before{content:"\f17d"}.fa-skype:before{content:"\f17e"}.fa-foursquare:before{content:"\f180"}.fa-trello:before{content:"\f181"}.fa-female:before{content:"\f182"}.fa-male:before{content:"\f183"}.fa-gittip:before,.fa-gratipay:before{content:"\f184"}.fa-sun-o:before{content:"\f185"}.fa-moon-o:before{content:"\f186"}.fa-archive:before{content:"\f187"}.fa-bug:before{content:"\f188"}.fa-vk:before{content:"\f189"}.fa-weibo:before{content:"\f18a"}.fa-renren:before{content:"\f18b"}.fa-pagelines:before{content:"\f18c"}.fa-stack-exchange:before{content:"\f18d"}.fa-arrow-circle-o-right:before{content:"\f18e"}.fa-arrow-circle-o-left:before{content:"\f190"}.fa-caret-square-o-left:before,.fa-toggle-left:before{content:"\f191"}.fa-dot-circle-o:before{content:"\f192"}.fa-wheelchair:before{content:"\f193"}.fa-vimeo-square:before{content:"\f194"}.fa-try:before,.fa-turkish-lira:before{content:"\f195"}.fa-plus-square-o:before{content:"\f196"}.fa-space-shuttle:before{content:"\f197"}.fa-slack:before{content:"\f198"}.fa-envelope-square:before{content:"\f199"}.fa-wordpress:before{content:"\f19a"}.fa-openid:before{content:"\f19b"}.fa-bank:before,.fa-institution:before,.fa-university:before{content:"\f19c"}.fa-graduation-cap:before,.fa-mortar-board:before{content:"\f19d"}.fa-yahoo:before{content:"\f19e"}.fa-google:before{content:"\f1a0"}.fa-reddit:before{content:"\f1a1"}.fa-reddit-square:before{content:"\f1a2"}.fa-stumbleupon-circle:before{content:"\f1a3"}.fa-stumbleupon:before{content:"\f1a4"}.fa-delicious:before{content:"\f1a5"}.fa-digg:before{content:"\f1a6"}.fa-pied-piper:before{content:"\f1a7"}.fa-pied-piper-alt:before{content:"\f1a8"}.fa-drupal:before{content:"\f1a9"}.fa-joomla:before{content:"\f1aa"}.fa-language:before{content:"\f1ab"}.fa-fax:before{content:"\f1ac"}.fa-building:before{content:"\f1ad"}.fa-child:before{content:"\f1ae"}.fa-paw:before{content:"\f1b0"}.fa-spoon:before{content:"\f1b1"}.fa-cube:before{content:"\f1b2"}.fa-cubes:before{content:"\f1b3"}.fa-behance:before{content:"\f1b4"}.fa-behance-square:before{content:"\f1b5"}.fa-steam:before{content:"\f1b6"}.fa-steam-square:before{content:"\f1b7"}.fa-recycle:before{content:"\f1b8"}.fa-automobile:before,.fa-car:before{content:"\f1b9"}.fa-cab:before,.fa-taxi:before{content:"\f1ba"}.fa-tree:before{content:"\f1bb"}.fa-spotify:before{content:"\f1bc"}.fa-deviantart:before{content:"\f1bd"}.fa-soundcloud:before{content:"\f1be"}.fa-database:before{content:"\f1c0"}.fa-file-pdf-o:before{content:"\f1c1"}.fa-file-word-o:before{content:"\f1c2"}.fa-file-excel-o:before{content:"\f1c3"}.fa-file-powerpoint-o:before{content:"\f1c4"}.fa-file-image-o:before,.fa-file-photo-o:before,.fa-file-picture-o:before{content:"\f1c5"}.fa-file-archive-o:before,.fa-file-zip-o:before{content:"\f1c6"}.fa-file-audio-o:before,.fa-file-sound-o:before{content:"\f1c7"}.fa-file-movie-o:before,.fa-file-video-o:before{content:"\f1c8"}.fa-file-code-o:before{content:"\f1c9"}.fa-vine:before{content:"\f1ca"}.fa-codepen:before{content:"\f1cb"}.fa-jsfiddle:before{content:"\f1cc"}.fa-life-bouy:before,.fa-life-buoy:before,.fa-life-ring:before,.fa-life-saver:before,.fa-support:before{content:"\f1cd"}.fa-circle-o-notch:before{content:"\f1ce"}.fa-ra:before,.fa-rebel:before{content:"\f1d0"}.fa-empire:before,.fa-ge:before{content:"\f1d1"}.fa-git-square:before{content:"\f1d2"}.fa-git:before{content:"\f1d3"}.fa-hacker-news:before,.fa-y-combinator-square:before,.fa-yc-square:before{content:"\f1d4"}.fa-tencent-weibo:before{content:"\f1d5"}.fa-qq:before{content:"\f1d6"}.fa-wechat:before,.fa-weixin:before{content:"\f1d7"}.fa-paper-plane:before,.fa-send:before{content:"\f1d8"}.fa-paper-plane-o:before,.fa-send-o:before{content:"\f1d9"}.fa-history:before{content:"\f1da"}.fa-circle-thin:before{content:"\f1db"}.fa-header:before{content:"\f1dc"}.fa-paragraph:before{content:"\f1dd"}.fa-sliders:before{content:"\f1de"}.fa-share-alt:before{content:"\f1e0"}.fa-share-alt-square:before{content:"\f1e1"}.fa-bomb:before{content:"\f1e2"}.fa-futbol-o:before,.fa-soccer-ball-o:before{content:"\f1e3"}.fa-tty:before{content:"\f1e4"}.fa-binoculars:before{content:"\f1e5"}.fa-plug:before{content:"\f1e6"}.fa-slideshare:before{content:"\f1e7"}.fa-twitch:before{content:"\f1e8"}.fa-yelp:before{content:"\f1e9"}.fa-newspaper-o:before{content:"\f1ea"}.fa-wifi:before{content:"\f1eb"}.fa-calculator:before{content:"\f1ec"}.fa-paypal:before{content:"\f1ed"}.fa-google-wallet:before{content:"\f1ee"}.fa-cc-visa:before{content:"\f1f0"}.fa-cc-mastercard:before{content:"\f1f1"}.fa-cc-discover:before{content:"\f1f2"}.fa-cc-amex:before{content:"\f1f3"}.fa-cc-paypal:before{content:"\f1f4"}.fa-cc-stripe:before{content:"\f1f5"}.fa-bell-slash:before{content:"\f1f6"}.fa-bell-slash-o:before{content:"\f1f7"}.fa-trash:before{content:"\f1f8"}.fa-copyright:before{content:"\f1f9"}.fa-at:before{content:"\f1fa"}.fa-eyedropper:before{content:"\f1fb"}.fa-paint-brush:before{content:"\f1fc"}.fa-birthday-cake:before{content:"\f1fd"}.fa-area-chart:before{content:"\f1fe"}.fa-pie-chart:before{content:"\f200"}.fa-line-chart:before{content:"\f201"}.fa-lastfm:before{content:"\f202"}.fa-lastfm-square:before{content:"\f203"}.fa-toggle-off:before{content:"\f204"}.fa-toggle-on:before{content:"\f205"}.fa-bicycle:before{content:"\f206"}.fa-bus:before{content:"\f207"}.fa-ioxhost:before{content:"\f208"}.fa-angellist:before{content:"\f209"}.fa-cc:before{content:"\f20a"}.fa-ils:before,.fa-shekel:before,.fa-sheqel:before{content:"\f20b"}.fa-meanpath:before{content:"\f20c"}.fa-buysellads:before{content:"\f20d"}.fa-connectdevelop:before{content:"\f20e"}.fa-dashcube:before{content:"\f210"}.fa-forumbee:before{content:"\f211"}.fa-leanpub:before{content:"\f212"}.fa-sellsy:before{content:"\f213"}.fa-shirtsinbulk:before{content:"\f214"}.fa-simplybuilt:before{content:"\f215"}.fa-skyatlas:before{content:"\f216"}.fa-cart-plus:before{content:"\f217"}.fa-cart-arrow-down:before{content:"\f218"}.fa-diamond:before{content:"\f219"}.fa-ship:before{content:"\f21a"}.fa-user-secret:before{content:"\f21b"}.fa-motorcycle:before{content:"\f21c"}.fa-street-view:before{content:"\f21d"}.fa-heartbeat:before{content:"\f21e"}.fa-venus:before{content:"\f221"}.fa-mars:before{content:"\f222"}.fa-mercury:before{content:"\f223"}.fa-intersex:before,.fa-transgender:before{content:"\f224"}.fa-transgender-alt:before{content:"\f225"}.fa-venus-double:before{content:"\f226"}.fa-mars-double:before{content:"\f227"}.fa-venus-mars:before{content:"\f228"}.fa-mars-stroke:before{content:"\f229"}.fa-mars-stroke-v:before{content:"\f22a"}.fa-mars-stroke-h:before{content:"\f22b"}.fa-neuter:before{content:"\f22c"}.fa-genderless:before{content:"\f22d"}.fa-facebook-official:before{content:"\f230"}.fa-pinterest-p:before{content:"\f231"}.fa-whatsapp:before{content:"\f232"}.fa-server:before{content:"\f233"}.fa-user-plus:before{content:"\f234"}.fa-user-times:before{content:"\f235"}.fa-bed:before,.fa-hotel:before{content:"\f236"}.fa-viacoin:before{content:"\f237"}.fa-train:before{content:"\f238"}.fa-subway:before{content:"\f239"}.fa-medium:before{content:"\f23a"}.fa-y-combinator:before,.fa-yc:before{content:"\f23b"}.fa-optin-monster:before{content:"\f23c"}.fa-opencart:before{content:"\f23d"}.fa-expeditedssl:before{content:"\f23e"}.fa-battery-4:before,.fa-battery-full:before{content:"\f240"}.fa-battery-3:before,.fa-battery-three-quarters:before{content:"\f241"}.fa-battery-2:before,.fa-battery-half:before{content:"\f242"}.fa-battery-1:before,.fa-battery-quarter:before{content:"\f243"}.fa-battery-0:before,.fa-battery-empty:before{content:"\f244"}.fa-mouse-pointer:before{content:"\f245"}.fa-i-cursor:before{content:"\f246"}.fa-object-group:before{content:"\f247"}.fa-object-ungroup:before{content:"\f248"}.fa-sticky-note:before{content:"\f249"}.fa-sticky-note-o:before{content:"\f24a"}.fa-cc-jcb:before{content:"\f24b"}.fa-cc-diners-club:before{content:"\f24c"}.fa-clone:before{content:"\f24d"}.fa-balance-scale:before{content:"\f24e"}.fa-hourglass-o:before{content:"\f250"}.fa-hourglass-1:before,.fa-hourglass-start:before{content:"\f251"}.fa-hourglass-2:before,.fa-hourglass-half:before{content:"\f252"}.fa-hourglass-3:before,.fa-hourglass-end:before{content:"\f253"}.fa-hourglass:before{content:"\f254"}.fa-hand-grab-o:before,.fa-hand-rock-o:before{content:"\f255"}.fa-hand-paper-o:before,.fa-hand-stop-o:before{content:"\f256"}.fa-hand-scissors-o:before{content:"\f257"}.fa-hand-lizard-o:before{content:"\f258"}.fa-hand-spock-o:before{content:"\f259"}.fa-hand-pointer-o:before{content:"\f25a"}.fa-hand-peace-o:before{content:"\f25b"}.fa-trademark:before{content:"\f25c"}.fa-registered:before{content:"\f25d"}.fa-creative-commons:before{content:"\f25e"}.fa-gg:before{content:"\f260"}.fa-gg-circle:before{content:"\f261"}.fa-tripadvisor:before{content:"\f262"}.fa-odnoklassniki:before{content:"\f263"}.fa-odnoklassniki-square:before{content:"\f264"}.fa-get-pocket:before{content:"\f265"}.fa-wikipedia-w:before{content:"\f266"}.fa-safari:before{content:"\f267"}.fa-chrome:before{content:"\f268"}.fa-firefox:before{content:"\f269"}.fa-opera:before{content:"\f26a"}.fa-internet-explorer:before{content:"\f26b"}.fa-television:before,.fa-tv:before{content:"\f26c"}.fa-contao:before{content:"\f26d"}.fa-500px:before{content:"\f26e"}.fa-amazon:before{content:"\f270"}.fa-calendar-plus-o:before{content:"\f271"}.fa-calendar-minus-o:before{content:"\f272"}.fa-calendar-times-o:before{content:"\f273"}.fa-calendar-check-o:before{content:"\f274"}.fa-industry:before{content:"\f275"}.fa-map-pin:before{content:"\f276"}.fa-map-signs:before{content:"\f277"}.fa-map-o:before{content:"\f278"}.fa-map:before{content:"\f279"}.fa-commenting:before{content:"\f27a"}.fa-commenting-o:before{content:"\f27b"}.fa-houzz:before{content:"\f27c"}.fa-vimeo:before{content:"\f27d"}.fa-black-tie:before{content:"\f27e"}.fa-fonticons:before{content:"\f280"}.fa-reddit-alien:before{content:"\f281"}.fa-edge:before{content:"\f282"}.fa-credit-card-alt:before{content:"\f283"}.fa-codiepie:before{content:"\f284"}.fa-modx:before{content:"\f285"}.fa-fort-awesome:before{content:"\f286"}.fa-usb:before{content:"\f287"}.fa-product-hunt:before{content:"\f288"}.fa-mixcloud:before{content:"\f289"}.fa-scribd:before{content:"\f28a"}.fa-pause-circle:before{content:"\f28b"}.fa-pause-circle-o:before{content:"\f28c"}.fa-stop-circle:before{content:"\f28d"}.fa-stop-circle-o:before{content:"\f28e"}.fa-shopping-bag:before{content:"\f290"}.fa-shopping-basket:before{content:"\f291"}.fa-hashtag:before{content:"\f292"}.fa-bluetooth:before{content:"\f293"}.fa-bluetooth-b:before{content:"\f294"}.fa-percent:before{content:"\f295"}
@charset "UTF-8";/*!@license H5P.ImageHotspots-1.10/styles/image-hotspots.css by Joubel licensed under MIT*/.h5p-image-hotspots{background-color:#fff;color:#333}.h5p-image-hotspots .h5p-content-controls{transition:opacity .6s;opacity:1}.h5p-image-hotspots.showing-popup .h5p-content-controls{opacity:0;pointer-events:none}.h5p-image-hotspots-container{margin:0 auto;position:relative;overflow:hidden;width:auto;max-width:100%;background:#fff}.h5p-image-hotspots-background{width:100%}.h5p-image-hotspot{position:absolute;font-size:1.2em;height:1.1666667em;width:1.1666667em;display:flex;align-items:center;justify-content:center;line-height:1;border-radius:.7em;-webkit-transition:all .2s ease-in-out;transition:all .2s ease-in-out;background:#fff;padding:0;border:1px solid transparent;margin:0;cursor:pointer;box-shadow:0 0 8px 0 rgba(0,0,0,.3)}.h5p-image-hotspot.disabled{display:none}.h5p-image-hotspot:not(.legacy-positioning){transform:translate(-50%,-50%)}.h5p-image-hotspot.active{z-index:1}.h5p-content:not(.using-mouse) .h5p-image-hotspot:focus{outline:0;border-color:#555}.h5p-image-hotspot.active,.h5p-image-hotspot:focus,.not-an-ios-device .h5p-image-hotspot:hover{transform:translate(-50%,-50%) scale(1.5)}.h5p-image-hotspot.active.legacy-positioning,.h5p-image-hotspot.legacy-positioning:focus,.not-an-ios-device .h5p-image-hotspot.legacy-positioning:hover{transform:scale(1.5)}.h5p-image-hotspot:before{font-family:H5PFontAwesome4}.h5p-image-hotspot-plus:before{content:'\f055'}.h5p-image-hotspot-minus:before{content:'\f056'}.h5p-image-hotspot-times:before{content:'\f057'}.h5p-image-hotspot-check:before{content:'\f058'}.h5p-image-hotspot-question:before{content:'\f059'}.h5p-image-hotspot-info:before{content:'\f05a'}.h5p-image-hotspot-exclamation:before{content:'\f06a'}.h5p-image-hotspot-popup{position:absolute;z-index:3;top:0;margin:0;padding:.3em;height:100%;max-height:100%;background:#fff;background:rgba(255,255,255);border-radius:3px;box-shadow:0 10px 20px rgba(0,0,0,.3);box-sizing:border-box;transition:left .3s ease-in-out;cursor:auto}.h5p-image-hotspot-popup.fullscreen-popup,.h5p-image-hotspot-popup.popup-overflowing{border-radius:0}.h5p-image-hotspot-has-header .h5p-image-hotspot-popup-body{padding-top:0}.h5p-image-hotspot-popup-pointer{opacity:0;position:absolute;-webkit-transition:left .3s ease-in-out;transition:left .3s ease-in-out;width:0;height:0;border-top:.6em solid transparent;border-left:.6em solid rgba(255,255,255);border-bottom:.6em solid transparent;transform:translate(0,-50%);transition:transform .3s,opacity .2s;transition-delay:.1s;cursor:auto}.h5p-image-hotspot-popup-pointer.legacy-positioning{transform:none}.h5p-image-hotspot-popup-pointer.visible{opacity:1;z-index:3}.h5p-image-hotspot-popup-pointer.to-the-left{transform:translate(-100%,-50%)}.h5p-image-hotspot-popup-pointer.to-the-left.legacy-positioning{transform:translate(-100%,0)}.h5p-image-hotspot-popup-pointer.visible.to-the-left{transform:translate(-1px,-50%)}.h5p-image-hotspot-popup-pointer.visible.to-the-left.legacy-positioning{transform:translate(-1px,0)}.h5p-image-hotspot-popup-pointer.visible.to-the-right{border-right:.6em solid rgba(255,255,255);border-left:none;transform:translate(calc(-100% + 1px),-50%)}.h5p-image-hotspot-popup-pointer.visible.to-the-right.legacy-positioning{transform:translate(calc(-100% + 1px),0)}.h5p-image-hotspot-popup-header{font-weight:700;margin:0;padding:1em 1.75em 0 1em;margin-bottom:.5em;line-height:1.1em}.h5p-image-hotspot-popup-body{padding:1.333333em;font-size:.75em}.h5p-text.h5p-image-hotspot-popup-body{bottom:0;overflow:auto;word-wrap:break-word;line-height:1.1}.fullscreen-popup .h5p-image-hotspot-popup-content{height:100%;overflow:auto}.h5p-image-hotspot-popup-content{overflow:auto}.h5p-image-hotspot-popup-content.overflowing:after{content:'';width:calc(100% - 1em);height:10em;position:absolute;left:0;bottom:0;background:linear-gradient(transparent 2em,#fff);opacity:1;transition:opacity 1s;pointer-events:none}.h5p-image-hotspot-popup-content.overflowing.has-scrolled:after{opacity:0}.h5p-image-hotspot-popup-content.h5p-image-hotspot-popup-content-no-header{padding-top:.35em}.h5p-image-hotspot-popup-content::-webkit-scrollbar{background-color:#ebebeb;width:.25em;border-radius:5px}.h5p-image-hotspot-popup-content::-webkit-scrollbar-thumb{background-color:#737170;border-radius:5px}.h5p-image-hotspot-popup-body>*{line-height:1.7}.h5p-image-hotspot-popup:not(.fullscreen-popup) .h5p-image-hotspot-popup-body.h5p-video>div{max-height:18em;min-height:16em}.h5p-image-hotspot-popup-body p{margin:0}.h5p-image-hotspot-close-popup-button{position:absolute;top:.5em;right:.5em;width:2em;height:2em;border:1px solid transparent;border-radius:50%;font-size:.7em;padding:.8em;cursor:pointer;background:#fff;box-shadow:0 0 8px 0 rgba(0,0,0,.2);background-image:url(data:image/svg+xml;base64,PHN2ZyB2ZXJzaW9uPSIxLjIiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIG92ZXJmbG93PSJ2aXNpYmxlIiBwcmVzZXJ2ZUFzcGVjdFJhdGlvPSJub25lIiB2aWV3Qm94PSIwIDAgMjQgMjQiIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMyI+PGc+PHBhdGggeG1sbnM6ZGVmYXVsdD0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGQ9Ik0xOSA2LjQxTDE3LjU5IDUgMTIgMTAuNTkgNi40MSA1IDUgNi40MSAxMC41OSAxMiA1IDE3LjU5IDYuNDEgMTkgMTIgMTMuNDEgMTcuNTkgMTkgMTkgMTcuNTkgMTMuNDEgMTJ6IiBzdHlsZT0iZmlsbDogcmdiKDAsIDAsIDApOyIgdmVjdG9yLWVmZmVjdD0ibm9uLXNjYWxpbmctc3Ryb2tlIi8+PC9nPjwvc3ZnPg==);background-repeat:no-repeat;background-size:80% 80%;background-position:center;transition:box-shadow .3s,border-color .3s}.h5p-image-hotspot-close-popup-button:focus,.h5p-image-hotspot-close-popup-button:hover{box-shadow:0 0 8px 0 rgba(0,0,0,.4)}.h5p-content:not(.using-mouse) .h5p-image-hotspot-close-popup-button:focus{border-color:rgba(0,0,0,.4);outline:0}.h5p-image-hotspots-overlay{position:absolute;top:0;left:0;width:100%;height:100%;-webkit-transition:background .5s ease-in-out;transition:background .5s ease-in-out;background:0 0;z-index:1;cursor:pointer}.h5p-image-hotspots-overlay.visible{background:rgba(0,0,0,.5)}.background-image-missing{font-size:1.5em;text-align:center;padding:2em}.h5p-image-hotspots.h5p-fullscreen,.h5p-image-hotspots.h5p-semi-fullscreen{background-color:#000}.h5p-image-hotspots.h5p-fullscreen .h5p-image-hotspots-container,.h5p-image-hotspots.h5p-semi-fullscreen .h5p-image-hotspots-container{position:relative;top:50%;-webkit-transform:translateY(-50%);-ms-transform:translateY(-50%);transform:translateY(-50%)}.h5p-image.h5p-image-hotspot-popup-body-fraction{display:inline-block}.h5p-text.h5p-image-hotspot-popup-body-fraction h2{font-size:1.3em}.h5p-text.h5p-image-hotspot-popup-body-fraction h3{font-size:1.15em}</style>
        
    </head>
    <body>
        <div style="margin: 20px 20px;display: flex; justify-content: center;">
            <div
                style="max-width:1400px;"
                class="h5p-content lag" data-content-id="*********"></div>        
        </div>        
    </body>
</html>