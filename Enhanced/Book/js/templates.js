var templatesRepo={"templates/components/bookItem.html":"<div class=\"bookItem\" v-bind:class=\"[book.searchresults?'searchMode':'', config.libThumbSize?config.libThumbSize:'m', book.clicked?'clicked':null, config.revealBookInfoOnHover?'revealBookInfoOnHover':null, loaded?'loaded':'']\" >\n\n    <div ng-controller=\"ThumbCtrl\" ng-init=\"init()\" class=\"standard\" v-if=\"!book.searchresults\" v-bind:class=\"[config.bookThumbAlignment?'thumbAlign'+config.bookThumbAlignment:'']\">\n\n        <book-thumb :book=\"book\"></book-thumb>\n\n        <div class=\"details\">\n            <a class=\"title meta\" v-on:click.prevent=\"bookClicked()\">\n                {{book.name}}\n            </a>\n            <a v-if=\"config.bookThumbAuthor\" class=\"author meta\" v-on:click.prevent=\"bookClicked()\">\n                {{book.meta.author}}\n            </a>\n            <a v-if=\"config.bookThumbPublisher\" class=\"publisher meta\" v-on:click.prevent=\"bookClicked()\">\n                {{book.meta.publisher}}\n            </a>\n            <a v-if=\"config.bookThumbPrice && book.pricing && book.pricing.purchasable\" class=\"price meta\" v-on:click.prevent=\"bookClicked()\">\n                {{'cartPrice'|t(data.lc)}} ${{book.pricing.price}}\n            </a>\n        </div>\n\n        <div class=\"clear\"></div>\n    </div>\n\n    <div ng-controller=\"ThumbCtrl\" ng-init=\"init()\" class=\"searchResult\" v-else>\n\n        <book-thumb :book=\"book\"></book-thumb>\n        \n\n        <div class=\"details\">\n\n            <h1 v-html=\"unsafe(book.name)\" class=\"name\"></h1>\n            <div v-if=\"book.meta.author\" class=\"author\" v-html=\"unsafe(book.meta.author)\"></div>\n            <div v-if=\"book.meta.publisher\" class=\"publisher\"><em>{{'publisher'|t(data.lc)}}</em> {{book.meta.publisher}}</div>\n            <div v-if=\"book.meta.rights\" class=\"rights\"><em>{{'rights'|t(data.lc)}}</em> {{book.meta.rights}}</div>\n            <div v-if=\"book.meta.language\" class=\"lang\"><em>{{'language'|t(data.lc)}}</em> {{book.meta.language|langFromId}}</div>\n            <div v-if=\"book.pricing && book.pricing.purchasable\" class=\"price\"><em>{{'cartPrice'|t(data.lc)}}</em> ${{book.pricing.price}}</div>\n        </div>\n\n        <div class=\"results\" v-if=\"book.searchresults.length\">\n\n\n            <ion-list>\n                <template v-for=\"(chapter,index) in book.searchresults\">\n                    <div class=\"ion-item item\" v-if=\"(chapter.results.length>0) && chapter.title\">\n                        <strong>{{chapter.title}}</strong>\n                    </div>\n                    <a href v-for=\"item in chapter.results\" class=\"item item-icon-left\"\n                              v-on:click.prevent=\"bookClicked(chapter)\">\n                        <i class=\"icon size16\" v-bind:class=\"[data.settings.rtl?'kb-chevron-left':'kb-chevron-right']\"></i>\n                        <p v-html=\"unsafe(item.context)\"></p>\n                    </a>\n                </template>\n                \n                <div class=\"ion-item item\" v-if=\"book.moreExists || book.more_exists\" style=\"margin-top: 10px\">\n                    <span>{{'moreSearchResultsExist'|t(data.lc)}}</span>\n                </div>\n\n            </ion-list>\n\n        </div>\n        <div class=\"clear\"></div>\n\n    </div>\n\n\n</div>","templates/components/bookmarkItem.html":"<div>\n    <a class=\"item item-icon-left\" v-on:click.prevent=\"noteItemClicked(item)\"\n        v-bind:data-chapter=\"item.chapter\"\n        v-bind:data-location=\"item.location\">\n        <ion-delete-button class=\"\" v-on:click.prevent.native=\"deleteBookmark(item.bmid, $event)\">\n        </ion-delete-button>\n        <div class=\"label\">\n            <i class=\"kb-bookmark-icon\"></i>\n            <span>{{'bookmark'|t(data.lc)}}</span>\n        </div>\n        <a href class=\"webDelBtn\" v-on:click.prevent=\"deleteBookmark(item.bmid, $event)\">\n            <span class=\"kb-delete size24\"></span>\n        </a>\n    </a>\n</div>","templates/components/bookmarkItem.mobile.html":"<div :class=\"{showDelete: showDelete}\">\n    <a href v-if=\"showDelete\" class=\"mobileDeleteBtn\" v-on:click.prevent=\"deleteBookmark(item.bmid, $event)\">\n        <span class=\"icon kb-delete size28 vAlignTop\"></span>\n    </a>\n    <a class=\"item item-icon-left\" v-on:click.prevent=\"noteItemClicked(item)\"\n        v-bind:data-chapter=\"item.chapter\"\n        v-bind:data-location=\"item.location\">\n        <ion-delete-button class=\"\" v-on:click.prevent.native=\"deleteBookmark(item.bmid, $event)\">\n        </ion-delete-button>\n        <div class=\"label\">\n            <i class=\"kb-bookmark-icon\"></i>\n            <span>{{'bookmark'|t(data.lc)}}</span>\n        </div>\n    </a>\n</div>","templates/components/bookSection.html":"<div class=\"bookSection\">\n\n    <h3 v-on:click=\"catClicked(section.id)\">\n        {{section.name}}\n    </h3>\n    <div class=\"bookListContainer\">\n        <a href @click.prevent=\"next()\" class=\"navBtn next\" v-bind:class=\"[config.libThumbSize?config.libThumbSize:'m']\" v-if=\"!scrollAtEnd\">\n            <span class=\"icon\" :class=\"[data.settings.rtl?'kb-chevron-left':'kb-chevron-right']\"></span>\n        </a>\n        <a href @click.prevent=\"prev()\" class=\"navBtn prev\" v-bind:class=\"[config.libThumbSize?config.libThumbSize:'m']\" v-if=\"!scrollAtStart\">\n            <span class=\"icon\" :class=\"[data.settings.rtl?'kb-chevron-right':'kb-chevron-left']\"></span>\n        </a>\n        <div class=\"bookList\" v-on:scroll=\"scrolled\" v-on:scrollstart=\"scrollstart\" v-on:scrollend=\"scrollend\">\n            <div class=\"inner\">\n                <book-item v-for=\"book in sectionBooks\" :book=\"book\" :xkey=\"book.id\" v-on:bookClicked=\"bookClicked\">\n                </book-item>\n                <div class=\"bookItem more\" v-if=\"hasMore()\" v-bind:class=\"[config.libThumbSize?config.libThumbSize:'m']\" v-on:click=\"catClicked(section.id)\">\n                    <div class=\"bookThumb\">\n                        <div class=\"coverImgContainer thumbnail\">\n                            <div class=\"inner\">\n                                <div class=\"vSpace40\"></div>\n                                <div class=\"iconContainer\" style=\"xfont-size:32px\">\n                                    <span class=\"icon\" v-bind:class=\"[data.settings.rtl?'kb-chevron-left':'kb-chevron-right']\"></span>\n                                </div>\n                                <div class=\"vSpace20\"></div>\n                                <span style=\"xfont-size:16px\">\n                                    {{'more'|t(data.lc)}} \n                                    <span class=\"moreCount\">{{section.books.total}}</span>\n                                </span>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n\n</div>","templates/components/bookThumb.html":"<div class=\"bookThumb\" v-if=\"book\">\n    <div v-if=\"book.downloaded\" class=\"downloaded\">\n        <i class=\"icon kb-check kb-circle size12\"></i>\n    </div>\n    <div v-if=\"book.favorite\" class=\"favorite\">\n        <i class=\"icon kb-heart kb-circle size12\"></i>\n    </div>\n    <div v-if=\"book.locked && data.currentLibrary.marklockedbooks\" class=\"locked\">\n        <i class=\"icon kb-lock kb-circle size12\"></i>\n    </div>\n    <div class=\"coverImgContainer thumbnail xfloatLeft\" v-on:click.prevent=\"$parent.bookClicked()\" v-bind:style=\"book.coverStyle_\">\n        <span v-if=\"imgLoading\" class=\"bookLoaderAnimContainer\">\n            <span class=\"bookLoaderAnim\"></span>\n        </span>\n        <img v-bind:src=\"book.coverImg\" class=\"thumbImg\" v-showafterimageload v-imgfallback=\"config.kotobee.liburl + 'img/ui/defaultCover.png'\" v-on:load=\"coverLoaded\" />\n    </div>\n    <span v-if=\"config.bookThumbShadow\" class=\"bookShadow\"></span>\n</div>","templates/components/categoryList.html":"<div class=\"innerList\">\n    <template v-for=\"cat in list\">\n        <a href class=\"item\" v-if=\"!cat.hide\" :xto=\"'/library/cat/'+cat.id\"  v-bind:style=\"cat|categoryStyle\" :class=\"{expanded:isVisible(cat)}\" v-on:click.prevent=\"itemClicked(cat)\">\n            {{cat.name}}\n            <span class=\"arrowIcon floatRight\">\n                <i v-if=\"cat.submode=='separate'\" class=\"icon size16 inlineBlock\" :class=\"rtl?'kb-chevron-left':'kb-chevron-right'\"></i>\n                <i v-if=\"cat.submode=='expand'\" class=\"icon kb-chevron-right size16 inlineBlock\" :class=\"cat.expand?'rotateMinus90':'rotate90'\" style=\"transition: transform 0.3s\"></i>\n            </span>\n        </a>\n        <category-list :list=\"cat.children\" :rtl=\"rtl\" xv-on:catClicked=\"categoryClicked\" class=\"categoryList\" :odd=\"!odd\" v-bind:class=\"[odd?'odd':'even', isVisible(cat)?'visible':'notVisible']\" :level=\"level+1\"/>\n    </template>\n</div>","templates/components/hlightItem.html":"<div>\n    <a class=\"item item-icon-left\" v-on:click.prevent=\"noteItemClicked(item)\"\n            v-bind:data-chapter=\"item.chapter\"\n            v-bind:data-location=\"item.location\">\n        <ion-delete-button class=\"\" v-on:click.prevent.native=\"deleteHlight(item.hid, $event)\">\n        </ion-delete-button>\n        <div class=\"label\">\n            <i class=\"kb-highlight-icon\"></i>\n            <span>{{'highlight'|t(data.lc)}}</span>\n        </div>\n        <div class=\"hlight content\" v-bind:style=\"{'background-color':item.color}\">{{item.src}}</div>\n        <a href class=\"webDelBtn\" v-on:click.prevent=\"deleteHlight(item.hid, $event)\">\n            <span class=\"kb-delete size24\"></span>\n        </a>\n    </a>\n</div>","templates/components/hlightItem.mobile.html":"<div :class=\"{showDelete: showDelete}\">\n    <a href v-if=\"showDelete\" class=\"mobileDeleteBtn\" v-on:click.prevent=\"deleteHlight(item.hid, $event)\">\n        <span class=\"icon kb-delete size28 vAlignTop\"></span>\n    </a>\n    <a class=\"item item-icon-left\" v-on:click.prevent=\"noteItemClicked(item)\"\n        v-bind:data-chapter=\"item.chapter\"\n        v-bind:data-location=\"item.location\">\n        <ion-delete-button class=\"\" v-on:click.prevent.native=\"deleteHlight(item.hid, $event)\">\n        </ion-delete-button>\n        <div class=\"label\">\n            <i class=\"kb-highlight-icon\"></i>\n            <span>{{'highlight'|t(data.lc)}}</span>\n        </div>\n        <div class=\"hlight content\" v-bind:style=\"{'background-color':item.color}\">{{item.src}}</div>\n    </a>\n</div>","templates/components/infiniteList.html":"<div v-bind:class='classArray' class='ionic-scroll ion-content'>\n    <div class=\"scroll\" v-bind:class=\"{padding:padding}\">\n        <div v-if=\"mobile\">\n            <scroller\n              ref=\"vueScroller\"\n              :on-refresh=\"pullToRefresh?refresh:null\"\n              :on-infinite=\"infiniteLoading?infinite:null\"\n              :refresh-text=\"pullText\"\n              :no-data-text=\"noDataText\">\n                <slot/>\n            </scroller>\n        </div>\n        <div v-else>\n            <slot/>\n        </div>\n    </div>\n</div>","templates/components/libraryRow.html":"<div class=\"libraryRow\">\n    <div v-if=\"row.isSection\">\n        <book-section :section=\"row\" v-if=\"row.books.structured?row.books.total:row.books.length\" v-on:bookClicked=\"library.bookClicked\">\n        </book-section>\n    </div>\n    <div v-else>\n        <a v-if=\"library.bannerUrl(row)\" :href=\"library.bannerUrl(row)\" target=\"_blank\" class=\"banner\">\n            <img :src=\"library.bannerPath(row)\" v-bind:class=\"library.bannerClass(row)\" />\n        </a>\n        <a v-else href class=\"banner\" v-on:click.prevent=\"library.bannerClicked(row, $event)\">\n            <img :src=\"library.bannerPath(row)\" v-bind:class=\"library.bannerClass(row)\" />\n        </a>\n    </div>\n</div>","templates/components/nextChapterScroll.html":"<div ng-animate=\"'animate'\" v-bind:class=\"[reachedBottomScroll?'shown':'hidden']\">{{'nextChapter'|t(data.lc)}}: {{chapterTitle}}</div>","templates/components/noPayments.html":"<div class=\"noPaymentMethods\">\n  <span class=\"noPaymentMethods_icon kb-no-payment\"></span>\n  <div class=\"noPaymentMethods_innerWrapper\">\n    <h3 class=\"noPaymentMethods_innerWrapper_title\">{{'noPaymentMethodsAvailableTitle'|t(data.lc)}}</h3>\n    <p class=\"noPaymentMethods_innerWrapper_desc noPaymentMethods\" v-html=\"injectSupportUrl('noPaymentMethodsAvailableDesc')\"></p>\n  </div>\n  <a v-if=\"bookInfoOptions\" class=\"noPaymentMethods-btn backBtn button button-small button-positive\" href v-on:click.prevent=\"goBack()\">\n    {{'goBack'|t(data.lc)}}\n  </a>\n</div>\n","templates/components/noteItem.html":"<div>\n    <a class=\"item item-icon-left\" v-on:click.prevent=\"noteItemClicked(item)\"\n            v-bind:data-chapter=\"item.chapter\"\n            v-bind:data-location=\"item.location\">\n        <ion-delete-button class=\"\" v-on:click.prevent.native=\"deleteNote(item, $event)\">\n        </ion-delete-button>\n        <div class=\"label\">\n            <i class=\"kb-note-icon\"></i>\n            <span>{{'noteFor'|t(data.lc)}}</span>\n            <span class=\"src\">\"{{item.src}}\"</span>\n        </div>\n        <div class=\"noteContainer\">\n            <div class=\"avatarContainer\" v-if=\"item.public\">\n                <div class=\"kAvatar\">\n                    <span>{{item|noteUsernameChar}}</span>\n                </div>\n            </div>\n            <div class=\"note content\" v-html=\"item.noteTrusted\" v-bind:class=\"[noteEntryStyle(item)]\"></div>\n        </div>\n        <a href class=\"webDelBtn\" v-on:click.prevent=\"deleteNote(item, $event)\">\n            <span class=\"kb-delete size24\"></span>\n        </a>\n    </a>\n</div>","templates/components/noteItem.mobile.html":"<div :class=\"{showDelete: showDelete}\">\n    <a href v-if=\"showDelete\" class=\"mobileDeleteBtn\" v-on:click.prevent=\"deleteNote(item, $event)\">\n        <span class=\"icon kb-delete size28 vAlignTop\"></span>\n    </a>\n    <a class=\"item item-icon-left\" v-on:click.prevent=\"noteItemClicked(item)\"\n            v-bind:data-chapter=\"item.chapter\"\n            v-bind:data-location=\"item.location\">\n        <ion-delete-button class=\"\" v-on:click.prevent.native=\"deleteNote(item, $event)\">\n        </ion-delete-button>\n        <div class=\"label\">\n            <i class=\"kb-note-icon\"></i>\n            <span>{{'noteFor'|t(data.lc)}}</span>\n            <span class=\"src\">\"{{item.src}}\"</span>\n        </div>\n        <div class=\"noteContainer\">\n            <div class=\"avatarContainer\" v-if=\"item.public\">\n                <div class=\"kAvatar\">\n                    <span>{{item|noteUsernameChar}}</span>\n                </div>\n            </div>\n            <div class=\"note content\" v-html=\"item.noteTrusted\" v-bind:class=\"[noteEntryStyle(item)]\"></div>\n        </div>\n    </a>\n</div>","templates/components/pan.html":"<div style=\"width:100%;height:100%;direction:ltr\">\n    <div class=\"scrollPane\" style=\"transform-origin: left top;\">\n        <slot/>\n    </div>\n    <transition name=\"fade\">\n        <div class=\"kotobeeScrollbarY\" v-if=\"showScrollbarY && scrollbarY\" v-bind:class=\"[rtl?'rtl':'']\" v-on:click=\"scrollClicked('y',$event)\">\n            <div class=\"head\" v-on:mousedown=\"scrollMousedown('y',$event)\"></div>\n        </div>\n    </transition>\n    <transition name=\"fade\">\n        <div class=\"kotobeeScrollbarX\" v-if=\"showScrollbarX && scrollbarX\" v-bind:class=\"[rtl?'rtl':'']\" v-on:click=\"scrollClicked('x',$event)\">\n            <div class=\"head\" v-on:mousedown=\"scrollMousedown('x',$event)\"></div>\n        </div>\n    </transition>\n</div>","templates/components/selectionOptions.html":"<div>\n    <div class=\"inner ui\">\n        <div class=\"buttons\">\n            <a ui=\"\" id=\"copyBtn\" v-tooltip=\"'copyToClipboard'\" data-tooltipOffset=\"5\" v-bind:class=\"[config.clipboardCopy?'':'disabled']\" v-bind:title=\"'copyToClipboard'|t(data.lc)\" class=\"button button-icon\" data-clipboard-text=\"\" v-on:click.prevent=\"copy()\">\n                <span class=\"icon kb-copy size24\"></span>\n            </a>\n            <div class=\"verticalBtnSeparator\"></div>\n            <a ui=\"\" v-tooltip=\"'textToSpeech'\" data-tooltipOffset=\"5\" v-bind:title=\"'textToSpeech'|t(data.lc)\" v-bind:class=\"[(config.tts || config.ttsFromHere)?'':'disabled']\" class=\"button button-icon\" v-on:click.prevent=\"ttsOptions()\">\n                <span class=\"icon kb-tts size24\"></span>\n            </a>\n            <a ui=\"\" v-tooltip=\"'wikipediaLookup'\" data-tooltipOffset=\"5\" v-bind:title=\"'wikipediaLookup'|t(data.lc)\" v-bind:class=\"[config.wikipediaLookup?'':'disabled']\" class=\"button button-icon wikipediaLogo\" v-on:click.prevent=\"wikipedia()\">\n                <span class=\"icon kb-wikipedia size32\"></span>\n            </a>\n            <a ui=\"\" v-tooltip=\"'googleLookup'\" data-tooltipOffset=\"5\" v-bind:title=\"'googleLookup'|t(data.lc)\" v-bind:class=\"[config.googleLookup?'':'disabled']\" class=\"button button-icon\" v-on:click.prevent=\"google()\">\n                <span class=\"icon kb-google size24\"></span>\n            </a>\n            <a ui=\"\" v-tooltip=\"'addNote'\" data-tooltipOffset=\"5\" v-bind:title=\"'addNote'|t(data.lc)\" v-bind:class=\"[config.annotations?'':'disabled']\" class=\"button button-icon noteBtn\" v-on:click.prevent=\"note($event)\" style=\"margin-top:-2px\">\n                <span class=\"icon kb-edit size26\"></span>\n            </a>\n            <a ui=\"\" v-tooltip=\"'highlight'\" data-tooltipOffset=\"5\" v-bind:title=\"'highlight'|t(data.lc)\" v-bind:class=\"[config.annotations?'':'disabled']\" class=\"button button-icon highlightBtn\" v-on:click.prevent=\"highlight()\">\n                <span class=\"icon kb-highlight size24\"></span>\n            </a>\n        </div>\n    </div>\n    <div class=\"options ui\" v-if=\"optionMode\">\n        <div class=\"buttons\" v-if=\"optionMode=='tts'\">\n            <a ui=\"\" v-tooltip=\"'readWord'\" data-tooltipOffset=\"5\"  data-tooltipFlow=\"bottom\" v-bind:title=\"'readWord'|t(data.lc)\" class=\"button button-icon\" v-on:click.prevent=\"tts()\">\n                <span class=\"icon kb-read-word size26\"></span>\n            </a>\n            <a ui=\"\" v-if=\"speechSynthesisSupported\" v-tooltip=\"'readFromHere'\" data-tooltipOffset=\"5\" data-tooltipFlow=\"bottom\" v-bind:class=\"[config.ttsFromHere?'':'disabled']\" v-bind:title=\"'readFromHere'|t(data.lc)\" class=\"button button-icon\" v-on:click.prevent=\"ttsReadFromHere()\" dir=\"ltr\">\n                <span class=\"icon kb-read-paragraph size18 size26\" xstyle=\"position:relative; left:-9px; top:1px\"></span>\n            </a>\n            <a ui=\"\" v-if=\"speechSynthesisSupported\" v-tooltip=\"'settings'\" data-tooltipOffset=\"5\" data-tooltipFlow=\"bottom\" v-bind:title=\"'settings'|t(data.lc)\" class=\"ttsLangCaret button button-icon\" v-on:click.prevent=\"ttsChangeLangClicked()\">\n                <span class=\"icon kb-cog size24\"></span>\n            </a>\n        </div>\n    </div>\n</div>","templates/components/stars.html":"<div class=\"stars\">\n    <span class=\"kb-star-full\" v-if=\"rating>=1\" v-on:mouseover=\"over(1)\" v-on:mouseleave=\"leave()\" v-on:click.prevent=\"select(1)\"></span>\n    <span class=\"kb-star-full\" v-if=\"rating>=2\" v-on:mouseover=\"over(2)\" v-on:mouseleave=\"leave()\" v-on:click.prevent=\"select(2)\"></span>\n    <span class=\"kb-star-full\" v-if=\"rating>=3\" v-on:mouseover=\"over(3)\" v-on:mouseleave=\"leave()\" v-on:click.prevent=\"select(3)\"></span>\n    <span class=\"kb-star-full\" v-if=\"rating>=4\" v-on:mouseover=\"over(4)\" v-on:mouseleave=\"leave()\" v-on:click.prevent=\"select(4)\"></span>\n    <span class=\"kb-star-full\" v-if=\"rating>=5\" v-on:mouseover=\"over(5)\" v-on:mouseleave=\"leave()\" v-on:click.prevent=\"select(5)\"></span>\n\n    <span class=\"kb-star-empty\" v-if=\"rating<0.2\" v-on:mouseover=\"over(1)\" v-on:mouseleave=\"leave()\" v-on:click.prevent=\"select(1)\"></span>\n    <span class=\"kb-star-empty\" v-if=\"rating<1.2\" v-on:mouseover=\"over(2)\" v-on:mouseleave=\"leave()\" v-on:click.prevent=\"select(2)\"></span>\n    <span class=\"kb-star-empty\" v-if=\"rating<2.2\" v-on:mouseover=\"over(3)\" v-on:mouseleave=\"leave()\" v-on:click.prevent=\"select(3)\"></span>\n    <span class=\"kb-star-empty\" v-if=\"rating<3.2\" v-on:mouseover=\"over(4)\" v-on:mouseleave=\"leave()\" v-on:click.prevent=\"select(4)\"></span>\n    <span class=\"kb-star-empty\" v-if=\"rating<4.2\" v-on:mouseover=\"over(5)\" v-on:mouseleave=\"leave()\" v-on:click.prevent=\"select(5)\"></span>\n\n    <span class=\"kb-star-half\" v-if=\"rating>=0.2 && rating<1\"></span>\n    <span class=\"kb-star-half\" v-if=\"rating>=1.2 && rating<2\"></span>\n    <span class=\"kb-star-half\" v-if=\"rating>=2.2 && rating<3\"></span>\n    <span class=\"kb-star-half\" v-if=\"rating>=3.2 && rating<4\"></span>\n    <span class=\"kb-star-half\" v-if=\"rating>=4.2 && rating<5\"></span>\n</div>","templates/components/textSelectControls.html":"<div id=\"expandContainer\" class=\"row\">\n    <div class=\"col\">\n        <a class=\"button button-clear\" v-on:click.prevent=\"extendLeft()\">\n            <i class=\"icon kb-arrow-left size16\"></i>\n            {{'extend'|t(data.lc)}}\n        </a>\n    </div>\n    <div class=\"col\">\n        <a class=\"button button-clear\" v-on:click.prevent=\"expand()\">\n            <i class=\"icon kb-expand size20\"></i>\n            {{'expand'|t(data.lc)}}</a>\n    </div>\n    <div class=\"col\">\n        <a class=\"button button-clear\" v-on:click.prevent=\"extendRight()\">\n            {{'extend'|t(data.lc)}}\n            <i class=\"icon kb-arrow-right size16\"></i>\n        </a>\n    </div>\n</div>","templates/components/zoomControls.html":"<div class=\"zoomControls\" v-if=\"data.book.chapter.layout=='fxl'\">\n    <div class=\"zoomGroup\">\n        <a ui class=\"zoomBtn ki-noHighlight size18\" v-if=\"!config.hideFxlBestYFitBtn\" v-on:click.prevent=\"fxlZoomFit({dir:'y',ease:true})\">\n            <span class=\"icon kb-resize-vertical\"></span>\n        </a>\n        <a ui class=\"zoomBtn ki-noHighlight size18\" v-if=\"config.fxlFitToWidthBtn\" v-on:click.prevent=\"fxlZoomFit({dir:'x',ease:true})\">\n            <span class=\"icon kb-resize-horizontal\"></span>\n        </a>\n    </div>\n    <div class=\"zoomGroup\" v-if=\"!mobile\">\n        <a ui class=\"zoomBtn ki-noHighlight\" v-on:click.prevent=\"fxlZoomOut()\">\n            <span class=\"icon kb-zoomout\"></span>\n        </a>\n        <a ui class=\"zoomBtn ki-noHighlight\" v-on:click.prevent=\"fxlZoomIn()\">\n            <span class=\"icon kb-zoomin\"></span>\n        </a>\n    </div>\n</div>\n","templates/headers/headerDefault.html":"<div>\n    <div class=\"buttons chapterBtnContainer\">\n        <button type=\"button\" class=\"button toLibBtn button-clear inlineBlock\" v-if=\"(config.kotobee.mode != 'book')||readerApp\" v-on:click.prevent=\"(config.kotobee.mode != 'book')?backClicked():exitBook()\">\n            <span class=\"icon size12 vAlignMiddle\" v-bind:class=\"[data.settings.rtl?'kb-chevron-right':'kb-chevron-left']\"></span>\n            {{((readerApp&&!data.currentLibrary)?'home':'library')|t(data.lc)}}\n        </button>\n\n        \n        <a ui class=\"button button-icon menuBtn\" v-on:click.prevent=\"chapterClicked()\">\n            <span class=\"icon kb-menu size20\"></span>\n        </a>\n\n        \n        <div class=\"chapterBtn\" v-if=\"data.book.chapter.title\" v-bind:class=\"[config.kotobee.mode]\">\n            <a class=\"xchapterBtn button button-clear\" v-on:click.prevent=\"chapterClicked()\">\n                {{data.book.chapter.title}}\n            </a>\n            <span class=\"dropdown\"></span>\n        </div>\n    </div>\n\n    <h1 class=\"title\"></h1>\n\n    <div class=\"buttons\">\n        <ion-spinner icon=\"ripple\" class=\"syncSpinner\" v-if=\"data.syncing\"></ion-spinner>\n        <div v-if=\"!data.syncing\">\n            <a ui v-if=\"data.book.chapter.layout=='fxl' && config.freehand\" v-bind:title=\"'addFreehand'|t(data.lc)\" class=\"button button-icon\" v-on:click.prevent=\"freehandClicked($event)\">\n                <span class=\"icon kb-freehand size21\"></span>\n            </a>\n            <a ui v-bind:title=\"'addNote'|t(data.lc)\" class=\"button button-icon noteBtn\" v-if=\"config.annotations\" v-on:click.prevent=\"noteClicked()\">\n                <span class=\"icon kb-edit size20\"></span>\n            </a>\n            <a ui v-bind:title=\"'bookmark'|t(data.lc)\" class=\"button button-icon\" v-if=\"config.annotations\" v-on:click.prevent=\"bookmarkClicked()\">\n                <span class=\"icon kb-bookmark size16\"></span>\n            </a>\n            <a ui v-bind:title=\"'apps'|t(data.lc)\" class=\"button button-icon\" v-if=\"data.miniapps && data.miniapps.length\" v-on:click.prevent=\"miniappsClicked()\">\n                <span class=\"icon kb-apps size16\"></span>\n            </a>\n        </div>\n    </div>\n</div>\n","templates/headers/headerFreehand.html":"<div class=\"headerfreehand\" ng-controller=\"FreehandCtrl\" ng-init=\"init()\">\n    <div>\n        <div class=\"buttons\">\n            <button type=\"button\" class=\"button button-clear inlineBlock\" v-on:click.prevent=\"backClicked()\">\n                <span class=\"icon size14 vAlignMiddle\" v-bind:class=\"[data.settings.rtl?'kb-chevron-right':'kb-chevron-left']\"></span>\n            </button>\n            <button ui type=\"button\" class=\"button button-icon inlineBlock secondary\" v-on:click.prevent=\"smoothingClicked()\">\n                <span class=\"icon kb-curve1 size28\"></span>\n            </button>\n            <button ui type=\"button\" class=\"button button-icon inlineBlock secondary\" v-on:click.prevent=\"brushesClicked()\">\n                <span class=\"icon kb-brush size24\"></span>\n            </button>\n            <button ui type=\"button\" class=\"button button-icon inlineBlock secondary\" v-on:click.prevent=\"colorsClicked()\">\n                <span class=\"icon kb-circle stroke\" v-bind:style=\"{background: currentStyle.color}\"></span>\n            </button>\n        </div>\n\n        <h1 class=\"title\"></h1>\n\n        <div class=\"buttons\">\n            <button ui type=\"button\" v-bind:class=\"[mode=='pen'?'selected':'']\" class=\"toolBtn button button-icon inlineBlock\" v-on:click.prevent=\"penClicked()\">\n                <span class=\"icon kb-pen size26\"></span>\n            </button>\n            <button ui type=\"button\" v-bind:class=\"[mode=='eraser'?'selected':'']\" class=\"toolBtn button button-icon inlineBlock\" v-on:click.prevent=\"eraserClicked()\">\n                <span class=\"icon kb-erase size20\"></span>\n            </button>\n            <div class=\"separator\"></div>\n            <button ui type=\"button\" v-bind:class=\"[mode=='eraser'?'selected':'']\" class=\"button button-icon inlineBlock\" v-on:click.prevent=\"undoClicked()\">\n                <span class=\"icon kb-undo size20\"></span>\n            </button>\n            <button ui type=\"button\" v-bind:class=\"[mode=='eraser'?'selected':'']\" class=\"button button-icon inlineBlock\" v-on:click.prevent=\"redoClicked()\">\n                <span class=\"icon kb-redo size20\"></span>\n            </button>\n            <button ui type=\"button\" v-bind:class=\"[mode=='eraser'?'selected':'']\" class=\"button button-icon inlineBlock\" v-on:click.prevent=\"clearClicked()\">\n                <span class=\"icon kb-trash size20\"></span>\n            </button>\n        </div>\n    </div>\n    <div class=\"options kotobeeGradient\" v-if=\"currentOptions\">\n\n        <div v-if=\"currentOptions=='smoothing'\" class=\"buttons\" style=\"margin-top:6px\">\n            <div class=\"hSpace10\"></div>\n            <span class=\"desc\">\n                {{'selectSmoothing'|t(data.lc)}}\n            </span>\n            <button v-for=\"item in smoothing\" v-bind:class=\"[item==smoothingValue?'selected':'']\" ui type=\"button\" class=\"smoothingBtn button button-icon inlineBlock\" v-on:click.prevent=\"selectSmoothing(item)\">\n                <span class=\"icon\" v-bind:class=\"['kb-smooth'+item, ['size18','size16','size28','size28','size28'][item-1]]\"></span>\n            </button>\n        </div>\n        <div v-if=\"currentOptions=='brushes'\" class=\"buttons\" style=\"margin-top:6px\">\n            <div class=\"hSpace10\"></div>\n            <span class=\"desc\">\n                {{'selectStroke'|t(data.lc)}}\n            </span>\n            <button v-for=\"brush in brushes\" ui type=\"button\" class=\"brushBtn button button-icon inlineBlock\" v-on:click.prevent=\"selectBrush(brush)\">\n                <span class=\"icon kb-circle\" v-bind:class=\"[brush==currentStyle.brush?'selected':'']\" v-bind:style=\"{padding: brush+'px'}\"></span>\n            </button>\n        </div>\n        <div v-if=\"currentOptions=='colors'\" class=\"buttons\" style=\"margin-top:6px\">\n            <div class=\"hSpace10\"></div>\n            <span class=\"desc\">\n                {{'selectColor'|t(data.lc)}}\n            </span>\n            <button v-for=\"color in colors\" ui type=\"button\" class=\"colorBtn button button-icon inlineBlock\" v-on:click.prevent=\"selectColor(color)\">\n                <span class=\"icon kb-circle\" v-bind:class=\"[color==currentStyle.color?'stroke':'']\" v-bind:style=\"{background: color}\"></span>\n            </button>\n        </div>\n        <h1 class=\"title\">\n        </h1>\n        <div class=\"buttons\">\n            <button ui type=\"button\" class=\"button button-icon button-clear inlineBlock\" v-on:click.prevent=\"closeOptions()\">\n                <span class=\"icon kb-close size32 vAlignTop\"></span>\n            </button>\n        </div>\n    </div>\n</div>","templates/headers/headerLibrary.html":"<div class=\"libHeader bar bar-header has-tabs-top bar-positive kotobeeGradient\" v-bind:class=\"[subMode?'cat':'']\" id=\"libraryHeader\" v-show=\"!searchOb.enabled\">\n\n    <div class=\"buttons\" v-if=\"readerApp && !subMode\">\n        \n        <button type=\"button\" class=\"button exitBtn button-clear icon inlineBlock\" v-on:click.prevent=\"exitLib()\">\n            <span class=\"icon size14 vAlignMiddle\" v-bind:class=\"[data.settings.rtl?'kb-chevron-right':'kb-chevron-left']\"></span>\n            {{'home'|t(data.lc)}}\n        </button>\n    </div>\n\n    \n\n    <div class=\"buttons\" v-if=\"subMode\">\n        <a class=\"button xicon-left button-clear size17\" v-on:click.prevent=\"back()\">\n            <span class=\"icon size20 vAlignCenter\" v-bind:class=\"[data.settings.rtl?'kb-chevron-right':'kb-chevron-left']\"></span>\n        </a>\n        <a class=\"button button-clear\" v-on:click.prevent=\"back()\">\n            <span class=\"icon kb-apps size16 vAlignCenter\"></span>\n        </a>\n    </div>\n\n    <h1 class=\"title\">\n        <span v-if=\"!readerApp\" class=\"icon size24 kb-book-open vAlignMiddle\" style=\"opacity:0.2\"></span>\n        <span class=\"hSpace5\"></span>\n        <span class=\"name\">\n            <template v-if=\"title\">\n                {{title|t(data.lc)}}\n            </template>\n            <template v-else>\n                {{data.currentLibrary.name}}\n            </template>\n        </span>\n    </h1>\n\n    <div class=\"buttons headerBtns\">\n\n        <a class=\"button button-icon size14\" v-if=\"view.topmenu.promocode && (data.user.pwd||data.user.code)\"\n                v-on:click.prevent=\"redeemCodeClicked()\">\n            <span class=\"icon kb-price-tag size14\"></span>\n            {{'redeemCode'|t(data.lc)}}\n        </a>\n\n        \n        <a class=\"button button-icon size14\" v-if=\"shouldShowLogin\" v-on:click.prevent=\"userLogin()\">\n            <span class=\"icon kb-user size14\"></span>\n            {{(data.user.loggedIn?'logout':'login')|t(data.lc)}}\n        </a>\n        <a class=\"button button-icon size14\" v-if=\"data.currentLibrary.libraryinfo\" v-on:click.prevent=\"menuItemClicked('info')\">\n            <span class=\"icon kb-info size16\"></span>\n            {{'info'|t(data.lc)}}\n        </a>\n        <div class=\"relative\" v-if=\"config.languageOptional\">\n            <a class=\"button button-icon size14\" id=\"libraryLangBtn\"\n                    v-on:click.prevent=\"langDropdownExpanded=!langDropdownExpanded\">\n                <span class=\"icon kb-globe size16\"></span>\n                {{data.settings.language|langFromId}}\n                <span class=\"icon kb-caret-down size14\"></span>\n            </a>\n            <ul id=\"langDropdown\" v-if=\"langDropdownExpanded\" class=\"list list-inset\" role=\"menu\"\n                aria-labelledby=\"dropdownMenu1\">\n                <a href class=\"item\" v-for=\"lang in data.languages\"\n                   v-bind:class=\"[(lang.val==data.settings.language)?'selected':'']\" v-on:click.prevent=\"langSelected(lang.val);\">{{lang.label}}</a>\n            </ul>\n        </div>\n\n        <div class=\"verticalBtnSeparator\"></div>\n\n        <a ui class=\"button button-icon padding\" v-if=\"(config.searchLibrary || config.searchLibContent)\" v-on:click.prevent=\"enableSearch()\">\n            \n            <span class=\"icon kb-search size21\"></span>\n            \n        </a>\n        \n        \n        \n        <a v-if=\"config.kotobee.mode=='library'\" v-show=\"data.cartData.length\" class=\"cartIcon\" xng-controller=\"CartCtrl\" v-on:click.prevent=\"showCart()\">\n          <span v-if=\"data.cartData.length\" class=\"cartIcon__indicator\">{{ data.cartData.length }}</span>\n          <i class=\"kb-cart size21\"></i>\n        </a>\n        \n    </div>\n</div>","templates/headers/headerLibrary.mobile.html":"<div class=\"libHeader bar bar-header has-tabs-top bar-positive kotobeeGradient\" v-bind:class=\"[subMode?'cat':'']\" id=\"libraryHeader\" v-show=\"!searchOb.enabled\">\n\n    <div class=\"buttons\" v-if=\"readerApp && !subMode\" >\n        \n        <button type=\"button\" class=\"button exitBtn button-clear icon inlineBlock\" v-on:click.prevent=\"exitLib()\">\n            <span class=\"icon size14 vAlignMiddle\" v-bind:class=\"[data.settings.rtl?'kb-chevron-right':'kb-chevron-left']\"></span>\n            {{'home'|t(data.lc)}}\n        </button>\n    </div>\n\n    <div class=\"buttons\" v-if=\"subMode\" >\n        <a class=\"button xicon-left button-clear size17\" v-on:click.prevent=\"back()\">\n            <span class=\"icon size20 vAlignCenter\" v-bind:class=\"[data.settings.rtl?'kb-chevron-right':'kb-chevron-left']\"></span>\n        </a>\n        <a class=\"button button-clear\" v-on:click.prevent=\"back()\">\n            <span class=\"icon kb-apps size16 vAlignCenter\"></span>\n        </a>\n    </div>\n\n    <h1 class=\"title\">\n        <span v-if=\"!readerApp\" class=\"icon size24 kb-book-open vAlignMiddle\" style=\"opacity:0.2\"></span>\n        <span class=\"hSpace5\"></span>\n        <span class=\"name\">\n            <template v-if=\"title\">\n                {{title|t(data.lc)}}\n            </template>\n            <template v-else>\n                {{data.currentLibrary.name}}\n            </template>\n        </span>\n    </h1>\n\n    <div class=\"buttons\">\n        <a ui class=\"button button-icon padding\" v-if=\"(config.searchLibrary || config.searchLibContent)\" v-on:click.prevent=\"enableSearch()\">\n            \n            <span class=\"icon kb-search size21\"></span>\n        </a>\n        \n        \n        <a v-if=\"config.kotobee.mode=='library'\" v-show=\"data.cartData.length\" class=\"cartIcon\" xng-controller=\"CartCtrl\" v-on:click.prevent=\"showCart()\">\n            <span v-if=\"data.cartData.length\" class=\"cartIcon__indicator\">{{ data.cartData.length }}</span>\n            <i class=\"kb-cart size21\"></i>\n        </a>\n        \n        <a class=\"button button-icon libMenuBtn\" v-on:click.prevent=\"showLibraryMenu()\">\n            <span class=\"icon kb-vertical-dots size21\"></span>\n        </a>\n        \n\n    </div>\n</div>","templates/headers/headerLibraryCategories.html":"<div class=\"tabs-striped tabs-top tabs-background-positive tabs-light\">\n    <div id=\"libraryTabs\" class=\"tabs tabs-icon-left\" xv-if=\"$parent.initialized\">\n        <router-link to=\"/library/tab/categories\" v-if=\"hasCategories\" class=\"tab-item disable-user-behavior\"\n           v-bind:class=\"[$parent.tab=='categories'?'active':'']\">\n            <i class=\"icon kb-apps size16\"></i> <span class=\"tab-title\">{{'categories'|t(data.lc)}}</span>\n        </router-link>\n        <router-link to=\"/library/tab/all\" class=\"tab-item disable-user-behavior\"\n           v-bind:class=\"[$parent.tab=='all'?'active':'']\">\n            <i class=\"icon kb-books size20\"></i>\n            \n            <span class=\"tab-title\" v-if=\"!$parent.supportsAccountView\">{{'allBooks'|t(data.lc)}}</span>\n            <span class=\"tab-title\" v-else>{{'library'|t(data.lc)}}</span>\n        \n        </router-link>\n        <router-link to=\"/library/tab/account\" v-if=\"$parent.loggedIn && $parent.supportsAccountView\" class=\"tab-item disable-user-behavior\"\n           v-bind:class=\"[$parent.tab=='account'?'active':'']\">\n            \n            <i class=\"icon kb-book size18\"></i>\n            \n            <span class=\"tab-title\">{{'myBooks'|t(data.lc)}}</span>\n        </router-link>\n        <router-link to=\"/library/tab/downloads\" v-if=\"desktop && (cloudParam('offline') || cloudParam('public') || $parent.loggedIn)\" class=\"tab-item disable-user-behavior\"\n           v-bind:class=\"[$parent.tab=='downloaded'?'active':'']\">\n            <i class=\"icon kb-folder size18\"></i>\n            <span class=\"tab-title\">{{'downloaded'|t(data.lc)}}</span>\n        </router-link>\n        <router-link to=\"/library/tab/favorites\" v-if=\"config.showFavorites && !cloudParam('public') && $parent.loggedIn\" class=\"tab-item disable-user-behavior\"\n           v-bind:class=\"[$parent.tab=='favorites'?'active':'']\">\n            <i class=\"icon kb-heart size16\"></i>\n            <span class=\"tab-title\">{{'favorites'|t(data.lc)}}</span>\n        </router-link>\n        <router-link v-for=\"cat in data.currentLibrary.categories\" :to=\"'/library/tab/'+cat.id\" v-if=\"data.currentLibrary && cat.tab\" class=\"tab-item disable-user-behavior\"\n           v-bind:class=\"[(cat==$parent.currentCustomCat)?'active':'']\">\n            <span class=\"tab-title\">{{cat.name}}</span>\n        </router-link>\n\n        <label id=\"librarySort\" v-if=\"$parent.tab!='categories'\">\n            <div>{{'sortBy'|t(data.lc)}} </div>\n            <select v-model=\"data.sortOb.model\" v-on:change=\"$parent.sortChanged()\">\n                <option v-for=\"item in data.sortOb.options\" v-bind:value=\"item.val\">\n                    {{item.name|t(data.lc)}}\n                </option>\n            </select>\n        </label>\n\n    </div>\n</div>","templates/headers/headerLibraryCategories.mobile.html":"<div class=\"tabs-striped tabs-top tabs-background-positive tabs-light\">\n    <div id=\"libraryTabs\" class=\"tabs tabs-icon-top\" v-bind:class=\"{ease:autohideEase}\" xv-if=\"$parent.initialized\">\n        <router-link to=\"/library/tab/categories\" v-if=\"hasCategories\" class=\"tab-item disable-user-behavior\"\n           v-bind:class=\"[$parent.tab=='categories'?'active':'']\"\n           style=\"margin-top:-2px\">\n            \n            <span class=\"icon kb-list size24\"></span>\n            <span class=\"tab-title ng-binding\">{{'categories'|t(data.lc)}}</span>\n        </router-link>\n        <router-link to=\"/library/tab/all\" class=\"tab-item disable-user-behavior\"\n           v-bind:class=\"[$parent.tab=='all'?'active':'']\">\n            <i class=\"icon kb-book-open size24\"></i>\n            \n            <span class=\"tab-title ng-binding\" v-if=\"!$parent.supportsAccountView\">{{'allBooks'|t(data.lc)}}</span>\n            <span class=\"tab-title ng-binding\" v-if=\"$parent.supportsAccountView\">{{'library'|t(data.lc)}}</span>\n        </router-link>\n        <router-link to=\"/library/tab/account\" v-if=\"data.user.loggedIn && $parent.supportsAccountView\" class=\"tab-item disable-user-behavior\"\n           v-bind:class=\"[$parent.tab=='account'?'active':'']\">\n            <i class=\"icon kb-book size24\"></i>\n            \n            <span class=\"tab-title ng-binding\">{{'myBooks'|t(data.lc)}}</span>\n        </router-link>\n        <router-link to=\"/library/tab/downloads\" v-if=\"native && (cloudParam('offline') || cloudParam('public') || data.user.loggedIn)\" class=\"tab-item disable-user-behavior\"\n           v-bind:class=\"[$parent.tab=='downloaded'?'active':'']\">\n            <i class=\"icon kb-folder size24\"></i>\n            <span class=\"tab-title ng-binding\">{{'downloaded'|t(data.lc)}}</span>\n        </router-link>\n        <router-link to=\"/library/tab/favorites\" v-if=\"config.showFavorites && !cloudParam('public') && data.user.loggedIn\" class=\"tab-item disable-user-behavior\"\n           v-bind:class=\"[$parent.tab=='favorites'?'active':'']\">\n            <i class=\"icon kb-heart size24\"></i>\n            <span class=\"tab-title ng-binding\">{{'favorites'|t(data.lc)}}</span>\n        </router-link>\n        <router-link v-for=\"cat in data.currentLibrary.categories\" :to=\"'/library/tab/'+cat.id\" v-if=\"cat.tab\" class=\"tab-item disable-user-behavior customCatTab\"\n           v-bind:class=\"[(cat==$parent.currentCustomCat)?'active':'']\">\n            <div class=\"tab-title ng-binding\">{{cat.name}}</div>\n        </router-link>\n    </div>\n</div>","templates/headers/headerSearch.html":"<div>\n    <div v-if=\"obj[prop]==onValue\" class=\"searchBar bar bar-header bar-light item-input-inset\" v-bind:class=\"[kotobeeGradient?'kotobeeGradient':'']\">\n\n        <label class=\"item-input-wrapper\">\n            <span class=\"icon kb-search placeholder-icon\"></span>\n            <input type=\"search\" v-model=\"key\" v-autofocus v-bind:placeholder=\"(placeholder?placeholder:'search')|t(data.lc)\" v-on:keyup.enter=\"search(key)\">\n        </label>\n\n        <button type=\"button\" class=\"button button-clear\" v-on:click.prevent=\"hide()\">\n            {{'cancel'|t(data.lc)}}\n        </button>\n    </div>\n</div>\n","templates/headers/headerSearch.mobile.html":"<div>\n    <div v-if=\"obj[prop]==onValue\" class=\"searchBar bar bar-header bar-light item-input-inset\" v-bind:class=\"[kotobeeGradient?'kotobeeGradient':'']\">\n\n        <label class=\"item-input-wrapper\">\n            <span class=\"icon kb-search placeholder-icon\"></span>\n            <input type=\"search\" v-model=\"key\" v-autofocus v-bind:placeholder=\"(placeholder?placeholder:'search')|t(data.lc)\" v-on:keyup.enter=\"search(key)\">\n        </label>\n\n        <div class=\"buttons\" style=\"xtop:0\">\n            <button class=\"button button-clear\" v-on:click.prevent=\"hide()\">\n                {{'cancel'|t(data.lc)}}\n            </button>\n        </div>\n    </div>\n</div>\n","templates/headers/headerSearchItems.html":"<div ng-controller=\"SearchItemsCtrl\" ng-init=\"init()\">\n    <div class=\"buttons\">\n        <button type=\"button\" class=\"button icon button-icon\" v-on:click.prevent=\"backClicked()\">\n            <span class=\"icon size20\" v-bind:class=\"[data.settings.rtl?'kb-chevron-right':'kb-chevron-left']\"></span>\n        </button>\n    </div>\n    \n\n    <h1 class=\"title\">\n        <span v-if=\"titleMode=='title'\"><strong>\"{{searchContent.key}}\"</strong> {{countLabel}}</span>\n        <span v-if=\"titleMode=='empty'\"><strong>{{'noResults'|t(data.lc)}}</strong></span>\n        <span v-if=\"titleMode=='hidden'\"><strong>{{'elemIsHidden'|t(data.lc)}}</strong></span>\n    </h1>\n\n    <div class=\"buttons\">\n        <a ui class=\"button button-icon\" v-if=\"titleMode!='empty'\" v-on:click.prevent=\"up()\">\n            <span class=\"icon kb-caret-up size24\"></span>\n        </a>\n        <a ui class=\"button button-icon\" v-if=\"titleMode!='empty'\" v-on:click.prevent=\"down()\">\n            <span class=\"icon kb-caret-down size24\"></span>\n        </a>\n    </div>\n</div>","templates/headers/headerSelection.html":"<div ng-controller=\"SelectionCtrl\" class=\"selection\">\n    <div class=\"buttons\">\n        <a class=\"button button-icon\" v-on:click.prevent=\"back()\">\n            <span class=\"icon size20\" v-bind:class=\"[data.settings.rtl?'kb-chevron-right':'kb-chevron-left']\"></span>\n        </a>\n    </div>\n    <h1 class=\"title\"></h1>\n\n    <div v-if=\"!optionMode\" class=\"buttons tools\" style=\"xtop:0\">\n        <a ui class=\"button button-icon\" v-if=\"config.clipboardCopy\" v-on:click.prevent=\"copy()\">\n            <span class=\"icon kb-copy size24\"></span>\n        </a>\n        <a ui class=\"button button-icon\" v-if=\"config.share && native\" v-on:click.prevent=\"share()\">\n            <span class=\"icon kb-share size24\"></span>\n        </a>\n        <div class=\"verticalBtnSeparator\"></div>\n        <a ui class=\"button button-icon\" v-if=\"config.tts || config.ttsFromHere\" v-on:click.prevent=\"ttsOptions()\">\n            <span class=\"icon kb-tts size24\"></span>\n        </a>\n        <a ui class=\"button button-icon wikipediaLogo\" v-if=\"config.wikipediaLookup\" v-on:click.prevent=\"wikipedia()\">\n            <span class=\"icon kb-wikipedia size32\"></span>\n        </a>\n        <a ui class=\"button button-icon\" v-if=\"config.googleLookup\" v-on:click.prevent=\"google()\">\n            <span class=\"icon kb-google size24\"></span>\n        </a>\n        <a ui class=\"button button-icon noteBtn\" v-if=\"config.annotations\" v-on:click.prevent=\"note($event)\" style=\"margin-top:-2px\">\n            <span class=\"icon kb-edit size26\"></span>\n        </a>\n        <a ui class=\"button button-icon highlightBtn\" v-if=\"config.annotations\" v-on:click.prevent=\"highlight()\">\n            <span class=\"icon kb-highlight size24\"></span>\n        </a>\n    </div>\n    <template v-else>\n        <div class=\"buttons tools\" v-if=\"optionMode=='tts'\">\n            <a ui=\"\" v-tooltip=\"'readWord'\" data-tooltipOffset=\"5\"  data-tooltipFlow=\"bottom\" v-bind:title=\"'readWord'|t(data.lc)\" class=\"button button-icon\" v-on:click.prevent=\"tts()\">\n                <span class=\"icon kb-read-word size26\"></span>\n            </a>\n            <a ui=\"\" v-if=\"speechSynthesisSupported\" v-tooltip=\"'readFromHere'\" data-tooltipOffset=\"5\" data-tooltipFlow=\"bottom\" v-bind:class=\"[config.ttsFromHere?'':'disabled']\" v-bind:title=\"'readFromHere'|t(data.lc)\" class=\"button button-icon\" v-on:click.prevent=\"ttsReadFromHere()\" dir=\"ltr\">\n                <span class=\"icon kb-read-paragraph size18 size26\" xstyle=\"position:relative; left:-9px; top:1px\"></span>\n            </a>\n            <a ui=\"\" v-if=\"speechSynthesisSupported\" v-tooltip=\"'settings'\" data-tooltipOffset=\"5\" data-tooltipFlow=\"bottom\" v-bind:title=\"'settings'|t(data.lc)\" class=\"ttsLangCaret button button-icon\" v-on:click.prevent=\"ttsChangeLangClicked()\">\n                <span class=\"icon kb-cog size24\"></span>\n            </a>\n        </div>\n    </template>\n</div>","templates/headers/headerTextSize.html":"<div ng-controller=\"TextSizeCtrl\" ng-init=\"init()\">\n    <div class=\"buttons\">\n        <button type=\"button\" class=\"button button-clear inlineBlock\" v-on:click.prevent=\"backClicked()\">\n            <span class=\"icon size14\" v-bind:class=\"[data.settings.rtl?'kb-chevron-right':'kb-chevron-left']\"></span>\n        </button>\n    </div>\n    <h1 class=\"title\">\n        <button ui type=\"button\" class=\"button button-icon inlineBlock\" style=\"margin-top:6px\" v-on:click.prevent=\"minus()\">\n            <span class=\"icon kb-minus size20\"></span>\n        </button>\n        <strong>{{'fontSize'|t(data.lc)}} {{data.settings.textSize}}</strong>\n        <button ui type=\"button\" class=\"button button-icon inlineBlock\" style=\"margin-top:3px\" v-on:click.prevent=\"plus()\">\n            <span class=\"icon kb-plus size24\"></span>\n        </button>\n    </h1>\n\n    \n</div>","templates/headers/headerTts.html":"<div class=\"headertts\" ng-init=\"init()\">\n    <div>\n        <div class=\"buttons\">\n            <button type=\"button\" class=\"button button-clear inlineBlock\" v-on:click.prevent=\"backClicked()\">\n                <span class=\"icon size14 vAlignMiddle\" v-bind:class=\"[data.settings.rtl?'kb-chevron-right':'kb-chevron-left']\"></span>\n            </button>\n        </div>\n\n        <h1 class=\"title\"></h1>\n\n        <div class=\"buttons centered\">\n            <button ui type=\"button\" class=\"button button-icon inlineBlock\" v-on:click.prevent=\"prevClicked()\">\n                <span class=\"icon size18\" :class=\"data.settings.rtl?'kb-chevron-thin-right':'kb-chevron-thin-left'\"></span>\n                <span  class=\"txt\">{{'prev'|t(data.lc)}}</span>\n            </button>\n            \n            <button ui type=\"button\" class=\"button button-icon inlineBlock\" v-on:click.prevent=\"stopClicked()\">\n                <span class=\"icon kb-stop size21\"></span>\n            </button>\n            <button ui type=\"button\" class=\"button button-icon inlineBlock\" v-on:click.prevent=\"pauseClicked()\">\n                <span v-if=\"data.ttsPaused\" class=\"icon kb-play size21\"></span>\n                <span v-else class=\"icon kb-pause size21\"></span>\n            </button>\n            \n            <button ui type=\"button\" class=\"button button-icon inlineBlock\" v-on:click.prevent=\"nextClicked()\">\n                <span class=\"txt\">{{'next'|t(data.lc)}}</span>\n                <span class=\"icon size18\" :class=\"data.settings.rtl?'kb-chevron-thin-left':'kb-chevron-thin-right'\"></span>\n            </button>\n        </div>\n\n        <button ui type=\"button\" class=\"button button-icon inlineBlock right\" v-on:click.prevent=\"optionsClicked()\">\n            <span class=\"icon kb-cog size21\"></span>\n        </button>\n\n    </div>\n</div>","templates/headers/readerHeader.html":"<div v-if=\"header\" class=\"readerheader\">\n    <header-default v-if=\"header.mode=='default'\"></header-default>\n    <header-selection v-if=\"header.mode=='selection'\"></header-selection>\n    <header-search-items v-if=\"header.mode=='searchItems'\"></header-search-items>\n    <header-textsize v-if=\"header.mode=='textSize'\"></header-textsize>\n    <header-freehand v-if=\"header.mode=='freehand'\"></header-freehand>\n    <header-tts v-if=\"header.mode=='tts'\"></header-tts>\n</div>","templates/ionic/ionCheckbox.html":"<label class=\"item item-checkbox\">\n    <div class=\"checkbox checkbox-input-hidden disable-pointer-events checkbox-circle\">\n        <input type=\"checkbox\" v-bind:checked=\"value\" v-on:change=\"valChanged\">\n        <i class=\"checkbox-icon\"></i>\n    </div>\n    <div class=\"item-content disable-pointer-events\">\n        <span>\n                <slot/>\n        </span>\n    </div>\n</label>","templates/ionic/ionContent.html":"<div v-bind:class='classArray' class='ionic-scroll ion-content'>\n    <div class=\"scroll\" v-bind:class=\"[padding?'padding':'',delegateHandle?'xdelegate-'+delegateHandle:null]\">\n        <div class=\"panzoom\" v-if=\"panzoom\" xv-bind:style=\"{direction:rtl?'rtl':'ltr'}\">\n            <pan xv-bind:class=\"[delegateHandle?'delegate-'+delegateHandle:null]\"\n                 :minZoom=\"0.5\"\n                 :maxZoom=\"2\"\n                 :padding=\"0\"\n                 :containerId=\"panContainerId\"\n                 :resetVariable=\"panResetVariable\"\n                 :wheelMode=\"'pan'\"\n                 :scrollbarX=\"!mobile\"\n                 :scrollbarY=\"!mobile\"\n                 :rtl=\"rtl\">\n                <slot/>\n            </pan>\n        </div>\n        \n        <div v-else>        \n            <slot/>\n        </div>\n    </div>\n</div>","templates/ionic/ionDeleteButton.html":"<div class='item-left-edit item-delete'>\n    <a href class='icon kb-delete size14 button icon button-icon'>\n        <slot/>\n    </a>\n</div>","templates/ionic/ionFooterBar.html":"<div class='bar bar-footer'>\n    <slot/>\n</div>","templates/ionic/ionHeaderBar.html":"<div class='bar bar-header' :class=\"{kotobeeGradient:customizable}\">\n    <slot/>\n</div>","templates/ionic/ionicLoading.html":"<div id=\"ionicLoading\">\n    <div v-if=\"active\">\n        <div class=\"backdrop visible backdrop-loading active\"></div>\n        <div class=\"loading-container visible active\">\n            <div class=\"loading\">\n                <span v-html=\"template\">\n                </span>\n            </div>\n        </div>\n    </div>\n</div>","templates/ionic/ionItem.html":"<div class='ion-item item'>\n    <slot/>\n</div>","templates/ionic/ionList.html":"<div class='list'>\n    <slot/>\n</div>","templates/ionic/ionPane.html":"<div class='ion-pane pane'>\n    <slot/>\n</div>","templates/ionic/ionRefresher.html":"<div class='ionRefresher'>\n    <slot/>\n</div>","templates/ionic/ionSideMenu.html":"<div class='ion-side-menu menu' v-bind:class='[directionClass]' v-bind:style=\"{width:menuWidth+'px'}\">\n    <slot/>\n</div>","templates/ionic/ionSideMenuContent.html":"<div class='menu-content pane menu-animated' v-on:mousedown=\"$parent.sideMenuOpened?clicked():null\">\n    <slot/>\n</div>\n","templates/ionic/ionSideMenus.html":"<div class='ion-side-menus view'>\n    <slot/>\n</div>\n","templates/ionic/ionSpinner.html":"<div class='kSpinner' :class=\"icon\">\n    <span v-if=\"icon=='basic'\">\n        \n    </span>\n    <span v-if=\"icon=='ripple'\">\n        \n    </span>\n    <template v-if=\"icon=='dots'\">\n        <div class=\"step1\"></div>\n        <div class=\"step2\"></div>\n        <div class=\"step3\"></div>\n    </template>\n</div>","templates/ionic/ionToggle.html":"<div class=\"item item-toggle toggle-large\" :class=\"{transparent:transparent, inline:inline, small:small}\">\n    <div>\n        <span>\n            <slot/>\n        </span>\n    </div>\n    <label class=\"toggle toggle-calm disable-user-behavior\">\n        <input type=\"checkbox\" v-bind:checked=\"value\" v-on:change=\"valChanged\">\n        <div class=\"track\">\n            <div class=\"handle\"></div>\n        </div>\n    </label>\n</div>","templates/panels/audio.html":"<div>\n    <ion-header-bar align-title=\"center\">\n        <h1 class=\"title\">{{modal.audio .title}}</h1>\n        <a class=\"button button-icon button-clear\" v-on:click.prevent=\"hideModal()\">\n            <span class=\"icon kb-close size32 vAlignTop\"></span>\n        </a>\n    </ion-header-bar>\n    <ion-content class=\"padding alignCenter\" :scroll=\"false\">\n        <audio style=\"max-width:100%;xmargin:10px\" controls autoplay>\n            <source v-bind:src=\"unsafeResource(modal.audio.src)\">\n        </audio>\n    </ion-content>\n</div>","templates/panels/backToLib.html":"<div>\n    <ion-header-bar align-title=\"center\" class=\"bar-dark\">\n        <h1 class=\"title\">{{'backToLibConfirmation'|t(data.lc)}}</h1>\n        <button type=\"button\" class=\"button button-icon button-clear\" v-on:click.prevent=\"hideModal()\">\n            <span class=\"icon kb-close size32 vAlignTop\"></span>\n        </button>\n    </ion-header-bar>\n    <ion-content class=\"padding\" :scroll=\"false\">\n        <h5 class=\"center\">{{'backToLibConfirmationMsg'|t(data.lc)}}</h5>\n    </ion-content>\n    <ion-footer-bar>\n        <div class=\"row\">\n            <div class=\"col\">\n                <button type=\"button\" class=\"button button-block button-positive zeroMargin\" v-on:click.prevent=\"modal.backToLibOptions.back()\">\n                    {{'yes'|t(data.lc)}}\n                </button>\n            </div>\n            <div class=\"col\">\n                <button type=\"button\" class=\"button button-block button-light zeroMargin\" v-on:click.prevent=\"hideModal()\">{{'cancel'|t(data.lc)}}</button>\n            </div>\n        </div>\n    </ion-footer-bar>\n</div>","templates/panels/bookDownloaded.html":"<div>\n    <ion-header-bar align-title=\"center\" class=\"bar-positive\">\n        <h1 class=\"title\">{{'downloadComplete'|t(data.lc)}}</h1>\n        <button class=\"button button-icon button-clear\" v-on:click.prevent=\"hideModal()\">\n            <span class=\"icon kb-close size32 vAlignTop\"></span>\n        </button>\n    </ion-header-bar>\n    <ion-content class=\"padding\" :scroll=\"false\">\n        \n            \n        <h5 class=\"center\">{{'bookDownloaded'|t(data.lc, modal.downloadedOptions.book)}}</h5>\n\n    </ion-content>\n    <ion-footer-bar>\n        <div class=\"row\">\n            <div class=\"col\">\n                <button class=\"button button-block button-positive zeroMargin openBtn\"\n                        v-on:click.prevent=\"modal.downloadedOptions.open(modal.downloadedOptions.book);hideModal()\">\n                    <i class=\"inlineBlock vAlignMiddle icon kb-book-open size20\" style=\"xmargin-right:5px\"></i> {{'openBook'|t(data.lc)}}</button>\n            </div>\n            <div class=\"col\">\n                <button class=\"button button-block button-light zeroMargin\" v-on:click.prevent=\"hideModal()\">{{'cancel'|t(data.lc)}}</button>\n            </div>\n        </div>\n    </ion-footer-bar>\n</div>","templates/panels/bookInfo.html":"<div ng-controller=\"BookInfoCtrl\" ng-init=\"init()\" v-if=\"modal && bookInfoOptions && bookInfoOptions.book\">\n\n    <ion-header-bar xalign-title=\"center\">\n        <h1 class=\"title\" v-html=\"unsafe(bookInfoOptions.book.name)\"></h1>\n        <a class=\"button button-icon button-clear\" v-if=\"!busy\" v-on:click.prevent=\"hideModal()\">\n            <span class=\"icon kb-close size32 vAlignTop\"></span>\n        </a>\n    </ion-header-bar>\n    \n    <ion-content :overflow-scroll=\"true\" :scroll=\"true\">\n\n        <bookInfoBanner v-if=\"bookInfoOptions.stage=='info'\"></bookInfoBanner>\n\n        <div v-if=\"!busy\" class=\"content\">\n\n            <div v-if=\"bookInfoOptions.stage=='info'\">\n              <bookInfoMeta></bookInfoMeta>\n              <bookInfoReviews></bookInfoReviews>\n            </div>\n\n            <div v-if=\"bookInfoOptions.stage=='emailCollection'\" class=\"emailCollectionStage\">\n                <div class=\"header\">\n                    <a class=\"backBtn button button-small vAlignMiddle\" href=\"#\" v-on:click.prevent=\"backBtnClicked(bookInfoOptions)\">\n                        {{'back'|t(data.lc)}}\n                    </a>\n                    <div class=\"hSpace10\"></div>\n                    <p v-html=\"unsafe(bookInfoOptions.book.name)\" class=\"name vAlignMiddle\"></p>\n                </div>\n                <div class=\"vSpace40\"></div>\n\n                <div class=\"alignCenter\">\n\n                    <div class=\"emailForm\">\n                        <div v-if=\"bookInfoOptions.emailCollectionMethod=='login'\">\n                            <h3>{{'prepayAccountLogin'|t(data.lc)}}</h3>\n                            <p>{{'prepayAccountLoginDesc'|t(data.lc)}}</p>\n                            <div class=\"card\">\n                                <label class=\"item item-input\">\n                                    <input type=\"email\" v-bind:disabled=\"bookInfoOptions.verifyingEmail\" v-model=\"bookInfoOptions.paymentEmail1\" v-on:keyup.enter=\"actionBtnClicked(bookInfoOptions.book)\" v-autofocus v-bind:placeholder=\"'email'|t(data.lc)\">\n                                </label>\n                                <label class=\"item item-input\">\n                                    <input type=\"password\" v-bind:disabled=\"bookInfoOptions.verifyingEmail\" v-model=\"bookInfoOptions.pwd\" v-on:keyup.enter=\"actionBtnClicked(bookInfoOptions.book)\" v-bind:placeholder=\"'password'|t(data.lc)\">\n                                </label>\n                            </div>\n                            <p class=\"error\" v-if=\"bookInfoOptions.paymentEmailError\">\n                                {{bookInfoOptions.paymentEmailError}}\n                            </p>\n                            <div class=\"vSpace5\"></div>\n                            <label>\n                                <a href v-on:click.prevent=\"set(bookInfoOptions,'emailCollectionMethod','new')\">{{'createNewAccount'|t(data.lc)}}</a>\n                            </label>\n                        </div>\n                        <div v-else>\n                            <h3>{{'prepayAccountCreate'|t(data.lc)}}</h3>\n                            <p>{{'prepayAccountCreateDesc'|t(data.lc)}}</p>\n                            <div class=\"card\">\n                                <label class=\"item item-input\">\n                                    <input type=\"email\" v-bind:disabled=\"bookInfoOptions.verifyingEmail\" v-model=\"bookInfoOptions.paymentEmail1\" v-on:keyup.enter=\"actionBtnClicked(bookInfoOptions.book)\" v-autofocus v-bind:placeholder=\"'email'|t(data.lc)\">\n                                </label>\n                                <label class=\"item item-input\">\n                                    <input type=\"email\" v-bind:disabled=\"bookInfoOptions.verifyingEmail\" v-model=\"bookInfoOptions.paymentEmail2\" v-on:keyup.enter=\"actionBtnClicked(bookInfoOptions.book)\" v-bind:placeholder=\"'confirmEmail'|t(data.lc)\">\n                                </label>\n                            </div>\n                            <p class=\"error\" v-if=\"bookInfoOptions.paymentEmailError\">\n                                {{bookInfoOptions.paymentEmailError}}\n                            </p>\n                            <div class=\"vSpace5\"></div>\n                            <label>{{'alreadyHaveAnAccount'|t(data.lc)}}\n                                <a href v-on:click.prevent=\"set(bookInfoOptions,'emailCollectionMethod','login')\">{{'loginInstead'|t(data.lc)}}</a>\n                            </label>\n                        </div>\n                        <div class=\"vSpace10\"></div>\n                        <div class=\"row\">\n                            <div class=\"col\">\n                                <button type=\"button\" class=\"button button-block button-positive zeroMargin\" v-bind:disabled=\"bookInfoOptions.verifyingEmail\"\n                                        v-on:click.prevent=\"actionBtnClicked(bookInfoOptions.book)\">{{'next'|t(data.lc)}}\n                                </button>\n                            </div>\n                            <div class=\"col\">\n                                <button type=\"button\" class=\"button button-block button-light zeroMargin\" v-bind:disabled=\"bookInfoOptions.verifyingEmail\"\n                                        v-on:click.prevent=\"backBtnClicked(bookInfoOptions)\">{{'cancel'|t(data.lc)}}\n                                </button>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n\n            <div v-if=\"bookInfoOptions.stage=='paymentMethod'\" class=\"paymentMethodStage\">\n                <div class=\"header\">\n                    <a class=\"backBtn\" :class=\"[data.settings&&data.settings.rtl? 'kb-chevron-right': 'kb-chevron-left']\" href=\"#\" v-on:click.prevent=\"backBtnClicked(bookInfoOptions)\"></a>\n                    <p class=\"name vAlignMiddle\">{{'selectPaymentMethod'|t(data.lc)}}</p>\n                </div>\n                <div class=\"vSpace40\"></div>\n\n                <div class=\"alignCenter\">\n                    <template v-if=\"!bookInfoOptions.book.paymentOptions.none\">\n                        \n                        \n                        <button v-if=\"config.kotobee.mode == 'library' && !data.currentLibrary.iskotobee\" ng-controller=\"CartCtrl\" class=\"button button-dark\" v-on:click.prevent=\"addBook(bookInfoOptions.book)\">\n                            <i class=\"kb-cart\"></i>\n                            <span>{{'addToCardBtn'|t(data.lc)}}</span>\n                        </button>\n                        \n                        <div v-if=\"config.kotobee.paymentinstructions\" class=\"paymentInstructions\">\n                            <div class=\"vSpace10\"></div>\n                            <div v-html=\"config.kotobee.paymentinstructions\"></div>\n                            <div class=\"vSpace10\"></div>\n                        </div>\n                    </template>\n\n                    <div :style=\"{ marginTop: bookInfoOptions.book.paymentOptions.none ? '100px': ''}\" class=\"paymentOptions\" v-bind:class=\"[bookInfoOptions.paying?'paying':'']\" v-if=\"bookInfoOptions.book.privilege == 'buy'\">\n                        <noPayments v-if=\"bookInfoOptions.book.paymentOptions.none\" :bookInfoOptions=\"bookInfoOptions\"></noPayments>\n                        <template v-else>\n                            <div v-if=\"bookInfoOptions.book.paymentOptions.stripe\" id=\"stripe-button-wrapper\" class=\"paymentOption\">\n                                <a class=\"button button-positive\" v-on:click.prevent=\"actionBtnClicked(bookInfoOptions.book, 'stripe')\" v-bind:class=\"['stripe']\">\n                                    <ion-spinner v-if=\"bookInfoOptions.paying=='stripe'\" icon=\"dots\" color=\"#aaa\" class=\"loadingSpinner\"></ion-spinner>\n                                </a>\n                            </div>\n                            <div v-if=\"bookInfoOptions.book.paymentOptions.paypal\" id=\"paypal-button-container\" class=\"paymentOption\">\n                            </div>\n                            <div v-if=\"bookInfoOptions.book.paymentOptions.moyasar\" id=\"moyasar-button-wrapper\" class=\"paymentOption\">\n                                <a class=\"button button-positive\" v-on:click.prevent=\"actionBtnClicked(bookInfoOptions.book, 'moyasar')\" v-bind:class=\"['moyasar']\">\n                                </a>\n                            </div>\n                        </template>\n                        \n                    </div>\n                </div>\n            </div>\n\n        </div>\n\n    </ion-content>\n\n</div>","templates/panels/bookInfoActionBtns.html":"<div v-if=\"!bookInfoOptions.noAction && bookInfoOptions.stage=='info'\" class=\"actionBtns bookActionBtns\">\n  \n  <div class=\"actionBtn\" v-bind:class=\"{'hasPayment':bookInfoOptions.book.price_localized}\">\n    \n    <button\n      v-if=\"desktop || native\"\n      :class=\"[bookInfoOptions.ui.actionBtnStyle]\"\n      class=\"downloadBtn button button-positive\"\n      v-on:click.prevent=\"actionBtnClicked(bookInfoOptions.book)\"\n      v-bind:disabled=\"bookInfoOptions.ui.actionBtnStyle=='downloadingBtn'\"\n    >\n      \n      <span v-html=\"bookInfoOptions.ui.actionBtnLabel\"></span>\n      <em v-if=\"(bookInfoOptions.ui.privilege=='download') &&bookInfoOptions.book.filesize\" class=\"sizeLabel\">\n        [{{bookInfoOptions.book.filesize|filesizeLabel}}]\n      </em>\n      <ion-spinner\n        v-if=\"bookInfoOptions.ui.actionBtnSpinnerIcon || bookInfoOptions.busy\"\n        v-bind:icon=\"bookInfoOptions.ui.actionBtnSpinnerIcon\"\n        class=\"loadingSpinner\"\n      ></ion-spinner>\n    </button>\n\n    \n    <a\n      v-else-if=\"bookInfoOptions.book.redirect\"\n      :class=\"[bookInfoOptions.ui.actionBtnStyle]\"\n      :href=\"bookInfoOptions.book.redirect\"\n      target=\"_blank\"\n      class=\"button button-positive\"\n    >\n      <span v-html=\"bookInfoOptions.ui.actionBtnLabel\"></span>\n      <ion-spinner\n        v-if=\"bookInfoOptions.ui.actionBtnSpinnerIcon\"\n        v-bind:icon=\"bookInfoOptions.ui.actionBtnSpinnerIcon\"\n        class=\"loadingSpinner\"\n      ></ion-spinner>\n    </a>\n\n    \n    <a\n      v-else\n      :class=\"[bookInfoOptions.ui.actionBtnStyle]\"\n      v-bind:href=\"bookInfoOptions.book.redirect\"\n      v-bind:target=\"bookInfoOptions.book.redirect?'_blank':''\"\n      class=\"button button-positive\"\n      v-on:click.prevent=\"[actionBtnClicked(bookInfoOptions.book)]\"\n    >\n      <span v-html=\"bookInfoOptions.ui.actionBtnLabel\"></span>\n      <ion-spinner\n        v-if=\"bookInfoOptions.ui.actionBtnSpinnerIcon\"\n        v-bind:icon=\"bookInfoOptions.ui.actionBtnSpinnerIcon\"\n        class=\"loadingSpinner\"\n      ></ion-spinner>\n    </a>\n\n    \n    <button\n      v-if=\"(desktop||native) && (bookInfoOptions.book.openmethod=='downloadandopen') && ((bookInfoOptions.book.privilege == 'download') || (bookInfoOptions.book.privilege == 'open') && !bookInfoOptions.book.downloaded)\"\n      class=\"button openRemotelyBtn button-positive\"\n      type=\"button\"\n      v-on:click.prevent=\"openRemotely(bookInfoOptions.book)\"\n    >\n      {{'openRemotely'|t(data.lc)}}\n    </button>\n\n    \n    <a href v-if=\"view.bookinfo && view.bookinfo.promocode\" class=\"redeemCodeBtn xbutton xbutton-royal\" v-on:click.prevent=\"redeemCodeClicked()\">\n      <span class=\"kb-price-tag priceIcon\"></span>\n      <span class=\"label\">{{'redeemCode'|t(data.lc)}}</span>\n    </a>\n  </div>\n</div>\n","templates/panels/bookInfoAuthor.html":"<div>\n    \n    <span v-if=\"$parent.metaExists(book.meta.dc.creator)\" v-html=\"unsafe(book.meta.dc.creator)\"></span>\n    \n</div>","templates/panels/bookInfoBanner.html":"<div id=\"bookInfoBanner\" v-if=\"!busy\">\n  <div class=\"bgImgContainer\">\n    <div class=\"bgImg\" :style=\"bgImg\">\n    </div>\n  </div>\n  \n  \n  <div class=\"bookCover\" :style=\"{height: (coverHeight && matchMedia ? coverHeight+'px' : '' )}\">\n    <img :src=\"getUrl\" alt=\"book cover\">\n  </div>\n\n  <section ref=\"section\">\n    <div class=\"bookInfo\">\n      <div>\n        \n        <h1 v-html=\"unsafe(bookInfoOptions.book.name)\" class=\"name\" :style=\"[interactiveExists ? {width: 'calc(100% - 50px)'} : '']\"></h1>\n        \n        \n        <span v-if=\"interactiveExists\" class=\"interactive\"><i class=\"icon kb-interactive\"></i></span>\n      </div>\n\n      \n      <div class=\"author\">\n        <bookInfoAuthor></bookInfoAuthor>\n      </div>\n\n      <div class=\"innerInfo\">\n        \n        <div class=\"general\">\n          \n          <span v-if=\"false\" class=\"pages\">\n            <i class=\"icon kb-pages\"></i>\n             <span>200 Pages</span>\n          </span>\n          <span class=\"lang\" v-if=\"metaExists(bookInfoOptions.book.meta.dc.lang)\">\n            <i class=\"icon kb-language size18\"></i>\n            <span>{{bookInfoOptions.book.meta.dc.lang|langFromId}}</span>\n          </span>\n        </div>\n\n        \n        <div class=\"views\">\n          <span v-if=\"!(desktop||native) && (views>1)\">\n            <i class=\"icon kb-eye size18\"></i>\n            <span>{{'views'|t(data.lc,{count:views})}}</span>\n          </span>\n          <span v-if=\"(desktop||native) && (downloads>1)\">\n            <i class=\"icon kb-eye size18\"></i>\n            <span>{{'views'|t(data.lc,{count:downloads})}}</span>\n          </span>\n        </div>\n\n        \n        <div v-if=\"config.kotobee.reviewsrating && !loadingReviews\" class=\"reviews\">\n          <stars class=\"stars inlineBlock\" :defaultRating=\"rating?rating:0\"></stars>\n          <span class=\"num\" v-if=\"totalRating\">{{totalRating}} {{'reviews'|t(data.lc)}}</span>\n        </div>\n\n        \n        \n        <div v-if=\"bookContentExists\" class=\"bookContent\">\n          <span class=\"title\">Content</span>\n          <span><i class=\"icon kb-audio\"></i></span>\n          <span><i class=\"icon kb-video\"></i></span>\n          <span><i class=\"icon kb-gallery\"></i></span>\n          <span><i class=\"icon kb-questions\"></i></span>\n          <span><i class=\"icon kb-widgets\"></i></span>\n        </div>\n      </div>\n    </div>\n\n    <bookInfoActionBtns class=\"largeScreen\"></bookInfoActionBtns>\n  </section>\n\n  <bookInfoActionBtns class=\"smallScreen\"></bookInfoActionBtns>\n\n  <div class=\"headerBtns\">\n    \n    <button\n        class=\"button floatRight button-small button-clear button-stable\"\n        type=\"button\"\n        v-if=\"(config.kotobee.mode=='library') && config.copyBookURLBtn && !native && !desktop\"\n        v-on:click.prevent=\"copyURL(bookInfoOptions.book)\"\n    >\n        <span class=\"icon kb-copy size24\"></span>\n    </button>\n\n    \n    <button\n        class=\"button floatRight button-small toggleFavBtn button-clear button-stable\"\n        type=\"button\"\n        v-if=\"config.showFavorites && !cloudParam('public') && data.user.loggedIn && !bookInfoOptions.noAction\"\n        v-on:click.prevent=\"toggleFav(bookInfoOptions.book)\"\n    >\n        <span class=\"icon kb-heart size26 outline\" v-if=\"!bookInfoOptions.book.favorite\"></span>\n        <span class=\"icon kb-heart size26\" v-if=\"bookInfoOptions.book.favorite\"></span>\n    </button>\n    </div>\n\n  \n  <div\n    class=\"progessBar\"\n    v-bind:class=\"[(bookInfoOptions.ui.actionBtnStyle=='downloadingBtn')?'show':'']\"\n    v-if=\"(desktop||native) && data.info && data.info['book'+bookInfoOptions.book.id]\"\n  >\n    <span v-bind:style=\"{'width':data.info['book'+bookInfoOptions.book.id].progress+'%'}\"></span>\n  </div>\n</div>\n","templates/panels/bookInfoMeta.html":"<div id=\"bookInfoMeta\" :style=\"[reviews && !reviews.length ? {'border-bottom': '0'} : '']\">\n  \n  <div v-if=\"!bookInfoOptions.noAction\" class=\"otherOptions\">\n    <div class=\"vSpace10\"></div>\n    <div v-if=\"pageview && bookInfoOptions.book.privilege=='buy'\" class=\"cloudEbookLoginContainer\">\n      <span>{{'haveAnAccount'|t(data.lc)}}</span>\n      <button class=\"cloudEbookLoginBtn button button-positive\" v-on:click.prevent=\"toLoginScreen()\">{{'loginNow'|t(data.lc)}}</button>\n    </div>\n\n    <div v-if=\"bookInfoOptions.updateAvailable\" class=\"updateAvailable\">\n      <button\n        class=\"button updateBtn button-positive\"\n        type=\"button\"\n        v-on:click.prevent=\"update(bookInfoOptions.book)\"\n        v-bind:class=\"[bookInfoOptions.book.updating?'fadeOut':'']\"\n      >\n        <span class=\"kb-download\"></span>\n        {{'updateAvailable'|t(data.lc)}}\n      </button>\n    </div>\n\n    <div v-if=\"deletable\">\n      <button class=\"button button-assertive deleteBtn noBorder\" type=\"button\" v-on:click.prevent=\"deleteBook(bookInfoOptions.book)\">\n        <span class=\"kb-close closeIcon\"></span>\n        <span>{{'deleteBook'|t(data.lc)}}</span>\n      </button>\n    </div>\n    <div class=\"vSpace10\"></div>\n  </div>\n\n  \n  <div class=\"overview\">\n    \n    \n    \n    <div class=\"desc\">\n      <p v-if=\"bookInfoOptions.book.meta.dc.description\" v-html=\"filterDescription(bookInfoOptions.book.meta.dc.description)\"></p>\n      <p v-else>\n        <span class=\"icon kb-notice\"></span>\n        {{'noDescriptionAvailable'|t(data.lc)}}\n      </p>\n    </div>\n\n    \n    <div class=\"bookCategory\" v-if=\"config.showCategories\">\n      <a\n        v-if=\"bookInfoOptions.book.categories\"\n        v-for=\"category in bookInfoOptions.book.categories\"\n        v-on:click.prevent=\"categoryClicked(category)\"\n        class=\"xcategory\"\n      >\n        {{category.name}}\n      </a>\n      <a\n        v-if=\"bookInfoOptions.book.category && !bookInfoOptions.book.categories\"\n        v-on:click.prevent=\"categoryClicked(bookInfoOptions.book.category)\"\n        class=\"xcategory\"\n      >\n        {{bookInfoOptions.book.category.name}}\n      </a>\n    </div>\n  </div>\n\n  \n  <div class=\"meta\">\n    \n    <div v-if=\"metaExists(bookInfoOptions.book.date)\" class=\"date\">\n      <span>{{'date'|t(data.lc)}}</span>\n      <strong>{{getDate(bookInfoOptions.book.date)}}</strong>\n    </div>\n    \n    <div v-if=\"metaExists(bookInfoOptions.book.meta.dc.publisher)\" class=\"publisher\">\n      <span>{{'publisher'|t(data.lc)}}</span>\n      <strong>{{bookInfoOptions.book.meta.dc.publisher}}</strong>\n    </div>\n    \n    <div v-if=\"metaExists(bookInfoOptions.book.meta.dc.rights)\" class=\"rights\">\n      <span>{{'rights'|t(data.lc)}}</span>\n      <strong>{{bookInfoOptions.book.meta.dc.rights}}</strong>\n    </div>\n    \n    <div v-if=\"metaExists(bookInfoOptions.book.meta.dc['isbn-id'])\" class=\"isbn\">\n      <span>{{'isbn'|t(data.lc)}}</span>\n      <strong>{{extractIsbn(bookInfoOptions.book.meta.dc['isbn-id'])}}</strong>\n    </div>\n  </div>\n</div>\n","templates/panels/bookInfoReviews.html":"<div id=\"bookInfoReviews\" class=\"reviews\" v-if=\"config.kotobee.cloudid && config.kotobee.reviews\">\n  <h4 v-if=\"config.kotobee.reviewsrating\" class=\"title\">{{'reviews'|t(data.lc)}}</h4>\n\n  <div class=\"mainWrapper\">\n    \n    <div class=\"total\" v-if=\"config.kotobee.reviewsrating && !loadingReviews && reviews.length\">\n      <div>\n        \n        <section class=\"num\">{{finalRate}}</section>\n        \n        <section class=\"rating\">\n          <stars class=\"stars inlineBlock\" :defaultRating=\"rating?rating:0\"></stars>\n          <div class=\"wrapper\">\n            <span class=\"reviewsNum\" v-if=\"reviewsNum\">{{reviewsNum}} {{'reviews'|t(data.lc)}}</span>\n            <span class=\"ratingNum\" v-if=\"totalRating\">{{totalRating}} {{'ratings'|t(data.lc)}}</span>\n          </div>\n        </section>\n      </div>\n    </div>\n\n    \n    <div class=\"reviewForm\" v-if=\"canShowReviewForm\">\n      <h4 v-if=\"reviews && !reviews.length\">{{'beFirstToReview'|t(data.lc)}}</h4>\n      <h4 v-else>{{'whatDoYouThink'|t(data.lc)}}</h4>\n      \n      <div v-if=\"config.kotobee.reviewsrating\">\n        <stars class=\"stars inlineBlock\" :defaultRating=\"0\" type=\"input\" v-on:selected=\"selectRating\"></stars>\n      </div>\n      \n      <textarea v-bind:placeholder=\"'enterReview'|t(data.lc)\"></textarea>\n      <div v-if=\"!data.user || !data.user.loggedIn\">\n          <input type=\"text\" class=\"nameTxt\" v-on:keyup.enter=\"submitReview($event)\" v-bind:placeholder=\"'enterName'|t(data.lc)\">\n      </div>\n      \n      <button class=\"button button-positive\" v-on:click.prevent=\"submitReview($event)\">{{'submit'|t(data.lc)}}</button>\n    </div>\n    <div class=\"clear\"></div>\n  </div>\n  \n  \n  <div class=\"reviewList\">\n    \n    <div class=\"loaderWrapper\" v-if=\"loadingReviews\">\n      <span class=\"icon size16 kb-spin kb-notch\"></span>\n    </div>\n    <div v-else class=\"content\">\n      \n      <div class=\"entry\" v-for=\"review in reviews\">\n        <section>\n          <div class=\"creator\">\n             <span v-if=\"review.username\">\n                {{review.username}}\n             </span>\n             <span v-else-if=\"review.name\">\n                {{review.name}}\n             </span>\n             <span v-else>\n                {{'anonymous'|t(data.lc)}}\n            </span>\n          </div>\n          <div v-if=\"config.kotobee.reviewsrating\">\n            <stars class=\"stars inlineBlock\" :defaultRating=\"review.rating?review.rating:0\"></stars>\n          </div>\n        </section>\n\n        <p class=\"date\">{{review.date|dateTimeFromServer}}</p>\n        <p class=\"content\">{{review.content}}</p>\n        \n      </div>\n\n      \n      <div class=\"getAllReviewsWrapper\" v-if=\"totalReviews > reviews.length\">\n        <button class=\"button button-positive button-small\" v-on:click.prevent=\"getAllReviews()\">{{'loadAllReviews'|t(data.lc)}}</button>\n      </div>\n    </div>\n  </div>\n</div>\n","templates/panels/bookSettings.html":"<div ng-controller=\"BookSettingsCtrl\" ng-init=\"init()\">\n    \n    <ion-header-bar>\n        <button class=\"button button-icon button-clear\" v-on:click.prevent=\"closePanel()\">\n            <span class=\"icon kb-close size32 vAlignTop\"></span>\n        </button>\n        <h1 class=\"title\">{{'settings'|t(data.lc)}}</h1>\n        \n    </ion-header-bar>\n    <ion-content class=\"content has-header\" :overflow-scroll=\"true\">\n        \n        <div v-if=\"data.user && data.user.loggedIn\" class=\"userInfo item\" v-bind:class=\"[data.user.name?'hasName':'noName']\">\n            <img class=\"floatLeft\" style=\"margin:0 8px;min-height:40px\" v-bind:src=\"'https://pikmail.herokuapp.com/'+data.user.email+'?size=55'\" hide-fallback=\"\" onerror=\"this.parentNode.removeChild(this)\">\n            <div class=\"vSpace5\"></div>\n            <span v-if=\"data.user.name\">{{data.user.name}} <br/></span>\n            <h3 class=\"email\">{{data.user.email}}</h3>\n            <div class=\"vSpace5\"></div>\n            <a href=\"#\" class=\"viewProfile\" v-on:click.prevent=\"profileClicked($event)\">{{'viewProfile'|t(data.lc)}}</a>\n        </div>\n\n        <ion-toggle v-if=\"config.styleControls\" v-model=\"data.settings.styling\" toggle-class=\"toggle-calm\" v-on:change=\"stylingChanged()\">{{'useStyling'|t(data.lc)}}\n        </ion-toggle>\n        <div v-if=\"(data.book.chapter.layout=='rfl') && config.textSizeControls\" class=\"item item-button-right\">\n            {{'fontSize'|t(data.lc)}}: <strong>{{data.settings.textSize}}</strong>\n            <button type=\"button\" class=\"button button-positive fontSizeBtn\" v-on:click.prevent=\"editFontSize()\">\n                {{'edit'|t(data.lc)}}..\n            </button>\n        </div>\n        <ion-toggle v-if=\"config.styleControls\" v-model=\"data.settings.lineAdjust\" toggle-class=\"toggle-calm\" v-on:change=\"lineAdjustChanged()\">\n            {{'autoAdjustLineHeight'|t(data.lc)}}\n        </ion-toggle>\n        <a href class=\"ion-item item\" v-if=\"data.user && data.user.email && cloudParam('sync')\" v-on:click.prevent=\"sync()\">\n                {{'syncwithServer'|t(data.lc)}}\n            </a>\n        <a href class=\"ion-item item\" item=\"item\" v-if=\"!native\" v-on:click.prevent=\"fullscreen()\">\n                {{'fullscreen'|t(data.lc)}}\n            </a>\n\n        <label class=\"item item-input item-select\" v-if=\"config.viewModeOptional\">\n                <div class=\"input-label\">\n                    {{'viewMode'|t(data.lc)}}\n                </div>\n                <select v-model=\"data.settings.viewMode\" v-on:change=\"viewModeChanged()\">\n                    <option v-for=\"viewMode in viewModes\" v-on:selected=\"viewMode.val==data.settings.viewMode\"\n                            v-bind:value=\"viewMode.val\">{{viewMode.label|t(data.lc)}}\n                    </option>\n                </select>\n            </label>\n\n        <ion-toggle v-if=\"(data.settings.viewMode!='double') && config.pageScrollOptional\" v-model=\"data.settings.pageScroll\" toggle-class=\"toggle-calm\" v-on:change=\"singlePageScrollChanged()\">\n            {{'scroll'|t(data.lc)}}\n        </ion-toggle>\n\n        <label class=\"item item-input item-select\" v-if=\"config.navAnimOptional\">\n                <div class=\"input-label\">\n                    {{'pageAnim'|t(data.lc)}}\n                </div>\n                <select v-model=\"data.settings.navAnim\" v-on:change=\"navAnimChanged()\">\n                    <option v-for=\"navAnim in navAnims\" v-on:selected=\"navAnim.val==data.settings.navAnim\"\n                            v-bind:value=\"navAnim.val\">{{navAnim.label|t(data.lc)}}\n                    </option>\n                </select>\n            </label>\n\n        \n\n        <label class=\"item item-input item-select\" v-if=\"config.languageOptional\">\n                <div class=\"input-label\">\n                    {{'interfaceLanguage'|t(data.lc)}}\n                </div>\n                <select v-model=\"data.settings.language\" v-on:change=\"langChanged()\">\n                    <option v-for=\"lang in data.languages\" v-on:selected=\"lang.val==data.settings.language\"\n                            v-bind:value=\"lang.val\">{{lang.label}}\n                    </option>\n                </select>\n            </label>\n        <a href class=\"ion-item item\" v-if=\"config.shareTab && (!native && !desktop && !readerApp)\" v-on:click.prevent=\"share()\">\n            <i class=\"icon kb-share\"></i> {{'share'|t(data.lc)}}\n        </a>\n        \n        <template v-if=\"data.user && (config.kotobee.mode!='library')\">\n            <a href class=\"ion-item item\" v-if=\"data.user.pwd || data.user.code|| data.user.loggedIn\" v-on:click.prevent=\"logout\">\n                {{'logout'|t(data.lc)}}\n            </a>\n        </template>\n\n        \n\n        \n        {{webWatermarkPlaceholder2}}\n\n        <div class=\"vSpace20\"></div>\n\n        \n    </ion-content>\n    \n</div>","templates/panels/bookSettings.mobile.html":"<div ng-controller=\"BookSettingsCtrl\" ng-init=\"init()\">\n    \n    <ion-header-bar align-title=\"center\">\n        <a class=\"button button-icon button-clear\" v-on:click.prevent=\"closePanel()\">\n            <span class=\"icon kb-close size32 vAlignTop\"></span>\n        </a>\n        <h1 class=\"title\">{{'settings'|t(data.lc)}}</h1>\n        \n    </ion-header-bar>\n    <ion-content has-bouncing=\"true\" :scroll=\"true\" direction=\"y\" :overflow-scroll=\"false\">\n        <ion-list>\n            <div v-if=\"data.user && data.user.loggedIn\" class=\"userInfo item\" v-bind:class=\"[data.user.name?'hasName':'noName']\">\n                <img class=\"floatLeft\" style=\"margin:0 8px;min-height:40px\" v-bind:src=\"'https://pikmail.herokuapp.com/'+data.user.email+'?size=55'\" hide-fallback=\"\" onerror=\"this.parentNode.removeChild(this)\">\n                <div class=\"vSpace5\"></div>\n                <span v-if=\"data.user.name\">{{data.user.name}} <br/></span>\n                <h3 class=\"email\">{{data.user.email}}</h3>\n                <div class=\"vSpace5\"></div>\n                <a href=\"#\" class=\"viewProfile\" v-on:click.prevent=\"profileClicked($event)\">{{'viewProfile'|t(data.lc)}}</a>\n            </div>\n\n            <ion-toggle v-if=\"config.styleControls\" v-model=\"data.settings.styling\" toggle-class=\"toggle-calm\" v-on:change=\"stylingChanged()\">{{'useStyling'|t(data.lc)}}\n            </ion-toggle>\n            <div v-if=\"(data.book.chapter.layout=='rfl') && config.textSizeControls\" class=\"item item-button-right\">\n                {{'fontSize'|t(data.lc)}}: <strong>{{data.settings.textSize}}</strong>\n                <button class=\"button button-positive fontSizeBtn\" v-on:click.prevent=\"editFontSize()\">\n                    {{'edit'|t(data.lc)}}..\n                </button>\n            </div>\n            <ion-toggle v-if=\"config.styleControls\" v-model=\"data.settings.lineAdjust\" toggle-class=\"toggle-calm\" v-on:change=\"lineAdjustChanged()\">\n                {{'autoAdjustLineHeight'|t(data.lc)}}\n            </ion-toggle>\n            <a href class=\"ion-item item\" v-if=\"data.user && data.user.email && cloudParam('sync')\" v-on:click.prevent=\"sync()\">\n                {{'syncwithServer'|t(data.lc)}}\n            </a>\n            <a href class=\"ion-item item\" item=\"item\" v-if=\"!native && !ios\" v-on:click.prevent=\"fullscreen()\">\n                {{'fullscreen'|t(data.lc)}}\n            </a>\n\n            <label class=\"item item-input item-select\" v-if=\"config.viewModeOptional\">\n                <div class=\"input-label\">\n                    {{'viewMode'|t(data.lc)}}\n                </div>\n                <select v-model=\"data.settings.viewMode\" v-on:change=\"viewModeChanged()\">\n                    <option v-for=\"viewMode in viewModes\" v-on:selected=\"viewMode.val==data.settings.viewMode\"\n                            v-bind:value=\"viewMode.val\">{{viewMode.label|t(data.lc)}}\n                    </option>\n                </select>\n            </label>\n\n            <ion-toggle v-if=\"(data.settings.viewMode!='double') && config.pageScrollOptional\" v-model=\"data.settings.pageScroll\" toggle-class=\"toggle-calm\" v-on:change=\"singlePageScrollChanged()\">\n                {{'scroll'|t(data.lc)}}\n            </ion-toggle>\n\n            <label class=\"item item-input item-select\" v-if=\"config.navAnimOptional\">\n                <div class=\"input-label\">\n                    {{'pageAnim'|t(data.lc)}}\n                </div>\n                <select v-model=\"data.settings.navAnim\" v-on:change=\"navAnimChanged()\">\n                    <option v-for=\"navAnim in navAnims\" v-on:selected=\"navAnim.val==data.settings.navAnim\"\n                            v-bind:value=\"navAnim.val\">{{navAnim.label|t(data.lc)}}\n                    </option>\n                </select>\n            </label>\n\n            <label class=\"item item-input item-select\" v-if=\"config.languageOptional\">\n                <div class=\"input-label\">\n                    {{'interfaceLanguage'|t(data.lc)}}\n                </div>\n                <select v-model=\"data.settings.language\" v-on:change=\"langChanged()\">\n                    <option v-for=\"lang in data.languages\" v-on:selected=\"lang.val==data.settings.language\"\n                            v-bind:value=\"lang.val\">{{lang.label}}\n                    </option>\n                </select>\n            </label>\n            <a href class=\"ion-item item\" v-if=\"config.shareTab && (!native && !desktop && !readerApp)\" v-on:click.prevent=\"share()\">\n                <i class=\"icon kb-share\"></i> {{'share'|t(data.lc)}}\n            </a>\n            \n            <template v-if=\"data.user && (config.kotobee.mode!='library')\">\n                <a href class=\"ion-item item\" v-if=\"data.user.pwd || data.user.code|| data.user.loggedIn\" v-on:click.prevent=\"logout()\">\n                    {{'logout'|t(data.lc)}}\n                </a>\n            </template>\n\n            \n\n            \n            {{webWatermarkPlaceholder2}}\n\n            \n\n            <div class=\"vSpace20\"></div>\n\n        </ion-list>\n    </ion-content>\n    \n</div>","templates/panels/bookUpdateAvailable.html":"<div>\n    <ion-header-bar align-title=\"center\" class=\"bar-positive\">\n        <h1 class=\"title\">{{'updateAvailable'|t(data.lc)}}</h1>\n        <button class=\"button button-icon button-clear\" v-on:click.prevent=\"hideModal()\">\n            <span class=\"icon kb-close size32 vAlignTop\"></span>\n        </button>\n    </ion-header-bar>\n    <ion-content class=\"padding\" :scroll=\"false\">\n        \n            \n        <h5 class=\"center\">{{'updateAvailableDesc'|t(data.lc)}}</h5>\n\n    </ion-content>\n    <ion-footer-bar>\n        <div class=\"row\">\n            <div class=\"col\">\n                <button class=\"button button-block button-positive zeroMargin openBtn\"\n                        v-on:click.prevent=\"modal.updateAvailableOptions.update(modal.updateAvailableOptions.book);hideModal()\">\n                    <i class=\"icon kb-book-download size20\" style=\"margin-right:5px\"></i> {{'update'|t(data.lc)}}</button>\n            </div>\n            <div class=\"col\">\n                <button class=\"button button-block button-light zeroMargin\"\n                        v-on:click.prevent=\"modal.updateAvailableOptions.open(modal.updateAvailableOptions.book);hideModal()\">\n                    <i class=\"icon kb-book-open size20\" style=\"margin-right:5px\"></i> {{'openBook'|t(data.lc)}}</button>\n            </div>\n            \n        </div>\n    </ion-footer-bar>\n</div>","templates/panels/cart.html":"<div ng-controller=\"CartCtrl\" class=\"cartPanel\">\n  \n  <ion-header-bar class=\"header\">\n      <h1 class=\"title\">{{'cartTitle'|t(data.lc)}}</h1>\n      <a class=\"button button-icon closeBtn button-clear\" v-on:click.prevent=\"hideModal()\">\n          <span class=\"icon kb-close size32 vAlignTop\"></span>\n      </a>\n  </ion-header-bar>\n\n  \n  <ion-content class=\"has-header has-footer\">\n      <div class=\"cartContent\" v-bind:class=\"[!data.cartData.length?'empty':'']\">\n        \n        <div v-if=\"!data.cartData.length\" class=\"cartContent__hint\">\n          <p>{{'cartEmptyHint'|t(data.lc)}}</p>\n          <button class=\"button button-block button-positive\" v-on:click.prevent=\"hideModal()\">{{'cartContinue'|t(data.lc)}}</button>\n        </div>\n\n        \n        <div v-if=\"data.cartData.length\">\n            <div class=\"cart__header\">\n              <span class=\"cart__header__title\">{{'cartItem'|t(data.lc)}}</span>\n              <span class=\"cart__header__price\">{{'cartPrice'|t(data.lc)}}</span>\n            </div>\n            <div class=\"clear\"></div>\n            <div class=\"cartContent__books\">\n              <div class=\"orderItem card\" v-for=\"book in data.cartData\">\n                \n                <img v-bind:src=\"book.bookImgUrl\" v-bind:alt=\"book.bookName\" />\n                \n                <h4>{{book.bookName}}</h4>\n                \n                <span>{{currency}}{{book.bookPrice}}</span>\n                \n                <button v-on:click.prevent=\"removeBook(book.bookId)\">\n                  <i class=\"kb-close size24\"></i>\n                </button>\n              </div>\n            </div>\n        </div>\n      </div>\n  </ion-content>\n  \n  \n  <ion-footer-bar v-if=\"data.cartData.length\">\n      <div class=\"row\">\n        <div class=\"col\">\n            <a class=\"button button-block button-positive zeroMargin\" v-on:click.prevent=\"showCheckout()\">\n                {{'checkoutBtn'|t(data.lc,{price:currency+getTotalPrice()})}}\n            </a>\n        </div>\n      </div>\n  </ion-footer-bar>\n\n</div>\n","templates/panels/chapters.html":"<div id=\"chapterMenu\">\n    <button class=\"button button-icon button-clear closeBtn\" v-on:click.prevent=\"closePanel($event)\">\n        <span class=\"icon kb-close size32 vAlignTop\"></span>\n    </button>\n    <div id=\"chaptersBook\" v-on:click.prevent=\"showBookInfo()\">\n        <div class=\"thumb\" v-bind:style=\"{'background-image':'url(\\''+bookThumb+'\\')'}\"></div>\n        <div class=\"info\" v-if=\"data.book.meta\">\n            <div class=\"title\" v-html=\"unsafe(data.book.meta.dc.title)\"></div>\n            <div class=\"subject\" v-html=\"unsafe(data.book.meta.dc.subject)\"></div>\n            <div class=\"author\" v-if=\"metaExists(data.book.meta.dc.creator)\" v-html=\"unsafe(data.book.meta.dc.creator)\"></div>\n            <div class=\"gotoPageContainer\" v-if=\"config.gotoPageBtn && data.book.chapter.layout=='fxl'\">\n                <a href=\"#\" v-on:click.prevent=\"gotoPageClicked($event)\" class=\"gotoPageBtn button button-dark button-small\">\n                    <span class=\"icon kb-file-fill size12\"></span>\n                    {{'page'|t(data.lc)}} {{data.currentPageOb.index+1-(config.gotoPageOffset?config.gotoPageOffset:0)}}\n                </a>\n            </div>\n        </div>\n        <div class=\"clear\"></div>\n    </div>\n    <div class=\"bar bar-dark\">\n        <h1 class=\"title fullWidth size16\" v-bind:class=\"[data.settings.rtl?'title-right':'title-left']\">\n            <span class=\"icon kb-list size14\"></span>\n            {{'toc'|t(data.lc)}}</h1>\n        <div class=\"buttons\">\n        </div>\n    </div>\n    <div id=\"chaptersContent\" class=\"has-footer\" :overflow-scroll=\"true\">\n            <div class=\"vSpace5\"></div>\n            <div id=\"chaptersInner\">\n            </div>\n            <div class=\"vSpace20\"></div>\n    </div>\n</div>","templates/panels/chapters.mobile.html":"<div id=\"chapterMenu\">\n    <button class=\"button button-icon button-clear closeBtn\" v-on:click.prevent=\"closePanel($event)\">\n        <span class=\"icon kb-close size32 vAlignTop\"></span>\n    </button>\n    <div id=\"chaptersBook\" v-on:click.prevent=\"showBookInfo()\">\n        <div class=\"thumb\" v-bind:style=\"{'background-image':'url(\\''+bookThumb+'\\')'}\"></div>\n        <div class=\"info\" v-if=\"data.book.meta\">\n            <div class=\"title\" v-html=\"unsafe(data.book.meta.dc.title)\"></div>\n            <div class=\"subject\" v-html=\"unsafe(data.book.meta.dc.subject)\"></div>\n            <div class=\"author\" v-if=\"metaExists(data.book.meta.dc.creator)\" v-html=\"unsafe(data.book.meta.dc.creator)\"></div>\n            <div class=\"gotoPageContainer\" v-if=\"config.gotoPageBtn && data.book.chapter.layout=='fxl'\">\n                <a href=\"#\" v-on:click.prevent=\"gotoPageClicked($event)\" class=\"gotoPageBtn button button-dark button-small\">\n                    <span class=\"icon kb-file-fill size12\"></span>\n                    {{'page'|t(data.lc)}} {{data.currentPageOb.index+1-(config.gotoPageOffset?config.gotoPageOffset:0)}}\n                </a>\n            </div>\n        </div>\n        <div class=\"clear\"></div>\n    </div>\n    <div class=\"bar bar-dark\">\n        <h1 class=\"title fullWidth size16\" v-bind:class=\"[data.settings.rtl?'title-right':'title-left']\">\n            {{'toc'|t(data.lc)}}</h1>\n        <div class=\"buttons\">\n        </div>\n    </div>\n    <div id=\"chaptersContent\">\n        <ion-content :overflow-scroll=\"false\" :autoscroll=\"false\" :scroll=\"true\" direction=\"y\" zooming=\"false\"\n                     has-bouncing=\"true\" delegate-handle=\"chapters\" class=\"has-footer\">\n            <ion-list can-swipe=\"listCanSwipe\">\n                <div class=\"vSpace5\"></div>\n                <div id=\"chaptersInner\">\n                </div>\n                <div class=\"vSpace20\"></div>\n                <div class=\"vSpace20\"></div>\n                <div class=\"vSpace20\"></div>\n            </ion-list>\n\n        </ion-content>\n    </div>\n</div>","templates/panels/checkout.html":"<div ng-controller=\"CartCtrl\" class=\"checkoutPanel\">\n  <ion-header-bar>\n    <button v-on:click.prevent=\"backToCart()\" class=\"button button-stable backToCartBtn\">{{'backToCartBtn'|t(data.lc)}}</button>\n    <h1 class=\"title\">{{'checkoutTitle'|t(data.lc)}}</h1>\n    <a class=\"button button-icon closeBtn button-clear\" v-on:click.prevent=\"hideModal()\">\n        <span class=\"icon kb-close size32 vAlignTop\"></span>\n    </a>\n  </ion-header-bar>\n\n  \n  <ion-content>\n      <div class=\"checkoutContent\">\n        <div class=\"checkoutContent__inner\">\n          <p v-if=\"getTotalPrice()\" class=\"checkoutContent__inner__totalPrice\">{{'total'|t(data.lc)}} <span>{{currency}}{{getTotalPrice()}}</span></p>\n          <h4 class=\"checkoutContent__inner__title\">{{'selectPaymentMethod'|t(data.lc)}}</h4>\n          <div class=\"checkoutContent__inner__btns\">\n            <div :style=\"{ marginTop: paymentOptions.none ? '100px': ''}\" class=\"paymentOptions\" v-bind:class=\"[paying?'paying':'']\">\n                <noPayments v-if=\"paymentOptions.none\"></noPayments>\n                <template v-else>\n                    <div v-if=\"paymentOptions.stripe\" id=\"stripe-button-wrapper\" class=\"paymentOption\">\n                        <a class=\"button button-positive\" v-on:click.prevent=\"makePayment('stripe')\" v-bind:class=\"['stripe']\">\n                            <ion-spinner v-if=\"paying=='stripe'\" icon=\"dots\" color=\"#aaa\" class=\"loadingSpinner\"></ion-spinner>\n                        </a>\n                    </div>\n                    <div v-if=\"paymentOptions.paypal\" id=\"paypal-button-container\" class=\"paymentOption\">\n                    </div>\n                    <div v-if=\"paymentOptions.moyasar\" id=\"moyasar-button-wrapper\" class=\"paymentOption\">\n                        <a class=\"button button-positive\" v-on:click.prevent=\"makePayment('moyasar')\" v-bind:class=\"['moyasar']\">\n                        </a>\n                    </div>\n                </template>\n            </div>\n          </div>\n        </div>\n      </div>\n  </ion-content>\n\n</div>","templates/panels/confirm.html":"<div>\n    <ion-header-bar align-title=\"center\" v-bind:class=\"[modal.confirmOptions.barStyle?modal.confirmOptions.barStyle:'bar-assertive']\">\n        <h1 class=\"title\">{{modal.confirmOptions.title|t(data.lc)}}</h1>\n        <button type=\"button\" class=\"button button-icon button-clear\" v-on:click.prevent=\"hideModal()\">\n            <span class=\"icon kb-close size32 vAlignTop\"></span>\n        </button>\n    </ion-header-bar>\n    <ion-content class=\"padding\" :scroll=\"false\">\n        <h5 class=\"center\">{{modal.confirmOptions.msg|t(data.lc)}}</h5>\n        <div class=\"dontAskAgain\" v-if=\"modal.confirmOptions.dontAsk\">\n            <div class=\"checkbox\">\n                <ion-checkbox v-model=\"modal.confirmOptions.dontAskChecked\">\n                    {{'dontAskAgain'|t(data.lc)}}\n                </ion-checkbox>\n            </div>\n        </div>\n    </ion-content>\n    <ion-footer-bar>\n        <div class=\"row\">\n            <div class=\"col\" v-for=\"btn in modal.confirmOptions.btns\">\n                <button type=\"button\" class=\"button button-block zeroMargin\" v-bind:class=\"[btn.style?btn.style:'']\" v-on:click.prevent=\"btn.func()\">\n                    {{btn.label|t(data.lc)}}\n                </button>\n            </div>\n        </div>\n    </ion-footer-bar>\n</div>","templates/panels/encryptionKey.html":"<div>\n    <ion-header-bar align-title=\"center\" class=\"bar-dark\">\n        <h1 class=\"title\">{{'encryptionPwd'|t(data.lc)}}</h1>\n        <button type=\"button\" class=\"button button-icon button-clear\" v-on:click.prevent=\"hideModal()\">\n            <span class=\"icon kb-close size32 vAlignTop\"></span>\n        </button>\n    </ion-header-bar>\n    <ion-content class=\"padding\" :scroll=\"false\">\n        <p>{{'enterEbookEncryptionPwd'|t(data.lc)}}</p>\n        <div class=\"list\" style=\"margin-bottom:2px\">\n            <label class=\"item item-input\">\n                <input type=\"password\" v-model=\"modal.encryptionOtions.model.pwd\" v-on:keyup.enter=\"modal.encryptionOtions.submit(modal.encryptionOtions.model.pwd)\"\n                       v-autofocus v-bind:placeholder=\"'password'|t(data.lc)\">\n            </label>\n        </div>\n    </ion-content>\n    <ion-footer-bar>\n        <div class=\"row\">\n            <div class=\"col\">\n                <button type=\"button\" class=\"button button-block button-positive zeroMargin\"\n                        v-on:click.prevent=\"modal.encryptionOtions.submit(modal.encryptionOtions.model.pwd)\">{{'ok'|t(data.lc)}}\n                </button>\n            </div>\n            <div class=\"col\">\n                <button type=\"button\" class=\"button button-block button-light zeroMargin\" v-on:click.prevent=\"hideModal()\">{{'cancel'|t(data.lc)}}</button>\n            </div>\n        </div>\n    </ion-footer-bar>\n</div>","templates/panels/exit.html":"<div>\n    <ion-header-bar align-title=\"center\" class=\"bar-dark\">\n        <h1 class=\"title\">{{'exitConfirmation'|t(data.lc)}}</h1>\n        <button type=\"button\" class=\"button button-icon button-clear\" v-on:click.prevent=\"hideModal()\">\n            <span class=\"icon kb-close size32 vAlignTop\"></span>\n        </button>\n    </ion-header-bar>\n    <ion-content class=\"padding\" :scroll=\"false\">\n        <h5 class=\"center\">{{'exitConfirmationMsg'|t(data.lc)}}</h5>\n    </ion-content>\n    <ion-footer-bar>\n        <div class=\"row\">\n            <div class=\"col\">\n                <button type=\"button\" class=\"button button-block button-positive zeroMargin\" v-on:click.prevent=\"modal.exitOptions.exit()\">\n                    {{'yes'|t(data.lc)}}\n                </button>\n            </div>\n            <div class=\"col\">\n                <button type=\"button\" class=\"button button-block button-light zeroMargin\" v-on:click.prevent=\"hideModal()\">\n                    {{'cancel'|t(data.lc)}}\n                </button>\n            </div>\n        </div>\n    </ion-footer-bar>\n</div>","templates/panels/externalSite.html":"<div>\n    <ion-header-bar align-title=\"center\" class=\"bar-dark\">\n        <h1 class=\"title\">{{modal.siteOptions.title}}</h1>\n        <button type=\"button\" class=\"button button-icon button-clear\" v-on:click.prevent=\"hideModal()\">\n            <span class=\"icon kb-close size32 vAlignTop\"></span>\n        </button>\n    </ion-header-bar>\n    <ion-content has-bouncing=\"true\" :scroll=\"false\" v-bind:class=\"[modal.siteOptions.status]\">\n        <iframe v-bind:src=\"modal.siteOptions.src\" iframe-onload=\"modal.siteOptions.loaded()\" style=\"xzoom:0.60\" width=\"100%\" height=\"100%\" nwfaketop nwdisable\n                frameborder=\"0\"></iframe>\n        <div v-if=\"modal.siteOptions.status=='error'\">\n            {{'errorLoadingPage'|t(data.lc)}}\n        </div>\n    </ion-content>\n    <ion-footer-bar class=\"bar-light\">\n        <div class=\"row\">\n            <div class=\"col textCenter\">\n                <a class=\"button button-clear footerBtn\"\n                   v-bind:href=\"modal.siteOptions.nsrc?modal.siteOptions.nsrc:modal.siteOptions.src\"\n                   target=\"_blank\">\n                    <i class=\"inlineBlock icon kb-external-link size18\"></i>\n                    {{'openInNewWindow'|t(data.lc)}}</a>\n            </div>\n        </div>\n    </ion-footer-bar>\n</div>","templates/panels/externalSite.mobile.html":"<div>\n    <ion-header-bar align-title=\"center\" class=\"bar-dark\">\n        <h1 class=\"title\">{{modal.siteOptions.title}}</h1>\n        <button class=\"button button-icon button-clear\" v-on:click.prevent=\"hideModal()\">\n            <span class=\"icon kb-close size32 vAlignTop\"></span>\n        </button>\n    </ion-header-bar>\n    <ion-content has-bouncing=\"true\" :scroll=\"false\" v-bind:class=\"[modal.siteOptions.status]\">\n        <div class=\"iframeWrapper\">\n            <iframe v-bind:src=\"modal.siteOptions.src\" iframe-onload=\"modal.siteOptions.loaded()\" style=\"xzoom:0.60\" width=\"100%\" height=\"100%\" nwfaketop nwdisable\n                    frameborder=\"0\"></iframe>\n        </div>\n        <div v-if=\"modal.siteOptions.status=='error'\">\n            {{'errorLoadingPage'|t(data.lc)}}\n        </div>\n    </ion-content>\n    <ion-footer-bar class=\"bar-light\">\n        <div class=\"row\">\n            <div class=\"col textCenter\">\n                <a v-if=\"!native\" class=\"button button-clear footerBtn\"\n                   v-bind:href=\"modal.siteOptions.nsrc?modal.siteOptions.nsrc:modal.siteOptions.src\"\n                   target=\"_blank\">\n                    <i class=\"icon kb-external-link size18\"></i>\n                    {{'openInNewWindow'|t(data.lc)}}</a>\n                <a v-if=\"native\" class=\"button button-clear footerBtn\"\n                   v-on:click.prevent=\"modal.siteOptions.openInNativeBrowser(modal.siteOptions.nsrc?modal.siteOptions.nsrc:modal.siteOptions.src)\">\n                    <i class=\"inlineBlock icon kb-external-link size18\"></i>\n                    {{'openInBrowser'|t(data.lc)}}</a>\n            </div>\n        </div>\n    </ion-footer-bar>\n</div>","templates/panels/flashUnsupported.html":"<div>\n    <ion-header-bar align-title=\"center\" class=\"bar-dark\">\n        <h1 class=\"title\">{{'flashPluginMissing'|t(data.lc)}}</h1>\n        <button type=\"button\" class=\"button button-icon button-clear\" v-on:click.prevent=\"hideModal()\">\n            <span class=\"icon kb-close size32 vAlignTop\"></span>\n        </button>\n    </ion-header-bar>\n    <ion-content has-bouncing=\"true\" :padding=\"true\" :scroll=\"false\">\n        <div class=\"center\" v-html=\"t('flashPluginMsg',data.lc)\"></div>\n        <textarea id=\"clipboardText\" class=\"text-center\" rows=\"2\" cols=\"2\" v-autofocus>{{modal.flashOb.text}}</textarea>\n    </ion-content>\n</div>","templates/panels/generalError.html":"<div>\n    <ion-header-bar align-title=\"center\" class=\"bar-assertive\">\n        <h1 class=\"title\">{{'error'|t(data.lc)}}</h1>\n        <button type=\"button\" class=\"button button-icon button-clear\" v-on:click.prevent=\"hideModal(null, modal.errorOptions.cb)\">\n            <span class=\"icon kb-close size32 vAlignTop\"></span>\n        </button>\n    </ion-header-bar>\n    <ion-content class=\"padding\">\n        <h5 class=\"center\" v-html=\"injectSupportUrl(modal.errorOptions.msg)\"></h5>\n    </ion-content>\n    <ion-footer-bar v-if=\"modal.errorOptions.cb\">\n        <div class=\"row\">\n            <div class=\"col\">\n                <button type=\"button\" class=\"button button-block button-stable zeroMargin\" v-on:click.prevent=\"hideModal(null, modal.errorOptions.cb)\">{{'ok'|t(data.lc)}}</button>\n            </div>\n        </div>\n    </ion-footer-bar>\n</div>","templates/panels/gotoPage.html":"<div id=\"pdfSaveModal\">\n    <ion-header-bar align-title=\"center\" class=\"bar-dark\">\n        <h1 class=\"title\">{{'gotoPage'|t(data.lc)}}</h1>\n        <button type=\"button\" class=\"button button-icon button-clear\" v-on:click.prevent=\"hideModal()\">\n            <span class=\"icon kb-close size32 vAlignTop\"></span>\n        </button>\n    </ion-header-bar>\n    <ion-content class=\"padding\" :scroll=\"false\">\n\n        <label class=\"item item-input noBorder\">\n            <div class=\"input-label alignRight\">\n                {{'page'|t(data.lc)}}\n            </div>\n            <label class=\"item item-input\">\n                <input type=\"number\" v-model=\"modal.gotoPageOptions.page\" v-on:keyup.enter=\"modal.gotoPageOptions.submit()\">\n            </label>\n        </label>\n\n    </ion-content>\n    <ion-footer-bar>\n        <div class=\"row\">\n            <div class=\"col\">\n                <button type=\"button\" class=\"button button-block button-positive zeroMargin\" v-on:click.prevent=\"modal.gotoPageOptions.submit()\">\n                    {{'ok'|t(data.lc)}}\n                </button>\n            </div>\n            <div class=\"col\">\n                <button type=\"button\" class=\"button button-block button-light zeroMargin\" v-on:click.prevent=\"hideModal()\">\n                    {{'cancel'|t(data.lc)}}\n                </button>\n            </div>\n        </div>\n    </ion-footer-bar>\n</div>","templates/panels/hlight.html":"<div>\n    <ion-header-bar align-title=\"center\" class=\"bar-dark\">\n        <h1 class=\"title\">{{'selectYourColor'|t(data.lc)}}</h1>\n        <button type=\"button\" class=\"button button-icon button-clear\" v-on:click.prevent=\"hideModal()\">\n            <span class=\"icon kb-close size32 vAlignTop\"></span>\n        </button>\n    </ion-header-bar>\n    <ion-content class=\"padding\" :scroll=\"false\">\n        <div class=\"colorPalette textCenter\">\n            <a class=\"color red\" v-bind:class=\"[modal.hlightOptions.color=='#f8c1c1'?'selected':'']\"\n               v-on:click.prevent=\"modal.hlightOptions.color='#f8c1c1'\" style=\"background-color:#f8c1c1\"></a>\n            <a class=\"color blue\" v-bind:class=\"[modal.hlightOptions.color=='#a1f9ff'?'selected':'']\"\n               v-on:click.prevent=\"modal.hlightOptions.color='#a1f9ff'\" style=\"background-color:#a1f9ff\"></a>\n            <a class=\"color orange\" v-bind:class=\"[modal.hlightOptions.color=='#aeffa1'?'selected':'']\"\n               v-on:click.prevent=\"modal.hlightOptions.color='#aeffa1'\" style=\"background-color:#aeffa1\"></a>\n            <a class=\"color pink\" v-bind:class=\"[modal.hlightOptions.color=='#fffb85'?'selected':'']\"\n               v-on:click.prevent=\"modal.hlightOptions.color='#fffb85'\" style=\"background-color:#fffb85\"></a>\n            <a class=\"color purple\" v-bind:class=\"[modal.hlightOptions.color=='#ffbe92'?'selected':'']\"\n               v-on:click.prevent=\"modal.hlightOptions.color='#ffbe92'\" style=\"background-color:#ffbe92\"></a>\n        </div>\n    </ion-content>\n    <ion-footer-bar>\n        <div class=\"row\">\n            <div class=\"col\">\n                <button type=\"button\" class=\"button button-block button-positive zeroMargin\"\n                        v-on:click.prevent=\"modal.hlightOptions.save(modal.hlightOptions.color)\">{{'save'|t(data.lc)}}\n                </button>\n            </div>\n            <div class=\"col\" v-if=\"modal.hlightOptions.remove\">\n                <button type=\"button\" class=\"button button-block button-assertive zeroMargin\"\n                        v-on:click.prevent=\"modal.hlightOptions.remove()\">{{'delete'|t(data.lc)}}\n                </button>\n            </div>\n            <div class=\"col\">\n                <button type=\"button\" class=\"button button-block button-light zeroMargin\" v-on:click.prevent=\"hideModal()\">\n                    {{'cancel'|t(data.lc)}}\n                </button>\n            </div>\n        </div>\n    </ion-footer-bar>\n</div>","templates/panels/image.html":"<div>\n    <ion-header-bar align-title=\"center\">\n        <h1 v-show=\"config.mediaModalTitle\" class=\"title\">{{modal.image.title}}</h1>\n        <a class=\"button button-icon button-clear\" v-on:click.prevent=\"hideModal()\">\n            <span class=\"icon kb-close size32 vAlignTop\"></span>\n        </a>\n    </ion-header-bar>\n    <div zooming=\"true\" direction=\"xy\" :overflow-scroll=\"true\"\n                 class=\"container has-header\" style=\"text-align:center\">\n        \n            <img v-bind:src=\"modal.image.thumbnail\" style=\"max-width:100%;xmargin:10px\"/>\n        \n    </div>\n</div>","templates/panels/image.mobile.html":"<div>\n    <ion-header-bar align-title=\"center\">\n        <h1 v-show=\"config.mediaModalTitle\" class=\"title\">{{modal.image.title}}</h1>\n        <a class=\"button button-icon button-clear\" v-on:click.prevent=\"hideModal()\">\n            <span class=\"icon kb-close size32 vAlignTop\"></span>\n        </a>\n    </ion-header-bar>\n    <ion-content :scroll=\"true\" has-header=\"true\">\n        \n            <img v-bind:src=\"modal.image.thumbnail\" style=\"max-width:100%\"/>\n        \n    </ion-content>\n</div>","templates/panels/libraryInfo.html":"<div>\n    <ion-header-bar align-title=\"center\" >\n      <h1 class=\"title\">{{'libraryInformation'|t(data.lc)}}</h1>\n        <a class=\"button button-icon button-clear\" v-on:click.prevent=\"hideModal()\">\n            <span class=\"icon kb-close size32 vAlignTop\"></span>\n        </a>\n    </ion-header-bar>\n    <ion-content class=\"padding\" :overflow-scroll=\"true\">\n        <div v-html=\"unsafe(modal.libraryInfo)\"></div>\n    </ion-content>\n</div>","templates/panels/libraryInfo.mobile.html":"<div>\n    <ion-header-bar align-title=\"center\">\n      <h1 class=\"title\">{{'libraryInformation'|t(data.lc)}}</h1>\n        <a class=\"button button-icon button-clear\" v-on:click.prevent=\"hideModal()\">\n            <span class=\"icon kb-close size32 vAlignTop\"></span>\n        </a>\n    </ion-header-bar>\n    <ion-content class=\"padding\" zooming=\"false\" direction=\"y\" :overflow-scroll=\"false\" has-bouncing=\"true\">\n        <div v-html=\"nativeLinks(modal.libraryInfo)\"></div>\n    </ion-content>\n</div>","templates/panels/loading.html":"<ion-content class=\"padding\">\n    <h5 class=\"center\">\n        {{modal.loadingMsg}}\n        <ion-spinner icon=\"dots\" class=\"loadingSpinner dark\"></ion-spinner>\n    </h5>\n</ion-content>","templates/panels/loading.mobile.html":"<ion-content :overflow-scroll=\"false\" class=\"padding\">\n    <h5 class=\"center\">\n        {{modal.loadingMsg|t(data.lc)}}\n        <ion-spinner icon=\"dots\" class=\"loadingSpinner dark\"></ion-spinner>\n    </h5>\n</ion-content>","templates/panels/localOnlyError.html":"<div>\n    <ion-header-bar align-title=\"center\" class=\"bar-dark\">\n        <h1 class=\"title\">{{'syncError'|t(data.lc)}}</h1>\n        <button type=\"button\" class=\"button button-icon button-clear\" v-on:click.prevent=\"hideModal()\">\n            <span class=\"icon kb-close size32 vAlignTop\"></span>\n        </button>\n    </ion-header-bar>\n    <ion-content class=\"padding\">\n        <h5 class=\"center\">{{'syncOnConnectionMsg'|t(data.lc)}}</h5>\n    </ion-content>\n    <ion-footer-bar>\n        <div class=\"row\">\n            <div class=\"col\">\n                <button type=\"button\" class=\"button button-block button-positive zeroMargin\" v-on:click.prevent=\"hideModal()\">{{'ok'|t(data.lc)}}</button>\n            </div>\n        </div>\n    </ion-footer-bar>\n</div>","templates/panels/localOnlyError.mobile.html":"<div>\n    <ion-header-bar align-title=\"center\" class=\"bar-dark\">\n        <h1 class=\"title\">{{'syncError'|t(data.lc)}}</h1>\n        <button class=\"button button-icon button-clear\" v-on:click.prevent=\"hideModal()\">\n            <span class=\"icon kb-close size32 vAlignTop\"></span>\n        </button>\n    </ion-header-bar>\n    <ion-content :overflow-scroll=\"false\" class=\"padding\">\n        <h5 class=\"center\">{{'syncOnConnectionMsg'|t(data.lc)}}</h5>\n    </ion-content>\n    <ion-footer-bar>\n        <div class=\"row\">\n            <div class=\"col\">\n                <button class=\"button button-block button-positive zeroMargin\" v-on:click.prevent=\"hideModal()\">{{'ok'|t(data.lc)}}</button>\n            </div>\n        </div>\n    </ion-footer-bar>\n</div>","templates/panels/loginPanel.html":"<div>\n    <ion-header-bar align-title=\"center\" class=\"bar-dark\" :xcustomizable=\"true\">\n        <h1 class=\"title\">{{'loginToLibrary'|t(data.lc)}}</h1>\n        <button type=\"button\" class=\"button button-icon button-clear\" v-on:click.prevent=\"hideModal()\">\n            <span class=\"icon kb-close size32 vAlignTop\"></span>\n        </button>\n    </ion-header-bar>\n    <ion-content class=\"padding\" :scroll=\"false\">\n        <div>\n            <div v-if=\"modal.loginOptions.errorReason\" class=\"errorMsg\">\n                {{modal.loginOptions.errorReason|t(data.lc)}}\n            </div>\n            <div v-if=\"modal.loginOptions.canLoginByEmail && (modal.loginOptions.mode=='email')\">\n                <div class=\"list\" style=\"margin-bottom:2px\">\n                    <label class=\"item item-input\">\n                        <input type=\"email\" class=\"emailInputTxt\" v-autofocus v-model=\"data.user.email\" v-on:keyup.enter=\"modal.loginOptions.signIn()\" v-bind:placeholder=\"'email'|t(data.lc)\" v-bind:disabled=\"ssoBusy\">\n                    </label>\n                    <label class=\"item item-input\">\n                        <input type=\"password\" v-model=\"modal.model.pwd\" v-on:keyup.enter=\"modal.loginOptions.signIn()\" v-bind:placeholder=\"'password'|t(data.lc)\" v-bind:disabled=\"ssoBusy\">\n                    </label>\n                </div>\n                <div class=\"table fullWidth\">\n                    <div class=\"padding5 halfWidth tableCell vAlignMiddle\">\n                        <button type=\"button\" class=\"button forgotPwdBtn button-light\" v-on:click.prevent=\"modal.loginOptions.forgotPassword()\">\n                            {{'forgotPwd?'|t(data.lc)}}\n                        </button>\n                    </div>\n                    <div class=\"padding5 halfWidth tableCell alignRight vAlignTop\">\n                        <button type=\"button\" v-if=\"modal.loginOptions.canLoginByCode\" class=\"button codeBtn button-light\" v-on:click.prevent=\"modal.loginOptions.redeemCode()\">\n                            <span class=\"icon kb-price-tag size14 vAlignMiddle\"></span>\n                            {{'redeemCode'|t(data.lc)}}\n                        </button>\n                    </div>\n                </div>\n            </div>\n            <div v-if=\"modal.loginOptions.canLoginByCode && (modal.loginOptions.mode=='code')\">\n                <div class=\"list\" style=\"margin-bottom:2px\">\n                    <label class=\"item item-input\">\n                        <input type=\"text\" class=\"codeInputTxt\" v-model=\"modal.model.code\" v-on:keyup.enter=\"modal.loginOptions.signIn()\" v-autofocus v-bind:placeholder=\"'code'|t(data.lc)\" v-bind:disabled=\"ssoBusy\">\n                    </label>\n                </div>\n                <div class=\"table fullWidth\">\n                    <div class=\"padding5 halfWidth tableCell vAlignMiddle xalignLeft\">\n                    </div>\n                    <div v-if=\"modal.loginOptions.canLoginByEmail\" class=\"padding5 halfWidth tableCell alignRight vAlignTop\">\n                        <button type=\"button\" v-if=\"modal.loginOptions.canLoginByCode\" class=\"button codeBtn button-light\" v-on:click.prevent=\"modal.loginOptions.signInByEmail()\" v-bind:disabled=\"ssoBusy\">\n                            <span class=\"icon kb-email size20 vAlignMiddle\"></span>\n                            {{'emailLogin'|t(data.lc)}}\n                        </button>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </ion-content>\n    <ion-footer-bar>\n        <div>\n            <div class=\"loginhtmlContainer alignLeft\">\n                <div v-if=\"config.kotobee.loginhtml\" v-html=\"unsafe(config.kotobee.loginhtml)\"></div>\n            </div>\n\n            <ion-checkbox class=\"rememberMe\" v-model=\"data.user.rememberMe\">\n                {{'rememberMe'|t(data.lc)}}\n            </ion-checkbox>\n            <div class=\"row\" v-if=\"cloudParam('registration')\">\n                <div class=\"col\">\n                    <button type=\"button\" class=\"button button-block button-positive zeroMargin\" v-bind:disabled=\"ssoBusy\"\n                            v-on:click.prevent=\"modal.loginOptions.signIn()\">{{'login'|t(data.lc)}}\n                        <span class=\"center\" v-if=\"ssoBusy\">\n                            <ion-spinner icon=\"dots\" class=\"loadingSpinner\"></ion-spinner>\n                        </span>\n                    </button>\n                </div>\n                <div class=\"col\">\n                    <button type=\"button\" class=\"button button-block button-stable zeroMargin\" v-on:click.prevent=\"modal.loginOptions.register()\" v-bind:disabled=\"ssoBusy\">\n                        {{'newAccount'|t(data.lc)}}\n                    </button>\n                </div>\n            </div>\n            <div class=\"row\" v-if=\"!cloudParam('registration')\">\n                <div class=\"col\">\n                    <button type=\"button\" class=\"button button-block button-positive zeroMargin\" v-bind:disabled=\"ssoBusy\"\n                            v-on:click.prevent=\"modal.loginOptions.signIn()\">{{'login'|t(data.lc)}}\n                        <span class=\"center\" v-if=\"ssoBusy\">\n                            <ion-spinner icon=\"dots\" class=\"loadingSpinner\"></ion-spinner>\n                        </span>\n                    </button>\n                </div>\n            </div>\n            \n            <div class=\"row alignCenter sso-btns\" v-if=\"data.sso\">\n                <div class=\"col\">\n                    {{'loginWith'|t(data.lc)}}\n                </div>\n                <div class=\"col\" v-if=\"data.sso.okta\">\n                    <button type=\"button\" class=\"button button-block button-stable zeroMargin\" v-bind:disabled=\"ssoBusy\"\n                            v-on:click.prevent=\"emit('SignIn', 'okta')\">\n                        <img v-bind:src=\"data.sso.oktaLogo\" class=\"inlineLogo okta\"/>\n                    </button>\n                </div>\n                <div class=\"col\" v-if=\"data.sso.google\">\n                    <button type=\"button\" class=\"button button-block button-stable zeroMargin\" v-bind:disabled=\"ssoBusy\"\n                            v-on:click.prevent=\"emit('SignIn', 'google')\">\n                        <img v-bind:src=\"data.sso.googleLogo\" class=\"inlineLogo google\"/>\n                    </button>\n                </div>\n                <div class=\"col\" v-if=\"data.sso.microsoft\">\n                  <button type=\"button\" class=\"button button-block button-stable zeroMargin\" v-bind:disabled=\"ssoBusy\"\n                          v-on:click.prevent=\"emit('SignIn', 'microsoft')\">\n                      <img v-bind:src=\"data.sso.microsoftLogo\" class=\"inlineLogo microsoft\"/>\n                  </button>\n                </div>\n                <div class=\"col\" v-if=\"data.sso.facebook\">\n                    <button type=\"button\" class=\"button button-block button-stable zeroMargin\" v-bind:disabled=\"ssoBusy\"\n                            v-on:click.prevent=\"emit('SignIn', 'facebook')\">\n                        <img v-bind:src=\"data.sso.facebookLogo\" class=\"inlineLogo facebook\"/>\n                    </button>\n                </div>\n                <div class=\"col\" v-if=\"data.sso.auth0\">\n                    <button type=\"button\" class=\"button button-block button-stable zeroMargin\" v-bind:disabled=\"ssoBusy\"\n                            v-on:click.prevent=\"emit('SignIn', 'auth0')\">\n                        <img v-bind:src=\"data.sso.auth0Logo\" class=\"inlineLogo auth0\"/>\n                    </button>\n                </div>\n                <div class=\"col\" v-if=\"data.sso.fusionauth\">\n                    <button type=\"button\" class=\"button button-block button-stable zeroMargin\" v-bind:disabled=\"ssoBusy\"\n                            v-on:click.prevent=\"emit('SignIn', 'fusionauth')\">\n                        <img v-bind:src=\"data.sso.fusionauthLogo\" class=\"inlineLogo fusionauth\"/>\n                    </button>\n                </div>\n            </div>\n        </div>\n    </ion-footer-bar>\n</div>","templates/panels/loginPanel.mobile.html":"<div>\n    <ion-header-bar align-title=\"center\" class=\"bar-dark\">\n        <h1 class=\"title\">{{'loginToLibrary'|t(data.lc)}}</h1>\n        <button class=\"button button-icon button-clear\" v-on:click.prevent=\"hideModal()\">\n            <span class=\"icon kb-close size32 vAlignTop\"></span>\n        </button>\n    </ion-header-bar>\n    <ion-content class=\"padding\" :scroll=\"false\">\n        <div>\n            <div v-if=\"modal.loginOptions.errorReason\" class=\"errorMsg\">\n                {{modal.loginOptions.errorReason|t(data.lc)}}\n            </div>\n            <div v-if=\"modal.loginOptions.canLoginByEmail && (modal.loginOptions.mode=='email')\">\n                <div class=\"list\" style=\"margin-bottom:2px\">\n                    <label class=\"item item-input\">\n                        <input type=\"email\" v-autofocus v-model=\"data.user.email\" v-on:keyup.enter=\"modal.loginOptions.signIn()\" v-bind:placeholder=\"'email'|t(data.lc)\" v-bind:disabled=\"ssoBusy\">\n                    </label>\n                    <label class=\"item item-input\">\n                        <input type=\"password\" v-model=\"modal.model.pwd\" v-on:keyup.enter=\"modal.loginOptions.signIn()\" v-bind:placeholder=\"'password'|t(data.lc)\" v-bind:disabled=\"ssoBusy\">\n                    </label>\n                </div>\n                <div class=\"table fullWidth\">\n                    <div class=\"padding5 halfWidth tableCell vAlignMiddle noPadding\">\n                        <button class=\"button forgotPwdBtn button-light alignLeft noPadding\" v-on:click.prevent=\"modal.loginOptions.forgotPassword()\" v-bind:disabled=\"ssoBusy\">\n                            {{'forgotPwd?'|t(data.lc)}}\n                        </button>\n                    </div>\n                    <div class=\"padding5 halfWidth tableCell alignRight vAlignTop\">\n                        <button v-if=\"modal.loginOptions.canLoginByCode\" class=\"button codeBtn button-light\" v-on:click.prevent=\"modal.loginOptions.redeemCode()\" v-bind:disabled=\"ssoBusy\">\n                            <span class=\"icon kb-price-tag size14 vAlignMiddle\"></span>\n                            {{'redeemCode'|t(data.lc)}}\n                        </button>\n                    </div>\n                </div>\n            </div>\n            <div v-if=\"modal.loginOptions.canLoginByCode && (modal.loginOptions.mode=='code')\">\n                <div class=\"list\" style=\"margin-bottom:2px\">\n                    <label class=\"item item-input\">\n                        <input type=\"text\" v-model=\"modal.model.code\" v-on:keyup.enter=\"modal.loginOptions.signIn()\" v-autofocus v-bind:placeholder=\"'code'|t(data.lc)\" v-bind:disabled=\"ssoBusy\">\n                    </label>\n                </div>\n                <div class=\"table fullWidth\">\n                    <div class=\"padding5 halfWidth tableCell vAlignMiddle xalignLeft\">\n                    </div>\n                    <div v-if=\"modal.loginOptions.canLoginByEmail\" class=\"padding5 halfWidth tableCell alignRight vAlignTop\">\n                        <button v-if=\"modal.loginOptions.canLoginByCode\" class=\"button codeBtn button-light\" v-on:click.prevent=\"modal.loginOptions.signInByEmail()\" v-bind:disabled=\"ssoBusy\">\n                            <span class=\"icon kb-email size20 vAlignMiddle\"></span>\n                            {{'emailLogin'|t(data.lc)}}\n                        </button>\n                    </div>\n                </div>\n            </div>\n            \n        </div>\n    </ion-content>\n    <ion-footer-bar>\n        <div>\n            <div class=\"loginhtmlContainer alignLeft\">\n                <div v-if=\"config.kotobee.loginhtml\" v-html=\"unsafe(config.kotobee.loginhtml)\"></div>\n            </div>\n            <ion-checkbox class=\"rememberMe\" v-model=\"data.user.rememberMe\">\n                {{'rememberMe'|t(data.lc)}}\n            </ion-checkbox>\n            <div class=\"row\"  v-if=\"cloudParam('registration')\">\n                <div class=\"col\">\n                    <button class=\"button button-block button-positive zeroMargin\" v-bind:disabled=\"ssoBusy\"\n                            v-on:click.prevent=\"modal.loginOptions.signIn()\">{{'login'|t(data.lc)}}\n                        <span class=\"center\" v-if=\"ssoBusy\">\n                            <ion-spinner icon=\"dots\" class=\"loadingSpinner\"></ion-spinner>\n                        </span>\n                    </button>\n                </div>\n                <div class=\"col\">\n                    <button class=\"button button-block button-stable zeroMargin\" v-on:click.prevent=\"modal.loginOptions.register()\" v-bind:disabled=\"ssoBusy\">\n                        {{'newAccount'|t(data.lc)}}\n                    </button>\n                </div>\n            </div>\n            <div class=\"row\" v-if=\"!cloudParam('registration')\">\n                <div class=\"col\">\n                    <button class=\"button button-block button-positive zeroMargin\"\n                            v-on:click.prevent=\"modal.loginOptions.signIn()\">{{'login'|t(data.lc)}}\n                        <span class=\"center\" v-if=\"ssoBusy\">\n                            <ion-spinner icon=\"dots\" class=\"loadingSpinner\"></ion-spinner>\n                        </span>\n                    </button>\n                </div>\n            </div>\n            \n            <div class=\"row alignCenter sso-btns\" v-if=\"data.sso\">\n                <div class=\"col\">\n                    {{'loginWith'|t(data.lc)}}\n                </div>\n                <div class=\"col\" v-if=\"data.sso.okta\">\n                    <button type=\"button\" class=\"button button-block button-stable zeroMargin\" v-bind:disabled=\"ssoBusy\"\n                            v-on:click.prevent=\"emit('SignIn', 'okta')\">\n                        <img v-bind:src=\"data.sso.oktaLogo\" class=\"inlineLogo okta\"/>\n                    </button>\n                </div>\n                \n                <div class=\"col\" v-if=\"data.sso.google\">\n                    <button type=\"button\" class=\"button button-block button-stable zeroMargin\" v-bind:disabled=\"ssoBusy\"\n                            v-on:click.prevent=\"emit('SignIn', 'google')\">\n                        <img v-bind:src=\"data.sso.googleLogo\" class=\"inlineLogo google\"/>\n                    </button>\n                </div>\n                <div class=\"col\" v-if=\"data.sso.microsoft\">\n                  <button type=\"button\" class=\"button button-block button-stable zeroMargin\" v-bind:disabled=\"ssoBusy\"\n                          v-on:click.prevent=\"emit('SignIn', 'microsoft')\">\n                      <img v-bind:src=\"data.sso.microsoftLogo\" class=\"inlineLogo microsoft\"/>\n                  </button>\n                </div>\n                \n                <div class=\"col\" v-if=\"data.sso.facebook\">\n                    <button type=\"button\" class=\"button button-block button-stable zeroMargin\" v-bind:disabled=\"ssoBusy\"\n                            v-on:click.prevent=\"emit('SignIn', 'facebook')\">\n                        <img v-bind:src=\"data.sso.facebookLogo\" class=\"inlineLogo facebook\"/>\n                    </button>\n                </div>\n                <div class=\"col\" v-if=\"data.sso.auth0\">\n                    <button type=\"button\" class=\"button button-block button-stable zeroMargin\" v-bind:disabled=\"ssoBusy\"\n                            v-on:click.prevent=\"emit('SignIn', 'auth0')\">\n                        <img v-bind:src=\"data.sso.auth0Logo\" class=\"inlineLogo auth0\"/>\n                    </button>\n                </div>\n                <div class=\"col\" v-if=\"data.sso.fusionauth\">\n                    <button type=\"button\" class=\"button button-block button-stable zeroMargin\" v-bind:disabled=\"ssoBusy\"\n                            v-on:click.prevent=\"emit('SignIn', 'fusionauth')\">\n                        <img v-bind:src=\"data.sso.fusionauthLogo\" class=\"inlineLogo fusionauth\"/>\n                    </button>\n                </div>\n            </div>\n        </div>\n    </ion-footer-bar>\n</div>","templates/panels/media.html":"<ion-list id=\"mediaContent\" :overflow-scroll=\"true\" class=\"has-footer has-header\" can-swipe=\"listCanSwipe\">\n\n    <a class=\"item item-thumbnail-left\" v-on:click.prevent=\"mediaClicked(item)\"\n       v-for=\"item in data.mediaList\">\n        \n        <div class=\"coverImgContainer\" v-bind:class=\"[item.type]\" v-bind:style=\"{'background-image':'url(\\''+item.thumbnail+'\\')'}\"></div>\n        <h2>{{item.title}}</h2>\n\n        <p>{{item.ext}}</p>\n    </a>\n\n    <div v-if=\"!data.mediaList.length\">\n        <div class=\"vSpace20\"></div>\n        <h5 class=\"panelEmpty\">\n            <span class=\"icon kb-notice vAlignMiddle size20\"></span>\n            <span class=\"hSpace2\"></span>\n            {{'noMediaContentAvailable'|t(data.lc)}}\n        </h5>\n    </div>\n</ion-list>","templates/panels/media.mobile.html":"<ion-list can-swipe=\"listCanSwipe\">\n\n    <a class=\"item item-thumbnail-left\" v-on:click.prevent=\"mediaClicked(item)\"\n       v-for=\"item in data.mediaList\" collection-item-height=\"100\" collection-item-width=\"'100%'\">\n        \n        <div class=\"coverImgContainer\" v-bind:class=\"[item.type]\" v-bind:style=\"{'background-image':'url(\\''+item.thumbnail+'\\')'}\"></div>\n        <h2>{{item.title}}</h2>\n\n        <p>{{item.ext}}</p>\n    </a>\n\n    <div v-if=\"!data.mediaList.length\">\n        <div class=\"vSpace20\"></div>\n        <h5 class=\"panelEmpty\">\n            <span class=\"icon kb-notice vAlignMiddle size20\"></span>\n            <span class=\"hSpace2\"></span>\n            {{'noMediaContentAvailable'|t(data.lc)}}\n        </h5>\n    </div>\n</ion-list>","templates/panels/miniapps.html":"<div ng-controller=\"MiniappsCtrl\" id=\"miniappMenu\" ng-init=\"init()\" >\n    <ion-header-bar>\n        <h1 class=\"title\">{{'apps'|t(data.lc)}}</h1>\n    </ion-header-bar>\n    <ion-content class=\"content has-header has-footer\" :overflow-scroll=\"true\">\n        <a v-for=\"app in data.miniapps\" v-bind:href=\"app.appClass.externalLink?app.appClass.externalLink:''\" target=\"_blank\" class=\"app\" v-on:click.prevent=\"miniappClicked(app)\">\n            <img class=\"icon\" v-bind:src=\"app|miniappIconPath\"/>\n            <p class=\"name\">{{app.dataJson._listingName?app.dataJson._listingName:app.configJson.locale[data.settings.language].name}}</p>\n        </a>\n        <div class=\"vSpace20\"></div>\n        <div class=\"vSpace20\"></div>\n    </ion-content>\n</div>","templates/panels/networkError.html":"<div>\n    <ion-header-bar align-title=\"center\" class=\"bar-dark\">\n        <h1 class=\"title\">{{'networkError'|t(data.lc)}}</h1>\n        <button type=\"button\" class=\"button button-icon button-clear\" v-on:click.prevent=\"hideModal()\">\n            <span class=\"icon kb-close size32 vAlignTop\"></span>\n        </button>\n    </ion-header-bar>\n    <ion-content class=\"padding\" :scroll=\"false\">\n        <h5 class=\"center\">{{'connectionFailedMsg'|t(data.lc)}}</h5>\n    </ion-content>\n    <ion-footer-bar>\n        <div class=\"row\">\n            <div class=\"col\">\n                <button type=\"button\" class=\"button button-block button-positive zeroMargin\" v-if=\"modal.errorOptions && modal.errorOptions.retry\"\n                        v-on:click.prevent=\"modal.errorOptions.retry(modal.errorOptions.ref)\">{{'retry'|t(data.lc)}}\n                </button>\n            </div>\n            <div class=\"col\">\n                <button type=\"button\" class=\"button button-block button-light zeroMargin\" v-on:click.prevent=\"hideModal()\">\n                    {{'cancel'|t(data.lc)}}\n                </button>\n            </div>\n        </div>\n    </ion-footer-bar>\n</div>","templates/panels/note.html":"<div ng-controller=\"NoteCtrl\" ng-init=\"init()\">\n    <ion-header-bar align-title=\"center\" class=\"bar-dark\">\n        <h1 class=\"title\">{{'enterNoteBelow'|t(data.lc)}}</h1>\n        <button type=\"button\" class=\"button button-icon button-clear\" v-on:click.prevent=\"hideModal()\">\n            <span class=\"icon kb-close size32 vAlignTop\"></span>\n        </button>\n    </ion-header-bar>\n    <ion-content class=\"content has-header has-footer padding\" :scroll=\"false\" v-bind:class=\"[!modal.noteOptions.allNotes.length?'noNotes':'', (data.user.perms && data.user.perms.publicnotes)?'hasPublicNotes':'']\">\n        <div v-if=\"!modal.noteOptions.allNotes.length\">\n            <h6 class=\"center\">{{'enterNote'|t(data.lc)}}</h6>\n        </div>\n        <div v-if=\"modal.noteOptions.allNotes.length\" class=\"previousNotes\">\n            <div class=\"noteContainer\" v-for=\"note in modal.noteOptions.allNotes\">\n                <div class=\"avatarContainer\" v-if=\"note.public\">\n                    <div class=\"kAvatar\">\n                        <span>{{note|noteUsernameChar}}</span>\n                    </div>\n                    <div class=\"name\">\n                        {{note|noteUsername}}\n                    </div>\n                </div>\n                <div class=\"textContainer\" v-bind:class=\"[noteEntryStyle(note,editing)]\">\n                    <div class=\"noteEntry\" v-html=\"note.note\"></div>\n                    <div class=\"date alignRight\">\n                        <span v-if=\"note.date\">\n                            {{note.date|dateTimeFromServer}}\n                        </span>\n                        <span v-if=\"!note.date\">\n                            {{note.dateLocal|dateTimeFromServer}}\n                        </span>\n                    </div>\n                    <div class=\"controls\" v-if=\"note.uid == data.user.id\">\n                        <a href class=\"editBtn vAlignMiddle\" v-on:click.prevent=\"edit(note)\">\n                            <span class=\"kb-edit size16\"></span>\n                        </a>\n                        <a href class=\"delBtn vAlignMiddle\" v-on:click.prevent=\"remove(note)\">\n                            <span class=\"kb-delete size21\"></span>\n                        </a>\n                    </div>\n                </div>\n            </div>\n        </div>\n        <div class=\"textarea\" v-bind:class=\"[editing?'editing':'']\">\n            <textarea rows=\"3\" v-autofocus v-model=\"modal.noteOptions.note.note\" v-if=\"!config.htmlInNotes\"\n                      enter-with-ctrl=\"save(modal.noteOptions.note)\"> </textarea>\n            <div id=\"noteTextArea\"></div>\n        </div>\n    </ion-content>\n    <ion-footer-bar>\n        <ion-checkbox v-if=\"config.kotobee.cloudid && config.kotobee.sync && data.user.perms && data.user.perms.publicnotes\" v-model=\"modal.noteOptions.public\" style=\"border-bottom: none;\">\n            {{'publicNote'|t(data.lc)}}\n        </ion-checkbox>\n        <div class=\"row\">\n            <div class=\"col space\">\n                <button type=\"button\" class=\"button button-block button-positive zeroMargin\"\n                        v-on:click.prevent=\"save(modal.noteOptions.note)\">{{'save'|t(data.lc)}}\n                </button>\n            </div>\n            \n            <div class=\"col space\">\n                <button type=\"button\" class=\"button button-block button-light zeroMargin\" v-on:click.prevent=\"hideModal()\">\n                    {{'cancel'|t(data.lc)}}\n                </button>\n            </div>\n        </div>\n    </ion-footer-bar>\n</div>\n","templates/panels/notebook.html":"<div ng-controller=\"NotebookCtrl\" xid=\"notebookPanel\" ng-init=\"noteBtns={notes:true,bookmarks:true,hlights:true}\">\n    <div id=\"notebookPanel\" v-if=\"data.notebookContent\">\n        <ion-header-bar>\n            <button v-if=\"false\" type=\"button\" class=\"button button-icon button-clear\"\n                    v-on:click.prevent=\"data.notebookContent.showDelete = !data.notebookContent.showDelete\">\n                <span class=\"icon kb-delete size32 vAlignTop\"></span>\n            </button>\n            <h1 class=\"title\">{{'notebook'|t(data.lc)}}</h1>\n            <a class=\"button button-icon closeBtn button-clear\" v-on:click.prevent=\"notebookClose()\">\n                <span class=\"icon kb-close size32 vAlignTop\"></span>\n            </a>\n        </ion-header-bar>\n        <div class=\"bar bar-subheader\">\n            <div class=\"button-bar noteBtns\">\n                <a class=\"button\" v-bind:class=\"[(noteBtns.notes&&noteBtns.bookmarks&&noteBtns.hlights)?'button-positive':'']\"\n                v-on:click.prevent=\"noteBtnClicked('all',noteBtns, $event)\">{{'all'|t(data.lc)}}</a>\n                <a class=\"button\" v-bind:class=\"[(noteBtns.notes && !noteBtns.bookmarks && !noteBtns.hlights) ?'button-positive':'']\"\n                v-on:click.prevent=\"noteBtnClicked('notes',noteBtns, $event)\">{{'notes'|t(data.lc)}}</a>\n                <a class=\"button\" v-bind:class=\"[(!noteBtns.notes && noteBtns.bookmarks && !noteBtns.hlights)?'button-positive':'']\"\n                v-on:click.prevent=\"noteBtnClicked('bookmarks',noteBtns, $event)\">{{'bookmarks'|t(data.lc)}}</a>\n                <a class=\"button\" v-bind:class=\"[(!noteBtns.notes && !noteBtns.bookmarks && noteBtns.hlights)?'button-positive':'']\"\n                v-on:click.prevent=\"noteBtnClicked('hlights',noteBtns, $event)\">{{'highlights'|t(data.lc)}}</a>\n            </div>\n        </div>\n        <ion-content v-if=\"data.notebookContent\" id=\"notebookContent\" delegate-handle=\"notebookScroll\" class=\"has-header has-subheader has-footer\" v-bind:class=\"[(config.notebookExportDisabled || !hasResults)?'noFooter':'']\"\n                    :padding=\"true\" :overflow-scroll=\"true\">\n            <div v-if=\"arrayEmpty(data.notebookContent.results)\" class=\"card noNotes\">\n                <div class=\"item item-divider\" style=\"background-color: #e8e3b4;\">\n                    {{'noNotesAvailable'|t(data.lc)}}\n                </div>\n                <div class=\"item item-text-wrap\">\n                    \n                    <span class=\"icon kb-edit size24 notebookIcon\" style=\"margin-bottom:20px\"></span>\n                    {{'addNotesHintMsg'|t(data.lc)}}\n                </div>\n                <div class=\"item item-text-wrap\">\n                    <span class=\"icon kb-bookmark size20 notebookIcon\" style=\"margin-bottom:20px\"></span>\n                    {{'addBookmarksHintMsg'|t(data.lc)}}\n                </div>\n                <div class=\"item item-text-wrap\">\n                    <span class=\"icon kb-highlight size24 notebookIcon\" style=\"margin-bottom:20px\"></span>\n                    {{'addHighlightsHintMsg'|t(data.lc)}}\n                </div>\n            </div>\n            \n            <ion-list v-else show-delete=\"data.notebookContent.showDelete\">\n                <template v-for=\"(category,index) in data.notebookContent.results\" v-if=\"chapterHasSomething(category)\">\n                    <div class=\"ion-item item chapter-title\" v-if=\"category && category.length\">\n                        \n                        <strong>{{category.title}}</strong>\n                    </div>\n                    \n                    <div class=\"notebookItemsWrapper\" v-if=\"category && category.length\">\n                        <template v-for=\"(item,index) in category\" :xkey=\"index\">\n                            <bookmarkItem :item=\"item\" v-if=\"(item.bmid!=null) && noteBtns.bookmarks\"></bookmarkItem>\n                            <noteItem :item=\"item\" v-if=\"(item.nid!=null) && noteBtns.notes\"></noteItem>\n                            <hlightItem :item=\"item\" v-if=\"(item.hid!=null) && noteBtns.hlights\"></hlightItem>\n                        </template>\n                    </div>\n                </template>\n                <div v-if=\"!chaptersHaveSomething()\">\n                    <div class=\"ion-item item chapter-title noBorder\">\n                        {{'noItems'|t(data.lc)}}\n                    </div>\n                </div>\n            </ion-list>\n            <div v-if=\"data.notebookContent.moreExist\" style=\"margin:20px\">\n                <button type=\"button\" class=\"button button-small button-dark\" v-on:click.prevent=\"loadAllNotebook($event)\" v-bind:disabled=\"loadingMoreNotes\">\n                    <div class=\"hSpace2\"></div>\n                    <span v-if=\"loadingMoreNotes\" class=\"icon size16 kb-spin kb-notch\"></span>\n                    {{'loadForAllChapters'|t(data.lc)}}\n                    <div class=\"hSpace2\"></div>\n                </button>\n                <div class=\"vSpace20\"></div>\n            </div>\n        </ion-content>\n        <ion-footer-bar class=\"bar-light\" v-if=\"!config.notebookExportDisabled && hasResults\">\n            <div class=\"row\">\n                <div class=\"col textCenter\">\n                    <a class=\"button button-clear footerBtn\" v-on:click.prevent=\"pdfExport($event, exportPdfOptimized)\">\n                        <i class=\"inlineBlock kb-pdf-icon\"></i>\n                        <span>{{'export'|t(data.lc)}}</span>\n                    </a>\n                </div>\n                \n            </div>\n        </ion-footer-bar>\n    </div>\n</div>","templates/panels/notebook.mobile.html":"<div ng-controller=\"NotebookCtrl\" ng-init=\"noteBtns={notes:true,bookmarks:true,hlights:true}\">\n    <div id=\"notebookPanel\" v-if=\"data.notebookContent\">\n        <ion-header-bar align-title=\"center\">\n            <button class=\"button button-icon button-clear\"\n                    v-on:click.prevent=\"data.notebookContent.showDelete = !data.notebookContent.showDelete\">\n                <span class=\"icon kb-delete size32 vAlignTop\"></span>\n            </button>\n            <h1 class=\"title\">{{'notebook'|t(data.lc)}}</h1>\n            <a class=\"button button-icon button-clear\" v-on:click.prevent=\"notebookClose()\">\n                <span class=\"icon kb-close size32 vAlignTop\"></span>\n            </a>\n        </ion-header-bar>\n        <div class=\"bar bar-subheader\">\n            <div class=\"button-bar noteBtns\">\n                <a class=\"button\" v-bind:class=\"[(noteBtns.notes&&noteBtns.bookmarks&&noteBtns.hlights)?'button-positive':'']\"\n                v-on:click.prevent=\"noteBtnClicked('all',noteBtns, $event)\">{{'all'|t(data.lc)}}</a>\n                <a class=\"button\" v-bind:class=\"[(noteBtns.notes && !noteBtns.bookmarks && !noteBtns.hlights) ?'button-positive':'']\"\n                v-on:click.prevent=\"noteBtnClicked('notes',noteBtns, $event)\">{{'notes'|t(data.lc)}}</a>\n                <a class=\"button\" v-bind:class=\"[(!noteBtns.notes && noteBtns.bookmarks && !noteBtns.hlights)?'button-positive':'']\"\n                v-on:click.prevent=\"noteBtnClicked('bookmarks',noteBtns, $event)\">{{'bookmarks'|t(data.lc)}}</a>\n                <a class=\"button\" v-bind:class=\"[(!noteBtns.notes && !noteBtns.bookmarks && noteBtns.hlights)?'button-positive':'']\"\n                v-on:click.prevent=\"noteBtnClicked('hlights',noteBtns, $event)\">{{'highlights'|t(data.lc)}}</a>\n            </div>\n        </div>\n        <ion-content id=\"notebookContent\" delegate-handle=\"notebookScroll\" class=\"has-header has-subheader has-footer\" v-bind:class=\"[(config.notebookExportDisabled || !hasResults)?'noFooter':'']\"\n                    :padding=\"true\" :autoscroll=\"false\" has-bouncing=\"true\" zooming=\"false\" :scroll=\"true\" direction=\"y\" :overflow-scroll=\"false\">\n            <div v-if=\"arrayEmpty(data.notebookContent.results)\" class=\"card noNotes\">\n                <div class=\"item item-divider\" style=\"background-color: #e8e3b4;\">\n                    {{'noNotesAvailable'|t(data.lc)}}\n                </div>\n                <div class=\"item item-text-wrap\">\n                    <span class=\"icon kb-edit size24 notebookIcon\" style=\"margin-bottom:20px\"></span>\n                    {{'addNotesHintMsg'|t(data.lc)}}\n                </div>\n                <div class=\"item item-text-wrap\">\n                    <span class=\"icon kb-bookmark size20 notebookIcon\" style=\"margin-bottom:20px\"></span>\n                    {{'addBookmarksHintMsg'|t(data.lc)}}\n                </div>\n                <div class=\"item item-text-wrap\">\n                    <span class=\"icon kb-highlight size24 notebookIcon\" style=\"margin-bottom:20px\"></span>\n                    {{'addHighlightsHintMsg'|t(data.lc)}}\n                </div>\n            </div>\n            <ion-list v-else show-delete=\"data.notebookContent.showDelete\">\n                <template v-for=\"(category,index) in data.notebookContent.results\" v-if=\"chapterHasSomething(category)\">\n                    <div class=\"ion-item item chapter-title\" v-if=\"category && category.length\">\n                        \n                        <strong>{{category.title}}</strong>\n                    </div>\n                    \n                    <div class=\"notebookItemsWrapper\" v-if=\"category && category.length\">\n                        <template v-for=\"(item,index) in category\" :xkey=\"index\">\n                            <bookmarkItem :item=\"item\" :showDelete=\"data.notebookContent.showDelete\" v-if=\"(item.bmid!=null) && noteBtns.bookmarks\"></bookmarkItem>\n                            <noteItem :item=\"item\" :showDelete=\"data.notebookContent.showDelete\" v-if=\"(item.nid!=null) && noteBtns.notes\"></noteItem>\n                            <hlightItem :item=\"item\" :showDelete=\"data.notebookContent.showDelete\" v-if=\"(item.hid!=null) && noteBtns.hlights\"></hlightItem>\n                        </template>\n                    </div>\n                </template>\n                <div v-if=\"!chaptersHaveSomething()\">\n                    <div class=\"ion-item item chapter-title noBorder\">\n                        {{'noItems'|t(data.lc)}}\n                    </div>\n                </div>\n            </ion-list>\n            <div v-if=\"data.notebookContent.moreExist\" style=\"margin:20px\">\n                <a class=\"button button-small button-dark\" v-on:click.prevent=\"loadAllNotebook($event)\" v-bind:disabled=\"loadingMoreNotes\">\n                    <div class=\"hSpace2\"></div>\n                    <span v-if=\"loadingMoreNotes\" class=\"icon size16 kb-spin kb-notch\"></span>\n                    {{'loadForAllChapters'|t(data.lc)}}\n                    <div class=\"hSpace2\"></div>\n                </a>\n                <div class=\"vSpace20\"></div>\n            </div>\n        </ion-content>\n        <ion-footer-bar class=\"bar-light\" v-if=\"!config.notebookExportDisabled && hasResults\">\n            <div class=\"row\" v-if=\"native\" >\n                <div class=\"col textCenter\">\n                    <a class=\"button button-clear footerBtn\" v-on:click.prevent=\"pdfExport($event, exportPdfOptimized)\">\n                        <i class=\"inlineBlock icon kb-pdf size21\"></i>\n                        {{'export'|t(data.lc)}}\n                    </a>\n                </div>\n                <div class=\"col textCenter\">\n                    <a class=\"button button-clear footerBtn\" v-on:click.prevent=\"shareNotebook()\">\n                        <i class=\"inlineBlock icon kb-share size21\"></i>\n                        {{'share'|t(data.lc)}}</a>\n                </div>\n            </div>\n            <div class=\"row\" v-if=\"!native\" >\n                <div class=\"col textCenter\">\n                    <a class=\"button button-clear footerBtn\" v-on:click.prevent=\"pdfExport($event)\">\n                        <i class=\"inlineBlock kb-pdf-icon\"></i>\n                        <span>{{'export'|t(data.lc)}}</span>\n                    </a>\n                </div>\n            </div>\n        </ion-footer-bar>\n    </div>\n</div>","templates/panels/pdfSave.html":"<div id=\"pdfSaveModal\">\n    <ion-header-bar align-title=\"center\" class=\"bar-dark\">\n        <h1 class=\"title\">{{'pdfOptions'|t(data.lc)}}</h1>\n        <button type=\"button\" class=\"button button-icon button-clear\" v-on:click.prevent=\"hideModal()\">\n            <span class=\"icon kb-close size32 vAlignTop\"></span>\n        </button>\n    </ion-header-bar>\n    <ion-content class=\"padding\" :scroll=\"false\">\n\n        <label class=\"item item-input noBorder\" v-if=\"config.pdfbooksaving\">\n            <div class=\"input-label alignRight\">\n                {{'range'|t(data.lc)}}\n            </div>\n            <select class=\"item item-input noBorder\" v-model=\"modal.pdfSaveOptions.range\">\n                <option v-on:selected=\"modal.pdfSaveOptions.range=='chapter'\" value=\"chapter\">{{'currentChapter'|t(data.lc)}}\n                </option>\n                <option v-on:selected=\"modal.pdfSaveOptions.range=='book'\" value=\"book\">{{'entireBook'|t(data.lc)}}\n                </option>\n            </select>\n        </label>\n\n        <label class=\"item item-input noBorder\" v-if=\"config.pdfsavingOptions && config.annotations\">\n            <div class=\"input-label alignRight\">\n                {{'pdfChapterNotes'|t(data.lc)}}\n            </div>\n            <ion-toggle class=\"\" v-model=\"modal.pdfSaveOptions.includeNotes\" toggle-class=\"toggle-calm\" style=\"xposition:static\">\n            </ion-toggle>\n        </label>\n\n        <div class=\"desc\" v-if=\"config.pdfsavingOptions && config.annotations\">\n            {{'pdfChapterNotesDesc'|t(data.lc)}}\n        </div>\n\n        <label class=\"item item-input noBorder\" v-if=\"config.pdfsavingOptions\">\n            <div class=\"input-label alignRight\">\n                {{'scale'|t(data.lc)}}\n            </div>\n            <label class=\"item item-input\">\n                <input type=\"number\" v-model=\"modal.pdfSaveOptions.scale\" v-on:keyup.enter=\"modal.pdfSaveOptions.submit()\" placeholder=\"100\">\n            </label>\n        </label>\n    </ion-content>\n    <ion-footer-bar>\n        <div class=\"row\">\n            <div class=\"col\">\n                <button type=\"button\" class=\"button button-block button-positive zeroMargin\" v-on:click.prevent=\"modal.pdfSaveOptions.submit()\">\n                    {{'ok'|t(data.lc)}}\n                </button>\n            </div>\n            <div class=\"col\">\n                <button type=\"button\" class=\"button button-block button-light zeroMargin\" v-on:click.prevent=\"hideModal()\">\n                    {{'cancel'|t(data.lc)}}\n                </button>\n            </div>\n        </div>\n    </ion-footer-bar>\n</div>","templates/panels/popoverCornerList.html":"<div id=\"cornerList\" xv-bind:style=\"height:57*data.popover.listOb.length+'px'\">\n    <ion-content>\n        <ion-list>\n            <a href class=\"ion-item item\" v-for=\"item in data.popover.listOb\" v-on:click.prevent=\"item.func()\">\n                {{item.name}}\n            </a>\n        </ion-list>\n    </ion-content>\n</div>","templates/panels/popoverLibraryMenu.html":"<div xv-if=\"data.popover.mode=='libraryMenu'\" id=\"libraryMenu\" xv-bind:style=\"cloudParam('public')?'height:155px':''\">\n    <ion-content :overflow-scroll=\"false\" has-bouncing=\"true\">\n        <div class=\"list\">\n            <a class=\"item\" v-if=\"!cloudParam('public') && !(data.user.pwd||data.user.code)\" v-on:click.prevent=\"data.popover.menuItem('login')\">{{\"login\"|t(data.lc)}}</a>\n            <label class=\"item item-input item-select fullWidth\">\n                <div class=\"input-label ng-binding\">\n                    {{'sortBy'|t(data.lc)}}\n                </div>\n                <select v-model=\"data.sortOb.model\" class=\"size13\" v-on:change=\"data.popover.menuItem('sort')\">\n                    <option v-for=\"item in data.sortOb.options\" v-bind:value=\"item.val\">\n                        {{item.name|t(data.lc)}}\n                    </option>\n                </select>\n            </label>\n            <a class=\"item\"  v-if=\"data.currentLibrary.libraryinfo\" v-on:click.prevent=\"data.popover.menuItem('info')\">{{'info'|t(data.lc)}}</a>\n            <a class=\"item\"  v-if=\"view.topmenu.promocode && (data.user.pwd||data.user.code)\" v-on:click.prevent=\"data.popover.menuItem('redeemCode')\">{{'redeemCode'|t(data.lc)}}</a>\n            <label class=\"item item-input item-select fullWidth\" v-if=\"config.languageOptional\">\n                <div class=\"input-label ng-binding\">\n                    {{'language'|t(data.lc)}}\n                </div>\n                <select v-model=\"data.settings.language\" v-on:change=\"data.popover.menuItem('language')\">\n                    <option v-for=\"lang in data.languages\" v-on:selected=\"lang.val==data.settings.language\"\n                            v-bind:value=\"lang.val\">{{lang.label}}\n                    </option>\n                </select>\n            </label>\n            <template v-if=\"!cloudParam('public') && (data.user.pwd||data.user.code)\">\n                <a class=\"item\" v-on:click.prevent=\"data.popover.menuItem('logout')\">{{\"logout\"|t(data.lc)}}</a>\n                <a class=\"item red\" v-on:click.prevent=\"data.popover.menuItem('requestDelete')\">{{\"requestAccountDelete\"|t(data.lc)}}..</a>\n            </template>\n            <a class=\"item\" v-if=\"native && !ios && !readerApp\" v-on:click.prevent=\"data.popover.menuItem('exit')\">{{'exit'|t(data.lc)}}</a>\n        </div>\n    </ion-content>\n</div>","templates/panels/popoverList.html":"<div class=\"ui\">\n    <ion-content>\n        <ion-list>\n            <a href class=\"ion-item item\" v-for=\"item in data.popover.listOb\" v-on:click.prevent=\"item.func()\">\n                {{item.name}}\n            </a>\n        </ion-list>\n    </ion-content>\n</div>","templates/panels/print.html":"<div>\n    <ion-header-bar>\n        <h1 class=\"title\">{{'print'|t(data.lc)}}</h1>\n        <a class=\"button button-icon button-clear\" v-on:click.prevent=\"modal.scope.notebookClose()\">\n            <span class=\"icon kb-close size32 vAlignTop\"></span>\n        </a>\n    </ion-header-bar>\n    <div class=\"bar bar-subheader\">\n        <div class=\"button-bar noteBtns\">\n            <a class=\"button ng-primary\"\n               v-on:click.prevent=\"modal.scope.noteBtnClicked('all',noteBtns)\">{{'all'|t(data.lc)}}</a>\n            <a class=\"button  ng-primary\" v-on:click.prevent=\"modal.scope.noteBtnClicked('hlights',noteBtns)\">{{'currentChapter'|t(data.lc)}}</a>\n        </div>\n    </div>\n    <ion-content id=\"printContent\" delegate-handle=\"notebookScroll\" class=\"has-header has-subheader has-footer\"\n                 :padding=\"true\" :overflow-scroll=\"true\">\n\n    </ion-content>\n    <ion-footer-bar class=\"bar-light\">\n\n    </ion-footer-bar>\n</div>","templates/panels/profile.html":"<div ng-controller=\"ProfileCtrl\" id=\"profilePanel\" ng-init=\"init()\">\n    <ion-header-bar align-title=\"center\" no-tap-scroll=\"true\">\n        <h1 class=\"title\">{{'userProfile'|t(data.lc)}}</h1>\n        <a class=\"button button-icon button-clear\" v-on:click.prevent=\"hideModal()\">\n            <span class=\"icon kb-close size32 vAlignTop\"></span>\n        </a>\n    </ion-header-bar>\n    <div class=\"bar bar-subheader\">\n        <div class=\"button-bar button-light\">\n            <a class=\"button\" v-on:click.prevent=\"setTab('info')\" v-bind:class=\"[tab=='info'?'button-dark':'']\">{{'information'|t(data.lc)}}</a>\n            <a class=\"button\" v-on:click.prevent=\"setTab('activity')\" v-bind:class=\"[tab=='activity'?'button-dark':'']\">{{'recentActivity'|t(data.lc)}}</a>\n            <a class=\"button\" v-on:click.prevent=\"setTab('payments')\" v-if=\"config.kotobee.mode=='library'\" v-bind:class=\"[tab=='payments'?'button-dark':'']\">{{'payments'|t(data.lc)}}</a>\n        </div>\n    </div>\n    <ion-content v-if=\"user\" class=\"has-header has-subheader content\" :padding=\"true\" delegate-handle=\"searchScroll\"\n                 :autoscroll=\"false\" :overflow-scroll=\"true\" has-bouncing=\"true\">\n\n        <div v-if=\"tab=='info'\">\n\n            <div class=\"kAvatar floatLeft large\">\n                <span>{{user|noteUsernameChar}}</span>\n            </div>\n\n            <label class=\"item noBorder bold\" v-if=\"user.email\">\n                {{user.email}}\n            </label>\n\n            <label class=\"item noBorder bold\" v-if=\"user.code\">\n                {{user.code}}\n            </label>\n            <p class=\"item noBorder\" v-if=\"user.joindate\" style=\"padding-top:0\">\n                {{'joinedOn'|t(data.lc)}} {{user.joindate|dateTimeFromServer}}\n            </p>\n\n            \n            <p class=\"item noBorder\" style=\"padding-top:4px\" v-if=\"data.user && data.user.loggedIn && data.user.email && data.user.pwd\">\n                <a href v-on:click.prevent=\"resetPwd=!resetPwd\">{{'resetPwd'|t(data.lc)}}</a>\n            </p>\n            <div id=\"resetPassword\" v-if=\"resetPwd\">\n                <form @submit.prevent=\"resetPassword()\">\n                    <div>\n                        <label>{{'currentPwd'|t(data.lc)}}</label>\n                        <input type=\"password\" autocomplete=\"off\" v-model=\"passwordObj.current.password\">\n                        <span v-show=\"!passwordObj.current.valid\">{{'s_pwdRequired'|t(data.lc)}}</span>\n                    </div>\n                    <div>\n                        <label>{{'newPwd'|t(data.lc)}}</label>\n                        <input type=\"password\" autocomplete=\"off\" v-model=\"passwordObj.new.password\">\n                        <span v-show=\"!passwordObj.new.valid\">{{'s_pwdRequired'|t(data.lc)}}</span>\n                    </div>\n                    <div>\n                        <label>{{'confirmPwd'|t(data.lc)}}</label>\n                        <input type=\"password\" autocomplete=\"off\" v-model=\"passwordObj.confirm.password\">\n                        <span v-show=\"!passwordObj.confirm.valid\">{{'s_pwdConfirmNotMatch'|t(data.lc)}}</span>\n                    </div>\n                    <button type=\"submit\" class=\"button button-positive\" :disabled=\"passwordObj.isLoading\">\n                        {{'updatePwd'|t(data.lc)}}\n                        <span class=\"loader\" :style=\"passwordObj.isLoading ? 'display: inline-block;' : 'display:none;'\"></span>\n                    </button>\n                </form>\n                <div class=\"vSpace20\"></div>\n            </div>\n\n            <div class=\"clear\"></div>\n\n            <div class=\"list\">\n\n                <label class=\"item item-input item-stacked-label\" v-if=\"config.kotobee.registrationsettings.name\">\n                    <span class=\"input-label\">\n                        {{'name'|t(data.lc)}} {{config.kotobee.registrationsettings.namerequired?\"*\":\"\"}}\n                    </span>\n                    <input v-model=\"user.name\" v-on:keyup.enter=\"create()\" type=\"text\">\n                </label>\n\n                <label class=\"item item-input item-stacked-label\" v-if=\"config.kotobee.registrationsettings.dob\">\n                    <span class=\"input-label\">\n                        {{'dob'|t(data.lc)}} {{config.kotobee.registrationsettings.dobrequired?\"*\":\"\"}}\n                    </span>\n                    <input v-model=\"user.dob\" v-on:keyup.enter=\"create()\" type=\"date\">\n                </label>\n\n                <label class=\"item item-input item-stacked-label\" v-if=\"config.kotobee.registrationsettings.organization\">\n                    <span class=\"input-label\">\n                        {{'organization'|t(data.lc)}} {{config.kotobee.registrationsettings.organizationrequired?\"*\":\"\"}}\n                    </span>\n                    <input v-model=\"user.organization\" v-on:keyup.enter=\"create()\" type=\"text\">\n                </label>\n\n                <label class=\"item item-input item-stacked-label\" v-if=\"config.kotobee.registrationsettings.univ\">\n                    <span class=\"input-label\">\n                        {{'univ'|t(data.lc)}} {{config.kotobee.registrationsettings.univrequired?\"*\":\"\"}}\n                    </span>\n                    <input v-model=\"user.univ\" v-on:keyup.enter=\"create()\" type=\"text\">\n                </label>\n\n                <label class=\"item item-input item-stacked-label\" v-if=\"config.kotobee.registrationsettings.faculty\">\n                    <span class=\"input-label\">\n                        {{'faculty'|t(data.lc)}} {{config.kotobee.registrationsettings.facultyrequired?\"*\":\"\"}}\n                    </span>\n                    <input v-model=\"user.faculty\" v-on:keyup.enter=\"create()\" type=\"text\">\n                </label>\n\n                <label class=\"item item-input item-stacked-label\" v-if=\"config.kotobee.registrationsettings.dept\">\n                    <span class=\"input-label\">\n                        {{'dept'|t(data.lc)}} {{config.kotobee.registrationsettings.deptrequired?\"*\":\"\"}}\n                    </span>\n                    <input v-model=\"user.dept\" v-on:keyup.enter=\"create()\" type=\"text\">\n                </label>\n\n                <label class=\"item item-input item-stacked-label\" v-if=\"config.kotobee.registrationsettings.division\">\n                    <span class=\"input-label\">\n                        {{'division'|t(data.lc)}} {{config.kotobee.registrationsettings.divisionrequired?\"*\":\"\"}}\n                    </span>\n                    <input v-model=\"user.division\" v-on:keyup.enter=\"create()\" type=\"text\">\n                </label>\n\n                <label class=\"item item-input item-stacked-label\" v-if=\"config.kotobee.registrationsettings.class\">\n                    <span class=\"input-label\">\n                        {{'class'|t(data.lc)}} {{config.kotobee.registrationsettings.classrequired?\"*\":\"\"}}\n                    </span>\n                    <input v-model=\"user.class\" v-on:keyup.enter=\"create()\" type=\"text\">\n                </label>\n\n                <label class=\"item item-input item-stacked-label\" v-if=\"config.kotobee.registrationsettings.customid\">\n                    <span class=\"input-label\">\n                        {{'customId'|t(data.lc)}} {{config.kotobee.registrationsettings.customidrequired?\"*\":\"\"}}\n                    </span>\n                    <input v-model=\"user.customid\" v-on:keyup.enter=\"create()\" type=\"text\">\n                </label>\n\n                <label class=\"item item-input item-stacked-label\" v-if=\"config.kotobee.registrationsettings.gender\">\n                    <span class=\"input-label\">\n                        {{'gender'|t(data.lc)}} {{config.kotobee.registrationsettings.genderrequired?\"*\":\"\"}}\n                    </span>\n                    <div>\n                        <select v-model=\"user.gender\">\n                            <option :value=\"null\" selected disabled>{{'select'|t(data.lc)}}</option>\n                            <option value=\"m\">{{'male'|t(data.lc)}}</option>\n                            <option value=\"f\">{{'female'|t(data.lc)}}</option>\n                        </select>\n                    </div>\n                </label>\n\n                <label class=\"item item-input item-stacked-label\" v-if=\"config.kotobee.registrationsettings.country\">\n                    <span class=\"input-label\">\n                        {{'country'|t(data.lc)}} {{config.kotobee.registrationsettings.countryrequired?\"*\":\"\"}}\n                    </span>\n                    <input v-model=\"user.country\" v-on:keyup.enter=\"create()\" type=\"text\">\n                </label>\n\n                <label class=\"item item-input item-stacked-label\" v-if=\"config.kotobee.registrationsettings.city\">\n                    <span class=\"input-label\">\n                        {{'city'|t(data.lc)}} {{config.kotobee.registrationsettings.cityrequired?\"*\":\"\"}}\n                    </span>\n                    <input v-model=\"user.city\" v-on:keyup.enter=\"create()\" type=\"text\">\n                </label>\n\n                <label class=\"item item-input item-stacked-label\" v-if=\"config.kotobee.registrationsettings.phone\">\n                    <span class=\"input-label\">\n                        {{'phone'|t(data.lc)}} {{config.kotobee.registrationsettings.phonerequired?\"*\":\"\"}}\n                    </span>\n                    <input v-model=\"user.phone\" v-on:keyup.enter=\"create()\" type=\"text\">\n                </label>\n\n                <label class=\"item item-input item-stacked-label\" v-if=\"config.kotobee.registrationsettings.info\">\n                    <span class=\"input-label\">\n                        {{'info'|t(data.lc)}} {{config.kotobee.registrationsettings.inforequired?\"*\":\"\"}}\n                    </span>\n                    <textarea v-model=\"user.info\" xv-on:keyup.enter=\"create()\"></textarea>\n                </label>\n            </div>\n            <button type=\"button\" v-if=\"fields>1\" class=\"button button-positive\" v-bind:disabled=\"saving\"\n                v-on:click.prevent=\"updateInfo()\">{{'update'|t(data.lc)}}\n            </button>\n\n        </div>\n        \n        <div v-if=\"tab=='activity'\">\n            <div class=\"padding\" v-if=\"activity\">\n                \n                <div v-if=\"activity.activeChapters && activity.activeChapters.length\" class=\"activeChapters\">\n                    <h4 class=\"notBold\">{{'activeChapters'|t(data.lc)}}</h4>\n                    <div class=\"item noBorder\" v-for=\"item in activity.activeChapters\">\n                        <p class=\"bold\">{{item.chapter}}</p>\n                        <p>{{'openedXTimes'|t(data.lc,{x:item.opens})}}</p>\n                    </div>\n                </div>\n            </div>\n        </div>\n        \n        <div v-if=\"tab=='payments'\">\n            <div class=\"padding\">\n                <div v-if=\"!payments || !payments.length\">\n                    <p>{{'noOrdersYet'|t(data.lc)}}</p>\n                </div>\n                <div v-if=\"payments && payments.length\" v-for=\"item in payments\">\n                    <div class=\"item\" v-if=\"item.status=='accepted'\">\n                        <h2 class=\"bold\">{{item.product}}</h2>\n                        <p>{{item.date|dateTimeFromServer}}</p>\n                        <p>{{item.status|t(data.lc)}}</p>\n                        <p v-if=\"item.status=='paid'\">${{item.amount}} {{'paidVia'|t(data.lc,{x:item.platform})}}</p>\n                    </div>\n                </div>\n            </div>\n        </div>\n\n        <div class=\"vSpace20\"></div>\n\n    </ion-content>\n    \n</div>","templates/panels/redeemCodePanel.html":"<div>\n    <ion-header-bar align-title=\"center\" class=\"bar-dark\">\n      <h1 class=\"title\">\n          {{'redeemCode'|t(data.lc)}}\n      </h1>\n      <button type=\"button\" class=\"button button-icon button-clear\" v-on:click.prevent=\"hideModal()\">\n          <span class=\"icon kb-close size32 vAlignTop\"></span>\n      </button>\n    </ion-header-bar>\n    <ion-content class=\"padding\" :scroll=\"false\">\n        <div class=\"list\" style=\"margin-bottom:2px\">\n            <label class=\"item item-input\">\n                <input type=\"text\" v-autofocus v-model=\"modal.redeemCodeInfo.model.code\" v-autofocus v-on:keyup.enter=\"modal.redeemCodeInfo.submit()\" v-bind:placeholder=\"'enterPromoCode'|t(data.lc)\">\n            </label>\n        </div>\n    </ion-content>\n    <ion-footer-bar>\n        <div class=\"row\">\n            <div class=\"col\">\n                <button type=\"button\" class=\"button button-block button-positive zeroMargin\" v-on:click.prevent=\"modal.redeemCodeInfo.submit()\">\n                    {{'redeem'|t(data.lc)}}\n                </button>\n            </div>\n        </div>\n    </ion-footer-bar>\n</div>","templates/panels/redeemCodePanel.mobile.html":"<div>\n    <ion-header-bar align-title=\"center\" class=\"bar-dark\">\n      <h1 class=\"title\">\n          {{'redeemCode'|t(data.lc)}}\n      </h1>\n      <button type=\"button\" class=\"button button-icon button-clear\" v-on:click.prevent=\"hideModal()\">\n          <span class=\"icon kb-close size32 vAlignTop\"></span>\n      </button>\n    </ion-header-bar>\n    <ion-content class=\"padding\" :scroll=\"false\">\n        <div class=\"list\" style=\"margin-bottom:2px\">\n            <label class=\"item item-input\">\n                <input type=\"text\" v-autofocus v-model=\"modal.redeemCodeInfo.model.code\" v-autofocus v-on:keyup.enter=\"modal.redeemCodeInfo.submit()\" v-bind:placeholder=\"'enterPromoCode'|t(data.lc)\">\n            </label>\n        </div>\n    </ion-content>\n    <ion-footer-bar>\n        <div class=\"row\">\n            <div class=\"col\">\n                <button type=\"button\" class=\"button button-block button-positive zeroMargin\" v-on:click.prevent=\"modal.redeemCodeInfo.submit()\">\n                    {{'redeem'|t(data.lc)}}\n                </button>\n            </div>\n        </div>\n    </ion-footer-bar>\n</div>","templates/panels/rightPanel.html":"<div>\n    <div id=\"miniappsMenu\" v-if=\"sideMenuMode=='miniapps'\">\n        <miniapps></miniapps>\n        \n    </div>\n\n    <div id=\"bookSettingsMenu\" v-if=\"sideMenuMode=='bookSettings'\">\n        \n        <bookSettings></bookSettings>\n    </div>\n\n    <div id=\"mediaMenu\" v-if=\"sideMenuMode=='media'\">\n        <ion-header-bar class=\"bg bar-dark\" no-tap-scroll=\"true\">\n            <a class=\"button button-icon button-clear\" v-on:click.prevent=\"closePanel()\">\n                <span class=\"icon kb-close size32 vAlignTop\"></span>\n            </a>\n            <h1 class=\"title\">{{'media'|t(data.lc)}}</h1>\n        </ion-header-bar>\n        <media ng-controller=\"MediaCtrl\" ng-init=\"init()\"></media>\n    </div>\n</div>","templates/panels/rightPanel.mobile.html":"<div>\n    <div id=\"miniappsMenu\" v-if=\"sideMenuMode=='miniapps'\">\n        <miniapps></miniapps>\n        \n    </div>\n\n    <div id=\"bookSettingsMenu\" v-if=\"sideMenuMode=='bookSettings'\">\n        \n        <bookSettings></bookSettings>\n    </div>\n\n    <div id=\"mediaMenu\" v-if=\"sideMenuMode=='media'\">\n        <ion-header-bar align-title=\"center\" class=\"bg bar-dark\">\n            <a class=\"button button-icon button-clear\" v-on:click.prevent=\"closePanel()\">\n                <span class=\"icon kb-close size32 vAlignTop\"></span>\n            </a>\n            <h1 class=\"title\">{{'media'|t(data.lc)}}</h1>\n        </ion-header-bar>\n        <ion-content :scroll=\"true\" direction=\"y\" zooming=\"false\" :overflow-scroll=\"false\" has-bouncing=\"true\" delegate-handle=\"media\" class=\"has-footer\">\n            <media ng-controller=\"MediaCtrl\" ng-init=\"init()\"></media>\n        </ion-content>\n    </div>\n</div>","templates/panels/sdk.html":"<div id=\"sdkProfiler\" ng-controller=\"SdkCtrl\" ng-init=\"init()\" v-bind:class=\"[expanded?'expanded':'']\">\n\n\n    <div color=\"dark\" v-on:click.prevent=\"profilerClicked()\" class=\"xbtn profilerBtn\">\n        <i class=\"icon ion-wrench\"></i>\n    </div>\n\n    \n\n\n    <div class=\"sdkBody\">\n\n        <div class=\"shortcuts\">\n            <a href v-on:click.prevent=\"toProfiles()\" class=\"button button-small floatRight\">↓ To Profiles</a>\n        </div>\n\n        <div class=\"currentConfig\">\n            <h4>Initial Configuration</h4>\n            <div v-for=\"(key, value) in config.kotobee\">\n                <p>\n                    <strong>{{key}}</strong>\n                    <span class=\"hSpace5\"></span>\n                    {{value}}\n                </p>\n            </div>\n            <h4>Updated Configuration</h4>\n            <div v-for=\"(key, value) in config.kotobee\">\n                <p>\n                    <strong>{{key}}</strong>\n                    <span class=\"hSpace5\"></span>\n                    {{value}}\n                </p>\n            </div>\n        </div>\n\n        <div class=\"profiles\">\n            <ion-toggle class=\"useProfilesSelector\" v-model=\"useProfiles\" toggle-class=\"toggle-calm\" v-on:change=\"useProfilesChanged()\">\n                Use SDK profiles\n            </ion-toggle>\n            <h4>Profiles</h4>\n            <div class=\"profileContainer\" v-for=\"(profile,index) in profiles\" :key=\"index\">\n                <div class=\"profile\" v-if=\"!profile.editing\" v-bind:class=\"[(profile.mode=='library')?'library':'book']\">\n                    {{profile.name}}\n                    <div class=\"summary\">\n                        <div v-if=\"profile.cloudid\" class=\"cloud\">\n                            <i class=\"icon ion-cloud\"></i>\n                            <span style=\"user-select:all\">\n                                {{profile.cloudid}}\n                            </span>\n                        </div>\n                        <div class=\"mode\">\n                            <i class=\"icon ion-document\"></i> {{profile.mode}}\n                        </div>\n                        <div class=\"liburl\">{{profile.liburl}}</div>\n                    </div>\n                    <div class=\"controlbar\">\n                        <div v-if=\"!profile.locked\">\n                            <a href v-on:click.prevent=\"editProfile(profile)\">\n                                <i class=\"icon ion-edit\"></i>\n                            </a>\n                            <a href v-on:click.prevent=\"delProfile(profile)\">\n                                <i class=\"icon ion-close-circled\"></i>\n                            </a>\n                        </div>\n                        <div v-if=\"profile.locked\">\n                            <a href v-on:click.prevent=\"lockedProfileClicked(profile)\" style=\"opacity:0.5\">\n                                <i class=\"icon ion-locked\"></i>\n                            </a>\n                        </div>\n                    </div>\n                    <div class=\"connect\">\n                        <button type=\"button\" class=\"button connectBtn button-small button-balanced zeroMargin\" v-on:click.prevent=\"connect(profile)\">\n                            Connect\n                        </button>\n                    </div>\n                </div>\n                <div v-if=\"profile.editing\">\n                    <div class=\"alignRight\">\n                        <button type=\"button\" class=\"button button-stable button-small zeroMargin\" v-on:click.prevent=\"cancelEditingProfile(profile)\">\n                            Cancel editing\n                        </button>\n                    </div>\n                    <sdk-profile-form :profile=\"profile\" :save=\"saveEditedProfile\" :cancel=\"cancelEditingProfile\"></sdk-profile-form>\n                </div>\n            </div>\n            <div class=\"newProfile\" v-if=\"!addingProfile\">\n                <button type=\"button\" class=\"button newProfileBtn button-medium button-positive zeroMargin\" v-on:click.prevent=\"showNewProfileForm()\">\n                    + Add Profile\n                </button>\n            </div>\n\n            <div class=\"addingProfile\" v-if=\"addingProfile\">\n                <h4>New Profile</h4>\n                <sdk-profile-form :profile=\"newProfile\" :save=\"addProfile\" :cancel=\"cancelNewProfileForm\"></sdk-profile-form>\n            </div>\n\n        </div>\n\n    </div>\n    \n</div>","templates/panels/sdkProfileForm.html":"<div class=\"profileForm\">\n    <div class=\"item item-text-wrap\">\n        <h4 class=\"padding5\">Name</h4>\n        <label class=\"item item-input\">\n            <input type=\"text\" v-model=\"profile.name\" v-autofocus placeholder=\"e.g. Test library1\">\n        </label>\n        <p class=\"exp\">The name to use for referencing this profile. This is used just for the SDK, and won't affect the reader in any way.</p>\n    </div>\n    <div class=\"item item-text-wrap\">\n        <h4 class=\"padding5\">Mode (mode variable)</h4>\n        <label class=\"item item-input\">\n            <input type=\"text\" v-model=\"profile.mode\" placeholder=\"book or library\">\n        </label>\n        <p class=\"exp\">Run the reader in book or library mode.</p>\n    </div>\n    <div class=\"item item-text-wrap\">\n        <h4 class=\"padding5\">URL (liburl variable)</h4>\n        <label class=\"item item-input\">\n            <input type=\"text\" v-model=\"profile.liburl\">\n        </label>\n        <p class=\"exp\">The server to use as the backend API. If you're not using a cloud ebook or library, this value isn't important.</p>\n    </div>\n    <div class=\"item item-text-wrap\">\n        <h4 class=\"padding5\">Cloud ID (cloudid variable)</h4>\n        <label class=\"item item-input\">\n            <input type=\"text\" v-model=\"profile.cloudid\" placeholder=\"e.g. 5c7699f3660db\">\n        </label>\n        <p class=\"exp\">Required for cloud ebooks and libraries. Leave blank to read the local epub folder.</p>\n    </div>\n    <div class=\"item item-text-wrap\">\n        <h4 class=\"padding5\">Host Path (hostPath variable)</h4>\n        <label class=\"item item-input\">\n            <input type=\"text\" v-model=\"profile.hostPath\" placeholder=\"e.g. fb2f14e181\">\n        </label>\n        <p class=\"exp\">[New] If this is filled, remote book content will be used instead of the local epub folder.</p>\n    </div>\n    <div class=\"item item-text-wrap alignRight\">\n        <button type=\"button\" class=\"button button-positive zeroMargin\" v-on:click.prevent=\"save(profile)\">\n            Save\n        </button>\n        <button type=\"button\" class=\"button button-stable zeroMargin\" v-on:click.prevent=\"cancel(profile)\">\n            Cancel\n        </button>\n    </div>\n\n</div>","templates/panels/search.html":"<div ng-init=\"srchOb={enabled:false}\">\n    <ion-header-bar v-show=\"!srchOb.enabled\" no-tap-scroll=\"true\">\n        <button type=\"button\" class=\"button button-icon\" v-show=\"searchContent.notEmpty\"\n           v-on:click.prevent=\"srchOb.enabled = !srchOb.enabled;\">\n            <span class=\"icon kb-search size24\"></span>\n         </button>\n        <h1 class=\"title\" v-bind:class=\"[searchContent.notEmpty?'titleOffset':'']\">{{(searchContent.notEmpty?\"results\":\"search\")|t(data.lc)}}</h1>\n        <a class=\"button button-icon button-clear\" v-on:click.prevent=\"hideModal()\">\n            <span class=\"icon kb-close size32 vAlignTop\"></span>\n        </a>\n    </ion-header-bar>\n    <header-search :onValue=\"true\" :offValue=\"false\" :obj=\"srchOb\" prop=\"enabled\" :searchKeyOb=\"searchContent\" v-on:search=\"search\"></header-search>\n    <ion-content class=\"has-header has-footer content\" :padding=\"true\" delegate-handle=\"searchScroll\" id=\"searchScroll\"\n                 :autoscroll=\"false\" :overflow-scroll=\"true\"\n                 has-bouncing=\"true\">\n        <div v-if=\"!searchContent.notEmpty\">\n            <div class=\"list list-inset\" style=\"margin-top: 0px;\">\n                <div class=\"vSpace20\"></div>\n                <h5><p v-if=\"searchContent.results\" class=\"error\">{{\"noResultsFound\"|t(data.lc)}}</p> {{'enterSearchKeywords'|t(data.lc)}}</h5>\n                <label class=\"item item-input\">\n                    <span class=\"icon kb-search placeholder-icon\"></span>\n                    <input id=\"searchModalField\" type=\"search\" v-bind:placeholder=\"'search'|t(data.lc)\" v-autofocus  v-on:keyup.enter=\"search(key)\" v-model=\"key\">\n                </label>\n                \n                <div class=\"row responsive-sm searchOptions\">\n                    \n                  <label class=\"item item-input noBorder noPadding\">\n                    <ion-toggle v-model=\"isExactWord\" toggle-class=\"toggle-calm\" style=\"height: 50px; border: none;\"></ion-toggle>\n                    <div class=\"input-label alignRight noPadding\">{{'searchExactWord'|t(data.lc)}}</div>\n                  </label>\n                    \n                  <label class=\"item item-input noBorder noPadding\">\n                    <ion-toggle v-model=\"isMatchCase\" toggle-class=\"toggle-calm\" style=\"height: 50px; border: none;\"></ion-toggle>\n                    <div class=\"input-label alignRight noPadding\">{{'searchMatchCase'|t(data.lc)}}</div>\n                  </label>\n                </div>\n\n                <div class=\"row responsive-sm\">\n                    <div class=\"col\">\n                        <button type=\"button\" class=\"button button-full button-positive noMargin\" v-on:click.prevent=\"search(key)\">\n                            <span class=\"icon kb-book-open size18\"></span> {{'searchBook'|t(data.lc)}}\n                        </button>\n                    </div>\n                    <div class=\"col\">\n                        <button type=\"button\" class=\"button button-full button-positive noMargin\" v-on:click.prevent=\"locateInChapter(key)\">\n                            <span class=\"icon kb-file size16\"></span> {{'searchChapterOnly'|t(data.lc)}}\n                        </button>\n                    </div>\n                </div>\n            </div>\n        </div>\n\n        <div v-if=\"searchContent.notEmpty\">\n            <ion-list v-if=\"searchContent && searchContent.results && searchContent.results.length\">\n                <template v-for=\"category in searchContent.results\">\n                    <template v-if=\"category && category.matches && category.matches.length\">\n                        <a href class=\"ion-item item\"> \n                            \n                            <strong>{{category.title}}</strong>\n                        </a>\n                        <a href v-for=\"(item, index) in category.matches\" class=\"item item-icon-left\"\n                        v-on:click.prevent=\"searchItemClicked({item:item,index:index,matches:category.matches})\">\n                        <i class=\"icon size16\" v-bind:class=\"[data.settings.rtl?'kb-chevron-left':'kb-chevron-right']\"></i>\n                        \n                        <p v-html=\"item.hint\"></p>\n                        \n                        \n                            \n                            \n                        </a>\n                    </template>\n                </template>\n\n                \n                <div class=\"button-bar bar-primary\" style=\"padding:5px\">\n                    <a class=\"button button-dark\" v-if=\"searchStack.length>1\" style=\"margin:2px\" v-on:click.prevent=\"searchBack()\">{{'prevResults'|t(data.lc)}}\n                    </a>\n                    <a class=\"button button-dark\" v-if=\"searchContent.more\" style=\"margin:2px\" v-on:click.prevent=\"searchMore()\">{{'nextResults'|t(data.lc)}}\n                    </a>\n                </div>\n\n\n            </ion-list>\n            \n        </div>\n    </ion-content>\n    <ion-footer-bar class=\"bar-light\" v-if=\"searchContent.notEmpty\">\n        <div class=\"row\">\n            <div class=\"col textCenter\">\n                <a class=\"button button-clear footerBtn\" v-on:click.prevent=\"locateInChapter(searchContent.key)\">\n                    <i class=\"icon kb-redo size21\"></i>\n                    {{'locateResults'|t(data.lc)}}</a>\n            </div>\n            <div class=\"col textCenter\">\n                <a class=\"button button-clear footerBtn\" v-on:click.prevent=\"clearResults()\">\n                    <i class=\"icon kb-trash size21\"></i>\n                    {{'clearResults'|t(data.lc)}}</a>\n            </div>\n        </div>\n    </ion-footer-bar>\n\n</div>","templates/panels/search.mobile.html":"<div ng-init=\"srchOb={enabled:false}\">\n    \n    <ion-header-bar align-title=\"center\" v-show=\"!srchOb.enabled\" no-tap-scroll=\"true\">\n        <button class=\"button button-icon\" v-show=\"searchContent.notEmpty\"\n           v-on:click.prevent=\"srchOb.enabled = !srchOb.enabled\">\n            <span class=\"icon kb-search size24\"></span>\n        </button>\n        <h1 class=\"title\" v-bind:class=\"[searchContent.notEmpty?'titleOffset':'']\">{{(searchContent.notEmpty?\"results\":\"search\")|t(data.lc)}}</h1>\n        <a class=\"button button-icon button-clear button-clear\" v-on:click.prevent=\"hideModal()\">\n            <span class=\"icon kb-close size32 vAlignTop\"></span>\n        </a>\n    </ion-header-bar>\n    <header-search :onValue=\"true\" :offValue=\"false\" :obj=\"srchOb\" prop=\"enabled\" :searchKeyOb=\"searchContent\" v-on:search=\"search\"></header-search>\n    <ion-content class=\"has-header has-footer\" :padding=\"true\" delegate-handle=\"searchScroll\" :xscroll=\"true\" id=\"searchScroll\"\n                 :autoscroll=\"false\" :overflow-scroll=\"false\"\n                 has-bouncing=\"true\">\n        <div v-if=\"!searchContent.notEmpty\">\n            <div class=\"list list-inset\" style=\"margin-top: 0px;\">\n                \n                <h5><p v-if=\"searchContent.results\" class=\"error\">{{\"noResultsFound\"|t(data.lc)}}</p> {{'enterSearchKeywords'|t(data.lc)}}</h5>\n                <label class=\"item item-input\">\n                    <span class=\"icon kb-search placeholder-icon\"></span>\n                    <input id=\"searchModalField\" type=\"search\" v-bind:placeholder=\"'search'|t(data.lc)\" v-autofocus v-on:keyup.enter=\"search(key)\" v-model=\"key\">\n                </label>\n\n                <div class=\"row responsive-sm searchOptions\">\n                    \n                    <label class=\"item item-input noBorder noPadding\">\n                        <ion-toggle v-model=\"isExactWord\" toggle-class=\"toggle-calm\" style=\"height: 50px; border: none;\"></ion-toggle>\n                      <div class=\"input-label alignRight noPadding\">{{'searchExactWord'|t(data.lc)}}</div>\n                    </label>\n                    \n                    <label class=\"item item-input noBorder noPadding\">\n                        <ion-toggle v-model=\"isMatchCase\" toggle-class=\"toggle-calm\" style=\"height: 50px; border: none;\"></ion-toggle>\n                        <div class=\"input-label alignRight noPadding\">{{'searchMatchCase'|t(data.lc)}}</div>\n                    </label>\n                </div>\n                  \n                <div class=\"row responsive-sm\">\n                    <div class=\"col\">\n                        <button class=\"button button-full button-positive noMargin\" v-on:click.prevent=\"search(key)\">\n                            <span class=\"icon kb-book-open size18\"></span> {{'searchBook'|t(data.lc)}}\n                        </button>\n                    </div>\n                    <div class=\"col\">\n                        <button class=\"button button-full button-positive noMargin\" v-on:click.prevent=\"locateInChapter(key)\">\n                            <span class=\"icon kb-file size16\"></span> {{'searchChapterOnly'|t(data.lc)}}\n                        </button>\n                    </div>\n                </div>\n            </div>\n        </div>\n\n        <div v-if=\"searchContent.notEmpty\">\n            <ion-list v-if=\"searchContent && searchContent.results && searchContent.results.length\">\n                <template v-for=\"category in searchContent.results\">\n                    <template v-if=\"category && category.matches && category.matches.length\">\n                        <a href class=\"ion-item item\"> \n                            \n                            <strong>{{category.title}}</strong>\n                        </a>\n                        <a href v-for=\"(item, index) in category.matches\" class=\"item item-icon-left\"\n                        v-on:click.prevent=\"searchItemClicked({item:item,index:index,matches:category.matches})\">\n                        <i class=\"icon size16\" v-bind:class=\"[data.settings.rtl?'kb-chevron-left':'kb-chevron-right']\"></i>\n                        \n                        <p v-html=\"item.hint\"></p>\n                        \n                        \n                            \n                            \n                        </a>\n                    </template>\n                </template>\n                \n                <div class=\"button-bar bar-primary\" style=\"padding:5px\">\n                    <a class=\"button button-dark\" v-if=\"searchStack.length>1\" style=\"margin:2px\" v-on:click.prevent=\"searchBack()\">{{'prevResults'|t(data.lc)}}\n                    </a>\n                    <a class=\"button button-dark\" v-if=\"searchContent.more\" style=\"margin:2px\" v-on:click.prevent=\"searchMore()\">{{'nextResults'|t(data.lc)}}\n                    </a>\n                </div>\n\n\n            </ion-list>\n            \n        </div>\n    </ion-content>\n    <ion-footer-bar class=\"bar-light\" v-if=\"searchContent.notEmpty\">\n        <div class=\"row\">\n            <div class=\"col textCenter\">\n                <a class=\"button button-clear footerBtn\" v-on:click.prevent=\"locateInChapter(searchContent.key)\">\n                    <i class=\"icon kb-redo size21\"></i>\n                    {{'locateResults'|t(data.lc)}}</a>\n            </div>\n            <div class=\"col textCenter\">\n                <a class=\"button button-clear footerBtn\" v-on:click.prevent=\"clearResults()\">\n                    <i class=\"icon kb-trash size21\"></i>\n                    {{'clearResults'|t(data.lc)}}</a>\n            </div>\n        </div>\n    </ion-footer-bar>\n\n</div>","templates/panels/share.html":"<div ng-controller=\"ShareCtrl\" id=\"sharePanel\" ng-init=\"init()\">\n    <ion-header-bar>\n        <h1 class=\"title\">{{'share'|t(data.lc)}}</h1>\n        <a class=\"button button-icon closeBtn button-clear\" v-on:click.prevent=\"shareClose()\">\n            <span class=\"icon kb-close size32 vAlignTop\"></span>\n        </a>\n    </ion-header-bar>\n    <div class=\"bar bar-subheader\">\n        <div class=\"button-bar noteBtns\">\n            <a class=\"button\" v-bind:class=\"[(currentTab=='embed')?'button-dark':'']\"\n               v-on:click.prevent=\"openTab('embed')\">{{'smartEmbed'|t(data.lc)}}</a>\n            <a class=\"button\" v-bind:class=\"[(currentTab=='social')?'button-dark':'']\"\n               v-on:click.prevent=\"openTab('social')\">{{'socialShare'|t(data.lc)}}</a>\n        </div>\n    </div>\n    <ion-content delegate-handle=\"shareScroll\" class=\"shareContent has-header has-subheader\"\n                 :padding=\"true\" :overflow-scroll=\"true\">\n\n        <div v-if=\"currentTab=='embed'\">\n            <h3>{{'smartEmbed'|t(data.lc)}}</h3>\n            <p>{{'smartEmbedDesc'|t(data.lc)}}</p>\n            <div class=\"textarea\">\n                <textarea class=\"embedCode\" rows=\"3\" v-autofocus v-model=\"embedCode\" v-on:click.prevent=\"embedCodeClicked()\" readonly></textarea>\n            </div>\n\n            <div class=\"options\">\n                <h4>{{'options'|t(data.lc)}}</h4>\n                <ion-checkbox class=\"bookTitle\" v-model=\"options.bookTitle\" v-on:change=\"optionChanged()\">\n                    {{'bookTitle'|t(data.lc)}}\n                </ion-checkbox>\n                <ion-checkbox class=\"bookTitle\" v-model=\"options.bookAuthor\" v-on:change=\"optionChanged()\">\n                    {{'bookAuthor'|t(data.lc)}}\n                </ion-checkbox>\n                <ion-checkbox class=\"bookTitle\" v-model=\"options.bookPublisher\" v-on:change=\"optionChanged()\">\n                    {{'bookPublisher'|t(data.lc)}}\n                </ion-checkbox>\n                \n                    \n                \n            </div>\n            \n        </div>\n\n        <div v-if=\"currentTab=='social'\">\n            <h3>{{'socialShare'|t(data.lc)}}</h3>\n            <p>{{'socialShareDesc'|t(data.lc)}}</p>\n            <div class=\"links\">\n                <a class=\"twitter\" v-bind:href=\"twitterLink\" target=\"_blank\">\n                    <svg class=\"icon\" viewBox=\"0 0 32 32\">\n                        <path d=\"M32 7.075c-1.175 0.525-2.444 0.875-3.769 1.031 1.356-0.813 2.394-2.1 2.887-3.631-1.269 0.75-2.675 1.3-4.169 1.594-1.2-1.275-2.906-2.069-4.794-2.069-3.625 0-6.563 2.938-6.563 6.563 0 0.512 0.056 1.012 0.169 1.494-5.456-0.275-10.294-2.888-13.531-6.862-0.563 0.969-0.887 2.1-0.887 3.3 0 2.275 1.156 4.287 2.919 5.463-1.075-0.031-2.087-0.331-2.975-0.819 0 0.025 0 0.056 0 0.081 0 3.181 2.263 5.838 5.269 6.437-0.55 0.15-1.131 0.231-1.731 0.231-0.425 0-0.831-0.044-1.237-0.119 0.838 2.606 3.263 4.506 6.131 4.563-2.25 1.762-5.075 2.813-8.156 2.813-0.531 0-1.050-0.031-1.569-0.094 2.913 1.869 6.362 2.95 10.069 2.95 12.075 0 18.681-10.006 18.681-18.681 0-0.287-0.006-0.569-0.019-0.85 1.281-0.919 2.394-2.075 3.275-3.394z\"></path>\n                    </svg>\n                </a>\n                <a class=\"fb\" v-bind:href=\"fbLink\" target=\"_blank\">\n                    <svg class=\"icon\" viewBox=\"0 0 32 32\">\n                        <path d=\"M29 0h-26c-1.65 0-3 1.35-3 3v26c0 1.65 1.35 3 3 3h13v-14h-4v-4h4v-2c0-3.306 2.694-6 6-6h4v4h-4c-1.1 0-2 0.9-2 2v2h6l-1 4h-5v14h9c1.65 0 3-1.35 3-3v-26c0-1.65-1.35-3-3-3z\"></path>\n                    </svg>\n                </a>\n                <a class=\"whatsapp\" v-bind:href=\"whatsappLink\" target=\"_blank\">\n                    <svg class=\"icon\" viewBox=\"0 0 32 32\">\n                        <path d=\"M27.281 4.65c-2.994-3-6.975-4.65-11.219-4.65-8.738 0-15.85 7.112-15.85 15.856 0 2.794 0.731 5.525 2.119 7.925l-2.25 8.219 8.406-2.206c2.319 1.262 4.925 1.931 7.575 1.931h0.006c0 0 0 0 0 0 8.738 0 15.856-7.113 15.856-15.856 0-4.238-1.65-8.219-4.644-11.219zM16.069 29.050v0c-2.369 0-4.688-0.637-6.713-1.837l-0.481-0.288-4.987 1.306 1.331-4.863-0.313-0.5c-1.325-2.094-2.019-4.519-2.019-7.012 0-7.269 5.912-13.181 13.188-13.181 3.519 0 6.831 1.375 9.319 3.862 2.488 2.494 3.856 5.8 3.856 9.325-0.006 7.275-5.919 13.188-13.181 13.188zM23.294 19.175c-0.394-0.2-2.344-1.156-2.706-1.288s-0.625-0.2-0.894 0.2c-0.262 0.394-1.025 1.288-1.256 1.556-0.231 0.262-0.462 0.3-0.856 0.1s-1.675-0.619-3.188-1.969c-1.175-1.050-1.975-2.35-2.206-2.744s-0.025-0.613 0.175-0.806c0.181-0.175 0.394-0.463 0.594-0.694s0.262-0.394 0.394-0.662c0.131-0.262 0.069-0.494-0.031-0.694s-0.894-2.15-1.219-2.944c-0.319-0.775-0.65-0.669-0.894-0.681-0.231-0.012-0.494-0.012-0.756-0.012s-0.694 0.1-1.056 0.494c-0.363 0.394-1.387 1.356-1.387 3.306s1.419 3.831 1.619 4.1c0.2 0.262 2.794 4.269 6.769 5.981 0.944 0.406 1.681 0.65 2.256 0.837 0.95 0.3 1.813 0.256 2.494 0.156 0.762-0.113 2.344-0.956 2.675-1.881s0.331-1.719 0.231-1.881c-0.094-0.175-0.356-0.275-0.756-0.475z\"></path>\n                    </svg>\n                </a>\n            </div>\n\n            <h3>{{'directLink'|t(data.lc)}}</h3>\n            <p>{{'copyLinkDesc'|t(data.lc)}}</p>\n            <div class=\"textarea\">\n                <textarea class=\"shareLink\" rows=\"2\" v-autofocus v-model=\"shareLink\" v-on:click.prevent=\"shareLinkClicked()\" readonly></textarea>\n            </div>\n\n        </div>\n\n\n    </ion-content>\n\n    <ion-footer-bar>\n        <div class=\"row\">\n            <div class=\"col\">\n                <button v-if=\"currentTab=='embed'\" type=\"button\" class=\"button button-positive button-block\" v-on:click.prevent=\"copyEmbedCode()\">\n                    {{'copyCode'|t(data.lc)}}\n                </button>\n                <button v-if=\"currentTab=='social'\" type=\"button\" class=\"button button-positive button-block\" v-on:click.prevent=\"copyLink()\">\n                    {{'copyLink'|t(data.lc)}}\n                </button>\n\n            </div>\n        </div>\n    </ion-footer-bar>\n    \n</div>","templates/panels/ssoEmailError.html":"<div>\n    <ion-header-bar align-title=\"center\" class=\"bar-assertive\">\n        <h1 class=\"title\">{{'ssoEmailError'|t(data.lc)}}</h1>\n        <button type=\"button\" class=\"button button-icon button-clear\" v-on:click.prevent=\"hideModal(null, modal.errorOptions.cb)\">\n            <span class=\"icon kb-close size32 vAlignTop\"></span>\n        </button>\n    </ion-header-bar>\n    <ion-content class=\"padding\">\n        <h5 class=\"center\">{{'ssoEmailErrorMsg'|t(data.lc,modal.errorOptions.provider)}}</h5>\n    </ion-content>\n    <ion-footer-bar v-if=\"modal.errorOptions.cb\">\n        <div class=\"row\">\n            <div class=\"col\">\n                <button type=\"button\" class=\"button button-block button-stable zeroMargin\" v-on:click.prevent=\"hideModal(null, modal.errorOptions.cb)\">{{'ok'|t(data.lc)}}</button>\n            </div>\n        </div>\n    </ion-footer-bar>\n</div>","templates/panels/success.html":"<div>\n    <ion-header-bar align-title=\"center\" class=\"bar-dark\">\n        <h1 class=\"title\">{{'success'|t(data.lc)}}</h1>\n        <button type=\"button\" class=\"button button-icon button-clear\" v-on:click.prevent=\"hideModal()\">\n            <span class=\"icon kb-close size32 vAlignTop\"></span>\n        </button>\n    </ion-header-bar>\n    <ion-content class=\"padding\" v-bind:overflow-scroll=\"config.overflowScroll\">\n        <h5 class=\"center\">{{modal.successOptions.msg}}</h5>\n    </ion-content>\n</div>","templates/panels/success.mobile.html":"<div>\n    <ion-header-bar align-title=\"center\" class=\"bar-dark\">\n        <h1 class=\"title\">{{'success'|t(data.lc)}}</h1>\n        <button class=\"button button-icon button-clear\" v-on:click.prevent=\"hideModal()\">\n            <span class=\"icon kb-close size32 vAlignTop\"></span>\n        </button>\n    </ion-header-bar>\n    <ion-content class=\"padding\" :overflow-scroll=\"false\">\n        <h5 class=\"center\">{{modal.successOptions.msg}}</h5>\n    </ion-content>\n</div>","templates/panels/tabMenu.html":"<div ng-controller=\"TabMenuCtrl\" ng-init=\"init()\" class=\"tabs tabs-icon-top tabs-positive ui\">\n    <a class=\"next chapterBtn button button-clear\" v-bind:disabled=\"!currentChapter.notLastChapter\" v-on:click.prevent=\"nextChapter()\">\n        <span>{{'next'|t(data.lc)}}</span>\n        <i class=\"inlineBlock icon size21\" style=\"margin-top:2px\" v-bind:class=\"[contentRtl?'kb-chevron-thin-left':'kb-chevron-thin-right']\"></i>\n    </a>\n\n    <a class=\"prev chapterBtn button button-clear\" v-bind:disabled=\"!currentChapter.notFirstChapter\" v-on:click.prevent=\"prevChapter()\">\n        <i class=\"inlineBlock icon size21\" style=\"margin-top:2px\" v-bind:class=\"[contentRtl?'kb-chevron-thin-right':'kb-chevron-thin-left']\"></i>\n        <span>{{'prev'|t(data.lc)}}</span>\n    </a>\n\n    <a v-if=\"config.chaptersTab\" v-on:click.prevent=\"chaptersClicked()\" class=\"tocBtn tab-item disable-user-behavior active\">\n        <i class=\"icon kb-chapters size26\"></i>\n        <span class=\"tab-title\">{{'chapters'|t(data.lc)}}</span>\n    </a>\n    <a v-if=\"config.mediaTab\" v-on:click.prevent=\"mediaClicked()\" class=\"tab-item disable-user-behavior active\">\n        <i class=\"icon kb-media size24\"></i>\n        <span class=\"tab-title\">{{'media'|t(data.lc)}}</span>\n    </a>\n    \n    <a v-if=\"config.notebookTab\" v-on:click.prevent=\"notebookClicked()\" class=\"tab-item disable-user-behavior active\">\n        <i class=\"icon kb-notebook size26\"></i>\n        <span class=\"tab-title\">{{'notebook'|t(data.lc)}}</span>\n    </a>\n    <a v-if=\"config.searchBooks\" v-on:click.prevent=\"searchClicked()\" class=\"tab-item disable-user-behavior active\">\n        <i class=\"icon kb-search size24\"></i>\n        <span class=\"tab-title\">{{'search'|t(data.lc)}}</span>\n    </a>\n    <a v-if=\"config.pdfsaving && (!desktop && !native)\" v-on:click.prevent=\"pdfClicked()\" class=\"tab-item disable-user-behavior active\">\n        <i class=\"icon kb-pdf size24\"></i>\n        <span class=\"tab-title\">{{'pdf'|t(data.lc)}}</span>\n    </a>\n    <a v-if=\"config.printing && !native\" v-on:click.prevent=\"printClicked()\" class=\"tab-item disable-user-behavior active printBtn\">\n        <i class=\"icon kb-print size26\"></i>\n        <span class=\"tab-title\">{{'print'|t(data.lc)}}</span>\n    </a>\n    <a v-if=\"config.shareTab && (!desktop && !native && !readerApp)\" v-on:click.prevent=\"shareClicked()\" class=\"tab-item disable-user-behavior active\">\n        <i class=\"icon kb-share size26\"></i>\n        <span class=\"tab-title\">{{'share'|t(data.lc)}}</span>\n    </a>\n    <a v-if=\"config.settingsTab\" v-on:click.prevent=\"settingsClicked()\" class=\"tab-item disable-user-behavior active\">\n        <i class=\"icon kb-cog size26\"></i>\n        <span class=\"tab-title\">{{'settings'|t(data.lc)}}</span>\n    </a>\n\n    <a v-for=\"tab in config.customTabs\" v-on:click.prevent=\"customTabClicked(tab, $event)\" class=\"tab-item disable-user-behavior active customTab\">\n        <span v-if=\"tab.iconStyle\" v-bind:style=\"tab.iconStyle\" class=\"customStyle\">\n        </span>\n        <span v-if=\"!tab.icon\">\n            <i class=\"icon kb-external-link size26\" v-if=\"tab.type=='website'\"></i>\n            <i class=\"icon kb-book-reference size26\" v-if=\"tab.type=='book'\"></i>\n            <i class=\"icon kb-info size26\" v-if=\"tab.type=='popup'\"></i>\n            <i class=\"icon kb-play-circle size26\" v-if=\"tab.type=='audio'\"></i>\n        </span>\n        <span class=\"tab-title\">{{tab.title}}</span>\n    </a>\n\n</div>","templates/panels/tabMenu.mobile.html":"<div ng-controller=\"TabMenuCtrl\" ng-init=\"init()\" class=\"tabs tabs-icon-top tabs-positive ui\">\n    <a v-if=\"config.chaptersTab\" v-on:click.prevent=\"chaptersClicked()\" class=\"tab-item disable-user-behavior active\">\n        <i class=\"icon kb-chapters size26\"></i>\n        <span class=\"tab-title\">{{'chapters'|t(data.lc)}}</span>\n    </a>\n    <a v-if=\"config.mediaTab\" v-on:click.prevent=\"mediaClicked()\" class=\"tab-item disable-user-behavior active\">\n        <i class=\"icon kb-media size24\"></i>\n        <span class=\"tab-title\">{{'media'|t(data.lc)}}</span>\n    </a>\n    \n    <a v-if=\"config.notebookTab\" v-on:click.prevent=\"notebookClicked()\" class=\"tab-item disable-user-behavior active\">\n        <i class=\"icon kb-notebook size26\"></i>\n        <span class=\"tab-title\">{{'notebook'|t(data.lc)}}</span>\n    </a>\n    <a v-if=\"config.searchBooks\" v-on:click.prevent=\"searchClicked()\" class=\"tab-item disable-user-behavior active\">\n        <i class=\"icon kb-search size24\"></i>\n        <span class=\"tab-title\">{{'search'|t(data.lc)}}</span>\n    </a>\n    <a v-if=\"config.pdfsaving && (!desktop && !native)\" v-on:click.prevent=\"pdfClicked()\" class=\"tab-item disable-user-behavior active\">\n        <i class=\"icon kb-pdf size24\"></i>\n        <span class=\"tab-title\">{{'pdf'|t(data.lc)}}</span>\n    </a>\n    <a v-if=\"config.printing && !native\" v-on:click.prevent=\"printClicked()\" class=\"tab-item disable-user-behavior active printBtn\">\n        <i class=\"icon kb-print size26\"></i>\n        <span class=\"tab-title\">{{'print'|t(data.lc)}}</span>\n    </a>\n    <a v-if=\"config.shareTab && (!desktop && !native && !readerApp)\" v-on:click.prevent=\"shareClicked()\" class=\"tab-item disable-user-behavior active\">\n        <i class=\"icon kb-share size26\"></i>\n        <span class=\"tab-title\">{{'share'|t(data.lc)}}</span>\n    </a>\n    <a v-if=\"config.settingsTab\" v-on:click.prevent=\"settingsClicked()\" class=\"tab-item disable-user-behavior active\">\n        <i class=\"icon kb-cog size26\"></i>\n        <span class=\"tab-title\">{{'settings'|t(data.lc)}}</span>\n    </a>\n\n    <a v-for=\"tab in config.customTabs\" v-on:click.prevent=\"customTabClicked(tab, $event)\" class=\"tab-item disable-user-behavior active customTab\">\n        <span v-if=\"tab.iconStyle\" v-bind:style=\"tab.iconStyle\" class=\"customStyle\">\n        </span>\n        <span v-if=\"!tab.icon\">\n            <i class=\"icon kb-external-link size26\" v-if=\"tab.type=='website'\"></i>\n            <i class=\"icon kb-book-reference size26\" v-if=\"tab.type=='book'\"></i>\n            <i class=\"icon kb-info size26\" v-if=\"tab.type=='popup'\"></i>\n            <i class=\"icon kb-play-circle size26\" v-if=\"tab.type=='audio'\"></i>\n        </span>\n        <span class=\"tab-title\">{{tab.title}}</span>\n    </a>\n\n</div>","templates/panels/ttsOptions.html":"<div class=\"ttsOptions\">\n  <ion-header-bar align-title=\"center\" class=\"bar-dark\">\n      <h1 class=\"title\">{{'ttsOptions'|t(data.lc)}}</h1>\n      <a class=\"button button-icon closeBtn button-clear\" v-on:click.prevent=\"hideModal()\">\n          <span class=\"icon kb-close size32 vAlignTop\"></span>\n      </a>\n  </ion-header-bar>\n\n  <ion-content class=\"has-header has-footer\">\n        <div class=\"ttsVoicesContent\" v-if=\"data.modal.ttsOptions.voices && data.modal.ttsOptions.voices.length\">\n            <div class=\"intro\">{{'ttsVoicesHint'|t(data.lc)}}</div>\n            <div class=\"langDropdown\">\n                <label class=\"item item-input xnoBorder\">\n                    <select class=\"item item-input noBorder\" v-model=\"data.modal.ttsOptions.lang\">\n                        <option v-for=\"voice in data.modal.ttsOptions.voices\" :value=\"voice.lang\">{{voice.name}}</option>\n                    </select>\n                </label>\n            </div>\n            <p v-if=\"data.modal.ttsOptions.error\" class=\"error\">{{'ttsVoicesError'|t(data.lc)}}</p>\n        </div>\n        <div class=\"ttsAuto\">\n            <ion-toggle class=\"autoToggle\" v-model=\"data.settings.ttsAuto\" :xinline=\"true\" :transparent=\"true\" :xsmall=\"true\" toggle-class=\"toggle-calm\" xv-on:change=\"settingsChanged()\">\n                {{'auto'|t(data.lc)}}\n            </ion-toggle>\n            <p>\n                {{'ttsAutoDesc'|t(data.lc)}}\n            </p>\n        </div>\n  </ion-content>\n  \n  <ion-footer-bar>\n      <div class=\"row\">\n        <div class=\"col\">\n            <a class=\"button button-block button-positive zeroMargin\" @click=\"data.modal.ttsOptions.save()\">{{'save'|t(data.lc)}}</a>\n        </div>\n      </div>\n  </ion-footer-bar>\n</div>","templates/panels/ttsVoices.html":"<div>\n  <ion-header-bar align-title=\"center\" class=\"bar-dark\">\n      <h1 class=\"title\">{{'tts'|t(data.lc)}}</h1>\n      <a class=\"button button-icon closeBtn button-clear\" v-on:click.prevent=\"hideModal()\">\n          <span class=\"icon kb-close size32 vAlignTop\"></span>\n      </a>\n  </ion-header-bar>\n\n  <ion-content class=\"ttsVoicesContent has-header has-footer\">\n        <div class=\"intro\">{{'ttsVoicesHint'|t(data.lc)}}</div>\n        <div class=\"langDropdown\">\n            <label class=\"item item-input xnoBorder\">\n                <select class=\"item item-input noBorder\" v-model=\"data.modal.ttsOptions.lang\">\n                    <option v-for=\"voice in data.modal.ttsOptions.voices\" :value=\"voice.lang\">{{voice.name}}</option>\n                </select>\n            </label>\n        </div>\n        <p v-if=\"data.modal.ttsOptions.error\" class=\"error\">{{'ttsVoicesError'|t(data.lc)}}</p>\n  </ion-content>\n  \n  <ion-footer-bar>\n      <div class=\"row\">\n        <div class=\"col\">\n            <a class=\"button button-block button-positive zeroMargin\" @click=\"data.modal.ttsOptions.save()\">{{'save'|t(data.lc)}}</a>\n        </div>\n      </div>\n  </ion-footer-bar>\n</div>","templates/panels/update.html":"<div>\n    <ion-header-bar align-title=\"center\">\n        <h1 class=\"title\">{{modal.title}}</h1>\n        <a class=\"button button-icon button-clear\" v-on:click.prevent=\"hideModal()\">\n            <span class=\"icon kb-close size32 vAlignTop\"></span>\n        </a>\n    </ion-header-bar>\n    <ion-content class=\"padding\" :scroll=\"false\">\n        <p><strong>{{'yourCurrentVersion'|t(data.lc)}}</strong> {{modal.updateOptions.currentVersion}}</p>\n        <p><strong>{{'availableVersion'|t(data.lc)}}</strong> {{modal.updateOptions.release.version}}</p>\n        <p><strong>{{'description'|t(data.lc)}}</strong></p>\n        <p>\n            <span v-html=\"htmlSafe(modal.updateOptions.release.description)\"></span>\n        </p>\n        <p>\n            <strong>{{'availablePlatforms'|t(data.lc)}}</strong>\n            <ul>\n                <li v-for=\"platform in modal.updateOptions.release.platforms\">{{platform|releasePlatform}}</li>\n            </ul>\n        </p>\n    </ion-content>\n\n    <ion-footer-bar>\n        <div class=\"row\">\n            <div class=\"col\">\n                <button type=\"button\" class=\"button button-block button-positive zeroMargin\" v-on:click.prevent=\"modal.updateOptions.download()\" v-autofocus>\n                    {{'updateNow'|t(data.lc)}}\n                </button>\n            </div>\n            <div class=\"col\" v-if=\"!modal.updateOptions.mandatory\" >\n                <button type=\"button\" class=\"button button-block button-light zeroMargin\" v-on:click.prevent=\"hideModal()\">{{'cancel'|t(data.lc)}}</button>\n            </div>\n        </div>\n    </ion-footer-bar>\n</div>","templates/panels/video.html":"<div>\n    <ion-header-bar align-title=\"center\">\n        <h1 class=\"title\">{{modal.video.title}}</h1>\n        <a class=\"button button-icon button-clear\" v-on:click.prevent=\"hideModal()\">\n            <span class=\"icon kb-close size32 vAlignTop\"></span>\n        </a>\n    </ion-header-bar>\n    <ion-content class=\"padding alignCenter\" :scroll=\"false\">\n        <video style=\"max-width:100%;xmargin:10px\" controls autoplay>\n            <source v-bind:src=\"unsafeResource(modal.video.src)\">\n        </video>\n    </ion-content>\n</div>","templates/panels/waitTimeout.html":"<div>\n    <ion-header-bar align-title=\"center\" class=\"bar-dark\">\n        <h1 class=\"title\">{{'timeout'|t(data.lc)}}</h1>\n        <button type=\"button\" class=\"button button-icon button-clear\" v-on:click.prevent=\"hideModal()\">\n            <span class=\"icon kb-close size32 vAlignTop\"></span>\n        </button>\n    </ion-header-bar>\n    <ion-content class=\"padding\" :scroll=\"false\">\n        <h5 class=\"center\">{{'modalTimeoutError'|t(data.lc)}}</h5>\n    </ion-content>\n    <ion-footer-bar>\n        <div class=\"row\">\n            <div class=\"col\">\n                <button type=\"button\" class=\"button button-block button-positive zeroMargin\" v-on:click.prevent=\"modal.timeoutOptions.wait()\">\n                    {{'yes'|t(data.lc)}}\n                </button>\n            </div>\n            <div class=\"col\">\n                <button type=\"button\" class=\"button button-block button-light zeroMargin\" v-on:click.prevent=\"hideModal()\">{{'cancel'|t(data.lc)}}</button>\n            </div>\n        </div>\n    </ion-footer-bar>\n</div>","templates/panels/xFrameError.html":"<div>\n    <ion-header-bar align-title=\"center\" class=\"bar-dark\">\n        <h1 class=\"title\">{{'error'|t(data.lc)}}</h1>\n        <button type=\"button\" class=\"button button-icon button-clear\" v-on:click.prevent=\"hideModal()\">\n            <span class=\"icon kb-close size32 vAlignTop\"></span>\n        </button>\n    </ion-header-bar>\n    <ion-content class=\"padding\">\n        <h5 class=\"center\">{{'xframeError'|t(data.lc)}}</h5>\n    </ion-content>\n    <ion-footer-bar>\n        <div class=\"row\">\n            <div class=\"col\">\n                <a class=\"button button-block button-positive zeroMargin\" v-on:click.prevent=\"modal.errorOptions.openInNativeBrowser(modal.errorOptions.url)\">\n                    {{'openInNewWindow'|t(data.lc)}}\n                </a>\n            </div>\n            <div class=\"col\">\n                <button type=\"button\" class=\"button button-block button-light zeroMargin\" v-on:click.prevent=\"hideModal()\">\n                    {{'cancel'|t(data.lc)}}\n                </button>\n            </div>\n        </div>\n    </ion-footer-bar>\n</div>","templates/system/modals.html":"<div v-bind:style=\"{pointerEvents:(data.modal && data.modal.mode)?'all':'none'}\">\n    <div class=\"modal-backdrop xactive\" v-bind:class=\"{active:data.modal && data.modal.mode}\" v-on:click=\"backdropClicked($event)\">\n        <div class=\"modal-wrapper\" xv-if=\"data.modal && data.modal.mode\">\n            <transition v-bind:name=\"data.modal.animation\" appear v-if=\"data.modal && data.modal.mode\">\n                <div v-bind:id=\"modalName|firstLetterSmall\" v-bind:class=\"[data.modal.dynamic?'':'fit', data.modal.animation]\" class=\"modal ui\">\n                    <component v-if=\"data.modal.mode\" :is=\"componentDef\" v-on:hook:mounted=\"mounted\" v-on:hook:destroyed=\"destroyed\"></component>\n                </div>\n            </transition>\n        </div>\n    </div>\n</div>","templates/system/popovers.html":"<div v-bind:style=\"{pointerEvents:(data.popover && data.popover.mode)?'all':'none'}\">\n    <div class=\"popover-backdrop xactive\" v-bind:class=\"{active:data.popover && data.popover.mode}\" v-on:click=\"backdropClicked($event)\">\n        <div class=\"popover-wrapper\" xv-if=\"data.popover && data.popover.mode\">\n            <transition v-bind:name=\"data.popover.animation\" appear v-if=\"data.popover && data.popover.mode\">\n                <div v-bind:id=\"popoverName|firstLetterSmall\" v-bind:class=\"[data.popover.animation]\" class=\"popover ui\">\n                    <component v-if=\"data.popover.mode\" :is=\"componentDef\"></component>\n                </div>\n            </transition>\n        </div>\n    </div>\n</div>","templates/views/app.html":"","templates/views/authRedirect.html":"<div id=\"oauth\" ng-controller=\"OAuthCtrl\" ng-init=\"init()\" class=\"ui\">\n    <ion-content class=\"padding\">\n        <h5 class=\"center\">\n            <ion-spinner icon=\"dots\" class=\"loadingSpinner dark\"></ion-spinner>\n        </h5>\n    </ion-content>\n</div>","templates/views/blank.html":"<div id=\"blankView\">\n    \n</div>","templates/views/forgotPwd.html":"<div id=\"forgotPwd\" ng-controller=\"ForgotPwdCtrl\" ng-init=\"init()\" class=\"ui\">\n\n    <div class=\"card\">\n        <div class=\"item item-divider header\">\n            <div class=\"bar bar-header bar-positive kotobeeGradient\">\n                <button type=\"button\" class=\"button button-clear floatLeft\" v-on:click.prevent=\"backClicked()\">\n                    <span class=\"icon size20\" v-bind:class=\"[rtl?'kb-chevron-right':'kb-chevron-left']\"></span>\n                </button>\n                <h1 class=\"title\">{{'forgotPwd'|t(data.lc)}}</h1>\n            </div>\n        </div>\n\n        <div class=\"item item-text-wrap\">\n            <div class=\"card\">\n\n                <h4 class=\"padding10\">{{'enterEmailToReset'|t(data.lc)}}</h4>\n\n                <label class=\"item item-input\">\n                    <input type=\"text\" v-model=\"user.email\" v-on:keyup.enter=\"submit()\" v-autofocus v-bind:placeholder=\"'email'|t(data.lc)\">\n                </label>\n            </div>\n        </div>\n        <div class=\"item item-divider\">\n            <div class=\"row\">\n                <div class=\"col\">\n                    <button type=\"button\" class=\"button button-block button-positive zeroMargin\"\n                            v-on:click.prevent=\"submit()\">{{'submit'|t(data.lc)}}\n                    </button>\n                </div>\n                <div class=\"col\">\n                    <button type=\"button\" class=\"button button-block button-light zeroMargin\" v-on:click.prevent=\"backClicked()\">\n                        {{'cancel'|t(data.lc)}}\n                    </button>\n                </div>\n                \n            </div>\n        </div>\n    </div>\n</div>\n","templates/views/forgotPwd.mobile.html":"<div id=\"forgotPwd\" ng-controller=\"ForgotPwdCtrl\" ng-init=\"init()\" class=\"ui\">\n\n    <div class=\"bar bar-header bar-positive kotobeeGradient\" id=\"registerHeader\">\n        <button class=\"button button-clear floatLeft\" v-on:click.prevent=\"backClicked()\">\n            <span class=\"icon size20\" v-bind:class=\"[rtl?'kb-chevron-right':'kb-chevron-left']\"></span>\n        </button>\n        <h1 class=\"title\">{{'forgotPwd'|t(data.lc)}}</h1>\n        <div class=\"buttons\" v-if=\"native\">\n            <a class=\"button button-icon ion-navicon padding\" v-on:click.prevent=\"showMenu()\"></a>\n        </div>\n    </div>\n    <div class=\"bar bar-subheader\">\n        <p class=\"xtitle\">{{'enterEmailToReset'|t(data.lc)}}</p>\n    </div>\n\n    <div class=\"content has-header has-subheader\">\n\n        \n\n        <div class=\"list\">\n            <label class=\"item item-input\">\n                <input type=\"text\" v-model=\"user.email\" v-on:keyup.enter=\"submit()\" v-autofocus v-bind:placeholder=\"'email'|t(data.lc)\">\n            </label>\n        </div>\n        <div class=\"row\">\n                <div class=\"col\">\n                    <button class=\"button button-block button-positive zeroMargin\"\n                            v-on:click.prevent=\"submit()\">{{'submit'|t(data.lc)}}\n                    </button>\n                </div>\n                <div class=\"col\">\n                    <button class=\"button button-block button-light zeroMargin\" v-on:click.prevent=\"backClicked()\">\n                        {{'cancel'|t(data.lc)}}\n                    </button>\n                </div>\n            </div>\n    </div>\n    \n\n</div>","templates/views/global.html":"<div v-bind:class=\"classes\">\n    \n        \n    \n\n    \n    <sdk v-if=\"!isRelease\"></sdk>\n    \n\n    <ion-nav-view v-if=\"vueExclude\"></ion-nav-view>\n\n    <transition :name=\"transitionName\">\n        <router-view class=\"view\" v-if=\"tick\"></router-view>\n    </transition>\n\n    <modals xid=\"kModals\"></modals>\n    <popovers id=\"kPopups\"></popovers>\n\n</div>","templates/views/invoice.html":"<div id=\"invoice\" ng-controller=\"InvoiceCtrl\" ng-init=\"init()\" class=\"ui\">\n    <div v-if=\"order\">\n        <div class=\"card\">\n            <div class=\"item item-divider header\">\n                <div class=\"bar bar-header bar-positive kotobeeGradient\">\n                    <h1 class=\"title\">\n                        {{'invoice'|t(data.lc)}} #{{order.id}}\n                    </h1>\n                </div>\n            </div>\n\n            <div>\n                <div class=\"item\">\n                    <div class=\"row alignCenter\">\n                        <div class=\"col msg\" v-if=\"!order\">\n                            <span v-if=\"loading\">\n                                {{'loading'|t(data.lc)}}..\n                            </span>\n                            <span v-if=\"!loading\">\n                                {{'noInvoices'|t(data.lc)}}\n                            </span>\n                        </div>\n                        <div class=\"col msg\" v-if=\"order\">\n                            <div class=\"alignLeft desc\">\n                                <p>\n                                    <strong>{{'date'|t(data.lc)}}</strong>\n                                    {{order.date|dateTimeFromServer}}\n                                </p>\n                                <p>\n                                    <strong>{{'name'|t(data.lc)}}</strong>\n                                    {{order.product}}\n                                </p>\n                                <p>\n                                    <strong>{{'amount'|t(data.lc)}}</strong>\n                                    ${{order.amount}}\n                                </p>\n                            </div>\n                            <hr>\n                            \n                            \n                            <div class=\"paymentOptions\">\n\n                                <h3>{{'selectPaymentMethod'|t(data.lc)}}</h3>\n\n                                <div class=\"paymentOptions\" v-bind:class=\"[paying?'paying':'']\">\n                                    <div v-if=\"paymentOptions.length\" v-for=\"paymentOption in paymentOptions\" v-bind:id=\"paymentOption.provider+'-button-wrapper'\" class=\"paymentOption\">\n                                        <a v-if=\"paymentOption.provider == 'stripe'\" class=\"button button-positive\" v-on:click.prevent=\"pay(paymentOption.provider)\" v-bind:class=\"[paymentOption.provider]\">\n                                            <ion-spinner v-if=\"paying==paymentOption.provider\" v-bind:icon=\"actionBtnSpinnerIcon\" class=\"loadingSpinner\"></ion-spinner>\n                                            <span v-if=\"paymentOption.provider=='stripe'\">\n                                                \n                                            </span>\n                                        </a>\n                                    </div>\n\n                                    \n                                    <div v-if=\"paymentOptions.length\" v-for=\"paymentOption in paymentOptions\" v-bind:id=\"paymentOption.provider+'-button-wrapper'\" class=\"paymentOption\">\n                                      <a v-if=\"paymentOption.provider == 'moyasar'\" class=\"button button-positive\" v-on:click.prevent=\"pay(paymentOption.provider)\" v-bind:class=\"[paymentOption.provider]\">\n                                        \n                                        <span v-if=\"paymentOption.provider=='moyasar'\"></span>\n                                      </a>\n                                    </div>\n                                    \n\n                                    \n\n                                </div>\n\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n\n    </div>\n</div>","templates/views/landing.html":"<div id=\"landing\" ng-controller=\"LandingCtrl\" ng-init=\"init()\" class=\"ui\">\n\n    <book-info id=\"bookInfoModal\" :pageview=\"true\" :book=\"data.book\" class=\"pageview\">\n    </book-info>\n    \n</div>\n","templates/views/library.html":"<div ng-controller=\"LibraryCtrl\" id=\"library\" v-if=\"data.currentLibrary\" class=\"libraryView ui\" ng-init=\"init();searchOb={enabled:false}\">\n\n    <header-library></header-library>\n\n    <header-search id=\"librarySearchHeader\" class=\"kotobeeGradient\" :obj=\"searchOb\" :onValue=\"true\" prop=\"enabled\" :offValue=\"false\"\n                  :xkotobeeGradient=\"true\" v-on:search=\"searchEbooks\" :placeholder=\"config.searchLibContent?'searchByContent':'searchByTitle'\"></header-search>\n\n    <header-library-categories></header-library-categories>\n\n    <ion-pane class=\"viewContainer\">\n        \n        \n        \n\n        <h3 id=\"libLoaderAnim\" class=\"libLoaderAnim\">\n            \n            <div class=\"splashSpinner large\">\n              <div class=\"dot1\"></div>\n              <div class=\"dot2\"></div>\n            </div>\n        </h3>\n\n        <div id=\"libraryThumbs\" class=\"has-header has-subheader\" :overflow-scroll=\"true\" v-on:scroll=\"scroll\"\n             style=\"xbackground-color:white\">\n\n            <div id=\"libraryThumbsContent\" :class=\"[tab=='categories'&&config.categoriesPanel?'panelOffset':'']\">\n\n                \n                \n\n                <div v-if=\"paneMsg\">\n                    <div class=\"vSpace20\"></div>\n                    <h5 class=\"panelEmpty\">\n                        <span class=\"icon kb-notice vAlignMiddle size20\"></span>\n                        <span class=\"hSpace2\"></span>\n                        {{paneMsg|t(data.lc)}}\n                    </h5>\n                </div>\n\n                <div v-else class=\"bookThumbsContainer\" xv-show=\"tab!='categories'\" v-show=\"(tab!='categories') || config.categoriesPanel\">\n                    \n                    <div v-if=\"sectionView\">\n                        <library-row v-for=\"row in rows\" :row=\"row\"></library-row>\n                    </div>\n                    <div v-else>\n                        <book-item v-for=\"book in bookSource\" :book=\"book\" :key=\"book.id\" v-on:bookClicked=\"bookClicked\">\n                        </book-item>\n                    </div>\n                </div>\n\n            </div>\n\n            <div id=\"libCategories\" v-show=\"tab=='categories'\" v-if=\"config.showCategories\" :class=\"[config.catSize?config.catSize:'m', config.categoriesPanel?'panel':'']\">\n                <div v-if=\"config.categoriesPanel\" class=\"floatRight\">\n                    <div class=\"vSpace5\"></div>\n                    <button class=\"button button-icon xbutton-clear closeBtn\" v-on:click.prevent=\"closeCategoriesPanel($event)\">\n                        <span class=\"icon kb-close size32 vAlignTop\"></span>\n                    </button>\n                </div>\n                <router-link to=\"/library/tab/categories\" class=\"item floatLeft noBorder\" v-if=\"data.currentLibrary && (catList!=data.currentLibrary.categories)\" style=\"background: transparent; font-size: 0.9em;\">\n                    <i class=\"icon size14\" v-bind:class=\"[data.settings.rtl?'kb-chevron-right':'kb-chevron-left']\"></i>\n                    {{'back'|t(data.lc)}}\n                </router-link>\n                <category-list :list=\"catList\" :rtl=\"data.settings.rtl\" v-on:catClicked=\"categoryClicked\" />\n            </div>\n\n            \n        </div>\n        \n    </ion-pane>\n\n\n</div>\n","templates/views/library.mobile.html":"<div ng-controller=\"LibraryCtrl\" id=\"library\" v-if=\"data.currentLibrary\" class=\"libraryView ui\" ng-init=\"init();searchOb={enabled:false}\">\n\n    <header-library v-bind:class=\"{ease:autohideEase}\"></header-library>\n\n    <header-search id=\"librarySearchHeader\" class=\"kotobeeGradient\" searchid=\"searchOb\" :onValue=\"true\" :offValue=\"false\" :obj=\"searchOb\"\n                  prop=\"enabled\" :xkotobeeGradient=\"true\" v-on:search=\"searchEbooks\" :placeholder=\"config.searchLibContent?'searchByContent':'searchByTitle'\"></header-search>\n\n    <header-library-categories :autohideEase=\"autohideEase\"></header-library-categories>\n    \n    <ion-pane class=\"viewContainer\">\n        <h3 id=\"libLoaderAnim\" class=\"libLoaderAnim\">\n            <div class=\"splashSpinner large\">\n              <div class=\"dot1\"></div>\n              <div class=\"dot2\"></div>\n            </div>\n        </h3>\n        <div xv-if=\"tab!='categories'\">\n            <infinite-list id=\"libraryThumbs\" class=\"has-subheader has-header xhas-footer\" delegate-handle=\"thumbs\"  v-bind:class=\"[config.autohideLibMode, autohideEase?'ease':'']\" \n                         :autoscroll=\"true\" xstyle=\"background-color:#fff\" :overflow-scroll=\"false\"\n                         has-bouncing=\"true\" header-shrink=\"library\" :scroll=\"true\"\n\n                         v-bind:vueScroller=\"true\"\n                         v-bind:pullText=\"'pullToRefresh'|t(data.lc)\"\n                         v-bind:noDataText=\"''\"\n                         v-on:refresh=\"doRefresh\"\n                         v-on:infinite=\"loadMore\"\n                         v-on:scroll=\"vScroll\"\n\n                         v-bind:pullToRefresh=\"true\"\n                         v-bind:infiniteLoading=\"true\"\n                    >\n                \n\n                <div id=\"libraryThumbsContent\">\n\n                    \n                    \n\n                    <div v-if=\"paneMsg\">\n                        <div class=\"vSpace20\"></div>\n                        <h5 class=\"panelEmpty\">\n                            <span class=\"icon kb-notice vAlignMiddle size20\"></span>\n                            <span class=\"hSpace2\"></span>\n                            {{paneMsg|t(data.lc)}}\n                        </h5>\n                    </div>\n\n                    <div v-else class=\"bookThumbsContainer\" xv-show=\"tab!='categories'\" v-show=\"(tab!='categories') || config.categoriesPanel\">\n                        \n                        <div v-if=\"sectionView\">\n                            <library-row v-for=\"row in rows\" :row=\"row\"></library-row>\n                        </div>\n                        <div v-else>\n                            <book-item v-for=\"book in bookSource\" :book=\"book\" :key=\"book.id\" v-on:bookClicked=\"bookClicked\">\n                            </book-item>\n                        </div>\n                    </div>\n                </div>\n\n                \n                \n\n            </infinite-list>\n        </div>\n        <div v-show=\"tab=='categories'\" v-if=\"config.showCategories\">\n            <ion-content id=\"libCategories\" class=\"has-subheader has-header xhas-footer\" :class=\"[config.catSize?config.catSize:'m']\"\n                         :autoscroll=\"true\" xstyle=\"background-color:white\" :overflow-scroll=\"false\">\n                <router-link to=\"/library/tab/categories\" class=\"ion-item item\" v-if=\"data.currentLibrary && (catList!=data.currentLibrary.categories)\" style=\"background: transparent; font-size: 0.9em;\">\n                    <i class=\"icon size14\" v-bind:class=\"[data.settings.rtl?'kb-chevron-right':'kb-chevron-left']\"></i>\n                    {{'back'|t(data.lc)}}\n                </router-link>\n                <category-list :list=\"catList\" :rtl=\"data.settings.rtl\" v-on:catClicked=\"categoryClicked\" />\n            </ion-content>\n        </div>\n\n        \n    </ion-pane>\n\n</div>\n","templates/views/login.html":"<div id=\"login\" ng-controller=\"LoginCtrl\" ng-init=\"init()\" class=\"ui\">\n    <div class=\"card\" v-if=\"data.user\">\n        <div class=\"item item-divider header\">\n            <div class=\"bar bar-header bar-positive kotobeeGradient\">\n                <h1 class=\"title\">{{'login'|t(data.lc)}}</h1>\n                <div class=\"buttons\">\n                    <div class=\"langOptions relative\" v-if=\"config.languageOptional && data.settings\">\n                        <a class=\"button button-icon size14\" id=\"libraryLangBtn\"\n                                v-on:click.prevent=\"langDropdownExpanded=!langDropdownExpanded\">\n                            <span class=\"icon kb-globe size14\"></span>\n                            {{data.settings.language|langFromId}}\n                            <span class=\"icon kb-caret-down size14\"></span>\n                        </a>\n                        <ul id=\"langDropdown\" v-if=\"langDropdownExpanded\" class=\"list list-inset\" role=\"menu\"\n                            aria-labelledby=\"dropdownMenu1\">\n                            <a class=\"item\" v-for=\"lang in data.languages\"\n                            v-bind:class=\"[(lang.val==data.settings.language)?'selected':'']\" v-on:click.prevent=\"langSelected(lang.val, $event);\">{{lang.label}}</a>\n                        </ul>\n                    </div>\n                </div>\n            </div>\n        </div>\n\n        <div v-if=\"canLoginByEmail && (mode=='email')\">\n            <div class=\"item item-text-wrap\">\n                <label class=\"item item-input\">\n                    <input type=\"email\" v-model=\"data.user.email\" v-autofocus v-on:keyup.enter=\"login()\" v-bind:placeholder=\"'email'|t(data.lc)\" v-bind:disabled=\"ssoBusy\">\n                </label>\n                <label class=\"item item-input\">\n                    <input type=\"password\" v-model=\"model.pwd\" v-on:keyup.enter=\"login()\" v-bind:placeholder=\"'password'|t(data.lc)\" v-bind:disabled=\"ssoBusy\">\n                </label>\n            </div>\n            <div class=\"table fullWidth\">\n                <div class=\"padding5 halfWidth tableCell vAlignMiddle alignLeft\">\n                    <button type=\"button\" class=\"button forgotPwdBtn button-light\" v-on:click.prevent=\"forgotPassword()\" v-bind:disabled=\"ssoBusy\">\n                        {{'forgotPwd?'|t(data.lc)}}\n                    </button>\n                </div>\n                <div class=\"padding5 halfWidth tableCell alignRight vAlignTop\">\n                    <button type=\"button\" v-if=\"canLoginByCode\" class=\"button codeBtn button-light\" v-on:click.prevent=\"redeemCode()\" v-bind:disabled=\"ssoBusy\">\n                        <span class=\"icon kb-price-tag size14 vAlignMiddle\"></span>\n                        {{'redeemCode'|t(data.lc)}}\n                    </button>\n                </div>\n            </div>\n        </div>\n\n        <div v-if=\"canLoginByCode && (mode=='code')\">\n            <div class=\"item item-text-wrap\">\n                <h4 class=\"padding10\">{{'enterPromoCode'|t(data.lc)}}</h4>\n                <label class=\"item item-input\">\n                    <input type=\"text\" v-model=\"model.code\" v-autofocus v-on:keyup.enter=\"login()\" v-bind:placeholder=\"'code'|t(data.lc)\" v-bind:disabled=\"ssoBusy\">\n                </label>\n            </div>\n            <div class=\"table fullWidth\">\n                <div class=\"padding5 halfWidth tableCell vAlignMiddle alignLeft\">\n                </div>\n                <div v-if=\"canLoginByEmail\" class=\"padding5 halfWidth tableCell alignRight vAlignTop\">\n                    <button type=\"button\" class=\"button codeBtn button-light\" v-on:click.prevent=\"signInByEmail()\" v-bind:disabled=\"ssoBusy\">\n                        <span class=\"icon kb-email size20 vAlignMiddle\"></span>\n                        {{'emailLogin'|t(data.lc)}}\n                    </button>\n                </div>\n            </div>\n        </div>\n\n        <div class=\"loginhtmlContainer alignLeft\">\n            <div v-if=\"config.kotobee.loginhtml\" v-html=\"unsafe(config.kotobee.loginhtml)\"></div>\n        </div>\n\n        <ion-checkbox class=\"rememberMe\" v-model=\"data.user.rememberMe\" v-bind:disabled=\"ssoBusy\">\n            {{'rememberMe'|t(data.lc)}}\n        </ion-checkbox>\n        <div class=\"item item-divider\">\n            <div class=\"row\" v-if=\"cloudParam('registration')\">\n                <div class=\"col\">\n                    <button type=\"button\" class=\"button button-block button-positive zeroMargin\" v-bind:disabled=\"ssoBusy\"\n                            v-on:click.prevent=\"login()\">{{'login'|t(data.lc)}}\n                        <span class=\"center\" v-if=\"ssoBusy\">\n                            <ion-spinner icon=\"dots\" class=\"loadingSpinner dark\"></ion-spinner>\n                        </span>\n                    </button>\n                </div>\n                <div class=\"col\">\n                    <button type=\"button\" class=\"button button-block button-stable zeroMargin\" v-on:click.prevent=\"register()\" v-bind:disabled=\"ssoBusy\">\n                        {{'newAccount'|t(data.lc)}}\n                    </button>\n                </div>\n            </div>\n            <div class=\"row\" v-if=\"!cloudParam('registration')\">\n                <div class=\"col\">\n                    <button type=\"button\" class=\"button button-block button-positive zeroMargin\" v-bind:disabled=\"ssoBusy\"\n                            v-on:click.prevent=\"login()\">{{'login'|t(data.lc)}}\n                        <span class=\"center\" v-if=\"ssoBusy\">\n                            <ion-spinner icon=\"dots\" class=\"loadingSpinner dark\"></ion-spinner>\n                        </span>\n                    </button>\n                </div>\n            </div>\n            \n            <div class=\"row alignCenter sso-btns\" v-if=\"data.sso\">\n                <div class=\"col\">\n                    {{'loginWith'|t(data.lc)}}\n                </div>\n                <div class=\"col\" v-if=\"data.sso.okta\">\n                    <button type=\"button\" class=\"button button-block button-stable zeroMargin\" v-bind:disabled=\"ssoBusy\"\n                            v-on:click.prevent=\"emit('SignIn', 'okta')\">\n                        <img v-bind:src=\"data.sso.oktaLogo\" class=\"inlineLogo okta\"/>\n                    </button>\n                </div>\n                <div class=\"col\" v-if=\"data.sso.google\">\n                    <button type=\"button\" class=\"button button-block button-stable zeroMargin\" v-bind:disabled=\"ssoBusy\"\n                            v-on:click.prevent=\"emit('SignIn', 'google')\">\n                        <img v-bind:src=\"data.sso.googleLogo\" class=\"inlineLogo google\"/>\n                    </button>\n                </div>\n                <div class=\"col\" v-if=\"data.sso.microsoft\">\n                    <button type=\"button\" class=\"button button-block button-stable zeroMargin\" v-bind:disabled=\"ssoBusy\"\n                            v-on:click.prevent=\"emit('SignIn', 'microsoft')\">\n                        <img v-bind:src=\"data.sso.microsoftLogo\" class=\"inlineLogo microsoft\"/>\n                    </button>\n                </div>\n                <div class=\"col\" v-if=\"data.sso.facebook\">\n                    <button type=\"button\" class=\"button button-block button-stable zeroMargin\" v-bind:disabled=\"ssoBusy\"\n                            v-on:click.prevent=\"emit('SignIn', 'facebook')\">\n                        <img v-bind:src=\"data.sso.facebookLogo\" class=\"inlineLogo facebook\"/>\n                    </button>\n                </div>\n                <div class=\"col\" v-if=\"data.sso.auth0\">\n                    <button type=\"button\" class=\"button button-block button-stable zeroMargin\" v-bind:disabled=\"ssoBusy\"\n                            v-on:click.prevent=\"emit('SignIn', 'auth0')\">\n                        <img v-bind:src=\"data.sso.auth0Logo\" class=\"inlineLogo auth0\"/>\n                    </button>\n                </div>\n                <div class=\"col\" v-if=\"data.sso.fusionauth\">\n                    <button type=\"button\" class=\"button button-block button-stable zeroMargin\" v-bind:disabled=\"ssoBusy\"\n                            v-on:click.prevent=\"emit('SignIn', 'fusionauth')\">\n                        <img v-bind:src=\"data.sso.fusionauthLogo\" class=\"inlineLogo fusionauth\"/>\n                    </button>\n                </div>\n            </div>\n        </div>\n    </div>\n</div>\n","templates/views/login.mobile.html":"<div id=\"login\" ng-controller=\"LoginCtrl\" ng-init=\"init()\" class=\"ui\" v-if=\"data.user\">\n        <div class=\"bar bar-header bar-positive kotobeeGradient\" id=\"loginHeader\">\n            <h1 class=\"title\">{{'login'|t(data.lc)}}</h1>\n            <div class=\"buttons\" v-if=\"native\">\n                <a class=\"button button-icon\" v-on:click.prevent=\"showMenu()\">\n                    <span class=\"icon kb-vertical-dots size24\"></span>\n                </a>\n            </div>\n        </div>\n        <div class=\"content has-header\">\n            <div v-if=\"canLoginByEmail && (mode=='email')\">\n                <div class=\"list noMargin\">\n                    <label class=\"item item-input\">\n                        <input type=\"email\" v-model=\"data.user.email\" v-autofocus v-on:keyup.enter=\"login()\" v-bind:placeholder=\"'email'|t(data.lc)\">\n                    </label>\n                    <label class=\"item item-input\">\n                        <input type=\"password\" v-model=\"model.pwd\" v-on:keyup.enter=\"login()\" v-bind:placeholder=\"'password'|t(data.lc)\">\n                    </label>\n                </div>\n                <div class=\"table fullWidth\">\n                    <div class=\"padding5 halfWidth tableCell vAlignMiddle alignLeft xnoPadding\">\n                        <button class=\"button forgotPwdBtn button-light noPadding alignLeft\" v-on:click.prevent=\"forgotPassword()\">\n                            {{'forgotPwd?'|t(data.lc)}}\n                        </button>\n                    </div>\n                    <div class=\"padding5 halfWidth tableCell alignRight vAlignTop\">\n                        <button v-if=\"canLoginByCode\" class=\"button codeBtn button-light\" v-on:click.prevent=\"redeemCode()\">\n                            <span class=\"icon kb-price-tag size14 vAlignMiddle\"></span>\n                            {{'redeemCode'|t(data.lc)}}\n                        </button>\n                    </div>\n                </div>\n            </div>\n\n            <div v-if=\"canLoginByCode && (mode=='code')\">\n                <div class=\"list\">\n                    <label class=\"item item-input\">\n                        <input type=\"text\" v-model=\"model.code\" v-on:keyup.enter=\"login()\" v-autofocus v-bind:placeholder=\"'code'|t(data.lc)\">\n                    </label>\n                </div>\n                <div class=\"table fullWidth\">\n                    <div class=\"padding5 halfWidth tableCell vAlignMiddle alignLeft\">\n                    </div>\n                    <div v-if=\"canLoginByEmail\" class=\"padding5 halfWidth tableCell alignRight vAlignTop\">\n                        <button v-if=\"canLoginByCode\" class=\"button codeBtn button-light\" v-on:click.prevent=\"signInByEmail()\">\n                            <span class=\"icon kb-email size20 vAlignMiddle\"></span>\n                            {{'emailLogin'|t(data.lc)}}\n                        </button>\n                    </div>\n                </div>\n            </div>\n\n            \n\n            <div class=\"vSpace20\"></div>\n\n            <div class=\"loginhtmlContainer alignLeft\">\n                <div v-if=\"config.kotobee.loginhtml\" v-html=\"unsafe(config.kotobee.loginhtml)\"></div>\n            </div>\n\n            <ion-checkbox class=\"rememberMe\" v-model=\"data.user.rememberMe\">\n                {{'rememberMe'|t(data.lc)}}\n            </ion-checkbox>\n\n            <div class=\"row\" v-if=\"cloudParam('registration')\" >\n                <div class=\"col\">\n                    <button class=\"button button-block button-positive zeroMargin\"\n                            v-on:click.prevent=\"login()\">{{'login'|t(data.lc)}}\n                        <span class=\"center\" v-if=\"ssoBusy\">\n                            <ion-spinner icon=\"dots\" class=\"loadingSpinner dark\"></ion-spinner>\n                        </span>\n                    </button>\n                </div>\n                <div class=\"col\">\n                    <button class=\"button button-block button-stable zeroMargin\" v-on:click.prevent=\"register()\">\n                        {{'newAccount'|t(data.lc)}}\n                    </button>\n                </div>\n            </div>\n            <div class=\"row\" v-if=\"!cloudParam('registration')\">\n                <div class=\"col\">\n                    <button class=\"button button-block button-positive zeroMargin\"\n                            v-on:click.prevent=\"login()\">{{'login'|t(data.lc)}}\n                        <span class=\"center\" v-if=\"ssoBusy\">\n                            <ion-spinner icon=\"dots\" class=\"loadingSpinner dark\"></ion-spinner>\n                        </span>\n                    </button>\n                </div>\n            </div>\n            \n            <div class=\"row alignCenter sso-btns\" v-if=\"data.sso\">\n\n                \n                \n\n                \n                <div class=\"col\" v-if=\"data.sso.okta || data.sso.google || data.sso.microsoft || data.sso.facebook || data.sso.auth0 || data.sso.fusionauth\">\n                    {{'loginWith'|t(data.lc)}}\n                </div>\n                <div class=\"col\" v-if=\"data.sso.okta\">\n                    <button type=\"button\" class=\"button button-block button-stable zeroMargin\" v-bind:disabled=\"ssoBusy\"\n                            v-on:click.prevent=\"emit('SignIn', 'okta')\">\n                        <img v-bind:src=\"data.sso.oktaLogo\" class=\"inlineLogo okta\"/>\n                    </button>\n                </div>\n\n                \n                \n\n                \n                <div class=\"col\" v-if=\"data.sso.google\">\n                    <button type=\"button\" class=\"button button-block button-stable zeroMargin\" v-bind:disabled=\"ssoBusy\"\n                            v-on:click.prevent=\"emit('SignIn', 'google')\">\n                        <img v-bind:src=\"data.sso.googleLogo\" class=\"inlineLogo google\"/>\n                    </button>\n                </div>\n                <div class=\"col\" v-if=\"data.sso.microsoft\">\n                    <button type=\"button\" class=\"button button-block button-stable zeroMargin\" v-bind:disabled=\"ssoBusy\"\n                            v-on:click.prevent=\"emit('SignIn', 'microsoft')\">\n                        <img v-bind:src=\"data.sso.microsoftLogo\" class=\"inlineLogo microsoft\"/>\n                    </button>\n                </div>\n\n                \n                \n\n                \n                <div class=\"col\" v-if=\"data.sso.facebook\">\n                    <button type=\"button\" class=\"button button-block button-stable zeroMargin\" v-bind:disabled=\"ssoBusy\"\n                            v-on:click.prevent=\"emit('SignIn', 'facebook')\">\n                        <img v-bind:src=\"data.sso.facebookLogo\" class=\"inlineLogo facebook\"/>\n                    </button>\n                </div>\n                <div class=\"col\" v-if=\"data.sso.auth0\">\n                    <button type=\"button\" class=\"button button-block button-stable zeroMargin\" v-bind:disabled=\"ssoBusy\"\n                            v-on:click.prevent=\"emit('SignIn', 'auth0')\">\n                        <img v-bind:src=\"data.sso.auth0Logo\" class=\"inlineLogo auth0\"/>\n                    </button>\n                </div>\n                <div class=\"col\" v-if=\"data.sso.fusionauth\">\n                    <button type=\"button\" class=\"button button-block button-stable zeroMargin\" v-bind:disabled=\"ssoBusy\"\n                            v-on:click.prevent=\"emit('SignIn', 'fusionauth')\">\n                        <img v-bind:src=\"data.sso.fusionauthLogo\" class=\"inlineLogo fusionauth\"/>\n                    </button>\n                </div>\n            </div>\n        </div>\n    \n\n</div>\n","templates/views/payment.html":"<div id=\"payment\" ng-controller=\"PaymentCtrl\" ng-init=\"init()\" class=\"ui\">\n    <div class=\"card\" v-if=\"data.payment\">\n        <div class=\"item item-divider header\">\n            <div class=\"bar bar-header bar-positive kotobeeGradient\">\n                <h1 class=\"title\" v-if=\"data.payment.success\">\n                    {{'paymentSuccessMessage'|t(data.lc)}}\n                </h1>\n                <h1 class=\"title\" v-else>\n                    {{'paymentCancelMessage'|t(data.lc)}}\n                </h1>\n            </div>\n        </div>\n        \n        <div>\n            <div class=\"item\">\n                <div class=\"row alignCenter\">\n                    <div class=\"col msg\" v-if=\"data.payment.success\">\n                        {{'paymentSuccessMessageDesc'|t(data.lc)}}\n                    </div>\n                    <div class=\"col msg\" v-else>\n                        {{'paymentCancelMessageDesc'|t(data.lc)}}\n                    </div>\n                </div>\n            </div>\n            <div class=\"item item-divider\">\n                <div v-if=\"config.kotobee.mode=='library'\" class=\"row alignCenter\">\n                    <div class=\"col\" v-if=\"bookID && (state == 'success')\">\n                        <button type=\"button\" class=\"button button-block button-positive zeroMargin\" v-on:click.prevent=\"openBook()\">\n                            <span>{{'openBook'|t(data.lc)}}</span>\n                        </button>\n                    </div>\n                    <div class=\"col\">\n                        <button type=\"button\" class=\"button button-block button-positive zeroMargin\" v-on:click.prevent=\"backToLib()\">\n                            <span>{{'backToLib'|t(data.lc)}}</span>\n                        </button>\n                    </div>\n                </div>\n                <div v-if=\"config.kotobee.mode=='book'\" class=\"row alignCenter\">\n                    <div class=\"col\" v-if=\"bookID && (state == 'success')\">\n                        <button type=\"button\" class=\"button button-block button-positive zeroMargin\" v-on:click.prevent=\"openBook()\">\n                            <span>{{'openBook'|t(data.lc)}}</span>\n                        </button>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n</div>","templates/views/payment.mobile.html":"<div id=\"payment\" ng-controller=\"PaymentCtrl\" ng-init=\"init()\" class=\"ui\">\n    <div class=\"card\" v-if=\"data.payment\">\n        <div class=\"item item-divider header\">\n            <div class=\"bar bar-header bar-positive kotobeeGradient\">\n                <h1 class=\"title\" v-if=\"data.payment.success\">\n                    {{'paymentSuccessMessage'|t(data.lc)}}\n                </h1>\n                <h1 class=\"title\" v-else>\n                    {{'paymentCancelMessage'|t(data.lc)}}\n                </h1>\n            </div>\n        </div>\n        \n        <div>\n            <div class=\"item\">\n                <div class=\"row alignCenter\">\n                    <div class=\"col msg\" v-if=\"data.payment.success\">\n                        {{'paymentSuccessMessageDesc'|t(data.lc)}}\n                    </div>\n                    <div class=\"col msg\" v-else>\n                        {{'paymentCancelMessageDesc'|t(data.lc)}}\n                    </div>\n                </div>\n            </div>\n            <div class=\"item item-divider\">\n                <div v-if=\"config.kotobee.mode=='library'\" class=\"row alignCenter\">\n                    <div class=\"col\" xv-if=\"bookID && (state == 'success')\">\n                        <button type=\"button\" class=\"button button-block button-positive zeroMargin\" v-on:click.prevent=\"openBook()\">\n                            <span>{{'openBook'|t(data.lc)}}</span>\n                        </button>\n                    </div>\n                    <div class=\"col\">\n                        <button type=\"button\" class=\"button button-block button-positive zeroMargin\" v-on:click.prevent=\"backToLib()\">\n                            <span>{{'backToLib'|t(data.lc)}}</span>\n                        </button>\n                    </div>\n                </div>\n                <div v-if=\"config.kotobee.mode=='book'\" class=\"row alignCenter\">\n                    <div class=\"col\" v-if=\"bookID && (state == 'success')\">\n                        <button type=\"button\" class=\"button button-block button-positive zeroMargin\" v-on:click.prevent=\"openBook()\">\n                            <span>{{'openBook'|t(data.lc)}}</span>\n                        </button>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n    \n</div>","templates/views/reader.html":"<div ng-controller=\"ChaptersCtrl\" id=\"chapters\" ng-init=\"init()\">\n    <div ng-controller=\"RendererCtrl\" id=\"renderer\" ng-init=\"init()\" v-if=\"data.book && data.book.chapter\" v-bind:class=\"[data.settings.navAnim, 'content_'+contentDir]\">\n        \n        <ion-side-menus delegate-handle=\"menus\">\n            <ion-side-menu-content id=\"readerBody\" drag-content=\"false\" ng-init=\"searchOb={enabled:false}\">\n                <ion-header-bar id=\"readerHeader\" class=\"bar-dark kotobeeGradient ui\" ng-controller=\"ReaderCtrl\" ng-init=\"init()\"\n                                no-tap-scroll=\"true\"\n                                v-show=\"!searchOb.enabled\">\n                    <reader-header style=\"width:100%\"></reader-header>\n                </ion-header-bar>\n                <div id=\"headerSeparator\"></div>\n                \n                              \n\n                \n                {{webWatermarkPlaceholder1}}\n\n                \n\n                <div class=\"perspectiveContainer\">\n\n                    <ion-content id=\"epubContainer\" ng-controller=\"ContentCtrl\" class=\"ebook-content has-header has-footer\" ng-init=\"init()\"\n                                 has-header=\"true\" has-footer=\"true\" delegate-handle=\"content\" :padding=\"true\"\n                                 :scroll=\"true\" direction=\"xy\" zooming=\"true\" :overflow-scroll=\"false\" tabIndex=\"0\" has-bouncing=\"true\"\n                                 :panzoom=\"data.book.chapter.viewport\" :panContainerId=\"'epubContainer'\" :panResetVariable=\"'data.book.chapter.url'\"\n                         on-touch=\"mouseDown()\" on-release=\"mouseUp()\" on-scroll=\"scroll()\" mouse-events>\n                        <div id=\"epubInner\"></div>\n                    </ion-content>\n\n                    <div id=\"nextPage\"></div>\n                    <div id=\"prevPage\"></div>\n\n                </div>\n\n                <h3 id=\"ebookLoaderAnim\">\n                    <div class=\"splashSpinner large\">\n                      <div class=\"dot1\"></div>\n                      <div class=\"dot2\"></div>\n                    </div>\n                </h3>\n\n                <selection-options id=\"selectionOptions\" class=\"ui\"></selection-options>\n\n                <div id=\"tabMenu\">      \n                    <tab-menu></tab-menu>\n                    <zoom-controls></zoom-controls>\n                </div>\n\n            </ion-side-menu-content>\n\n            <ion-side-menu side=\"left\" class=\"ui fullHeight\">\n                <div v-if=\"!data.settings.rtl\" class=\"fullHeight\">\n                    <chapters class=\"fullHeight\"></chapters>\n                    <notebook v-if=\"data.widescreen\" id=\"notebookModal\"></notebook>\n                </div>\n                <rightPanel v-else></rightPanel>\n            </ion-side-menu>\n\n            <ion-side-menu side=\"right\" class=\"ui fullHeight\">\n                <div v-if=\"data.settings.rtl\" class=\"fullHeight\">\n                    <chapters class=\"fullHeight\"></chapters>\n                    <notebook v-if=\"data.widescreen\" id=\"notebookModal\"></notebook>\n                </div>\n                <rightPanel v-else></rightPanel>\n            </ion-side-menu>\n\n        </ion-side-menus>\n        \n    </div>\n</div>\n","templates/views/reader.mobile.html":"<div ng-controller=\"ChaptersCtrl\" id=\"chapters\" ng-init=\"init()\" v-bind:class=\"[config.autohideReaderMode]\">\n    <div ng-controller=\"RendererCtrl\" id=\"renderer\" ng-init=\"init()\" v-if=\"data.book && data.book.chapter\" v-bind:class=\"[data.settings.navAnim, 'content_'+contentDir]\">\n        <ion-side-menus delegate-handle=\"menus\" enable-menu-with-back-views=\"true\">\n            <ion-side-menu-content id=\"readerBody\" edge-drag-threshold=\"30\" drag-content=\"!config.disableSideMenuSwipe\">\n                <ion-header-bar id=\"readerHeader\" class=\"bar-dark kotobeeGradient ui\" v-bind:class=\"{ease:autohideEase}\" ng-controller=\"ReaderCtrl\" ng-init=\"init()\" no-tap-scroll=\"true\">\n                    <reader-header style=\"width:100%\"></reader-header>\n                </ion-header-bar>\n                \n\n                \n                {{webWatermarkPlaceholder1}}\n                \n                \n\n                <div class=\"perspectiveContainer\">\n\n                    <ion-content id=\"epubContainer\" ng-controller=\"ContentCtrl\" class=\"ebook-content has-header has-footer\" ng-init=\"init()\"\n                                 has-header=\"true\" has-footer=\"true\" delegate-handle=\"content\" :padding=\"true\"\n                                 :scroll=\"true\" direction=\"xy\" zooming=\"true\" :overflow-scroll=\"false\" tabIndex=\"0\" has-bouncing=\"true\"\n                                 :panzoom=\"data.book.chapter.viewport\" :panContainerId=\"'epubContainer'\" :panResetVariable=\"'data.book.chapter.url'\"\n                                 on-touch=\"mouseDown()\" on-release=\"mouseUp()\" on-scroll=\"scroll()\" mouse-events\n                                 :autoscroll=\"false\" header-shrink=\"reader\" v-headershrink=\"'reader'\" v-on:scroll=\"vScroll\"\n                                 v-on:swipeLeft=\"swipeLeft\" v-on:swipeRight=\"swipeRight\" v-on:doubleTap=\"doubleTap\">\n\n                        <div id=\"epubInner\"></div>\n                    </ion-content>\n\n                    <div id=\"nextPage\"></div>\n                    <div id=\"prevPage\"></div>\n\n                </div>\n                \n                <div class=\"navControls\" v-show=\"!config.hideFxlNavBtns\">\n                    <a v-bind:class=\"[data.book.chapter.notLastChapter?'show':'', autohideEase?'ease':'']\" ui class=\"navBtn ki-noHighlight next\" v-on:click.prevent=\"nextChapter()\">\n                        <span class=\"icon size20\" v-bind:class=\"[contentDir=='rtl'?'kb-chevron-left':'kb-chevron-right']\"></span>\n                    </a>\n                    <a v-bind:class=\"[data.book.chapter.notFirstChapter?'show':'', autohideEase?'ease':'']\" ui class=\"navBtn ki-noHighlight prev\" v-on:click.prevent=\"prevChapter()\">\n                        <span class=\"icon size20\" v-bind:class=\"[contentDir=='rtl'?'kb-chevron-right':'kb-chevron-left']\"></span>\n                    </a>\n                </div>\n\n                <h3 id=\"ebookLoaderAnim\">\n                    <div class=\"splashSpinner large\">\n                      <div class=\"dot1\"></div>\n                      <div class=\"dot2\"></div>\n                    </div>\n                </h3> \n\n                <div id=\"tabMenu\" v-bind:class=\"{ease:autohideEase}\">      \n                    <text-select-controls v-if=\"header.mode=='selection'\"></text-select-controls>\n                    <tab-menu></tab-menu>\n                    <zoom-controls></zoom-controls>\n                </div>\n\n            </ion-side-menu-content>\n\n            <ion-side-menu side=\"left\" swipe-gesture=\"false\" class=\"ui fullHeight\">\n                <chapters v-if=\"!data.settings.rtl\" class=\"fullHeight\"></chapters>\n                <rightPanel v-else></rightPanel>\n            </ion-side-menu>\n\n            <ion-side-menu side=\"right\" swipe-gesture=\"false\" class=\"ui fullHeight\">\n                <chapters v-if=\"data.settings.rtl\" class=\"fullHeight\"></chapters>\n                <rightPanel v-else></rightPanel>\n            </ion-side-menu>\n\n        </ion-side-menus>\n\n    </div>\n</div>\n\n","templates/views/redeemCode.html":"<div id=\"redeemCode\" ng-controller=\"RedeemCodeCtrl\" ng-init=\"init()\" class=\"ui\">\n\n    <div class=\"card\">\n        <div class=\"item item-divider header\">\n            <div class=\"bar bar-header bar-positive kotobeeGradient\">\n                <button type=\"button\" class=\"button button-clear floatLeft\" v-on:click.prevent=\"backClicked()\">\n                    <span class=\"icon size20\" v-bind:class=\"[rtl?'kb-chevron-right':'kb-chevron-left']\"></span>\n                </button>\n                <h1 class=\"title\">{{'redeemCode'|t(data.lc)}}</h1>\n            </div>\n        </div>\n\n        <div class=\"item item-text-wrap\">\n            <div class=\"card\">\n\n                <h4 class=\"padding10\">{{'enterPromoCode'|t(data.lc)}}</h4>\n\n                <label class=\"item item-input\">\n                    <input type=\"text\" v-model=\"user.code\" v-on:keyup.enter=\"submit()\" v-autofocus v-bind:placeholder=\"'code'|t(data.lc)\">\n                </label>\n            </div>\n            <ion-checkbox class=\"rememberMe\" v-model=\"data.user.rememberMe\">\n                {{'rememberMe'|t(data.lc)}}\n            </ion-checkbox>\n        </div>\n        <div class=\"item item-divider\">\n            <div class=\"row\">\n                <div class=\"col\">\n                    <button type=\"button\" class=\"button button-block button-positive zeroMargin\"\n                            v-on:click.prevent=\"submit()\">{{'redeem'|t(data.lc)}}\n                    </button>\n                </div>\n                <div class=\"col\">\n                    <button type=\"button\" class=\"button button-block button-light zeroMargin\" v-on:click.prevent=\"backClicked()\">\n                        {{'cancel'|t(data.lc)}}\n                    </button>\n                </div>\n            </div>\n        </div>\n    </div>\n</div>\n","templates/views/redeemCode.mobile.html":"<div id=\"redeemCode\" ng-controller=\"RedeemCodeCtrl\" ng-init=\"init()\" class=\"ui\">\n    <div class=\"bar bar-header bar-positive kotobeeGradient\" id=\"redeemCodeHeader\">\n        <button type=\"button\" class=\"button button-clear floatLeft\" v-on:click.prevent=\"backClicked()\">\n            <span class=\"icon size20\" v-bind:class=\"[rtl?'kb-chevron-right':'kb-chevron-left']\"></span>\n        </button>\n        <h1 class=\"title\">{{'redeemCode'|t(data.lc)}}</h1>\n        <div class=\"buttons\" v-if=\"native\">\n            <a class=\"button button-icon ion-navicon padding\" v-on:click.prevent=\"showMenu()\"></a>\n        </div>\n    </div>\n    <div class=\"bar bar-subheader\">\n        <h2 class=\"title\">{{'enterPromoCode'|t(data.lc)}}</h2>\n    </div>\n    <div class=\"content has-header has-subheader\">\n        <div class=\"list\">\n            <label class=\"item item-input\">\n                <input type=\"text\" v-model=\"user.code\" v-on:keyup.enter=\"submit()\" v-autofocus v-bind:placeholder=\"'code'|t(data.lc)\">\n            </label>\n        </div>\n        <ion-checkbox class=\"rememberMe\" v-model=\"data.user.rememberMe\">\n            {{'rememberMe'|t(data.lc)}}\n        </ion-checkbox>\n        <div class=\"row\">\n            <div class=\"col\">\n                <button class=\"button button-block button-positive zeroMargin\"\n                    v-on:click.prevent=\"submit()\">{{'redeem'|t(data.lc)}}\n                </button>\n            </div>\n            <div class=\"col\">\n                <button class=\"button button-block button-light zeroMargin\" v-on:click.prevent=\"backClicked()\">\n                    {{'cancel'|t(data.lc)}}\n                </button>\n            </div>\n        </div>\n    </div>\n</div>\n","templates/views/register.html":"<div id=\"register\" ng-controller=\"RegisterCtrl\" ng-init=\"init()\" class=\"ui\">\n\n    <div class=\"card\">\n        <div class=\"item item-divider header\">\n            <div class=\"bar bar-header bar-positive kotobeeGradient\">\n                <button type=\"button\" class=\"button button-clear floatLeft\" v-on:click.prevent=\"backClicked()\">\n                    <span class=\"icon size20\" v-bind:class=\"[rtl?'kb-chevron-right':'kb-chevron-left']\"></span>\n                </button>\n                <h1 class=\"title\">{{'registration'|t(data.lc)}}</h1>\n            </div>\n        </div>\n\n        <div class=\"item item-text-wrap\">\n            <div class=\"card\">\n\n                <h4 class=\"padding10\">{{'emailVerificationNote'|t(data.lc)}}</h4>\n\n                <label class=\"item item-input\">\n                    <input type=\"text\" v-model=\"user.email1\" v-on:keyup.enter=\"create()\" v-autofocus v-bind:placeholder=\"'email'|t(data.lc)\">\n                </label>\n                <label class=\"item item-input\">\n                    <input type=\"text\" v-model=\"user.email2\" v-on:keyup.enter=\"create()\" v-bind:placeholder=\"'confirmEmail'|t(data.lc)\">\n                </label>\n                <label class=\"item item-input\" v-if=\"view.registration && view.registration.promocode\">\n                    <input type=\"text\" v-model=\"user.promocode\" v-on:keyup.enter=\"create()\" v-bind:placeholder=\"(view.registration.promocodeMandatory?'redeemCode':'optionalPromocode')|t(data.lc)\">\n                </label>\n            </div>\n\n            <div class=\"list\">\n                <label class=\"item item-input item-stacked-label\" v-if=\"config.kotobee.registrationsettings.name\">\n                    <span class=\"input-label\">\n                        {{'name'|t(data.lc)}} {{config.kotobee.registrationsettings.namerequired?\"*\":\"\"}}\n                    </span>\n                    <input v-model=\"user.name\" v-on:keyup.enter=\"create()\" type=\"text\">\n                </label>\n\n                <label class=\"item item-input item-stacked-label\" v-if=\"config.kotobee.registrationsettings.dob\">\n                    <span class=\"input-label\">\n                        {{'dob'|t(data.lc)}} {{config.kotobee.registrationsettings.dobrequired?\"*\":\"\"}}\n                    </span>\n                    <input v-model=\"user.dob\" v-on:keyup.enter=\"create()\" type=\"date\">\n                </label>\n\n                <label class=\"item item-input item-stacked-label\" v-if=\"config.kotobee.registrationsettings.organization\">\n                    <span class=\"input-label\">\n                        {{'organization'|t(data.lc)}} {{config.kotobee.registrationsettings.organizationrequired?\"*\":\"\"}}\n                    </span>\n                    <input v-model=\"user.organization\" v-on:keyup.enter=\"create()\" type=\"text\">\n                </label>\n\n                <label class=\"item item-input item-stacked-label\" v-if=\"config.kotobee.registrationsettings.univ\">\n                    <span class=\"input-label\">\n                        {{'univ'|t(data.lc)}} {{config.kotobee.registrationsettings.univrequired?\"*\":\"\"}}\n                    </span>\n                    <input v-model=\"user.univ\" v-on:keyup.enter=\"create()\" type=\"text\">\n                </label>\n\n                <label class=\"item item-input item-stacked-label\" v-if=\"config.kotobee.registrationsettings.faculty\">\n                    <span class=\"input-label\">\n                        {{'faculty'|t(data.lc)}} {{config.kotobee.registrationsettings.facultyrequired?\"*\":\"\"}}\n                    </span>\n                    <input v-model=\"user.faculty\" v-on:keyup.enter=\"create()\" type=\"text\">\n                </label>\n\n                <label class=\"item item-input item-stacked-label\" v-if=\"config.kotobee.registrationsettings.dept\">\n                    <span class=\"input-label\">\n                        {{'dept'|t(data.lc)}} {{config.kotobee.registrationsettings.deptrequired?\"*\":\"\"}}\n                    </span>\n                    <input v-model=\"user.dept\" v-on:keyup.enter=\"create()\" type=\"text\">\n                </label>\n\n                <label class=\"item item-input item-stacked-label\" v-if=\"config.kotobee.registrationsettings.division\">\n                    <span class=\"input-label\">\n                        {{'division'|t(data.lc)}} {{config.kotobee.registrationsettings.divisionrequired?\"*\":\"\"}}\n                    </span>\n                    <input v-model=\"user.division\" v-on:keyup.enter=\"create()\" type=\"text\">\n                </label>\n\n                <label class=\"item item-input item-stacked-label\" v-if=\"config.kotobee.registrationsettings.class\">\n                    <span class=\"input-label\">\n                        {{'class'|t(data.lc)}} {{config.kotobee.registrationsettings.classrequired?\"*\":\"\"}}\n                    </span>\n                    <input v-model=\"user.class\" v-on:keyup.enter=\"create()\" type=\"text\">\n                </label>\n\n                <label class=\"item item-input item-stacked-label\" v-if=\"config.kotobee.registrationsettings.customid\">\n                    <span class=\"input-label\">\n                        {{'customId'|t(data.lc)}} {{config.kotobee.registrationsettings.customidrequired?\"*\":\"\"}}\n                    </span>\n                    <input v-model=\"user.customid\" v-on:keyup.enter=\"create()\" type=\"text\">\n                </label>\n\n                <label class=\"item item-input item-stacked-label\" v-if=\"config.kotobee.registrationsettings.gender\">\n                    <span class=\"input-label\">\n                        {{'gender'|t(data.lc)}} {{config.kotobee.registrationsettings.genderrequired?\"*\":\"\"}}\n                    </span>\n                    <div>\n                        <select v-model=\"user.gender\">\n                            <option :value=\"null\" selected disabled>{{'select'|t(data.lc)}}</option>\n                            <option value=\"m\">{{'male'|t(data.lc)}}</option>\n                            <option value=\"f\">{{'female'|t(data.lc)}}</option>\n                        </select>\n                    </div>\n                </label>\n\n                <label class=\"item item-input item-stacked-label\" v-if=\"config.kotobee.registrationsettings.country\">\n                    <span class=\"input-label\">\n                        {{'country'|t(data.lc)}} {{config.kotobee.registrationsettings.countryrequired?\"*\":\"\"}}\n                    </span>\n                    <input v-model=\"user.country\" v-on:keyup.enter=\"create()\" type=\"text\">\n                </label>\n\n                <label class=\"item item-input item-stacked-label\" v-if=\"config.kotobee.registrationsettings.city\">\n                    <span class=\"input-label\">\n                        {{'city'|t(data.lc)}} {{config.kotobee.registrationsettings.cityrequired?\"*\":\"\"}}\n                    </span>\n                    <input v-model=\"user.city\" v-on:keyup.enter=\"create()\" type=\"text\">\n                </label>\n\n                <label class=\"item item-input item-stacked-label\" v-if=\"config.kotobee.registrationsettings.phone\">\n                    <span class=\"input-label\">\n                        {{'phone'|t(data.lc)}} {{config.kotobee.registrationsettings.phonerequired?\"*\":\"\"}}\n                    </span>\n                    <input v-model=\"user.phone\" v-on:keyup.enter=\"create()\" type=\"text\">\n                </label>\n\n                <label class=\"item item-input item-stacked-label\" v-if=\"config.kotobee.registrationsettings.info\">\n                    <span class=\"input-label\">\n                        {{'info'|t(data.lc)}} {{config.kotobee.registrationsettings.inforequired?\"*\":\"\"}}\n                    </span>\n                    <textarea v-model=\"user.info\" xv-on:keyup.enter=\"create()\"></textarea>\n                </label>\n\n            </div>\n\n        </div>\n\n        <div class=\"item item-divider\">\n            <div class=\"row\">\n                <div class=\"col\">\n                    <button type=\"button\" class=\"button button-block button-positive zeroMargin\" v-bind:disabled=\"creating\"\n                            v-on:click.prevent=\"create()\">{{'create'|t(data.lc)}}\n                    </button>\n                </div>\n                <div class=\"col\">\n                    <button type=\"button\" class=\"button button-block button-light zeroMargin\" v-on:click.prevent=\"backClicked()\">\n                        {{'cancel'|t(data.lc)}}\n                    </button>\n                </div>\n                \n            </div>\n        </div>\n    </div>\n</div>\n","templates/views/register.mobile.html":"<div id=\"register\" ng-controller=\"RegisterCtrl\" ng-init=\"init()\" class=\"ui\">\n\n    <div class=\"bar bar-header bar-positive kotobeeGradient\" id=\"registerHeader\">\n        <button type=\"button\" class=\"button button-clear floatLeft\" v-on:click.prevent=\"backClicked()\">\n            <span class=\"icon size20\" v-bind:class=\"[rtl?'kb-chevron-right':'kb-chevron-left']\"></span>\n        </button>\n        <h1 class=\"title\">{{'registration'|t(data.lc)}}</h1>\n        <div class=\"buttons\" v-if=\"native\">\n            <a class=\"button button-icon ion-navicon padding\" v-on:click.prevent=\"showMenu()\"></a>\n        </div>\n    </div>\n    <div class=\"bar bar-subheader\">\n        <h2 class=\"title\">{{'emailVerificationNote'|t(data.lc)}}</h2>\n    </div>\n    <ion-content class=\"content has-header has-subheader padding\" xscroll=\"false\">\n\n        <div class=\"list\">\n            <label class=\"item item-input\">\n                <input type=\"text\" v-model=\"user.email1\" v-on:keyup.enter=\"create()\" v-autofocus v-bind:placeholder=\"'email'|t(data.lc)\">\n            </label>\n            <label class=\"item item-input\">\n                <input type=\"text\" v-model=\"user.email2\" v-on:keyup.enter=\"create()\" v-bind:placeholder=\"'confirmEmail'|t(data.lc)\">\n            </label>\n            <label class=\"item item-input\" v-if=\"view.registration && view.registration.promocode\">\n                <input type=\"text\" v-model=\"user.promocode\" v-on:keyup.enter=\"create()\" v-bind:placeholder=\"(view.registration.promocodeMandatory?'redeemCode':'optionalPromocode')|t(data.lc)\">\n            </label>\n            \n            <label class=\"item item-input item-stacked-label\" v-if=\"config.kotobee.registrationsettings.name\">\n                <span class=\"input-label\">\n                    {{'name'|t(data.lc)}} {{config.kotobee.registrationsettings.namerequired?\"*\":\"\"}}\n                </span>\n                <input v-model=\"user.name\" v-on:keyup.enter=\"create()\" type=\"text\">\n            </label>\n\n            <label class=\"item item-input item-stacked-label\" v-if=\"config.kotobee.registrationsettings.dob\">\n                <span class=\"input-label\">\n                    {{'dob'|t(data.lc)}} {{config.kotobee.registrationsettings.dobrequired?\"*\":\"\"}}\n                </span>\n                <input v-model=\"user.dob\" v-on:keyup.enter=\"create()\" type=\"date\">\n            </label>\n\n            <label class=\"item item-input item-stacked-label\" v-if=\"config.kotobee.registrationsettings.organization\">\n                <span class=\"input-label\">\n                    {{'organization'|t(data.lc)}} {{config.kotobee.registrationsettings.organizationrequired?\"*\":\"\"}}\n                </span>\n                <input v-model=\"user.organization\" v-on:keyup.enter=\"create()\" type=\"text\">\n            </label>\n\n            <label class=\"item item-input item-stacked-label\" v-if=\"config.kotobee.registrationsettings.univ\">\n                <span class=\"input-label\">\n                    {{'univ'|t(data.lc)}} {{config.kotobee.registrationsettings.univrequired?\"*\":\"\"}}\n                </span>\n                <input v-model=\"user.univ\" v-on:keyup.enter=\"create()\" type=\"text\">\n            </label>\n\n            <label class=\"item item-input item-stacked-label\" v-if=\"config.kotobee.registrationsettings.faculty\">\n                <span class=\"input-label\">\n                    {{'faculty'|t(data.lc)}} {{config.kotobee.registrationsettings.facultyrequired?\"*\":\"\"}}\n                </span>\n                <input v-model=\"user.faculty\" v-on:keyup.enter=\"create()\" type=\"text\">\n            </label>\n\n            <label class=\"item item-input item-stacked-label\" v-if=\"config.kotobee.registrationsettings.dept\">\n                <span class=\"input-label\">\n                    {{'dept'|t(data.lc)}} {{config.kotobee.registrationsettings.deptrequired?\"*\":\"\"}}\n                </span>\n                <input v-model=\"user.dept\" v-on:keyup.enter=\"create()\" type=\"text\">\n            </label>\n\n            <label class=\"item item-input item-stacked-label\" v-if=\"config.kotobee.registrationsettings.division\">\n                <span class=\"input-label\">\n                    {{'division'|t(data.lc)}} {{config.kotobee.registrationsettings.divisionrequired?\"*\":\"\"}}\n                </span>\n                <input v-model=\"user.division\" v-on:keyup.enter=\"create()\" type=\"text\">\n            </label>\n\n            <label class=\"item item-input item-stacked-label\" v-if=\"config.kotobee.registrationsettings.class\">\n                <span class=\"input-label\">\n                    {{'class'|t(data.lc)}} {{config.kotobee.registrationsettings.classrequired?\"*\":\"\"}}\n                </span>\n                <input v-model=\"user.class\" v-on:keyup.enter=\"create()\" type=\"text\">\n            </label>\n\n            <label class=\"item item-input item-stacked-label\" v-if=\"config.kotobee.registrationsettings.customid\">\n                <span class=\"input-label\">\n                    {{'customId'|t(data.lc)}} {{config.kotobee.registrationsettings.customidrequired?\"*\":\"\"}}\n                </span>\n                <input v-model=\"user.customid\" v-on:keyup.enter=\"create()\" type=\"text\">\n            </label>\n\n            <label class=\"item item-input item-stacked-label\" v-if=\"config.kotobee.registrationsettings.gender\">\n                <span class=\"input-label\">\n                    {{'gender'|t(data.lc)}} {{config.kotobee.registrationsettings.genderrequired?\"*\":\"\"}}\n                </span>\n                <div>\n                    <select v-model=\"user.gender\">\n                        <option :value=\"null\" selected disabled>{{'select'|t(data.lc)}}</option>\n                        <option value=\"m\">{{'male'|t(data.lc)}}</option>\n                        <option value=\"f\">{{'female'|t(data.lc)}}</option>\n                    </select>\n                </div>\n            </label>\n\n            <label class=\"item item-input item-stacked-label\" v-if=\"config.kotobee.registrationsettings.country\">\n                <span class=\"input-label\">\n                    {{'country'|t(data.lc)}} {{config.kotobee.registrationsettings.countryrequired?\"*\":\"\"}}\n                </span>\n                <input v-model=\"user.country\" v-on:keyup.enter=\"create()\" type=\"text\">\n            </label>\n\n            <label class=\"item item-input item-stacked-label\" v-if=\"config.kotobee.registrationsettings.city\">\n                <span class=\"input-label\">\n                    {{'city'|t(data.lc)}} {{config.kotobee.registrationsettings.cityrequired?\"*\":\"\"}}\n                </span>\n                <input v-model=\"user.city\" v-on:keyup.enter=\"create()\" type=\"text\">\n            </label>\n\n            <label class=\"item item-input item-stacked-label\" v-if=\"config.kotobee.registrationsettings.phone\">\n                <span class=\"input-label\">\n                    {{'phone'|t(data.lc)}} {{config.kotobee.registrationsettings.phonerequired?\"*\":\"\"}}\n                </span>\n                <input v-model=\"user.phone\" v-on:keyup.enter=\"create()\" type=\"text\">\n            </label>\n\n            <label class=\"item item-input item-stacked-label\" v-if=\"config.kotobee.registrationsettings.info\">\n                <span class=\"input-label\">\n                    {{'info'|t(data.lc)}} {{config.kotobee.registrationsettings.inforequired?\"*\":\"\"}}\n                </span>\n                <textarea v-model=\"user.info\" xv-on:keyup.enter=\"create()\"></textarea>\n            </label>\n\n        </div>\n\n    </ion-content>\n    <ion-footer-bar>\n\n        <div class=\"row\">\n            <div class=\"col\">\n                <button class=\"button button-block button-positive zeroMargin\" v-bind:disabled=\"creating\"\n                        v-on:click.prevent=\"create()\">{{'create'|t(data.lc)}}\n                </button>\n            </div>\n            <div class=\"col\">\n                <button class=\"button button-block button-light zeroMargin\" v-on:click.prevent=\"backClicked()\">\n                    {{'cancel'|t(data.lc)}}\n                </button>\n            </div>\n        </div>\n    </ion-footer-bar>\n    \n</div>\n","templates/views/test.html":"<div>\n    <pan\n         :scrollbarX=\"true\"\n         :scrollbarY=\"true\"\n         :minZoom=\"0.5\"\n         :maxZoom=\"3\"\n         :xrtl=\"null\"\n         :xpadding=\"100\"\n        >\n        <div style=\"position:static;width:500px;height:600px; background: yellow;xmargin: 20px;\">\n            Alot of content here\n        </div>\n    </pan>\n</div>\n\n","templates/web/panels/tabMenu.html":"<div ng-controller=\"TabMenuCtrl\" ng-init=\"init()\" class=\"tabs tabs-icon-top tabs-positive ui\">\n    <a class=\"next chapterBtn button button-clear\" ng-disabled=\"!data.book.chapter.notLastChapter\" ng-click=\"nextChapter()\">\n        <span>{{'next'|t:data.lc}}</span> <i class=\"icon fa size24 vAlignBottom\"\n                                ng-class=\"data.settings.rtl?'fa-chevron-circle-left':'fa-chevron-circle-right'\"></i>\n    </a>\n\n    <a class=\"prev chapterBtn button button-clear\" ng-disabled=\"!data.book.chapter.notFirstChapter\" ng-click=\"prevChapter()\">\n        <i class=\"icon fa size24 vAlignBottom\"\n           ng-class=\"data.settings.rtl?'fa-chevron-circle-right':'fa-chevron-circle-left'\"></i>\n        <span>{{'prev'|t:data.lc}}</span>\n    </a>\n\n    <a ng-if=\"::config.chaptersTab\" ng-click=\"chaptersClicked()\" class=\"tocBtn tab-item disable-user-behavior active\"\n       icon=\"ion-navicon-round\">\n        <i class=\"icon ion-navicon-round\"></i>\n        <span class=\"tab-title ng-binding\">{{'chapters'|t:data.lc}}</span>\n    </a>\n    <a ng-if=\"::config.mediaTab\" ng-click=\"mediaClicked()\" class=\"tab-item disable-user-behavior active\"\n       icon=\"ion-images\">\n        <i class=\"icon ion-images\"></i>\n        <span class=\"tab-title ng-binding\">{{'media'|t:data.lc}}</span>\n    </a>\n    \n    <a ng-if=\"::config.notebookTab\" ng-click=\"notebookClicked()\" class=\"tab-item disable-user-behavior active\"\n       icon=\"ion-clipboard\">\n        <i class=\"icon ion-clipboard size30\"></i>\n        <span class=\"tab-title ng-binding\">{{'notebook'|t:data.lc}}</span>\n    </a>\n    <a ng-if=\"::config.searchBooks\" ng-click=\"searchClicked()\" class=\"tab-item disable-user-behavior active\"\n       icon=\"ion-search\">\n        <i class=\"icon ion-search\"></i>\n        <span class=\"tab-title ng-binding\">{{'search'|t:data.lc}}</span>\n    </a>\n    <a ng-if=\"::config.pdfsaving && (!desktop && !native)\" ng-click=\"pdfClicked()\" class=\"tab-item disable-user-behavior active\"\n       icon=\"ion-gear-a\">\n        <i class=\"icon fa fa-file-pdf-o size24 baseline\"></i>\n        <span class=\"tab-title ng-binding\">{{'pdf'|t:data.lc}}</span>\n    </a>\n    <a ng-if=\"::config.printing && (!native && !mobile)\" ng-click=\"printClicked()\" class=\"tab-item disable-user-behavior active printBtn\"\n       icon=\"ion-gear-a\">\n        <i class=\"icon ion-android-print\"></i>\n        <span class=\"tab-title ng-binding\">{{'print'|t:data.lc}}</span>\n    </a>\n    <a ng-if=\"::config.settingsTab\" ng-click=\"settingsClicked()\" class=\"tab-item disable-user-behavior active\"\n       icon=\"ion-gear-a\">\n        <i class=\"icon ion-gear-a\"></i>\n        <span class=\"tab-title ng-binding\">{{'settings'|t:data.lc}}</span>\n    </a>\n\n    <a ng-repeat=\"tab in config.tabs\" ng-click=\"customTabClicked(tab, $event)\" class=\"tab-item disable-user-behavior active customTab\"\n       icon=\"ion-gear-a\">\n        <span ng-if=\"tab.iconStyle\" ng-style=\"tab.iconStyle\" class=\"customStyle\">\n        </span>\n        <span ng-if=\"!tab.icon\">\n            <i class=\"icon ion-android-open\" ng-if=\"tab.type=='website'\"></i>\n            <i class=\"icon ion-forward\" ng-if=\"tab.type=='book'\"></i>\n            <i class=\"icon ion-ios-information\" ng-if=\"tab.type=='popup'\"></i>\n            <i class=\"icon ion-volume-low\" ng-if=\"tab.type=='audio'\"></i>\n        </span>\n        <span class=\"tab-title ng-binding\">{{tab.title}}</span>\n    </a>\n</div>"};