var id,self=this;function parse(e){var n=(e=e||{}).html,t=e.WEBVIEW_SERVER_URL,r=e.mobile,a=e.native,s=[],i=[];s.push(/([< \/][^>]*?>)((\s*[^<\s]+\s+?)+)([^<\s]+\s*)(<)/g),i.push(/(>)([^<\n]*?[^<]+?)(<[^\/])/g),i.push(/(<\/[^>]*?>)([^<]*?)(<\/)/g);for(var l=0;l<s.length;l++)var f=s[l],n=n.replace(f,function(e,n){if(n.match(/<[^>]*?class="[^"]*?parsed.*?>/g))return e;if(0==n.indexOf("<pre"))return e;if(0==n.indexOf("<code"))return e;if(0==n.indexOf("<script"))return e;var r="",a=arguments[2].split(" ");""==a[a.length-1]&&a.splice(-1,1),a.push(arguments[4]);for(var t=0;t<a.length;t++){var s=t==a.length-1?"":" ";r+="<span>"+a[t]+s+"</span>"}return n+r+"<"});for(l=0;l<i.length;l++)n=n.replace(i[l],function(e,n){return arguments[2].trim()?n+("<span>"+arguments[2]+"</span>")+arguments[3]:e});n=n.replace(/(<a [\s\S]*?)(>)/g,'$1 onclick="return false;" $2'),a&&r&&(n=n.replace(/(<img[^>]*src=)"(.*?)"/g,function(e,n){var r,a=arguments[2];try{a=(r=a)?0!==r.indexOf("/")?0!==r.indexOf("file://")?0!==r.indexOf("content://")?r:t+r.replace("content:/","/_app_content_"):t+r.replace("file://","/_app_file_"):t+"/_app_file_"+r:r}catch(e){}return n+'"'+a+'"'})),self.postMessage({data:n,id:id})}self.addEventListener("message",function(e){var n=e.data.func,r=e.data.params;id=e.data.id,self[n].apply(self,r)},!1);var log=console.log;function clone(e){if(null==e||"object"!=typeof e)return e;var n=e.constructor();for(var r in e)e.hasOwnProperty(r)&&(n[r]=clone(e[r]));return n}