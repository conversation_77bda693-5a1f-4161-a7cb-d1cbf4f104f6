<!DOCTYPE html><html xng-app="reader.Kotobee" lang="en"><head><meta charset="utf-8"><meta name="viewport" content="viewport-fit=cover,initial-scale=1,maximum-scale=1,minimum-scale=1,user-scalable=no,width=device-width"><meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' * gap://ready file: kotobee: filesystem: data: blob:; style-src * 'self' 'unsafe-inline' kotobee: filesystem: data:; script-src 'self' 'unsafe-inline' 'unsafe-eval' kotobee: https://*.digitaloceanspaces.com https://*.amazonaws.com https://*.moyasar.com https://polyfill.io https://*.google.com https://*.googleapis.com https://*.youtube.com https://*.stripe.com https://*.paypal.com https://*.kotobee.com http://*.youtube.com https://*.ytimg.com http://*.ytimg.com https://www.googletagmanager.com; img-src * 'self' 'unsafe-inline' 'unsafe-eval' kotobee: filesystem: file: data: blob: android-webview-video-poster:; object-src 'self' blob:"><script src="config.js?c=428"></script><style splash>body,html{height:100%}#splash .logo img,#splash h4 .loaderContainer.invisible{visibility:hidden}[class*=" icon-"],[class*=" kb-"],[class^=icon-],[class^=kb-]{font-family:icomoon!important;font-style:normal;font-weight:400;font-variant:normal;text-transform:none;line-height:1;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}*,:after,:before{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}body{width:100%;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;overflow-x:hidden;overflow-y:hidden;padding:20px;margin:0}.loading #html5Fallback{display:none}#splash{position:absolute;top:0;bottom:0;left:0;right:0;z-index:0;background:center center no-repeat #fff;text-align:center}.loading #splash{display:none!important}#splash .logo{position:absolute;z-index:1;top:40px;bottom:50%;left:40px;right:40px;background-size:contain;background-repeat:no-repeat;background-position:center center;opacity:0;-webkit-transition:opacity .3s ease-in-out;-moz-transition:opacity .3s ease-in-out;transition:opacity .3s ease-in-out}#splash h4{position:absolute;width:100%;color:inherit;opacity:0;-webkit-transition:opacity .3s ease-in-out;-moz-transition:opacity .3s ease-in-out;transition:opacity .3s ease-in-out;vertical-align:baseline;xheight:25px;margin:5px 0 0;font-weight:400;xtop:80%;bottom:10%;height:40px;font-size:16px}#splash h4.loaded{opacity:1}#splash h4 .loaderContainer{cursor:default;color:#b693c1;white-space:nowrap}@media (max-height:280px){#splash h4 .loaderContainer{display:none}}#splash h4 .loaderContainer .wait{margin-left:3px}#kotobee.rtl #splash h4 .loaderContainer .wait{margin-right:3px;margin-left:0}#splash h4 .loaderContainer .loadingIcon{font-size:3em}#splash h4 .loaderContainer .loadingIcon.spinner{font-size:3em;display:block;-webkit-animation:splashAnimRotate 1.4s infinite cubic-bezier(.68,-.55,.265,1.55);-moz-animation:splashAnimRotate 1.4s infinite cubic-bezier(.68,-.55,.265,1.55);animation:splashAnimRotate 1.4s infinite cubic-bezier(.68,-.55,.265,1.55)}#splash h4 .loaderContainer svg.spinner{width:40px;height:40px;-webkit-animation:splashAnimRotate 1.4s infinite cubic-bezier(.68,-.55,.265,1.55);-moz-animation:splashAnimRotate 1.4s infinite cubic-bezier(.68,-.55,.265,1.55);animation:splashAnimRotate 1.4s infinite cubic-bezier(.68,-.55,.265,1.55)}#splash h3.slogan{position:absolute;padding:0 50px;xbottom:30px;width:100%;margin-top:20px;font-weight:400;color:inherit;font-size:24px;top:55%;bottom:20%}[class*=" icon-"],[class^=icon-]{speak:none}.icon-spinner:before{content:"\e981"}@-webkit-keyframes splashAnimRotate{0%{-webkit-transform:rotate(0);transform:rotate(0)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-moz-keyframes splashAnimRotate{0%{-moz-transform:rotate(0);transform:rotate(0)}100%{-moz-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes splashAnimRotate{0%{-webkit-transform:rotate(0);-moz-transform:rotate(0);transform:rotate(0)}100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);transform:rotate(360deg)}}@font-face{font-family:icomoon;src:url(fonts/icomoon.eot?u5fxms);src:url(fonts/icomoon.eot?u5fxms#iefix) format("embedded-opentype"),url(fonts/icomoon.ttf?u5fxms) format("truetype"),url(fonts/icomoon.woff?u5fxms) format("woff"),url(fonts/icomoon.svg?u5fxms#icomoon) format("svg");font-weight:400;font-style:normal;font-display:block}[class*=" kb-"],[class^=kb-]{speak:never}.kb-read-word:before{content:"\e918"}.kb-read-paragraph:before{content:"\e919"}.kb-bookmark-icon:before{content:"\e910";color:#4c4c4c}.kb-close-icon:before{content:"\e911"}.kb-highlight-icon:before{content:"\e914";color:#4c4c4c}.kb-note-icon:before{content:"\e915";color:#4c4c4c}.kb-pdf-icon:before{content:"\e916";color:#fff}.kb-no-payment:before{content:"\e90d";color:#dbdbdb}.kb-widgets:before{content:"\e903"}.kb-video:before{content:"\e904"}.kb-questions:before{content:"\e905"}.kb-pages:before{content:"\e906"}.kb-language:before{content:"\e907"}.kb-interactive:before{content:"\e908"}.kb-gallery:before{content:"\e909"}.kb-eye:before{content:"\e90a"}.kb-audio:before{content:"\e90b"}.kb-freehand:before{content:"\b94d"}.kb-smooth1:before{content:"\b947"}.kb-smooth2:before{content:"\b948"}.kb-highlight:before{content:"\b924"}.kb-stop:before{content:"\e912"}.kb-pause:before{content:"\e913"}.kb-play:before{content:"\e917"}.kb-star-half:before{content:"\e900"}.kb-star-full:before{content:"\e901"}.kb-star-empty:before{content:"\e902"}.kb-smooth4:before{content:"\b94a"}.kb-smooth5:before{content:"\b94b"}.kb-bookmark:before{content:"\b925"}.kb-edit:before{content:"\b909"}.kb-expand-horizontal:before{content:"\b93b"}.kb-wikipedia:before{content:"\b91c"}.kb-apps:before{content:"\b926"}.kb-pencil:before{content:"\b93c"}.kb-lock:before{content:"\b918"}.kb-folder:before{content:"\b932"}.kb-search:before{content:"\b913"}.kb-pencil1:before{content:"\b907"}.kb-notebook:before{content:"\b903"}.kb-color:before,.kb-droplet:before,.kb-water:before{content:"\b940"}.kb-check:before{content:"\b916"}.kb-expand:before{content:"\b93f"}.kb-price-tag:before{content:"\b92f"}.kb-redo:before{content:"\b919"}.kb-media:before{content:"\b917"}.kb-print:before{content:"\b914"}.kb-book:before{content:"\b90c"}.kb-books:before{content:"\b90d"}.kb-google:before{content:"\b91f"}.kb-notch:before{content:"\b900"}.kb-cart:before{content:"\e93a"}.kb-tts:before{content:"\b91d"}.kb-pen:before{content:"\b944"}.kb-undo:before{content:"\b942"}.kb-delete:before{content:"\b936"}.kb-close:before{content:"\b935"}.kb-notice:before{content:"\b931"}.kb-resize-horizontal:before{content:"\b928"}.kb-heart:before{content:"\e90c"}.kb-list:before{content:"\b934"}.kb-smooth3:before{content:"\b949"}.kb-arrow-right:before{content:"\b93d"}.kb-arrow-left:before{content:"\b93e"}.kb-book2:before{content:"\b910"}.kb-curve1:before{content:"\b946"}.kb-email:before{content:"\b93a"}.kb-download:before{content:"\b939"}.kb-plus:before{content:"\b92a"}.kb-share:before{content:"\b929"}.kb-resize-vertical:before{content:"\b927"}.kb-copy:before{content:"\b91e"}.kb-file:before{content:"\b937"}.kb-chevron-right:before{content:"\b905"}.kb-chevron-left:before{content:"\b906"}.kb-external-link:before{content:"\b91a"}.kb-box2:before{content:"\b915"}.kb-book-open:before{content:"\b90e"}.kb-zoomout:before{content:"\b90a"}.kb-zoomin:before{content:"\b90b"}.kb-addnote:before{content:"\b908"}.kb-zond-cheveron-left:before{content:"\e90e"}.kb-zond-cheveron-right:before{content:"\e90f"}.kb-curve2:before{content:"\b945"}.kb-erase:before{content:"\b943"}.kb-brush:before{content:"\b941"}.kb-vertical-dots:before{content:"\b933"}.kb-minus:before{content:"\b92d"}.kb-chevron-thin-left:before{content:"\b923"}.kb-chevron-thin-right:before{content:"\b922"}.kb-globe:before{content:"\b90f"}.kb-info:before{content:"\b911"}.kb-user:before{content:"\b92e"}.kb-menu:before{content:"\b901"}.kb-book-reference:before{content:"\b921"}.kb-cog:before{content:"\b8fd"}.kb-trash:before{content:"\b938"}.kb-caret-up:before{content:"\b92b"}.kb-caret-down:before{content:"\b92c"}.kb-play-circle:before{content:"\b920"}.kb-chapters:before{content:"\b91b"}.kb-pdf:before{content:"\b904"}.kb-file-fill:before{content:"\b94c"}</style><script type="text/javascript" kotobee></script><link rel="manifest" href="manifest.json"><meta property="og:title" content="Geography Book 3"/>
<meta name="description" content="This is a Geography Career Guidance Sample. It shows the nature of this smartbook and tools used during study.The Virtual Smart Study Kit is a future proof, standard way of learning. It uses internationally certified and recognised methods of education delivery. It uses 3D, simulations, text, images, videos, and various interactive media.This smart study kit is compatible across all devices.
"/>
<meta property="og:description" content="This is a Geography Career Guidance Sample. It shows the nature of this smartbook and tools used during study.The Virtual Smart Study Kit is a future proof, standard way of learning. It uses internationally certified and recognised methods of education delivery. It uses 3D, simulations, text, images, videos, and various interactive media.This smart study kit is compatible across all devices.
"/>
<meta property="og:image" content="epub/EPUB/imgs/leonardo_vision_xl_g.jpg"/>
<meta property="og:image:type" content="image/jpeg">
<link rel="image_src" type="image/jpeg" href="epub/EPUB/imgs/leonardo_vision_xl_g.jpg"/>
<link rel="shortcut icon" type="image/vnd.microsoft.icon" href="fav.ico">
<link rel="shortcut icon" type="image/x-icon" href="fav.ico"/>
</head><body id="kotobee" ng-controller="GlobalCtrl" ng-init="init()" ng-class="(mobile?'mobile':'web')" animation="slide-left-right-ios7"><script>if(config.noLoadingScreen)
        document.body.classList.add("loading");</script><div id="splash" class="ui"><div class="logo"><img></div><h4><span class="loaderContainer"><span class="icon kb-notch loadingIcon spinner"></span></span></h4><h3 class="slogan"></h3></div><global id="kotobeeVue"></global><script>"undefined"==typeof rootDir&&(rootDir="");var result=getLogo(),splash=document.getElementById("splash"),logoContainer=splash.getElementsByClassName("logo")[0],img=logoContainer.getElementsByTagName("img")[0];img.src=result.logo;try{logoContainer.style.cssText="%webWatermarkPlaceholder3%"}catch(o){}var logoCss,inlineBg=logoContainer.style.backgroundImage;inlineBg||(logoCss="",config.hideLogo?logoCss+="display:none":logoCss+="background-image:url('"+result.logo+"');",logoContainer.style.cssText=logoCss),img.onload=function(){var o,e,n,i,t,s,l;inlineBg||(o=window,n=(e=document).documentElement,i=e.getElementsByTagName("body")[0],t=o.innerWidth||n.clientWidth||i.clientWidth,s=o.innerHeight||n.clientHeight||i.clientHeight,t=t||o.screen.width,s=s||o.screen.height,(l={}).width=t-80,l.height=.5*s-40,logoCss+="opacity:1;",config.splashLogoWidth?logoCss+="background-size:"+config.splashLogoWidth+"px":l.height<img.height||l.width<img.width?logoCss+="background-size:contain;":logoCss+="background-size:auto;",logoContainer.style.cssText=logoCss)};var slogan=splash.getElementsByClassName("slogan")[0];slogan.innerHTML=config.slogan?config.slogan:"",getBgStyle();var loaderContainer=splash.getElementsByClassName("loaderContainer")[0];function getBgStyle(){var o="";isKotobeeHosted()&&(o=publicRoot);var e={"background-image":'url("'+(config.splashBg?o+"imgUser/"+config.splashBg:"none")+'")',"background-color":config.splashBgColor?config.splashBgColor:"#3f3248",color:(config.splashColor?config.splashColor:"#fff")+" !important","background-size":config.splashBgSize?config.splashBgSize:"cover"};"stretch"==e["background-size"]&&(e["background-size"]="100% 100%");var n="";for(var i in e)n+=i+":"+e[i]+";";splash.style.cssText=n}function getLogo(){var o="";isKotobeeHosted()&&(o=publicRoot);var e=rootDir+"img/ui/defaultWelcomeLogoNew.png";return config.logo&&(e=config.logo.match(/http[s]?:\/\//g)?config.logo:o+"imgUser/"+config.logo),{logo:e,slogan:config.slogan?config.slogan:""}}function isKotobeeHosted(o){var e=(o=o||window.location.href).split("://");if(!(e.length<=1)){var n=e[1];if(0<=n.indexOf("books.kotobee.com"))return!1;if(0<=n.indexOf("books-test.kotobee.com"))return!1;for(var i=["kotobee.com"],t=0;t<i.length;t++){if(0<=n.indexOf(i[t]+"/ebook"))return!0;if(0<=n.indexOf(i[t]+"/library"))return!0;if(0<=n.indexOf(i[t]+"/lti"))return!0}var s="kotobee.com";"undefined"!=typeof hostingDomain&&hostingDomain&&0==(s=hostingDomain.replace(/https?:\/\//g,"")).indexOf("www.")&&(s=s.substr(4)),"undefined"==typeof hostUrlMode&&(hostUrlMode="subdomain");var l="undefined"!=typeof hostUrlMode&&"folder"==hostUrlMode?"":".";if(0<=o.indexOf(l+s)){if("folder"==hostUrlMode)return!0;var r=s.replace(".","\\."),g=new RegExp("https?:\\/\\/(.*?\\.)"+r+".*","g"),a=o.replace(g,"$1");if(a){var c=a.replace(/(.*?)\./g,"$1");if(0<=s.indexOf("kotobee.com")){if("www"==c)return!1;if("dev"==c)return!1;if("test"==c)return!1;if("qa"==c)return!1}else if("www"==c)return!1;return!0}}}}function log(o){}config.hideLoadingMsg?loaderContainer.className="invisible":setTimeout(function(){loaderContainer.parentNode.className="loaded"},30),config.loaderColor&&(loaderContainer.style.color=config.loaderColor),config.loaderOpacity&&(loaderContainer.children[0].style.opacity=config.loaderOpacity);</script><div id="html5Fallback" class="card ui"><div class="item item-divider"><i class="icon ion-alert-circled"></i> <strong>HTML5-Supported Browser Required</strong></div><div class="item item-text-wrap">The ebook you are displaying may contain multimedia and interactive elements that requires HTML5 capabilities in order to run. Your current browser is incapable of supporting all these elements.<br>If you would like to continue despite the consequences, please click the button below.</div><div class="item item-divider"><a class="button fullWidth" onclick="continueWithoutHTML5()">Continue despite the consequences..</a></div></div><div class="alignCenter ui" id="kotobeeLink"></div><div id="localFallback" class="card ui"><div class="item item-divider"><i class="icon ion-alert-circled"></i> <strong>Error running locally!</strong></div><div class="item item-text-wrap"><p>The Kotobee reader web application needs to be run through a web server, otherwise, odd behaviour may be experienced.</p><p>To resolve and run as intended, you may do one of the following procedures:</p><ul><li class="item"><i class="icon size12 kb-chevron-right"></i> Upload all your files to an online server, and access the directory through the browser</li><li class="item"><i class="icon size12 kb-chevron-right"></i> Set a local web server, using something like <a href="http://www.wampserver.com/en/">Wampserver</a> and access your directory through this local server</li><li class="item"><i class="icon size12 kb-chevron-right"></i> [Advanced] Use the switch <em>--allow-file-access-from-files</em> to run Google Chrome. This may be done as follows:<br><br><div>OS X<br><code>open -a 'Google Chrome' --args -allow-file-access-from-files</code></div><div>On other *nix<br><code>google-chrome --allow-file-access-from-files</code></div><div>On windows<br><code>C:\ ... \Application\chrome.exe --allow-file-access-from-files</code></div></li></ul><p></p></div><div class="item item-divider"><a class="button fullWidth" onclick="continueLocally()">Continue despite the consequences..</a></div></div><link rel="stylesheet" href="css/styles.min.css?c=54971"><style type="text/css" user></style><style type="text/css" kotobee></style><script src="js/templates.js?c=54971"></script><script src="js/script.min.js?c=54971"></script></body></html>