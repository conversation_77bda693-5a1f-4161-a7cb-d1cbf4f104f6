# ZiExplorer Technical Specifications

## Component Architecture

### Core Components

#### 1. ZiExplorerManager.ts
```typescript
interface ZiExplorerManager {
  // File System Operations
  initializeFileSystem(): Promise<void>
  createFolder(path: string, name: string): Promise<boolean>
  importFiles(files: FileList, targetPath: string): Promise<ImportResult[]>
  copyFiles(sourcePaths: string[], targetPath: string): Promise<boolean>
  moveFiles(sourcePaths: string[], targetPath: string): Promise<boolean>
  deleteFiles(paths: string[]): Promise<boolean>
  
  // File System Monitoring
  watchFolder(path: string, callback: (changes: FileChange[]) => void): void
  unwatchFolder(path: string): void
  
  // Utility Functions
  getFileInfo(path: string): Promise<FileInfo>
  generateThumbnail(filePath: string): Promise<string>
  validateFileName(name: string): ValidationResult
}
```

#### 2. NativeFileSystemBridge.ts
```typescript
interface NativeFileSystemBridge {
  // Platform Detection
  detectPlatform(): Platform
  isNativeFileSystemSupported(): boolean
  
  // File System Access
  requestDirectoryAccess(): Promise<FileSystemDirectoryHandle>
  createZiExplorerStructure(): Promise<void>
  
  // File Operations
  readFile(handle: FileSystemFileHandle): Promise<File>
  writeFile(handle: FileSystemFileHandle, content: ArrayBuffer): Promise<void>
  createDirectory(parent: FileSystemDirectoryHandle, name: string): Promise<FileSystemDirectoryHandle>
  
  // Fallback Operations
  downloadFile(content: ArrayBuffer, filename: string): void
  uploadFiles(): Promise<FileList>
}
```

#### 3. ZiExplorerWindow.tsx
```typescript
interface ZiExplorerWindowProps {
  initialPath?: string
  windowId: string
  onClose: () => void
  onMinimize: () => void
  onMaximize: () => void
}

interface ZiExplorerWindowState {
  currentPath: string
  selectedFiles: string[]
  viewMode: 'grid' | 'list' | 'details'
  sortBy: 'name' | 'date' | 'size' | 'type'
  sortOrder: 'asc' | 'desc'
  searchQuery: string
  isLoading: boolean
  clipboard: ClipboardItem[]
}
```

## File System Integration

### Native File System Access API Implementation
```typescript
class NativeFileSystemService {
  private rootHandle: FileSystemDirectoryHandle | null = null
  private ziExplorerHandle: FileSystemDirectoryHandle | null = null
  
  async initialize(): Promise<void> {
    if ('showDirectoryPicker' in window) {
      try {
        // Request access to Documents folder or user-selected folder
        this.rootHandle = await window.showDirectoryPicker({
          mode: 'readwrite',
          startIn: 'documents'
        })
        
        // Create or access ZiExplorer folder
        this.ziExplorerHandle = await this.getOrCreateDirectory(this.rootHandle, 'ZiExplorer')
        
        // Create default structure
        await this.createDefaultStructure()
      } catch (error) {
        console.warn('Native file system access denied, falling back to IndexedDB')
        await this.initializeFallback()
      }
    } else {
      await this.initializeFallback()
    }
  }
  
  private async createDefaultStructure(): Promise<void> {
    if (!this.ziExplorerHandle) return
    
    const folders = ['Documents', 'Pictures', 'Music']
    for (const folder of folders) {
      await this.getOrCreateDirectory(this.ziExplorerHandle, folder)
    }
  }
  
  private async getOrCreateDirectory(
    parent: FileSystemDirectoryHandle, 
    name: string
  ): Promise<FileSystemDirectoryHandle> {
    try {
      return await parent.getDirectoryHandle(name)
    } catch {
      return await parent.getDirectoryHandle(name, { create: true })
    }
  }
}
```

### Fallback IndexedDB Implementation
```typescript
class IndexedDBFileSystemService {
  private db: IDBDatabase | null = null
  
  async initialize(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('ZiExplorerDB', 1)
      
      request.onerror = () => reject(request.error)
      request.onsuccess = () => {
        this.db = request.result
        resolve()
      }
      
      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result
        
        // Create object stores
        if (!db.objectStoreNames.contains('files')) {
          const fileStore = db.createObjectStore('files', { keyPath: 'path' })
          fileStore.createIndex('parentPath', 'parentPath', { unique: false })
          fileStore.createIndex('type', 'type', { unique: false })
        }
        
        if (!db.objectStoreNames.contains('folders')) {
          const folderStore = db.createObjectStore('folders', { keyPath: 'path' })
          folderStore.createIndex('parentPath', 'parentPath', { unique: false })
        }
      }
    })
  }
  
  async createFile(path: string, content: ArrayBuffer, metadata: FileMetadata): Promise<void> {
    if (!this.db) throw new Error('Database not initialized')
    
    const transaction = this.db.transaction(['files'], 'readwrite')
    const store = transaction.objectStore('files')
    
    const fileData = {
      path,
      content,
      metadata,
      createdAt: new Date(),
      modifiedAt: new Date()
    }
    
    await store.put(fileData)
  }
}
```

## UI Component Specifications

### ZiExplorerWindow Component
```typescript
export function ZiExplorerWindow({ 
  initialPath = '/Documents', 
  windowId, 
  onClose, 
  onMinimize, 
  onMaximize 
}: ZiExplorerWindowProps) {
  const [state, setState] = useState<ZiExplorerWindowState>({
    currentPath: initialPath,
    selectedFiles: [],
    viewMode: 'grid',
    sortBy: 'name',
    sortOrder: 'asc',
    searchQuery: '',
    isLoading: false,
    clipboard: []
  })
  
  const { files, folders, isLoading } = useFileSystem(state.currentPath)
  const { copyFiles, moveFiles, deleteFiles } = useFileOperations()
  
  return (
    <div className="zi-explorer-window h-full flex flex-col">
      {/* Window Header */}
      <ZiExplorerHeader
        currentPath={state.currentPath}
        onNavigate={(path) => setState(prev => ({ ...prev, currentPath: path }))}
        onSearch={(query) => setState(prev => ({ ...prev, searchQuery: query }))}
        onViewModeChange={(mode) => setState(prev => ({ ...prev, viewMode: mode }))}
      />
      
      {/* Main Content */}
      <div className="flex-1 flex">
        {/* Sidebar */}
        <ZiExplorerSidebar
          currentPath={state.currentPath}
          onNavigate={(path) => setState(prev => ({ ...prev, currentPath: path }))}
        />
        
        {/* File Area */}
        <ZiExplorerContent
          files={files}
          folders={folders}
          viewMode={state.viewMode}
          selectedFiles={state.selectedFiles}
          onSelectionChange={(files) => setState(prev => ({ ...prev, selectedFiles: files }))}
          onFileOpen={handleFileOpen}
          onFolderOpen={(path) => setState(prev => ({ ...prev, currentPath: path }))}
        />
      </div>
      
      {/* Status Bar */}
      <ZiExplorerStatusBar
        fileCount={files.length}
        folderCount={folders.length}
        selectedCount={state.selectedFiles.length}
      />
    </div>
  )
}
```

### File Grid Component
```typescript
export function FileGrid({ 
  files, 
  folders, 
  selectedFiles, 
  onSelectionChange, 
  onFileOpen, 
  onFolderOpen 
}: FileGridProps) {
  const [contextMenu, setContextMenu] = useState<ContextMenuState | null>(null)
  
  const handleContextMenu = (event: React.MouseEvent, item: FileItem) => {
    event.preventDefault()
    setContextMenu({
      x: event.clientX,
      y: event.clientY,
      item
    })
  }
  
  const handleItemClick = (item: FileItem, event: React.MouseEvent) => {
    if (event.detail === 1) {
      // Single click - select item
      const newSelection = event.ctrlKey || event.metaKey
        ? selectedFiles.includes(item.path)
          ? selectedFiles.filter(path => path !== item.path)
          : [...selectedFiles, item.path]
        : [item.path]
      
      onSelectionChange(newSelection)
    } else if (event.detail === 2) {
      // Double click - open item
      if (item.type === 'folder') {
        onFolderOpen(item.path)
      } else {
        onFileOpen(item.path)
      }
    }
  }
  
  return (
    <div className="file-grid grid grid-cols-auto-fill gap-4 p-4">
      {folders.map(folder => (
        <FileGridItem
          key={folder.path}
          item={folder}
          isSelected={selectedFiles.includes(folder.path)}
          onClick={handleItemClick}
          onContextMenu={handleContextMenu}
        />
      ))}
      
      {files.map(file => (
        <FileGridItem
          key={file.path}
          item={file}
          isSelected={selectedFiles.includes(file.path)}
          onClick={handleItemClick}
          onContextMenu={handleContextMenu}
        />
      ))}
      
      {contextMenu && (
        <ContextMenu
          x={contextMenu.x}
          y={contextMenu.y}
          item={contextMenu.item}
          onClose={() => setContextMenu(null)}
        />
      )}
    </div>
  )
}
```

## Animation and Transition Specifications

### Liquid Glass Effects
```css
.zi-explorer-window {
  background: linear-gradient(135deg, 
    rgba(255,255,255,0.1) 0%, 
    rgba(255,255,255,0.05) 100%
  );
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255,255,255,0.2);
  border-radius: 16px;
  box-shadow: 
    0 8px 32px rgba(0,0,0,0.1),
    inset 0 1px 0 rgba(255,255,255,0.2);
}

.file-grid-item {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

.file-grid-item:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 12px 24px rgba(0,0,0,0.15);
}

.file-grid-item.selected {
  background: linear-gradient(135deg, 
    rgba(59, 130, 246, 0.3) 0%, 
    rgba(99, 102, 241, 0.2) 100%
  );
  border: 2px solid rgba(59, 130, 246, 0.5);
}
```

### Smooth Transitions
```typescript
const transitionVariants = {
  enter: {
    opacity: 0,
    scale: 0.9,
    y: 20
  },
  center: {
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      duration: 0.3,
      ease: [0.4, 0, 0.2, 1]
    }
  },
  exit: {
    opacity: 0,
    scale: 0.9,
    y: -20,
    transition: {
      duration: 0.2,
      ease: [0.4, 0, 0.2, 1]
    }
  }
}
```

## Performance Optimization

### Virtual Scrolling Implementation
```typescript
export function VirtualizedFileGrid({ items, itemHeight = 120, containerHeight }: VirtualizedFileGridProps) {
  const [scrollTop, setScrollTop] = useState(0)
  const [containerRef, setContainerRef] = useState<HTMLDivElement | null>(null)
  
  const visibleItems = useMemo(() => {
    if (!containerRef) return []
    
    const startIndex = Math.floor(scrollTop / itemHeight)
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight) + 1,
      items.length
    )
    
    return items.slice(startIndex, endIndex).map((item, index) => ({
      ...item,
      index: startIndex + index
    }))
  }, [items, scrollTop, itemHeight, containerHeight])
  
  return (
    <div
      ref={setContainerRef}
      className="virtual-grid-container"
      style={{ height: containerHeight, overflow: 'auto' }}
      onScroll={(e) => setScrollTop(e.currentTarget.scrollTop)}
    >
      <div style={{ height: items.length * itemHeight, position: 'relative' }}>
        {visibleItems.map(item => (
          <div
            key={item.path}
            style={{
              position: 'absolute',
              top: item.index * itemHeight,
              height: itemHeight,
              width: '100%'
            }}
          >
            <FileGridItem item={item} />
          </div>
        ))}
      </div>
    </div>
  )
}
```

### Thumbnail Generation
```typescript
class ThumbnailGenerator {
  private canvas: HTMLCanvasElement
  private ctx: CanvasRenderingContext2D
  
  constructor() {
    this.canvas = document.createElement('canvas')
    this.ctx = this.canvas.getContext('2d')!
  }
  
  async generateImageThumbnail(file: File): Promise<string> {
    return new Promise((resolve) => {
      const img = new Image()
      img.onload = () => {
        const size = 120
        this.canvas.width = size
        this.canvas.height = size
        
        // Calculate aspect ratio and draw
        const aspectRatio = img.width / img.height
        let drawWidth = size
        let drawHeight = size
        
        if (aspectRatio > 1) {
          drawHeight = size / aspectRatio
        } else {
          drawWidth = size * aspectRatio
        }
        
        const x = (size - drawWidth) / 2
        const y = (size - drawHeight) / 2
        
        this.ctx.clearRect(0, 0, size, size)
        this.ctx.drawImage(img, x, y, drawWidth, drawHeight)
        
        resolve(this.canvas.toDataURL('image/jpeg', 0.8))
      }
      
      img.src = URL.createObjectURL(file)
    })
  }
  
  async generatePDFThumbnail(file: File): Promise<string> {
    // Implementation using PDF.js
    const pdfjsLib = await import('pdfjs-dist')
    const arrayBuffer = await file.arrayBuffer()
    const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise
    const page = await pdf.getPage(1)
    
    const viewport = page.getViewport({ scale: 0.5 })
    this.canvas.width = viewport.width
    this.canvas.height = viewport.height
    
    await page.render({
      canvasContext: this.ctx,
      viewport: viewport
    }).promise
    
    return this.canvas.toDataURL('image/jpeg', 0.8)
  }
}
```

## Error Handling and Recovery

### Error Types and Handling
```typescript
enum ZiExplorerErrorType {
  FILE_SYSTEM_ACCESS_DENIED = 'FILE_SYSTEM_ACCESS_DENIED',
  FILE_NOT_FOUND = 'FILE_NOT_FOUND',
  INSUFFICIENT_STORAGE = 'INSUFFICIENT_STORAGE',
  INVALID_FILE_TYPE = 'INVALID_FILE_TYPE',
  NETWORK_ERROR = 'NETWORK_ERROR',
  PERMISSION_DENIED = 'PERMISSION_DENIED'
}

class ZiExplorerError extends Error {
  constructor(
    public type: ZiExplorerErrorType,
    message: string,
    public recoverable: boolean = true,
    public retryAction?: () => Promise<void>
  ) {
    super(message)
    this.name = 'ZiExplorerError'
  }
}

class ErrorHandler {
  static handle(error: ZiExplorerError): void {
    switch (error.type) {
      case ZiExplorerErrorType.FILE_SYSTEM_ACCESS_DENIED:
        this.showAccessDeniedDialog(error)
        break
      case ZiExplorerErrorType.INSUFFICIENT_STORAGE:
        this.showStorageFullDialog(error)
        break
      default:
        this.showGenericErrorDialog(error)
    }
  }
  
  private static showAccessDeniedDialog(error: ZiExplorerError): void {
    // Show user-friendly dialog with fallback options
  }
}
```

This technical specification provides the detailed implementation guidelines for building ZiExplorer according to the context document requirements.
