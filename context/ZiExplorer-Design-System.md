# ZiExplorer Design System

## Design Philosophy

### Apple-Inspired Aesthetics
ZiExplorer follows Apple's design principles with modern enhancements:
- **Clarity**: Clean, uncluttered interface with purposeful elements
- **Deference**: Content takes precedence over UI chrome
- **Depth**: Layered interface with realistic motion and vitality

### Liquid Glass Visual Language
- **Translucency**: Semi-transparent surfaces with backdrop blur
- **Layering**: Multiple depth levels with appropriate shadows
- **Fluidity**: Smooth animations and organic transitions
- **Responsiveness**: Adaptive to user interaction and context

## Color System

### Primary Palette
```css
:root {
  /* Liquid Glass Base Colors */
  --glass-primary: rgba(255, 255, 255, 0.1);
  --glass-secondary: rgba(255, 255, 255, 0.05);
  --glass-tertiary: rgba(255, 255, 255, 0.02);
  
  /* Accent Colors */
  --accent-blue: rgba(59, 130, 246, 0.8);
  --accent-purple: rgba(139, 92, 246, 0.8);
  --accent-green: rgba(34, 197, 94, 0.8);
  --accent-orange: rgba(249, 115, 22, 0.8);
  --accent-red: rgba(239, 68, 68, 0.8);
  
  /* Text Colors */
  --text-primary: rgba(0, 0, 0, 0.9);
  --text-secondary: rgba(0, 0, 0, 0.6);
  --text-tertiary: rgba(0, 0, 0, 0.4);
  
  /* Dark Mode */
  --text-primary-dark: rgba(255, 255, 255, 0.9);
  --text-secondary-dark: rgba(255, 255, 255, 0.6);
  --text-tertiary-dark: rgba(255, 255, 255, 0.4);
}
```

### Contextual Colors
```css
/* File Type Colors */
.file-type-document { --file-color: var(--accent-blue); }
.file-type-image { --file-color: var(--accent-green); }
.file-type-audio { --file-color: var(--accent-purple); }
.file-type-video { --file-color: var(--accent-orange); }
.file-type-archive { --file-color: var(--accent-red); }

/* Status Colors */
.status-success { --status-color: rgba(34, 197, 94, 0.8); }
.status-warning { --status-color: rgba(245, 158, 11, 0.8); }
.status-error { --status-color: rgba(239, 68, 68, 0.8); }
.status-info { --status-color: rgba(59, 130, 246, 0.8); }
```

## Typography

### Font Hierarchy
```css
/* Primary Font Stack */
.zi-font-primary {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 
               'Helvetica Neue', Arial, sans-serif;
}

/* Monospace for Code/Paths */
.zi-font-mono {
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', 
               Consolas, 'Courier New', monospace;
}

/* Text Sizes */
.zi-text-xs { font-size: 0.75rem; line-height: 1rem; }
.zi-text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.zi-text-base { font-size: 1rem; line-height: 1.5rem; }
.zi-text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.zi-text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.zi-text-2xl { font-size: 1.5rem; line-height: 2rem; }

/* Font Weights */
.zi-font-light { font-weight: 300; }
.zi-font-normal { font-weight: 400; }
.zi-font-medium { font-weight: 500; }
.zi-font-semibold { font-weight: 600; }
.zi-font-bold { font-weight: 700; }
```

## Component Design Specifications

### Window Container
```css
.zi-explorer-window {
  /* Liquid Glass Background */
  background: linear-gradient(135deg, 
    rgba(255,255,255,0.1) 0%, 
    rgba(255,255,255,0.05) 100%
  );
  backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid rgba(255,255,255,0.2);
  border-radius: 16px;
  
  /* Shadow System */
  box-shadow: 
    0 8px 32px rgba(0,0,0,0.1),
    0 1px 0 rgba(255,255,255,0.2) inset,
    0 -1px 0 rgba(0,0,0,0.1) inset;
  
  /* Responsive Sizing */
  min-width: 320px;
  min-height: 240px;
  max-width: 100vw;
  max-height: 100vh;
}

/* Window Header */
.zi-explorer-header {
  height: 44px;
  background: linear-gradient(180deg, 
    rgba(255,255,255,0.1) 0%, 
    rgba(255,255,255,0.05) 100%
  );
  border-bottom: 1px solid rgba(255,255,255,0.1);
  border-radius: 16px 16px 0 0;
  padding: 0 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Traffic Light Controls */
.zi-traffic-lights {
  display: flex;
  gap: 8px;
}

.zi-traffic-light {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.zi-traffic-light.close {
  background: linear-gradient(135deg, #ff5f57 0%, #ff3b30 100%);
}

.zi-traffic-light.minimize {
  background: linear-gradient(135deg, #ffbd2e 0%, #ff9500 100%);
}

.zi-traffic-light.maximize {
  background: linear-gradient(135deg, #28ca42 0%, #30d158 100%);
}

.zi-traffic-light:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}
```

### File Grid System
```css
.zi-file-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 16px;
  padding: 16px;
  overflow-y: auto;
}

.zi-file-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255,255,255,0.05);
  border: 1px solid transparent;
}

.zi-file-item:hover {
  background: rgba(255,255,255,0.1);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0,0,0,0.1);
}

.zi-file-item.selected {
  background: linear-gradient(135deg, 
    rgba(59, 130, 246, 0.2) 0%, 
    rgba(99, 102, 241, 0.15) 100%
  );
  border-color: rgba(59, 130, 246, 0.4);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.zi-file-icon {
  width: 48px;
  height: 48px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  border-radius: 8px;
  background: var(--file-color, rgba(255,255,255,0.1));
}

.zi-file-name {
  font-size: 0.875rem;
  font-weight: 500;
  text-align: center;
  color: var(--text-primary);
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
```

### Sidebar Navigation
```css
.zi-explorer-sidebar {
  width: 240px;
  background: linear-gradient(180deg, 
    rgba(255,255,255,0.08) 0%, 
    rgba(255,255,255,0.04) 100%
  );
  border-right: 1px solid rgba(255,255,255,0.1);
  padding: 16px 0;
  overflow-y: auto;
}

.zi-sidebar-section {
  margin-bottom: 24px;
}

.zi-sidebar-title {
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: var(--text-secondary);
  padding: 0 16px 8px;
}

.zi-sidebar-item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  margin: 0 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--text-primary);
}

.zi-sidebar-item:hover {
  background: rgba(255,255,255,0.1);
}

.zi-sidebar-item.active {
  background: linear-gradient(135deg, 
    rgba(59, 130, 246, 0.2) 0%, 
    rgba(99, 102, 241, 0.15) 100%
  );
  color: var(--accent-blue);
}

.zi-sidebar-icon {
  width: 20px;
  height: 20px;
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}
```

## Animation System

### Transition Presets
```css
/* Smooth Transitions */
.zi-transition-smooth {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.zi-transition-fast {
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

.zi-transition-slow {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Spring Animations */
.zi-spring-bounce {
  transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.zi-spring-gentle {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
```

### Framer Motion Variants
```typescript
export const windowVariants = {
  hidden: {
    opacity: 0,
    scale: 0.9,
    y: 20
  },
  visible: {
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      duration: 0.3,
      ease: [0.4, 0, 0.2, 1]
    }
  },
  exit: {
    opacity: 0,
    scale: 0.9,
    y: -20,
    transition: {
      duration: 0.2,
      ease: [0.4, 0, 0.2, 1]
    }
  }
}

export const fileItemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: (index: number) => ({
    opacity: 1,
    y: 0,
    transition: {
      delay: index * 0.05,
      duration: 0.3,
      ease: [0.4, 0, 0.2, 1]
    }
  }),
  hover: {
    y: -4,
    scale: 1.02,
    transition: {
      duration: 0.2,
      ease: [0.4, 0, 0.2, 1]
    }
  }
}

export const contextMenuVariants = {
  hidden: {
    opacity: 0,
    scale: 0.95,
    y: -10
  },
  visible: {
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      duration: 0.15,
      ease: [0.4, 0, 0.2, 1]
    }
  }
}
```

## Responsive Design

### Breakpoint System
```css
/* Mobile First Approach */
.zi-responsive {
  /* Mobile (default) */
  --zi-grid-columns: 3;
  --zi-sidebar-width: 0;
  --zi-padding: 12px;
  --zi-gap: 12px;
}

/* Tablet */
@media (min-width: 768px) {
  .zi-responsive {
    --zi-grid-columns: 4;
    --zi-sidebar-width: 200px;
    --zi-padding: 16px;
    --zi-gap: 16px;
  }
}

/* Desktop */
@media (min-width: 1024px) {
  .zi-responsive {
    --zi-grid-columns: 6;
    --zi-sidebar-width: 240px;
    --zi-padding: 20px;
    --zi-gap: 20px;
  }
}

/* Large Desktop */
@media (min-width: 1440px) {
  .zi-responsive {
    --zi-grid-columns: 8;
    --zi-sidebar-width: 280px;
    --zi-padding: 24px;
    --zi-gap: 24px;
  }
}
```

### Touch Optimizations
```css
/* Touch-friendly sizing */
@media (pointer: coarse) {
  .zi-file-item {
    min-height: 44px;
    padding: 16px 12px;
  }
  
  .zi-sidebar-item {
    min-height: 44px;
    padding: 12px 16px;
  }
  
  .zi-button {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Hover states only for non-touch devices */
@media (hover: hover) {
  .zi-file-item:hover {
    transform: translateY(-2px);
  }
}
```

## Accessibility

### Focus Management
```css
.zi-focus-visible {
  outline: 2px solid var(--accent-blue);
  outline-offset: 2px;
  border-radius: 4px;
}

.zi-focus-visible:focus-visible {
  outline: 2px solid var(--accent-blue);
  outline-offset: 2px;
}

/* Remove default focus styles */
.zi-focus-visible:focus:not(:focus-visible) {
  outline: none;
}
```

### Screen Reader Support
```css
.zi-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.zi-sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}
```

## Dark Mode Support

### Color Scheme Variables
```css
@media (prefers-color-scheme: dark) {
  :root {
    --glass-primary: rgba(0, 0, 0, 0.3);
    --glass-secondary: rgba(0, 0, 0, 0.2);
    --glass-tertiary: rgba(0, 0, 0, 0.1);
    
    --text-primary: var(--text-primary-dark);
    --text-secondary: var(--text-secondary-dark);
    --text-tertiary: var(--text-tertiary-dark);
  }
  
  .zi-explorer-window {
    background: linear-gradient(135deg, 
      rgba(0,0,0,0.3) 0%, 
      rgba(0,0,0,0.2) 100%
    );
    border-color: rgba(255,255,255,0.1);
  }
}
```

## Performance Considerations

### GPU Acceleration
```css
.zi-gpu-accelerated {
  transform: translateZ(0);
  will-change: transform, opacity;
}

.zi-smooth-scroll {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}
```

### Reduced Motion Support
```css
@media (prefers-reduced-motion: reduce) {
  .zi-transition-smooth,
  .zi-transition-fast,
  .zi-transition-slow {
    transition: none;
  }
  
  .zi-spring-bounce,
  .zi-spring-gentle {
    transition: none;
  }
}
```

This design system ensures consistent, beautiful, and accessible UI components throughout ZiExplorer while maintaining the liquid glass aesthetic and Apple-inspired design principles.
