# ZiExplorer Context Document

## Overview
ZiExplorer is the core file management system for the ZiWorkspace desktop environment. It serves as a smart file manager that bridges virtual desktop functionality with native device file system operations, providing a seamless file management experience across desktop and mobile platforms.

## Core Philosophy
ZiExplorer operates on the principle of **"Virtual Interface, Native Storage"** - providing a sophisticated virtual desktop interface while performing actual file operations on the device's native file system.

## Architecture

### Current Implementation Analysis
Based on the existing codebase:
- **Virtual File System**: Currently uses BrowserFS with IndexedDB for browser-based file storage
- **File Explorer Component**: Exists with basic file browsing capabilities
- **Window Management**: Integrated with react-winbox for draggable/resizable windows
- **Desktop Environment**: Apple-inspired design with liquid glass aesthetics

### Target Architecture
```
ZiExplorer/
├── Core/
│   ├── NativeFileSystemBridge.ts    # Bridge to device file system
│   ├── ZiExplorerManager.ts         # Main file operations manager
│   └── FileSystemWatcher.ts         # File system change monitoring
├── Components/
│   ├── ZiExplorerWindow.tsx         # Main explorer window
│   ├── FileGrid.tsx                 # File/folder grid view
│   ├── FileList.tsx                 # File/folder list view
│   ├── ContextMenu.tsx              # Right-click context menu
│   ├── ImportDialog.tsx             # File import interface
│   └── FolderTree.tsx               # Sidebar folder navigation
├── Hooks/
│   ├── useNativeFileSystem.ts       # Native file system operations
│   ├── useFileOperations.ts         # Copy, paste, delete operations
│   └── useFileWatcher.ts            # File change monitoring
└── Types/
    └── ZiExplorerTypes.ts           # Type definitions
```

## File System Structure

### Native Device Structure
```
Device Root/
└── ZiExplorer/                      # Main ZiExplorer folder
    ├── Documents/                   # Document files
    │   ├── Folders/                 # User-created folders
    │   └── Files/                   # Document files
    ├── Pictures/                    # Image files
    │   ├── Albums/                  # User-created albums
    │   └── Images/                  # Image files
    └── Music/                       # Audio files
        ├── Playlists/               # User-created playlists
        └── Tracks/                  # Audio files
```

### Platform-Specific Locations
- **Windows**: `%USERPROFILE%/Documents/ZiExplorer/`
- **macOS**: `~/Documents/ZiExplorer/`
- **Linux**: `~/Documents/ZiExplorer/`
- **Android**: `/storage/emulated/0/Documents/ZiExplorer/`
- **iOS**: App Documents Directory (sandboxed)

## User Interaction Patterns

### Single Click Behavior
- **Documents Section**: Opens import dialog for document files
- **Pictures Section**: Opens import dialog for image files  
- **Music Section**: Opens import dialog for audio files
- **Existing Files/Folders**: Shows context menu with options:
  - Copy
  - Cut
  - Paste (if clipboard has content)
  - Delete
  - Rename
  - Share (Bluetooth/other methods)
  - Properties

### Double Click/Tap Behavior
- **Documents Section**: Opens Documents window in ZiExplorer
- **Pictures Section**: Opens Pictures window in ZiExplorer
- **Music Section**: Opens Music window in ZiExplorer
- **Files**: Opens file in appropriate viewer/editor
- **Folders**: Navigates into folder

### Context Menu Actions
```typescript
interface ContextMenuAction {
  id: string
  label: string
  icon: string
  action: () => void
  disabled?: boolean
  separator?: boolean
}

const contextMenuActions: ContextMenuAction[] = [
  { id: 'open', label: 'Open', icon: '📂', action: openFile },
  { id: 'copy', label: 'Copy', icon: '📋', action: copyFile },
  { id: 'cut', label: 'Cut', icon: '✂️', action: cutFile },
  { id: 'paste', label: 'Paste', icon: '📄', action: pasteFile, disabled: !hasClipboard },
  { separator: true },
  { id: 'delete', label: 'Delete', icon: '🗑️', action: deleteFile },
  { id: 'rename', label: 'Rename', icon: '✏️', action: renameFile },
  { separator: true },
  { id: 'share', label: 'Share', icon: '📤', action: shareFile },
  { id: 'properties', label: 'Properties', icon: 'ℹ️', action: showProperties }
]
```

## Window Design Specifications

### Apple-Inspired Window Design
- **Header**: Liquid glass effect with traffic light controls (close, minimize, maximize)
- **Sidebar**: Folder tree navigation with smooth animations
- **Main Area**: File grid/list view with smooth transitions
- **Footer**: Status bar with file count, selection info, and view controls

### Window Behaviors
- **Draggable**: Full window dragging from header
- **Resizable**: All edges and corners
- **Minimizable**: To taskbar with preview
- **Maximizable**: Full screen with restore capability
- **Multi-instance**: Multiple explorer windows can be open

### Responsive Design
- **Desktop**: Full window with sidebar and main area
- **Tablet**: Collapsible sidebar, touch-optimized controls
- **Mobile**: Full-screen modal with bottom navigation

## File Operations

### Import Operations
```typescript
interface ImportOptions {
  targetFolder: 'Documents' | 'Pictures' | 'Music'
  allowedTypes: string[]
  multiSelect: boolean
  preserveStructure: boolean
}

const importConfigs = {
  Documents: {
    allowedTypes: ['.pdf', '.doc', '.docx', '.txt', '.md', '.rtf'],
    multiSelect: true,
    preserveStructure: true
  },
  Pictures: {
    allowedTypes: ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg', '.webp'],
    multiSelect: true,
    preserveStructure: true
  },
  Music: {
    allowedTypes: ['.mp3', '.wav', '.flac', '.aac', '.ogg', '.m4a'],
    multiSelect: true,
    preserveStructure: true
  }
}
```

### File System Operations
- **Create Folder**: Native folder creation with validation
- **Copy/Paste**: Native file system copy operations
- **Move**: Native file system move operations
- **Delete**: Move to recycle bin, permanent delete option
- **Rename**: In-place renaming with validation
- **Share**: Platform-specific sharing (Bluetooth, email, etc.)

## Technology Integration

### Native File System APIs
- **File System Access API** (Chrome/Edge): Direct file system access
- **File API** (All browsers): File reading and basic operations
- **Web Share API**: Native sharing capabilities
- **Bluetooth Web API**: Bluetooth file sharing

### Fallback Strategies
- **IndexedDB**: For browsers without native file system access
- **Download/Upload**: For file transfer in restricted environments
- **Cloud Storage**: Optional integration with cloud providers

## Performance Considerations

### Optimization Strategies
- **Lazy Loading**: Load file thumbnails and metadata on demand
- **Virtual Scrolling**: For large file lists
- **Debounced Search**: Efficient file searching
- **Caching**: Intelligent caching of file metadata and thumbnails
- **Progressive Enhancement**: Core functionality works without advanced APIs

### Mobile Optimizations
- **Touch Gestures**: Swipe, pinch, long-press support
- **Reduced Animations**: On low-end devices
- **Efficient Rendering**: Minimize DOM updates
- **Battery Awareness**: Reduce background operations

## Security and Privacy

### File Access Security
- **Sandboxed Operations**: Restrict access to ZiExplorer folder only
- **User Consent**: Explicit permission for file operations
- **Secure Sharing**: Encrypted file sharing when possible
- **Privacy Protection**: No unauthorized file access or transmission

### Data Protection
- **Local Storage**: All files remain on device by default
- **Encryption**: Optional file encryption for sensitive documents
- **Audit Trail**: Log of file operations for security review
- **Permission Management**: Granular control over file access

## Integration Points

### VSSKit Library Integration
- **Book Import**: Direct import of ebooks to Documents
- **Study Materials**: Organized storage of educational content
- **Sync Capabilities**: Optional sync with library content

### ZiApps Integration
- **File Associations**: Open files in appropriate ZiApps
- **Recent Files**: Cross-app recent file access
- **Shared Clipboard**: Unified copy/paste across apps

### AI Assistant Integration
- **Smart Organization**: AI-powered file organization suggestions
- **Content Analysis**: Automatic tagging and categorization
- **Search Enhancement**: Natural language file search
- **Workflow Automation**: AI-assisted file operations

## Implementation Phases

### Phase 1: Core Infrastructure
1. Native file system bridge implementation
2. Basic file operations (create, read, update, delete)
3. Folder structure initialization
4. Simple file browser interface

### Phase 2: Enhanced UI/UX
1. Apple-inspired window design
2. Liquid glass visual effects
3. Smooth animations and transitions
4. Context menu implementation

### Phase 3: Advanced Features
1. File sharing capabilities
2. Advanced search and filtering
3. Thumbnail generation
4. Multi-selection operations

### Phase 4: Platform Optimization
1. Mobile-specific optimizations
2. Touch gesture support
3. Performance enhancements
4. Accessibility improvements

## Success Metrics

### Functionality Metrics
- File operation success rate: >99%
- Import operation completion rate: >95%
- Cross-platform compatibility: 100%
- Performance benchmarks: <100ms for basic operations

### User Experience Metrics
- User satisfaction with file management: >4.5/5
- Task completion rate: >90%
- Learning curve: <5 minutes for basic operations
- Error recovery rate: >95%

## Future Enhancements

### Advanced Features
- **Cloud Integration**: Seamless cloud storage integration
- **Version Control**: File versioning and history
- **Collaboration**: Real-time file collaboration
- **Advanced Search**: AI-powered content search
- **Automation**: Workflow automation and scripting

### Platform Extensions
- **Desktop Apps**: Native desktop application versions
- **Browser Extensions**: Enhanced web browser integration
- **Mobile Apps**: Dedicated mobile applications
- **API Access**: Third-party integration capabilities

---

This context document serves as the foundation for ZiExplorer development, ensuring consistency, quality, and alignment with the overall VSSKit ecosystem vision.
