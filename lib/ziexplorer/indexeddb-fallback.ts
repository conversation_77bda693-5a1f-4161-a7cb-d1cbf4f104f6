// IndexedDB Fallback File System for ZiExplorer

import { FileItem, FolderItem, ImportResult, ValidationResult, ZIEXPLORER_SECTIONS } from './types'

interface StoredFile {
  path: string
  name: string
  type: 'file' | 'folder'
  size?: number
  mimeType?: string
  content?: ArrayBuffer
  createdAt: Date
  modifiedAt: Date
  parentPath: string
}

export class IndexedDBFallback {
  private db: IDBDatabase | null = null
  private dbName = 'ZiExplorerFS'
  private dbVersion = 1
  private storeName = 'files'

  async initialize(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion)

      request.onerror = () => {
        reject(new Error('Failed to open IndexedDB'))
      }

      request.onsuccess = () => {
        this.db = request.result
        this.initializeDefaultStructure().then(resolve).catch(reject)
      }

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result
        
        // Create object store
        if (!db.objectStoreNames.contains(this.storeName)) {
          const store = db.createObjectStore(this.storeName, { keyPath: 'path' })
          store.createIndex('parentPath', 'parentPath', { unique: false })
          store.createIndex('type', 'type', { unique: false })
          store.createIndex('name', 'name', { unique: false })
        }
      }
    })
  }

  private async initializeDefaultStructure(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized')

    // Check if root structure exists
    const rootExists = await this.fileExists('/')
    if (rootExists) return

    // Create default ZiExplorer folder structure
    const transaction = this.db.transaction([this.storeName], 'readwrite')
    const store = transaction.objectStore(this.storeName)

    // Create root folder
    const rootFolder: StoredFile = {
      path: '/',
      name: 'ZiExplorer',
      type: 'folder',
      createdAt: new Date(),
      modifiedAt: new Date(),
      parentPath: ''
    }
    store.add(rootFolder)

    // Create main sections
    for (const section of ZIEXPLORER_SECTIONS) {
      const sectionFolder: StoredFile = {
        path: section.path,
        name: section.name,
        type: 'folder',
        createdAt: new Date(),
        modifiedAt: new Date(),
        parentPath: '/'
      }
      store.add(sectionFolder)
    }

    // Create additional system folders
    const systemFolders = [
      { path: '/Recent', name: 'Recent', parentPath: '/' },
      { path: '/Favorites', name: 'Favorites', parentPath: '/' },
      { path: '/Shared', name: 'Shared', parentPath: '/' },
      { path: '/RecycleBin', name: 'Recycle Bin', parentPath: '/' }
    ]

    for (const folder of systemFolders) {
      const folderData: StoredFile = {
        ...folder,
        type: 'folder',
        createdAt: new Date(),
        modifiedAt: new Date()
      }
      store.add(folderData)
    }

    return new Promise((resolve, reject) => {
      transaction.oncomplete = () => resolve()
      transaction.onerror = () => reject(new Error('Failed to create default structure'))
    })
  }

  async listDirectory(path: string): Promise<FileItem[]> {
    if (!this.db) throw new Error('Database not initialized')

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.storeName], 'readonly')
      const store = transaction.objectStore(this.storeName)
      const index = store.index('parentPath')
      const request = index.getAll(path)

      request.onsuccess = () => {
        const files: FileItem[] = request.result.map((stored: StoredFile) => ({
          path: stored.path,
          name: stored.name,
          type: stored.type,
          size: stored.size,
          mimeType: stored.mimeType,
          createdAt: stored.createdAt,
          modifiedAt: stored.modifiedAt
        }))

        // Sort: folders first, then files, alphabetically
        files.sort((a, b) => {
          if (a.type !== b.type) {
            return a.type === 'folder' ? -1 : 1
          }
          return a.name.localeCompare(b.name)
        })

        resolve(files)
      }

      request.onerror = () => {
        reject(new Error('Failed to list directory'))
      }
    })
  }

  async createFolder(parentPath: string, name: string): Promise<boolean> {
    if (!this.db) throw new Error('Database not initialized')

    const folderPath = `${parentPath}/${name}`.replace('//', '/')
    
    // Check if folder already exists
    const exists = await this.fileExists(folderPath)
    if (exists) return false

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.storeName], 'readwrite')
      const store = transaction.objectStore(this.storeName)

      const folderData: StoredFile = {
        path: folderPath,
        name,
        type: 'folder',
        createdAt: new Date(),
        modifiedAt: new Date(),
        parentPath
      }

      const request = store.add(folderData)

      request.onsuccess = () => resolve(true)
      request.onerror = () => reject(new Error('Failed to create folder'))
    })
  }

  async importFiles(files: FileList, targetPath: string): Promise<ImportResult[]> {
    if (!this.db) throw new Error('Database not initialized')

    const results: ImportResult[] = []
    const transaction = this.db.transaction([this.storeName], 'readwrite')
    const store = transaction.objectStore(this.storeName)

    for (const file of Array.from(files)) {
      try {
        const filePath = `${targetPath}/${file.name}`.replace('//', '/')
        
        // Check if file already exists
        const exists = await this.fileExists(filePath)
        if (exists) {
          results.push({
            success: false,
            path: filePath,
            error: 'File already exists'
          })
          continue
        }

        // Read file content
        const content = await this.readFileAsArrayBuffer(file)

        const fileData: StoredFile = {
          path: filePath,
          name: file.name,
          type: 'file',
          size: file.size,
          mimeType: file.type,
          content,
          createdAt: new Date(),
          modifiedAt: new Date(),
          parentPath: targetPath
        }

        await new Promise<void>((resolve, reject) => {
          const request = store.add(fileData)
          request.onsuccess = () => resolve()
          request.onerror = () => reject(new Error('Failed to store file'))
        })

        results.push({
          success: true,
          path: filePath
        })
      } catch (error) {
        results.push({
          success: false,
          path: `${targetPath}/${file.name}`,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    return results
  }

  async copyFile(sourcePath: string, targetPath: string): Promise<boolean> {
    if (!this.db) throw new Error('Database not initialized')

    try {
      const sourceFile = await this.getFile(sourcePath)
      if (!sourceFile) return false

      const fileName = sourcePath.substring(sourcePath.lastIndexOf('/') + 1)
      const newPath = `${targetPath}/${fileName}`.replace('//', '/')

      // Check if target already exists
      const exists = await this.fileExists(newPath)
      if (exists) return false

      const transaction = this.db.transaction([this.storeName], 'readwrite')
      const store = transaction.objectStore(this.storeName)

      const newFile: StoredFile = {
        ...sourceFile,
        path: newPath,
        parentPath: targetPath,
        createdAt: new Date(),
        modifiedAt: new Date()
      }

      return new Promise((resolve, reject) => {
        const request = store.add(newFile)
        request.onsuccess = () => resolve(true)
        request.onerror = () => reject(new Error('Failed to copy file'))
      })
    } catch (error) {
      console.error('Copy file error:', error)
      return false
    }
  }

  async deleteFile(path: string): Promise<boolean> {
    if (!this.db) throw new Error('Database not initialized')

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.storeName], 'readwrite')
      const store = transaction.objectStore(this.storeName)

      const request = store.delete(path)

      request.onsuccess = () => resolve(true)
      request.onerror = () => reject(new Error('Failed to delete file'))
    })
  }

  async readFile(path: string): Promise<File | null> {
    if (!this.db) throw new Error('Database not initialized')

    const storedFile = await this.getFile(path)
    if (!storedFile || storedFile.type !== 'file' || !storedFile.content) {
      return null
    }

    return new File([storedFile.content], storedFile.name, {
      type: storedFile.mimeType || 'application/octet-stream',
      lastModified: storedFile.modifiedAt.getTime()
    })
  }

  validateFileName(name: string): ValidationResult {
    // Check for invalid characters
    const invalidChars = /[<>:"/\\|?*\x00-\x1f]/
    if (invalidChars.test(name)) {
      return {
        isValid: false,
        error: 'File name contains invalid characters'
      }
    }

    // Check length
    if (name.length === 0) {
      return {
        isValid: false,
        error: 'File name cannot be empty'
      }
    }

    if (name.length > 255) {
      return {
        isValid: false,
        error: 'File name is too long'
      }
    }

    // Check for reserved names
    const reservedNames = ['CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9']
    if (reservedNames.includes(name.toUpperCase())) {
      return {
        isValid: false,
        error: 'File name is reserved'
      }
    }

    return { isValid: true }
  }

  // Helper methods
  private async fileExists(path: string): Promise<boolean> {
    if (!this.db) return false

    return new Promise((resolve) => {
      const transaction = this.db!.transaction([this.storeName], 'readonly')
      const store = transaction.objectStore(this.storeName)
      const request = store.get(path)

      request.onsuccess = () => resolve(!!request.result)
      request.onerror = () => resolve(false)
    })
  }

  private async getFile(path: string): Promise<StoredFile | null> {
    if (!this.db) return null

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.storeName], 'readonly')
      const store = transaction.objectStore(this.storeName)
      const request = store.get(path)

      request.onsuccess = () => resolve(request.result || null)
      request.onerror = () => reject(new Error('Failed to get file'))
    })
  }

  private readFileAsArrayBuffer(file: File): Promise<ArrayBuffer> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result as ArrayBuffer)
      reader.onerror = () => reject(new Error('Failed to read file'))
      reader.readAsArrayBuffer(file)
    })
  }

  // Storage info
  async getStorageInfo(): Promise<{ used: number; available: number; total: number }> {
    if (!navigator.storage || !navigator.storage.estimate) {
      return { used: 0, available: 0, total: 0 }
    }

    try {
      const estimate = await navigator.storage.estimate()
      const used = estimate.usage || 0
      const total = estimate.quota || 0
      const available = total - used

      return { used, available, total }
    } catch (error) {
      console.error('Failed to get storage info:', error)
      return { used: 0, available: 0, total: 0 }
    }
  }
}
