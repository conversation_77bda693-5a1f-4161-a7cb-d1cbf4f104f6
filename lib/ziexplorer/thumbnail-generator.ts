// Thumbnail Generator for ZiExplorer

import { ThumbnailOptions, getFileTypeFromExtension } from './types'

export class ThumbnailGenerator {
  private canvas: HTMLCanvasElement
  private ctx: CanvasRenderingContext2D
  private thumbnailCache: Map<string, string> = new Map()

  constructor() {
    this.canvas = document.createElement('canvas')
    const context = this.canvas.getContext('2d')
    if (!context) {
      throw new Error('Canvas 2D context not supported')
    }
    this.ctx = context
  }

  async generateThumbnail(
    file: File, 
    options: Partial<ThumbnailOptions> = {}
  ): Promise<string | null> {
    const opts: ThumbnailOptions = {
      width: 120,
      height: 120,
      quality: 0.8,
      format: 'jpeg',
      ...options
    }

    // Check cache first
    const cacheKey = `${file.name}-${file.lastModified}-${opts.width}x${opts.height}`
    if (this.thumbnailCache.has(cacheKey)) {
      return this.thumbnailCache.get(cacheKey)!
    }

    const fileType = getFileTypeFromExtension(file.name)
    let thumbnail: string | null = null

    try {
      switch (fileType) {
        case 'image':
          thumbnail = await this.generateImageThumbnail(file, opts)
          break
        case 'document':
          thumbnail = await this.generateDocumentThumbnail(file, opts)
          break
        case 'audio':
          thumbnail = await this.generateAudioThumbnail(file, opts)
          break
        default:
          thumbnail = await this.generateGenericThumbnail(file, opts)
      }

      // Cache the result
      if (thumbnail) {
        this.thumbnailCache.set(cacheKey, thumbnail)
      }

      return thumbnail
    } catch (error) {
      console.error('Failed to generate thumbnail:', error)
      return null
    }
  }

  private async generateImageThumbnail(
    file: File, 
    options: ThumbnailOptions
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      const img = new Image()
      
      img.onload = () => {
        try {
          this.canvas.width = options.width
          this.canvas.height = options.height

          // Calculate aspect ratio and draw
          const aspectRatio = img.width / img.height
          let drawWidth = options.width
          let drawHeight = options.height

          if (aspectRatio > 1) {
            drawHeight = options.width / aspectRatio
          } else {
            drawWidth = options.height * aspectRatio
          }

          const x = (options.width - drawWidth) / 2
          const y = (options.height - drawHeight) / 2

          // Clear canvas and draw image
          this.ctx.clearRect(0, 0, options.width, options.height)
          this.ctx.fillStyle = '#f3f4f6'
          this.ctx.fillRect(0, 0, options.width, options.height)
          
          this.ctx.drawImage(img, x, y, drawWidth, drawHeight)

          const dataUrl = this.canvas.toDataURL(`image/${options.format}`, options.quality)
          resolve(dataUrl)
        } catch (error) {
          reject(error)
        } finally {
          URL.revokeObjectURL(img.src)
        }
      }

      img.onerror = () => {
        URL.revokeObjectURL(img.src)
        reject(new Error('Failed to load image'))
      }

      img.src = URL.createObjectURL(file)
    })
  }

  private async generateDocumentThumbnail(
    file: File, 
    options: ThumbnailOptions
  ): Promise<string> {
    // For PDF files, try to use PDF.js if available
    if (file.type === 'application/pdf') {
      try {
        return await this.generatePDFThumbnail(file, options)
      } catch (error) {
        console.warn('PDF thumbnail generation failed, using generic thumbnail')
      }
    }

    // Generate generic document thumbnail
    return this.generateDocumentIcon(file, options)
  }

  private async generatePDFThumbnail(
    file: File, 
    options: ThumbnailOptions
  ): Promise<string> {
    // Dynamic import to avoid bundling PDF.js if not needed
    const pdfjsLib = await import('pdfjs-dist')
    
    // Set worker source
    pdfjsLib.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.js'

    const arrayBuffer = await file.arrayBuffer()
    const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise
    const page = await pdf.getPage(1)

    const viewport = page.getViewport({ scale: 1 })
    const scale = Math.min(
      options.width / viewport.width,
      options.height / viewport.height
    )

    const scaledViewport = page.getViewport({ scale })

    this.canvas.width = options.width
    this.canvas.height = options.height

    // Clear canvas with white background
    this.ctx.fillStyle = '#ffffff'
    this.ctx.fillRect(0, 0, options.width, options.height)

    // Center the PDF page
    const x = (options.width - scaledViewport.width) / 2
    const y = (options.height - scaledViewport.height) / 2

    this.ctx.save()
    this.ctx.translate(x, y)

    await page.render({
      canvasContext: this.ctx,
      viewport: scaledViewport
    }).promise

    this.ctx.restore()

    return this.canvas.toDataURL(`image/${options.format}`, options.quality)
  }

  private async generateAudioThumbnail(
    file: File, 
    options: ThumbnailOptions
  ): Promise<string> {
    // Try to extract album art if available
    try {
      // This would require a library like music-metadata-browser
      // For now, generate a generic audio icon
      return this.generateAudioIcon(file, options)
    } catch (error) {
      return this.generateAudioIcon(file, options)
    }
  }

  private generateDocumentIcon(file: File, options: ThumbnailOptions): string {
    this.canvas.width = options.width
    this.canvas.height = options.height

    // Clear canvas
    this.ctx.clearRect(0, 0, options.width, options.height)

    // Draw document icon background
    this.ctx.fillStyle = '#3b82f6'
    this.ctx.fillRect(10, 10, options.width - 20, options.height - 20)

    // Draw document icon
    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = `${options.width * 0.4}px Arial`
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillText('📄', options.width / 2, options.height / 2)

    // Draw file extension
    const extension = file.name.substring(file.name.lastIndexOf('.') + 1).toUpperCase()
    this.ctx.font = `${options.width * 0.1}px Arial`
    this.ctx.fillStyle = '#ffffff'
    this.ctx.fillText(extension, options.width / 2, options.height - 15)

    return this.canvas.toDataURL(`image/${options.format}`, options.quality)
  }

  private generateAudioIcon(file: File, options: ThumbnailOptions): string {
    this.canvas.width = options.width
    this.canvas.height = options.height

    // Clear canvas
    this.ctx.clearRect(0, 0, options.width, options.height)

    // Draw audio icon background
    this.ctx.fillStyle = '#8b5cf6'
    this.ctx.fillRect(10, 10, options.width - 20, options.height - 20)

    // Draw audio icon
    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = `${options.width * 0.4}px Arial`
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillText('🎵', options.width / 2, options.height / 2)

    // Draw file extension
    const extension = file.name.substring(file.name.lastIndexOf('.') + 1).toUpperCase()
    this.ctx.font = `${options.width * 0.1}px Arial`
    this.ctx.fillStyle = '#ffffff'
    this.ctx.fillText(extension, options.width / 2, options.height - 15)

    return this.canvas.toDataURL(`image/${options.format}`, options.quality)
  }

  private async generateGenericThumbnail(
    file: File, 
    options: ThumbnailOptions
  ): Promise<string> {
    this.canvas.width = options.width
    this.canvas.height = options.height

    // Clear canvas
    this.ctx.clearRect(0, 0, options.width, options.height)

    // Draw generic file icon background
    this.ctx.fillStyle = '#6b7280'
    this.ctx.fillRect(10, 10, options.width - 20, options.height - 20)

    // Draw file icon
    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = `${options.width * 0.4}px Arial`
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillText('📁', options.width / 2, options.height / 2)

    // Draw file extension
    const extension = file.name.substring(file.name.lastIndexOf('.') + 1).toUpperCase()
    this.ctx.font = `${options.width * 0.1}px Arial`
    this.ctx.fillStyle = '#ffffff'
    this.ctx.fillText(extension || 'FILE', options.width / 2, options.height - 15)

    return this.canvas.toDataURL(`image/${options.format}`, options.quality)
  }

  // Cache management
  clearCache(): void {
    this.thumbnailCache.clear()
  }

  getCacheSize(): number {
    return this.thumbnailCache.size
  }

  removeCachedThumbnail(cacheKey: string): void {
    this.thumbnailCache.delete(cacheKey)
  }

  // Utility method to get file icon without generating full thumbnail
  getFileIcon(fileName: string): string {
    const fileType = getFileTypeFromExtension(fileName)
    
    switch (fileType) {
      case 'document':
        return '📄'
      case 'image':
        return '🖼️'
      case 'audio':
        return '🎵'
      default:
        return '📁'
    }
  }

  // Batch thumbnail generation
  async generateThumbnails(
    files: File[], 
    options?: Partial<ThumbnailOptions>
  ): Promise<Map<string, string | null>> {
    const results = new Map<string, string | null>()
    
    // Process files in batches to avoid overwhelming the browser
    const batchSize = 5
    for (let i = 0; i < files.length; i += batchSize) {
      const batch = files.slice(i, i + batchSize)
      const batchPromises = batch.map(async (file) => {
        const thumbnail = await this.generateThumbnail(file, options)
        return { file: file.name, thumbnail }
      })
      
      const batchResults = await Promise.all(batchPromises)
      batchResults.forEach(({ file, thumbnail }) => {
        results.set(file, thumbnail)
      })
      
      // Small delay between batches to prevent blocking
      if (i + batchSize < files.length) {
        await new Promise(resolve => setTimeout(resolve, 10))
      }
    }
    
    return results
  }
}
