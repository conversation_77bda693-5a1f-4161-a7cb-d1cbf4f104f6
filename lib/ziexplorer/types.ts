// ZiExplorer Type Definitions

export interface FileItem {
  path: string
  name: string
  type: 'file' | 'folder'
  size?: number
  mimeType?: string
  createdAt: Date
  modifiedAt: Date
  thumbnail?: string
  isSelected?: boolean
}

export interface FolderItem extends FileItem {
  type: 'folder'
  children?: FileItem[]
  isExpanded?: boolean
}

export interface ZiExplorerSection {
  id: 'documents' | 'pictures' | 'music'
  name: string
  icon: string
  path: string
  allowedTypes: string[]
  color: string
}

export interface ImportOptions {
  targetFolder: 'documents' | 'pictures' | 'music'
  allowedTypes: string[]
  multiSelect: boolean
  preserveStructure: boolean
}

export interface FileOperation {
  type: 'copy' | 'cut' | 'paste' | 'delete' | 'rename' | 'share'
  sourcePaths: string[]
  targetPath?: string
  newName?: string
}

export interface ClipboardItem {
  operation: 'copy' | 'cut'
  paths: string[]
  timestamp: number
}

export interface ContextMenuAction {
  id: string
  label: string
  icon: string
  action?: () => void | Promise<void>
  disabled?: boolean
  separator?: boolean
  shortcut?: string
  submenu?: ContextMenuAction[]
}

export interface ZiExplorerState {
  currentPath: string
  selectedFiles: string[]
  viewMode: 'grid' | 'list' | 'details'
  sortBy: 'name' | 'date' | 'size' | 'type'
  sortOrder: 'asc' | 'desc'
  searchQuery: string
  isLoading: boolean
  clipboard: ClipboardItem | null
  showHiddenFiles: boolean
}

export interface FileSystemCapabilities {
  nativeFileSystemSupported: boolean
  webShareSupported: boolean
  bluetoothSupported: boolean
  persistentStorageSupported: boolean
}

export interface ImportResult {
  success: boolean
  path: string
  error?: string
}

export interface FileChange {
  type: 'added' | 'modified' | 'deleted'
  path: string
  item?: FileItem
}

export interface ValidationResult {
  isValid: boolean
  error?: string
}

export interface ThumbnailOptions {
  width: number
  height: number
  quality: number
  format: 'jpeg' | 'png' | 'webp'
}

export interface ShareOptions {
  method: 'bluetooth' | 'email' | 'link' | 'native' | 'download'
  title?: string
  text?: string
}

export interface ZiExplorerConfig {
  maxFileSize: number
  allowedFileTypes: Record<string, string[]>
  thumbnailSize: number
  enableVirtualScrolling: boolean
  enableFileWatcher: boolean
  defaultViewMode: 'grid' | 'list' | 'details'
}

export interface FileSystemError {
  type: 'ACCESS_DENIED' | 'FILE_NOT_FOUND' | 'INSUFFICIENT_STORAGE' | 'INVALID_FILE_TYPE' | 'NETWORK_ERROR' | 'PERMISSION_DENIED'
  message: string
  recoverable: boolean
  retryAction?: () => Promise<void>
}

export interface Platform {
  type: 'windows' | 'macos' | 'linux' | 'android' | 'ios' | 'web'
  version: string
  isMobile: boolean
  isTouch: boolean
}

// Default configurations
export const DEFAULT_ZIEXPLORER_CONFIG: ZiExplorerConfig = {
  maxFileSize: 100 * 1024 * 1024, // 100MB
  allowedFileTypes: {
    documents: ['.pdf', '.doc', '.docx', '.txt', '.md', '.rtf', '.odt', '.pages'],
    pictures: ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg', '.webp', '.tiff', '.heic'],
    music: ['.mp3', '.wav', '.flac', '.aac', '.ogg', '.m4a', '.wma', '.opus']
  },
  thumbnailSize: 120,
  enableVirtualScrolling: true,
  enableFileWatcher: true,
  defaultViewMode: 'grid'
}

export const ZIEXPLORER_SECTIONS: ZiExplorerSection[] = [
  {
    id: 'documents',
    name: 'Documents',
    icon: '📄',
    path: '/Documents',
    allowedTypes: DEFAULT_ZIEXPLORER_CONFIG.allowedFileTypes.documents,
    color: 'rgba(59, 130, 246, 0.8)'
  },
  {
    id: 'pictures',
    name: 'Pictures',
    icon: '🖼️',
    path: '/Pictures',
    allowedTypes: DEFAULT_ZIEXPLORER_CONFIG.allowedFileTypes.pictures,
    color: 'rgba(34, 197, 94, 0.8)'
  },
  {
    id: 'music',
    name: 'Music',
    icon: '🎵',
    path: '/Music',
    allowedTypes: DEFAULT_ZIEXPLORER_CONFIG.allowedFileTypes.music,
    color: 'rgba(139, 92, 246, 0.8)'
  }
]

// Utility type guards
export const isFileItem = (item: FileItem): item is FileItem => {
  return item.type === 'file'
}

export const isFolderItem = (item: FileItem): item is FolderItem => {
  return item.type === 'folder'
}

export const isValidFileType = (fileName: string, allowedTypes: string[]): boolean => {
  const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
  return allowedTypes.includes(extension)
}

export const getFileTypeFromExtension = (fileName: string): string => {
  const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
  
  if (DEFAULT_ZIEXPLORER_CONFIG.allowedFileTypes.documents.includes(extension)) {
    return 'document'
  }
  if (DEFAULT_ZIEXPLORER_CONFIG.allowedFileTypes.pictures.includes(extension)) {
    return 'image'
  }
  if (DEFAULT_ZIEXPLORER_CONFIG.allowedFileTypes.music.includes(extension)) {
    return 'audio'
  }
  
  return 'unknown'
}

export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

export const formatDate = (date: Date): string => {
  const now = new Date()
  const diffTime = Math.abs(now.getTime() - date.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays === 1) {
    return 'Today'
  } else if (diffDays === 2) {
    return 'Yesterday'
  } else if (diffDays <= 7) {
    return `${diffDays} days ago`
  } else {
    return date.toLocaleDateString()
  }
}
