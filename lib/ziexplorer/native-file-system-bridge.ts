// Native File System Bridge for ZiExplorer

import {
  FileItem,
  FolderItem,
  Platform,
  FileSystemCapabilities,
  FileSystemError,
  ImportResult,
  ValidationResult
} from './types'
import { IndexedDBFallback } from './indexeddb-fallback'

declare global {
  interface Window {
    showDirectoryPicker?: (options?: {
      mode?: 'read' | 'readwrite'
      startIn?: 'desktop' | 'documents' | 'downloads' | 'music' | 'pictures' | 'videos'
    }) => Promise<FileSystemDirectoryHandle>
    showOpenFilePicker?: (options?: {
      multiple?: boolean
      excludeAcceptAllOption?: boolean
      types?: Array<{
        description: string
        accept: Record<string, string[]>
      }>
    }) => Promise<FileSystemFileHandle[]>
  }
}

export class NativeFileSystemBridge {
  private rootHandle: FileSystemDirectoryHandle | null = null
  private ziExplorerHandle: FileSystemDirectoryHandle | null = null
  private indexedDBFallback: IndexedDBFallback | null = null
  private capabilities: FileSystemCapabilities
  private platform: Platform
  private usingFallback = false

  constructor() {
    this.capabilities = this.detectCapabilities()
    this.platform = this.detectPlatform()
  }

  // Platform and capability detection
  private detectPlatform(): Platform {
    const userAgent = navigator.userAgent.toLowerCase()
    const isMobile = /android|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent)
    const isTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0

    let type: Platform['type'] = 'web'
    let version = 'unknown'

    if (userAgent.includes('windows')) {
      type = 'windows'
    } else if (userAgent.includes('mac')) {
      type = userAgent.includes('iphone') || userAgent.includes('ipad') ? 'ios' : 'macos'
    } else if (userAgent.includes('linux')) {
      type = 'linux'
    } else if (userAgent.includes('android')) {
      type = 'android'
    }

    return { type, version, isMobile, isTouch }
  }

  private detectCapabilities(): FileSystemCapabilities {
    return {
      nativeFileSystemSupported: 'showDirectoryPicker' in window,
      webShareSupported: 'share' in navigator,
      bluetoothSupported: 'bluetooth' in navigator,
      persistentStorageSupported: 'storage' in navigator && 'persist' in navigator.storage
    }
  }

  // Initialization
  async initialize(): Promise<void> {
    if (this.capabilities.nativeFileSystemSupported) {
      try {
        await this.initializeNativeFileSystem()
      } catch (error) {
        console.warn('Native file system access denied, falling back to IndexedDB')
        await this.initializeFallback()
      }
    } else {
      await this.initializeFallback()
    }
  }

  private async initializeNativeFileSystem(): Promise<void> {
    if (!window.showDirectoryPicker) {
      throw new Error('Native file system not supported')
    }

    // Request access to Documents folder or user-selected folder
    this.rootHandle = await window.showDirectoryPicker({
      mode: 'readwrite',
      startIn: 'documents'
    })

    // Create or access ZiExplorer folder
    this.ziExplorerHandle = await this.getOrCreateDirectory(this.rootHandle, 'ZiExplorer')

    // Create default structure
    await this.createDefaultStructure()
  }

  private async initializeFallback(): Promise<void> {
    // Initialize IndexedDB fallback
    console.log('Initializing IndexedDB fallback for file system')
    this.indexedDBFallback = new IndexedDBFallback()
    await this.indexedDBFallback.initialize()
    this.usingFallback = true
  }

  private async createDefaultStructure(): Promise<void> {
    if (!this.ziExplorerHandle) return

    const folders = ['Documents', 'Pictures', 'Music']
    for (const folder of folders) {
      await this.getOrCreateDirectory(this.ziExplorerHandle, folder)
    }
  }

  // Directory operations
  private async getOrCreateDirectory(
    parent: FileSystemDirectoryHandle, 
    name: string
  ): Promise<FileSystemDirectoryHandle> {
    try {
      return await parent.getDirectoryHandle(name)
    } catch {
      return await parent.getDirectoryHandle(name, { create: true })
    }
  }

  async createFolder(path: string, name: string): Promise<boolean> {
    if (this.usingFallback && this.indexedDBFallback) {
      return await this.indexedDBFallback.createFolder(path, name)
    }

    try {
      const parentHandle = await this.getDirectoryHandle(path)
      if (!parentHandle) return false

      await parentHandle.getDirectoryHandle(name, { create: true })
      return true
    } catch (error) {
      console.error('Failed to create folder:', error)
      return false
    }
  }

  // File operations
  async importFiles(files: FileList, targetPath: string): Promise<ImportResult[]> {
    if (this.usingFallback && this.indexedDBFallback) {
      return await this.indexedDBFallback.importFiles(files, targetPath)
    }

    const results: ImportResult[] = []

    for (const file of Array.from(files)) {
      try {
        const result = await this.importSingleFile(file, targetPath)
        results.push(result)
      } catch (error) {
        results.push({
          success: false,
          path: `${targetPath}/${file.name}`,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    return results
  }

  private async importSingleFile(file: File, targetPath: string): Promise<ImportResult> {
    const targetHandle = await this.getDirectoryHandle(targetPath)
    if (!targetHandle) {
      throw new Error('Target directory not found')
    }

    const fileHandle = await targetHandle.getFileHandle(file.name, { create: true })
    const writable = await fileHandle.createWritable()
    
    await writable.write(file)
    await writable.close()

    return {
      success: true,
      path: `${targetPath}/${file.name}`
    }
  }

  async readFile(path: string): Promise<File | null> {
    if (this.usingFallback && this.indexedDBFallback) {
      return await this.indexedDBFallback.readFile(path)
    }

    try {
      const fileHandle = await this.getFileHandle(path)
      if (!fileHandle) return null

      return await fileHandle.getFile()
    } catch (error) {
      console.error('Failed to read file:', error)
      return null
    }
  }

  async deleteFile(path: string): Promise<boolean> {
    if (this.usingFallback && this.indexedDBFallback) {
      return await this.indexedDBFallback.deleteFile(path)
    }

    try {
      const parentPath = path.substring(0, path.lastIndexOf('/'))
      const fileName = path.substring(path.lastIndexOf('/') + 1)

      const parentHandle = await this.getDirectoryHandle(parentPath)
      if (!parentHandle) return false

      await parentHandle.removeEntry(fileName, { recursive: true })
      return true
    } catch (error) {
      console.error('Failed to delete file:', error)
      return false
    }
  }

  async copyFile(sourcePath: string, targetPath: string): Promise<boolean> {
    if (this.usingFallback && this.indexedDBFallback) {
      return await this.indexedDBFallback.copyFile(sourcePath, targetPath)
    }

    try {
      const sourceFile = await this.readFile(sourcePath)
      if (!sourceFile) return false

      const fileName = sourcePath.substring(sourcePath.lastIndexOf('/') + 1)
      const dataTransfer = new DataTransfer()
      dataTransfer.items.add(sourceFile)

      const results = await this.importFiles(dataTransfer.files, targetPath)
      return results.length > 0 && results[0].success
    } catch (error) {
      console.error('Failed to copy file:', error)
      return false
    }
  }

  // Directory listing
  async listDirectory(path: string): Promise<FileItem[]> {
    if (this.usingFallback && this.indexedDBFallback) {
      return await this.indexedDBFallback.listDirectory(path)
    }

    try {
      const dirHandle = await this.getDirectoryHandle(path)
      if (!dirHandle) return []

      const items: FileItem[] = []

      for await (const [name, handle] of dirHandle.entries()) {
        const item = await this.createFileItemFromHandle(handle, `${path}/${name}`)
        if (item) {
          items.push(item)
        }
      }

      return items.sort((a, b) => {
        // Folders first, then files
        if (a.type !== b.type) {
          return a.type === 'folder' ? -1 : 1
        }
        return a.name.localeCompare(b.name)
      })
    } catch (error) {
      console.error('Failed to list directory:', error)
      return []
    }
  }

  private async createFileItemFromHandle(
    handle: FileSystemHandle, 
    path: string
  ): Promise<FileItem | null> {
    try {
      if (handle.kind === 'directory') {
        return {
          path,
          name: handle.name,
          type: 'folder',
          createdAt: new Date(), // File System Access API doesn't provide creation time
          modifiedAt: new Date()
        }
      } else {
        const file = await (handle as FileSystemFileHandle).getFile()
        return {
          path,
          name: handle.name,
          type: 'file',
          size: file.size,
          mimeType: file.type,
          createdAt: new Date(file.lastModified),
          modifiedAt: new Date(file.lastModified)
        }
      }
    } catch (error) {
      console.error('Failed to create file item:', error)
      return null
    }
  }

  // Helper methods
  private async getDirectoryHandle(path: string): Promise<FileSystemDirectoryHandle | null> {
    if (!this.ziExplorerHandle) return null

    const pathParts = path.split('/').filter(part => part && part !== 'ZiExplorer')
    let currentHandle = this.ziExplorerHandle

    for (const part of pathParts) {
      try {
        currentHandle = await currentHandle.getDirectoryHandle(part)
      } catch {
        return null
      }
    }

    return currentHandle
  }

  private async getFileHandle(path: string): Promise<FileSystemFileHandle | null> {
    const parentPath = path.substring(0, path.lastIndexOf('/'))
    const fileName = path.substring(path.lastIndexOf('/') + 1)

    const parentHandle = await this.getDirectoryHandle(parentPath)
    if (!parentHandle) return null

    try {
      return await parentHandle.getFileHandle(fileName)
    } catch {
      return null
    }
  }

  // File validation
  validateFileName(name: string): ValidationResult {
    if (this.usingFallback && this.indexedDBFallback) {
      return this.indexedDBFallback.validateFileName(name)
    }

    // Check for invalid characters
    const invalidChars = /[<>:"/\\|?*]/
    if (invalidChars.test(name)) {
      return {
        isValid: false,
        error: 'File name contains invalid characters'
      }
    }

    // Check length
    if (name.length === 0) {
      return {
        isValid: false,
        error: 'File name cannot be empty'
      }
    }

    if (name.length > 255) {
      return {
        isValid: false,
        error: 'File name is too long'
      }
    }

    // Check for reserved names (Windows)
    const reservedNames = ['CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9']
    if (reservedNames.includes(name.toUpperCase())) {
      return {
        isValid: false,
        error: 'File name is reserved'
      }
    }

    return { isValid: true }
  }

  // Getters
  get isNativeFileSystemSupported(): boolean {
    return this.capabilities.nativeFileSystemSupported
  }

  get platformInfo(): Platform {
    return this.platform
  }

  get fileSystemCapabilities(): FileSystemCapabilities {
    return this.capabilities
  }
}
