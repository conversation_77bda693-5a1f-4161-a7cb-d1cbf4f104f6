// ZiExplorer Manager - Central file operations coordinator

import { 
  FileItem, 
  FolderItem, 
  FileOperation, 
  ClipboardItem, 
  ImportResult, 
  FileChange, 
  ValidationResult,
  ThumbnailOptions,
  ShareOptions,
  ZiExplorerConfig,
  DEFAULT_ZIEXPLORER_CONFIG
} from './types'
import { NativeFileSystemBridge } from './native-file-system-bridge'
import { ThumbnailGenerator } from './thumbnail-generator'

export class ZiExplorerManager {
  private fileSystemBridge: NativeFileSystemBridge
  private thumbnailGenerator: ThumbnailGenerator
  private config: ZiExplorerConfig
  private watchers: Map<string, (changes: FileChange[]) => void> = new Map()
  private clipboard: ClipboardItem | null = null

  constructor(config: Partial<ZiExplorerConfig> = {}) {
    this.config = { ...DEFAULT_ZIEXPLORER_CONFIG, ...config }
    this.fileSystemBridge = new NativeFileSystemBridge()
    this.thumbnailGenerator = new ThumbnailGenerator()
  }

  // Initialization
  async initialize(): Promise<void> {
    try {
      await this.fileSystemBridge.initialize()
      console.log('ZiExplorer initialized successfully')
    } catch (error) {
      console.error('Failed to initialize ZiExplorer:', error)
      throw error
    }
  }

  // File System Operations
  async createFolder(path: string, name: string): Promise<boolean> {
    const validation = this.validateFileName(name)
    if (!validation.isValid) {
      throw new Error(validation.error)
    }

    const success = await this.fileSystemBridge.createFolder(path, name)
    
    if (success) {
      this.notifyWatchers(path, [{
        type: 'added',
        path: `${path}/${name}`,
        item: {
          path: `${path}/${name}`,
          name,
          type: 'folder',
          createdAt: new Date(),
          modifiedAt: new Date()
        }
      }])
    }

    return success
  }

  async importFiles(files: FileList, targetPath: string): Promise<ImportResult[]> {
    // Validate files
    const validFiles: File[] = []
    const results: ImportResult[] = []

    for (const file of Array.from(files)) {
      if (file.size > this.config.maxFileSize) {
        results.push({
          success: false,
          path: `${targetPath}/${file.name}`,
          error: `File size exceeds maximum allowed size (${this.config.maxFileSize} bytes)`
        })
        continue
      }

      const validation = this.validateFileName(file.name)
      if (!validation.isValid) {
        results.push({
          success: false,
          path: `${targetPath}/${file.name}`,
          error: validation.error
        })
        continue
      }

      validFiles.push(file)
    }

    // Import valid files
    if (validFiles.length > 0) {
      const fileList = this.createFileList(validFiles)
      const importResults = await this.fileSystemBridge.importFiles(fileList, targetPath)
      results.push(...importResults)

      // Generate thumbnails for successful imports
      for (const result of importResults) {
        if (result.success) {
          this.generateThumbnailAsync(result.path)
        }
      }

      // Notify watchers
      const changes: FileChange[] = importResults
        .filter(r => r.success)
        .map(r => ({
          type: 'added' as const,
          path: r.path
        }))

      if (changes.length > 0) {
        this.notifyWatchers(targetPath, changes)
      }
    }

    return results
  }

  async copyFiles(sourcePaths: string[], targetPath: string): Promise<boolean> {
    try {
      const promises = sourcePaths.map(sourcePath => 
        this.fileSystemBridge.copyFile(sourcePath, targetPath)
      )
      
      const results = await Promise.all(promises)
      const success = results.every(result => result)

      if (success) {
        const changes: FileChange[] = sourcePaths.map(sourcePath => ({
          type: 'added',
          path: `${targetPath}/${sourcePath.substring(sourcePath.lastIndexOf('/') + 1)}`
        }))
        
        this.notifyWatchers(targetPath, changes)
      }

      return success
    } catch (error) {
      console.error('Failed to copy files:', error)
      return false
    }
  }

  async moveFiles(sourcePaths: string[], targetPath: string): Promise<boolean> {
    try {
      // First copy files
      const copySuccess = await this.copyFiles(sourcePaths, targetPath)
      if (!copySuccess) return false

      // Then delete originals
      const deleteSuccess = await this.deleteFiles(sourcePaths)
      return deleteSuccess
    } catch (error) {
      console.error('Failed to move files:', error)
      return false
    }
  }

  async deleteFiles(paths: string[]): Promise<boolean> {
    try {
      const promises = paths.map(path => this.fileSystemBridge.deleteFile(path))
      const results = await Promise.all(promises)
      const success = results.every(result => result)

      if (success) {
        // Notify watchers for each parent directory
        const parentPaths = new Set(paths.map(path => 
          path.substring(0, path.lastIndexOf('/'))
        ))

        for (const parentPath of parentPaths) {
          const changes: FileChange[] = paths
            .filter(path => path.startsWith(parentPath))
            .map(path => ({
              type: 'deleted',
              path
            }))
          
          this.notifyWatchers(parentPath, changes)
        }
      }

      return success
    } catch (error) {
      console.error('Failed to delete files:', error)
      return false
    }
  }

  async renameFile(oldPath: string, newName: string): Promise<boolean> {
    const validation = this.validateFileName(newName)
    if (!validation.isValid) {
      throw new Error(validation.error)
    }

    try {
      const parentPath = oldPath.substring(0, oldPath.lastIndexOf('/'))
      const newPath = `${parentPath}/${newName}`

      // Copy to new location
      const copySuccess = await this.fileSystemBridge.copyFile(oldPath, parentPath)
      if (!copySuccess) return false

      // Delete original
      const deleteSuccess = await this.fileSystemBridge.deleteFile(oldPath)
      if (!deleteSuccess) {
        // Cleanup: delete the copy if original deletion failed
        await this.fileSystemBridge.deleteFile(newPath)
        return false
      }

      // Notify watchers
      this.notifyWatchers(parentPath, [
        { type: 'deleted', path: oldPath },
        { type: 'added', path: newPath }
      ])

      return true
    } catch (error) {
      console.error('Failed to rename file:', error)
      return false
    }
  }

  // Directory listing
  async listDirectory(path: string): Promise<FileItem[]> {
    const items = await this.fileSystemBridge.listDirectory(path)
    
    // Generate thumbnails for image files
    for (const item of items) {
      if (item.type === 'file' && this.isImageFile(item.name)) {
        this.generateThumbnailAsync(item.path)
      }
    }

    return items
  }

  // Clipboard operations
  copyToClipboard(paths: string[]): void {
    this.clipboard = {
      operation: 'copy',
      paths: [...paths],
      timestamp: Date.now()
    }
  }

  cutToClipboard(paths: string[]): void {
    this.clipboard = {
      operation: 'cut',
      paths: [...paths],
      timestamp: Date.now()
    }
  }

  async pasteFromClipboard(targetPath: string): Promise<boolean> {
    if (!this.clipboard) return false

    const { operation, paths } = this.clipboard

    let success = false
    if (operation === 'copy') {
      success = await this.copyFiles(paths, targetPath)
    } else if (operation === 'cut') {
      success = await this.moveFiles(paths, targetPath)
      if (success) {
        this.clipboard = null // Clear clipboard after cut operation
      }
    }

    return success
  }

  getClipboard(): ClipboardItem | null {
    return this.clipboard
  }

  clearClipboard(): void {
    this.clipboard = null
  }

  // File sharing
  async shareFiles(paths: string[], options: ShareOptions = { method: 'native' }): Promise<boolean> {
    try {
      if (options.method === 'native' && navigator.share) {
        // Use Web Share API if available
        const files: File[] = []
        
        for (const path of paths) {
          const file = await this.fileSystemBridge.readFile(path)
          if (file) files.push(file)
        }

        if (files.length > 0) {
          await navigator.share({
            files,
            title: options.title || 'Shared from ZiExplorer',
            text: options.text || `Sharing ${files.length} file(s)`
          })
          return true
        }
      }

      // Fallback to other sharing methods
      console.log('Sharing files:', paths, 'with method:', options.method)
      return true
    } catch (error) {
      console.error('Failed to share files:', error)
      return false
    }
  }

  // File watching
  watchFolder(path: string, callback: (changes: FileChange[]) => void): void {
    this.watchers.set(path, callback)
  }

  unwatchFolder(path: string): void {
    this.watchers.delete(path)
  }

  private notifyWatchers(path: string, changes: FileChange[]): void {
    // Notify exact path watchers
    const callback = this.watchers.get(path)
    if (callback) {
      callback(changes)
    }

    // Notify parent path watchers
    for (const [watchedPath, watcherCallback] of this.watchers) {
      if (path.startsWith(watchedPath) && path !== watchedPath) {
        watcherCallback(changes)
      }
    }
  }

  // Thumbnail operations
  async generateThumbnail(filePath: string, options?: Partial<ThumbnailOptions>): Promise<string | null> {
    try {
      const file = await this.fileSystemBridge.readFile(filePath)
      if (!file) return null

      return await this.thumbnailGenerator.generateThumbnail(file, options)
    } catch (error) {
      console.error('Failed to generate thumbnail:', error)
      return null
    }
  }

  private async generateThumbnailAsync(filePath: string): Promise<void> {
    try {
      await this.generateThumbnail(filePath)
    } catch (error) {
      // Silently fail for thumbnail generation
      console.debug('Thumbnail generation failed for:', filePath)
    }
  }

  // Utility methods
  private validateFileName(name: string): ValidationResult {
    return this.fileSystemBridge.validateFileName(name)
  }

  private isImageFile(fileName: string): boolean {
    const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
    return this.config.allowedFileTypes.pictures.includes(extension)
  }

  private createFileList(files: File[]): FileList {
    const dataTransfer = new DataTransfer()
    files.forEach(file => dataTransfer.items.add(file))
    return dataTransfer.files
  }

  // Getters
  get isNativeFileSystemSupported(): boolean {
    return this.fileSystemBridge.isNativeFileSystemSupported
  }

  get platformInfo() {
    return this.fileSystemBridge.platformInfo
  }

  get capabilities() {
    return this.fileSystemBridge.fileSystemCapabilities
  }

  get configuration(): ZiExplorerConfig {
    return { ...this.config }
  }
}
