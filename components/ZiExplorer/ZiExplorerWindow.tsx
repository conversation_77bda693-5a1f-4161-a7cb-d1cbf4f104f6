'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useFloatingWindowStore } from '@/lib/stores/floating-window-store'
import { ImportDialog } from './ImportDialog'
import { ZIEXPLORER_SECTIONS } from '@/lib/ziexplorer/types'

interface ZiExplorerWindowProps {
  initialPath?: string
  windowId?: string
  isMobile?: boolean
  isLowEndDevice?: boolean
}

export function ZiExplorerWindow({
  initialPath = "/",
  windowId = "ziexplorer-main",
  isMobile = false,
  isLowEndDevice = false
}: ZiExplorerWindowProps) {
  const [showImportDialog, setShowImportDialog] = useState(false)
  const [importTarget, setImportTarget] = useState<'documents' | 'pictures' | 'music'>('documents')
  const [clickTimeout, setClickTimeout] = useState<NodeJS.Timeout | null>(null)

  // Handle folder clicks with single/double click detection
  const handleFolderClick = (sectionId: 'documents' | 'pictures' | 'music') => {
    if (clickTimeout) {
      // Double click - open folder in separate window
      clearTimeout(clickTimeout)
      setClickTimeout(null)
      openFolderWindow(sectionId)
    } else {
      // Single click - set timeout for double click detection
      const timeout = setTimeout(() => {
        // Single click - open import dialog
        setImportTarget(sectionId)
        setShowImportDialog(true)
        setClickTimeout(null)
      }, 300)
      setClickTimeout(timeout)
    }
  }

  // Open folder in new floating window
  const openFolderWindow = (sectionId: 'documents' | 'pictures' | 'music') => {
    const { openWindow } = useFloatingWindowStore.getState()
    const section = ZIEXPLORER_SECTIONS.find(s => s.id === sectionId)

    if (section) {
      const contentTypeMap: { [key: string]: string } = {
        'documents': 'ziexplorer-documents',
        'pictures': 'ziexplorer-pictures',
        'music': 'ziexplorer-music'
      }

      openWindow({
        id: `ziexplorer-${sectionId}-${Date.now()}`,
        title: `${section.name} - ZiExplorer`,
        content: contentTypeMap[sectionId],
        size: { width: 800, height: 600 },
        position: { x: 100 + Math.random() * 200, y: 100 + Math.random() * 200 },
        windowType: 'app'
      })
    }
  }

  // Handle import for section-specific imports
  const handleImport = async (files: FileList, targetPath: string) => {
    try {
      // Simple import - just close dialog for now
      setShowImportDialog(false)
      console.log(`Imported ${files.length} files to ${targetPath}`)
      return []
    } catch (error) {
      console.error('Import failed:', error)
      return []
    }
  }



  return (
    <>
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="h-full w-full flex flex-col"
        style={{
          background: `linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)`,
          backdropFilter: 'blur(20px) saturate(180%)',
          border: '1px solid rgba(255,255,255,0.2)',
          borderRadius: '16px',
          boxShadow: '0 8px 32px rgba(0,0,0,0.1), 0 1px 0 rgba(255,255,255,0.2) inset'
        }}
      >
        {/* Simple Header */}
        <div className="flex items-center justify-between p-4 border-b border-white/20">
          <h2 className="text-lg font-semibold text-white">ZiExplorer</h2>
          <div className="text-sm text-white/70">File Manager</div>
        </div>

        {/* Simple Folder Grid */}
        <div className="flex-1 p-6">
          <div className="grid grid-cols-3 gap-6 max-w-md mx-auto">
            {ZIEXPLORER_SECTIONS.map((section) => (
              <motion.div
                key={section.id}
                className="flex flex-col items-center p-4 rounded-xl cursor-pointer transition-all duration-200"
                style={{
                  background: 'rgba(255,255,255,0.1)',
                  backdropFilter: 'blur(10px)',
                  border: '1px solid rgba(255,255,255,0.2)'
                }}
                whileHover={{
                  scale: 1.05,
                  background: 'rgba(255,255,255,0.15)'
                }}
                whileTap={{ scale: 0.95 }}
                onClick={() => handleFolderClick(section.id)}
              >
                <div className="text-4xl mb-2">{section.icon}</div>
                <div className="text-white text-sm font-medium text-center">
                  {section.name}
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Import Dialog for Section Imports */}
        <ImportDialog
          isOpen={showImportDialog}
          targetPath={`/${importTarget.charAt(0).toUpperCase() + importTarget.slice(1)}`}
          targetSection={importTarget}
          onClose={() => setShowImportDialog(false)}
          onImport={handleImport}
          isMobile={isMobile}
          isLowEndDevice={isLowEndDevice}
        />

      </motion.div>
    </>
  )
}


