'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useFileSystem, useFileSelection, useContextMenu } from '@/hooks/useZiExplorer'
import { ZiExplorerHeader } from './ZiExplorerHeader'
import { ZiExplorerSidebar } from './ZiExplorerSidebar'
import { ZiExplorerContent } from './ZiExplorerContent'
import { ZiExplorerStatusBar } from './ZiExplorerStatusBar'
import { ZiExplorerContextMenu } from './ZiExplorerContextMenu'
import { ImportDialog } from './ImportDialog'
import { ZIEXPLORER_SECTIONS } from '@/lib/ziexplorer/types'

interface ZiExplorerWindowProps {
  initialPath?: string
  windowId: string
  onClose?: () => void
  onMinimize?: () => void
  onMaximize?: () => void
  isMobile?: boolean
  isLowEndDevice?: boolean
}

export function ZiExplorerWindow({
  initialPath = '/Documents',
  windowId,
  onClose,
  onMinimize,
  onMaximize,
  isMobile = false,
  isLowEndDevice = false
}: ZiExplorerWindowProps) {
  const [currentPath, setCurrentPath] = useState(initialPath)
  const [viewMode, setViewMode] = useState<'grid' | 'list' | 'details'>('grid')
  const [searchQuery, setSearchQuery] = useState('')
  const [showImportDialog, setShowImportDialog] = useState(false)
  const [importTarget, setImportTarget] = useState<'documents' | 'pictures' | 'music'>('documents')
  const [sidebarCollapsed, setSidebarCollapsed] = useState(isMobile)

  const { files, isLoading, error, reload } = useFileSystem(currentPath)
  const { 
    selectedFiles, 
    selectFile, 
    selectAll, 
    clearSelection, 
    isSelected, 
    hasSelection, 
    selectionCount 
  } = useFileSelection()
  const { 
    contextMenu, 
    showContextMenu, 
    hideContextMenu, 
    getContextMenuActions 
  } = useContextMenu()

  // Filter files based on search query
  const filteredFiles = files.filter(file => 
    file.name.toLowerCase().includes(searchQuery.toLowerCase())
  )

  // Handle navigation
  const handleNavigate = (path: string) => {
    setCurrentPath(path)
    clearSelection()
  }

  // Handle file/folder opening
  const handleFileOpen = (filePath: string) => {
    // TODO: Implement file opening logic
    console.log('Opening file:', filePath)
  }

  const handleFolderOpen = (folderPath: string) => {
    handleNavigate(folderPath)
  }

  // Handle section clicks (Documents, Pictures, Music)
  const handleSectionClick = (sectionId: 'documents' | 'pictures' | 'music', isDoubleClick: boolean) => {
    if (isDoubleClick) {
      // Double click - open section window
      const section = ZIEXPLORER_SECTIONS.find(s => s.id === sectionId)
      if (section) {
        handleNavigate(section.path)
      }
    } else {
      // Single click - open import dialog
      setImportTarget(sectionId)
      setShowImportDialog(true)
    }
  }

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 'a':
            event.preventDefault()
            selectAll(filteredFiles)
            break
          case 'f':
            event.preventDefault()
            // Focus search input
            const searchInput = document.querySelector('[data-search-input]') as HTMLInputElement
            searchInput?.focus()
            break
        }
      } else {
        switch (event.key) {
          case 'Escape':
            clearSelection()
            hideContextMenu()
            break
          case 'Delete':
            if (hasSelection) {
              // TODO: Implement delete confirmation
              console.log('Delete selected files:', selectedFiles)
            }
            break
          case 'F2':
            if (selectionCount === 1) {
              // TODO: Implement rename
              console.log('Rename file:', selectedFiles[0])
            }
            break
        }
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [filteredFiles, hasSelection, selectedFiles, selectionCount, selectAll, clearSelection, hideContextMenu])

  // Animation variants
  const windowVariants = {
    hidden: {
      opacity: 0,
      scale: 0.9,
      y: 20
    },
    visible: {
      opacity: 1,
      scale: 1,
      y: 0,
      transition: {
        duration: isLowEndDevice ? 0.2 : 0.3,
        ease: [0.4, 0, 0.2, 1]
      }
    },
    exit: {
      opacity: 0,
      scale: 0.9,
      y: -20,
      transition: {
        duration: isLowEndDevice ? 0.1 : 0.2,
        ease: [0.4, 0, 0.2, 1]
      }
    }
  }

  return (
    <>
      <motion.div
        variants={windowVariants}
        initial="hidden"
        animate="visible"
        exit="exit"
        className={`zi-explorer-window h-full flex flex-col ${isMobile ? 'mobile-explorer' : 'desktop-explorer'}`}
        style={{
          background: `
            linear-gradient(135deg, 
              rgba(255,255,255,0.1) 0%, 
              rgba(255,255,255,0.05) 100%
            )
          `,
          backdropFilter: 'blur(20px) saturate(180%)',
          border: '1px solid rgba(255,255,255,0.2)',
          borderRadius: '16px',
          boxShadow: `
            0 8px 32px rgba(0,0,0,0.1),
            0 1px 0 rgba(255,255,255,0.2) inset,
            0 -1px 0 rgba(0,0,0,0.1) inset
          `
        }}
      >
        {/* Window Header */}
        <ZiExplorerHeader
          currentPath={currentPath}
          onNavigate={handleNavigate}
          onSearch={setSearchQuery}
          searchQuery={searchQuery}
          onViewModeChange={setViewMode}
          viewMode={viewMode}
          onClose={onClose}
          onMinimize={onMinimize}
          onMaximize={onMaximize}
          onToggleSidebar={() => setSidebarCollapsed(!sidebarCollapsed)}
          sidebarCollapsed={sidebarCollapsed}
          isMobile={isMobile}
          isLowEndDevice={isLowEndDevice}
        />

        {/* Main Content */}
        <div className="flex-1 flex overflow-hidden">
          {/* Sidebar */}
          <AnimatePresence>
            {!sidebarCollapsed && (
              <motion.div
                initial={{ width: 0, opacity: 0 }}
                animate={{ width: isMobile ? 200 : 240, opacity: 1 }}
                exit={{ width: 0, opacity: 0 }}
                transition={{ 
                  duration: isLowEndDevice ? 0.2 : 0.3, 
                  ease: [0.4, 0, 0.2, 1] 
                }}
                className="overflow-hidden"
              >
                <ZiExplorerSidebar
                  currentPath={currentPath}
                  onNavigate={handleNavigate}
                  onSectionClick={handleSectionClick}
                  isMobile={isMobile}
                  isLowEndDevice={isLowEndDevice}
                />
              </motion.div>
            )}
          </AnimatePresence>

          {/* File Content Area */}
          <div className="flex-1 flex flex-col overflow-hidden">
            <ZiExplorerContent
              files={filteredFiles}
              viewMode={viewMode}
              selectedFiles={selectedFiles}
              currentPath={currentPath}
              onSelectionChange={selectFile}
              onFileOpen={handleFileOpen}
              onFolderOpen={handleFolderOpen}
              onContextMenu={showContextMenu}
              onRefresh={loadFiles}
              isLoading={isLoading}
              error={error}
              isMobile={isMobile}
              isLowEndDevice={isLowEndDevice}
            />

            {/* Status Bar */}
            <ZiExplorerStatusBar
              folders={filteredFiles.filter(f => f.type === 'folder')}
              files={filteredFiles.filter(f => f.type === 'file')}
              selectedFiles={selectedFiles}
              currentPath={currentPath}
              isLoading={isLoading}
              isMobile={isMobile}
              isLowEndDevice={isLowEndDevice}
            />
          </div>
        </div>

      </motion.div>
    </>
  )
}

// CSS styles for the component
const styles = `
.zi-explorer-window {
  min-width: 320px;
  min-height: 240px;
  max-width: 100vw;
  max-height: 100vh;
}

.mobile-explorer {
  border-radius: 0;
  border: none;
}

.desktop-explorer {
  border-radius: 16px;
}

/* Scrollbar styling */
.zi-explorer-window ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.zi-explorer-window ::-webkit-scrollbar-track {
  background: rgba(255,255,255,0.1);
  border-radius: 4px;
}

.zi-explorer-window ::-webkit-scrollbar-thumb {
  background: rgba(255,255,255,0.3);
  border-radius: 4px;
}

.zi-explorer-window ::-webkit-scrollbar-thumb:hover {
  background: rgba(255,255,255,0.5);
}

/* Focus styles */
.zi-explorer-window *:focus-visible {
  outline: 2px solid rgba(59, 130, 246, 0.8);
  outline-offset: 2px;
  border-radius: 4px;
}

/* Selection styles */
.zi-file-item.selected {
  background: linear-gradient(135deg, 
    rgba(59, 130, 246, 0.2) 0%, 
    rgba(99, 102, 241, 0.15) 100%
  );
  border-color: rgba(59, 130, 246, 0.4);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

/* Loading animation */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.zi-loading {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
`

// Inject styles
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style')
  styleElement.textContent = styles
  document.head.appendChild(styleElement)
}
