'use client'

import React, { useState, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useFloatingWindowStore } from '@/lib/stores/floating-window-store'
import { useZiExplorer } from '@/hooks/useZiExplorer'
import { ZiExplorerHeader } from './ZiExplorerHeader'
import { ZiExplorerSidebar } from './ZiExplorerSidebar'
import { ZiExplorerContent } from './ZiExplorerContent'
import { ZiExplorerStatusBar } from './ZiExplorerStatusBar'
import { ImportDialog } from './ImportDialog'
import { ZIEXPLORER_SECTIONS } from '@/lib/ziexplorer/types'

interface ZiExplorerWindowProps {
  initialPath?: string
  windowId?: string
  isMobile?: boolean
  isLowEndDevice?: boolean
}

interface ZiExplorerWindowState {
  currentPath: string
  selectedFiles: string[]
  viewMode: 'grid' | 'list' | 'details'
  sortBy: 'name' | 'date' | 'size' | 'type'
  sortOrder: 'asc' | 'desc'
  searchQuery: string
  isLoading: boolean
  showImportDialog: boolean
  importTarget: 'documents' | 'pictures' | 'music'
}

export function ZiExplorerWindow({
  initialPath = "/Documents",
  windowId = "ziexplorer-main",
  isMobile = false,
  isLowEndDevice = false
}: ZiExplorerWindowProps) {
  const [state, setState] = useState<ZiExplorerWindowState>({
    currentPath: initialPath,
    selectedFiles: [],
    viewMode: 'grid',
    sortBy: 'name',
    sortOrder: 'asc',
    searchQuery: '',
    isLoading: false,
    showImportDialog: false,
    importTarget: 'documents'
  })

  const { manager, isInitialized } = useZiExplorer()

  // Navigation handlers
  const handleNavigate = useCallback((path: string) => {
    setState(prev => ({ ...prev, currentPath: path, selectedFiles: [] }))
  }, [])

  const handleSearch = useCallback((query: string) => {
    setState(prev => ({ ...prev, searchQuery: query }))
  }, [])

  const handleViewModeChange = useCallback((mode: 'grid' | 'list' | 'details') => {
    setState(prev => ({ ...prev, viewMode: mode }))
  }, [])

  const handleSelectionChange = useCallback((files: string[]) => {
    setState(prev => ({ ...prev, selectedFiles: files }))
  }, [])

  const handleSortChange = useCallback((sortBy: 'name' | 'date' | 'size' | 'type', sortOrder: 'asc' | 'desc') => {
    setState(prev => ({ ...prev, sortBy, sortOrder }))
  }, [])

  // File operations
  const handleFileOpen = useCallback(async (filePath: string) => {
    if (!manager) return

    try {
      // TODO: Implement file opening logic
      console.log('Opening file:', filePath)
    } catch (error) {
      console.error('Failed to open file:', error)
    }
  }, [manager])

  const handleFolderOpen = useCallback((folderPath: string) => {
    handleNavigate(folderPath)
  }, [handleNavigate])

  // Import operations
  const handleImportRequest = useCallback((targetSection: 'documents' | 'pictures' | 'music') => {
    setState(prev => ({
      ...prev,
      showImportDialog: true,
      importTarget: targetSection
    }))
  }, [])

  const handleImport = useCallback(async (files: FileList, targetPath: string) => {
    if (!manager) return

    try {
      setState(prev => ({ ...prev, isLoading: true }))

      // TODO: Implement actual file import using manager
      console.log(`Importing ${files.length} files to ${targetPath}`)

      setState(prev => ({
        ...prev,
        showImportDialog: false,
        isLoading: false
      }))

      return []
    } catch (error) {
      console.error('Failed to import files:', error)
      setState(prev => ({ ...prev, isLoading: false }))
      return []
    }
  }, [manager])



  if (!isInitialized) {
    return (
      <div className="h-full w-full flex items-center justify-center">
        <div className="text-white/70">Initializing ZiExplorer...</div>
      </div>
    )
  }

  return (
    <div className="zi-explorer-window h-full flex flex-col">
      {/* Window Header */}
      <ZiExplorerHeader
        currentPath={state.currentPath}
        onNavigate={handleNavigate}
        onSearch={handleSearch}
        onViewModeChange={handleViewModeChange}
        viewMode={state.viewMode}
        searchQuery={state.searchQuery}
        onImportRequest={handleImportRequest}
        isLoading={state.isLoading}
      />

      {/* Main Content */}
      <div className="flex-1 flex min-h-0">
        {/* Sidebar */}
        <ZiExplorerSidebar
          currentPath={state.currentPath}
          onNavigate={handleNavigate}
          isMobile={isMobile}
        />

        {/* File Area */}
        <ZiExplorerContent
          currentPath={state.currentPath}
          viewMode={state.viewMode}
          sortBy={state.sortBy}
          sortOrder={state.sortOrder}
          searchQuery={state.searchQuery}
          selectedFiles={state.selectedFiles}
          onSelectionChange={handleSelectionChange}
          onFileOpen={handleFileOpen}
          onFolderOpen={handleFolderOpen}
          onSortChange={handleSortChange}
          onImportRequest={handleImportRequest}
          isLoading={state.isLoading}
          manager={manager}
          isMobile={isMobile}
          isLowEndDevice={isLowEndDevice}
        />
      </div>

      {/* Status Bar */}
      <ZiExplorerStatusBar
        currentPath={state.currentPath}
        selectedCount={state.selectedFiles.length}
        isLoading={state.isLoading}
      />

      {/* Import Dialog */}
      <ImportDialog
        isOpen={state.showImportDialog}
        targetPath={`/${state.importTarget.charAt(0).toUpperCase() + state.importTarget.slice(1)}`}
        targetSection={state.importTarget}
        onClose={() => setState(prev => ({ ...prev, showImportDialog: false }))}
        onImport={handleImport}
        isMobile={isMobile}
        isLowEndDevice={isLowEndDevice}
      />
    </div>
  )
}


