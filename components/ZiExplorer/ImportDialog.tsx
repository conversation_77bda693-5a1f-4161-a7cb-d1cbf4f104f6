'use client'

import React, { useState, useRef, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ImportResult } from '@/lib/ziexplorer/types'

interface ImportDialogProps {
  isOpen: boolean
  targetPath: string
  onClose: () => void
  onImport: (files: FileList, targetPath: string) => Promise<ImportResult[]>
  isMobile?: boolean
  isLowEndDevice?: boolean
}

interface FilePreviewProps {
  file: File
  onRemove: () => void
  isMobile?: boolean
}

function FilePreview({ file, onRemove, isMobile = false }: FilePreviewProps) {
  const [preview, setPreview] = useState<string | null>(null)

  React.useEffect(() => {
    if (file.type.startsWith('image/')) {
      const reader = new FileReader()
      reader.onload = (e) => setPreview(e.target?.result as string)
      reader.readAsDataURL(file)
    }
  }, [file])

  const formatFileSize = (bytes: number): string => {
    const units = ['B', 'KB', 'MB', 'GB']
    let size = bytes
    let unitIndex = 0
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024
      unitIndex++
    }
    
    return `${size.toFixed(unitIndex === 0 ? 0 : 1)} ${units[unitIndex]}`
  }

  const getFileIcon = (file: File): string => {
    if (file.type.startsWith('image/')) return '🖼️'
    if (file.type.startsWith('video/')) return '🎬'
    if (file.type.startsWith('audio/')) return '🎵'
    if (file.type.includes('pdf')) return '📄'
    if (file.type.includes('text')) return '📝'
    return '📄'
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className="file-preview"
      style={{
        display: 'flex',
        alignItems: 'center',
        gap: '12px',
        padding: '12px',
        background: 'rgba(255, 255, 255, 0.05)',
        borderRadius: '8px',
        border: '1px solid rgba(255, 255, 255, 0.1)'
      }}
    >
      {/* File Icon/Preview */}
      <div
        style={{
          width: isMobile ? '40px' : '48px',
          height: isMobile ? '40px' : '48px',
          borderRadius: '6px',
          overflow: 'hidden',
          background: 'rgba(255, 255, 255, 0.1)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexShrink: 0
        }}
      >
        {preview ? (
          <img
            src={preview}
            alt={file.name}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover'
            }}
          />
        ) : (
          <span style={{ fontSize: isMobile ? '20px' : '24px' }}>
            {getFileIcon(file)}
          </span>
        )}
      </div>

      {/* File Info */}
      <div style={{ flex: 1, minWidth: 0 }}>
        <div
          style={{
            fontSize: isMobile ? '14px' : '13px',
            fontWeight: 500,
            color: 'white',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            marginBottom: '2px'
          }}
          title={file.name}
        >
          {file.name}
        </div>
        <div
          style={{
            fontSize: '11px',
            color: 'rgba(255, 255, 255, 0.6)'
          }}
        >
          {formatFileSize(file.size)} • {file.type || 'Unknown type'}
        </div>
      </div>

      {/* Remove Button */}
      <button
        onClick={onRemove}
        style={{
          width: '24px',
          height: '24px',
          borderRadius: '50%',
          background: 'rgba(239, 68, 68, 0.2)',
          border: '1px solid rgba(239, 68, 68, 0.3)',
          color: '#ef4444',
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '12px',
          flexShrink: 0,
          transition: 'all 0.2s ease'
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.background = 'rgba(239, 68, 68, 0.3)'
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.background = 'rgba(239, 68, 68, 0.2)'
        }}
      >
        ×
      </button>
    </motion.div>
  )
}

export function ImportDialog({
  isOpen,
  targetPath,
  onClose,
  onImport,
  isMobile = false,
  isLowEndDevice = false
}: ImportDialogProps) {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])
  const [isImporting, setIsImporting] = useState(false)
  const [importResults, setImportResults] = useState<ImportResult[]>([])
  const [isDragOver, setIsDragOver] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Reset state when dialog opens/closes
  React.useEffect(() => {
    if (isOpen) {
      setSelectedFiles([])
      setImportResults([])
      setIsImporting(false)
    }
  }, [isOpen])

  const handleFileSelect = useCallback((files: FileList) => {
    const newFiles = Array.from(files)
    setSelectedFiles(prev => [...prev, ...newFiles])
  }, [])

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
    
    if (e.dataTransfer.files) {
      handleFileSelect(e.dataTransfer.files)
    }
  }, [handleFileSelect])

  const handleImport = async () => {
    if (selectedFiles.length === 0) return

    setIsImporting(true)
    try {
      const fileList = new DataTransfer()
      selectedFiles.forEach(file => fileList.items.add(file))
      
      const results = await onImport(fileList.files, targetPath)
      setImportResults(results)
      
      // Clear selected files if all imports were successful
      if (results.every(result => result.success)) {
        setSelectedFiles([])
      }
    } catch (error) {
      console.error('Import failed:', error)
    } finally {
      setIsImporting(false)
    }
  }

  const removeFile = (index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index))
  }

  const dialogVariants = {
    hidden: { 
      opacity: 0, 
      scale: 0.9,
      y: 20,
      transition: {
        duration: isLowEndDevice ? 0.15 : 0.2,
        ease: [0.4, 0, 0.2, 1]
      }
    },
    visible: { 
      opacity: 1, 
      scale: 1,
      y: 0,
      transition: {
        duration: isLowEndDevice ? 0.2 : 0.3,
        ease: [0.4, 0, 0.2, 1]
      }
    }
  }

  const getTargetFolderName = () => {
    const parts = targetPath.split('/')
    return parts[parts.length - 1] || 'ZiExplorer'
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
            style={{
              position: 'fixed',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: 'rgba(0, 0, 0, 0.5)',
              backdropFilter: 'blur(4px)',
              zIndex: 9998,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              padding: isMobile ? '20px' : '40px'
            }}
          >
            {/* Dialog */}
            <motion.div
              variants={dialogVariants}
              initial="hidden"
              animate="visible"
              exit="hidden"
              onClick={(e) => e.stopPropagation()}
              style={{
                width: '100%',
                maxWidth: isMobile ? '100%' : '500px',
                maxHeight: '80vh',
                background: 'linear-gradient(135deg, rgba(30, 30, 30, 0.95) 0%, rgba(20, 20, 20, 0.95) 100%)',
                backdropFilter: 'blur(20px)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                borderRadius: '16px',
                overflow: 'hidden',
                display: 'flex',
                flexDirection: 'column'
              }}
            >
              {/* Header */}
              <div
                style={{
                  padding: '20px 24px',
                  borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between'
                }}
              >
                <div>
                  <h2 style={{
                    margin: 0,
                    fontSize: isMobile ? '18px' : '20px',
                    fontWeight: 600,
                    color: 'white'
                  }}>
                    Import Files
                  </h2>
                  <p style={{
                    margin: '4px 0 0 0',
                    fontSize: '13px',
                    color: 'rgba(255, 255, 255, 0.6)'
                  }}>
                    Import files to {getTargetFolderName()}
                  </p>
                </div>
                
                <button
                  onClick={onClose}
                  style={{
                    width: '32px',
                    height: '32px',
                    borderRadius: '50%',
                    background: 'rgba(255, 255, 255, 0.1)',
                    border: 'none',
                    color: 'white',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '16px',
                    transition: 'all 0.2s ease'
                  }}
                >
                  ×
                </button>
              </div>

              {/* Content */}
              <div style={{ flex: 1, overflow: 'hidden', display: 'flex', flexDirection: 'column' }}>
                {/* Drop Zone */}
                <div
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                  onDrop={handleDrop}
                  onClick={() => fileInputRef.current?.click()}
                  style={{
                    margin: '20px 24px',
                    padding: '40px 20px',
                    border: `2px dashed ${isDragOver ? 'rgba(59, 130, 246, 0.6)' : 'rgba(255, 255, 255, 0.3)'}`,
                    borderRadius: '12px',
                    background: isDragOver ? 'rgba(59, 130, 246, 0.1)' : 'rgba(255, 255, 255, 0.05)',
                    cursor: 'pointer',
                    textAlign: 'center',
                    transition: 'all 0.2s ease'
                  }}
                >
                  <div style={{ fontSize: '48px', marginBottom: '12px' }}>📁</div>
                  <div style={{ 
                    fontSize: isMobile ? '14px' : '16px', 
                    fontWeight: 500, 
                    color: 'white',
                    marginBottom: '8px'
                  }}>
                    {isDragOver ? 'Drop files here' : 'Drag files here or click to browse'}
                  </div>
                  <div style={{ 
                    fontSize: '12px', 
                    color: 'rgba(255, 255, 255, 0.6)' 
                  }}>
                    Supports images, documents, audio, and video files
                  </div>
                </div>

                {/* Selected Files */}
                {selectedFiles.length > 0 && (
                  <div style={{ 
                    flex: 1, 
                    overflow: 'auto', 
                    padding: '0 24px',
                    marginBottom: '20px'
                  }}>
                    <h3 style={{
                      margin: '0 0 12px 0',
                      fontSize: '14px',
                      fontWeight: 600,
                      color: 'rgba(255, 255, 255, 0.8)'
                    }}>
                      Selected Files ({selectedFiles.length})
                    </h3>
                    
                    <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                      <AnimatePresence>
                        {selectedFiles.map((file, index) => (
                          <FilePreview
                            key={`${file.name}-${index}`}
                            file={file}
                            onRemove={() => removeFile(index)}
                            isMobile={isMobile}
                          />
                        ))}
                      </AnimatePresence>
                    </div>
                  </div>
                )}

                {/* Import Results */}
                {importResults.length > 0 && (
                  <div style={{ 
                    padding: '0 24px 20px',
                    maxHeight: '150px',
                    overflow: 'auto'
                  }}>
                    <h3 style={{
                      margin: '0 0 12px 0',
                      fontSize: '14px',
                      fontWeight: 600,
                      color: 'rgba(255, 255, 255, 0.8)'
                    }}>
                      Import Results
                    </h3>
                    
                    {importResults.map((result, index) => (
                      <div
                        key={index}
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '8px',
                          padding: '6px 0',
                          fontSize: '12px',
                          color: result.success ? '#10b981' : '#ef4444'
                        }}
                      >
                        <span>{result.success ? '✓' : '✗'}</span>
                        <span style={{ flex: 1, overflow: 'hidden', textOverflow: 'ellipsis' }}>
                          {result.path.split('/').pop()}
                        </span>
                        {result.error && (
                          <span style={{ color: 'rgba(255, 255, 255, 0.5)' }}>
                            {result.error}
                          </span>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Footer */}
              <div
                style={{
                  padding: '16px 24px',
                  borderTop: '1px solid rgba(255, 255, 255, 0.1)',
                  display: 'flex',
                  justifyContent: 'flex-end',
                  gap: '12px'
                }}
              >
                <button
                  onClick={onClose}
                  style={{
                    padding: '8px 16px',
                    borderRadius: '8px',
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                    background: 'transparent',
                    color: 'white',
                    cursor: 'pointer',
                    fontSize: '13px',
                    fontWeight: 500,
                    transition: 'all 0.2s ease'
                  }}
                >
                  Cancel
                </button>
                
                <button
                  onClick={handleImport}
                  disabled={selectedFiles.length === 0 || isImporting}
                  style={{
                    padding: '8px 16px',
                    borderRadius: '8px',
                    border: 'none',
                    background: selectedFiles.length === 0 || isImporting
                      ? 'rgba(255, 255, 255, 0.1)'
                      : 'linear-gradient(135deg, #3b82f6 0%, #6366f1 100%)',
                    color: selectedFiles.length === 0 || isImporting ? 'rgba(255, 255, 255, 0.5)' : 'white',
                    cursor: selectedFiles.length === 0 || isImporting ? 'not-allowed' : 'pointer',
                    fontSize: '13px',
                    fontWeight: 500,
                    transition: 'all 0.2s ease',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '6px'
                  }}
                >
                  {isImporting && (
                    <div
                      style={{
                        width: '12px',
                        height: '12px',
                        border: '2px solid rgba(255, 255, 255, 0.3)',
                        borderTop: '2px solid white',
                        borderRadius: '50%',
                        animation: 'spin 1s linear infinite'
                      }}
                    />
                  )}
                  {isImporting ? 'Importing...' : `Import ${selectedFiles.length} file${selectedFiles.length !== 1 ? 's' : ''}`}
                </button>
              </div>
            </motion.div>
          </motion.div>

          {/* Hidden File Input */}
          <input
            ref={fileInputRef}
            type="file"
            multiple
            onChange={(e) => e.target.files && handleFileSelect(e.target.files)}
            style={{ display: 'none' }}
          />
        </>
      )}
    </AnimatePresence>
  )
}
