'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { FileItem } from '@/lib/ziexplorer/types'

interface StatusBarProps {
  currentPath: string
  selectedCount: number
  isLoading?: boolean
  totalFiles?: number
  totalFolders?: number
  folders?: FileItem[]
  files?: FileItem[]
  selectedFiles?: string[]
  isMobile?: boolean
  isLowEndDevice?: boolean
}

interface StorageInfo {
  used: number
  total: number
  available: number
}

export function ZiExplorerStatusBar({
  currentPath,
  selectedCount,
  isLoading = false,
  totalFiles = 0,
  totalFolders = 0,
  folders = [],
  files = [],
  selectedFiles = [],
  isMobile = false,
  isLowEndDevice = false
}: StatusBarProps) {
  const [storageInfo, setStorageInfo] = useState<StorageInfo | null>(null)

  // Get storage information
  useEffect(() => {
    const getStorageInfo = async () => {
      try {
        if ('storage' in navigator && 'estimate' in navigator.storage) {
          const estimate = await navigator.storage.estimate()
          setStorageInfo({
            used: estimate.usage || 0,
            total: estimate.quota || 0,
            available: (estimate.quota || 0) - (estimate.usage || 0)
          })
        }
      } catch (error) {
        console.debug('Storage API not available')
      }
    }

    getStorageInfo()
  }, [])

  // Calculate selection info
  const getSelectionInfo = () => {
    if (selectedFiles.length === 0) {
      return {
        count: folders.length + files.length,
        size: files.reduce((total, file) => total + (file.size || 0), 0),
        type: 'total'
      }
    }

    const selectedItems = [...folders, ...files].filter(item => 
      selectedFiles.includes(item.path)
    )

    return {
      count: selectedItems.length,
      size: selectedItems
        .filter(item => item.type === 'file')
        .reduce((total, file) => total + (file.size || 0), 0),
      type: 'selected'
    }
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B'
    
    const units = ['B', 'KB', 'MB', 'GB', 'TB']
    let size = bytes
    let unitIndex = 0
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024
      unitIndex++
    }
    
    return `${size.toFixed(unitIndex === 0 ? 0 : 1)} ${units[unitIndex]}`
  }

  const formatStoragePercentage = (used: number, total: number): number => {
    if (total === 0) return 0
    return Math.round((used / total) * 100)
  }

  const selectionInfo = getSelectionInfo()

  const statusBarVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: isLowEndDevice ? 0.2 : 0.3,
        ease: [0.4, 0, 0.2, 1]
      }
    }
  }

  const loadingVariants = {
    animate: {
      opacity: [0.5, 1, 0.5],
      transition: {
        duration: 1.5,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  }

  return (
    <motion.div
      variants={statusBarVariants}
      initial="hidden"
      animate="visible"
      className="ziexplorer-status-bar"
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: isMobile ? '8px 16px' : '6px 16px',
        background: 'linear-gradient(135deg, rgba(30, 30, 30, 0.8) 0%, rgba(20, 20, 20, 0.8) 100%)',
        backdropFilter: 'blur(10px)',
        borderTop: '1px solid rgba(255, 255, 255, 0.1)',
        fontSize: isMobile ? '12px' : '11px',
        color: 'rgba(255, 255, 255, 0.7)',
        fontWeight: 500,
        minHeight: isMobile ? '44px' : '32px',
        gap: '16px',
        flexWrap: isMobile ? 'wrap' : 'nowrap'
      }}
    >
      {/* Left Section - Item Count and Selection */}
      <div 
        className="status-left"
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: '16px',
          flex: 1,
          minWidth: 0
        }}
      >
        {/* Loading Indicator */}
        {isLoading && (
          <motion.div
            variants={loadingVariants}
            animate="animate"
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '6px',
              color: 'rgba(59, 130, 246, 0.8)'
            }}
          >
            <div
              style={{
                width: '12px',
                height: '12px',
                border: '2px solid rgba(59, 130, 246, 0.3)',
                borderTop: '2px solid rgba(59, 130, 246, 0.8)',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite'
              }}
            />
            <span>Loading...</span>
          </motion.div>
        )}

        {/* Item Count */}
        {!isLoading && (
          <div className="item-count">
            {selectionInfo.type === 'selected' ? (
              <span style={{ color: 'rgba(59, 130, 246, 0.9)' }}>
                {selectionInfo.count} selected
              </span>
            ) : (
              <span>
                {folders.length > 0 && `${folders.length} folder${folders.length !== 1 ? 's' : ''}`}
                {folders.length > 0 && files.length > 0 && ', '}
                {files.length > 0 && `${files.length} file${files.length !== 1 ? 's' : ''}`}
                {folders.length === 0 && files.length === 0 && 'Empty folder'}
              </span>
            )}
          </div>
        )}

        {/* Selection Size */}
        {!isLoading && selectionInfo.size > 0 && (
          <div className="selection-size">
            <span style={{ color: 'rgba(255, 255, 255, 0.5)' }}>•</span>
            <span>{formatFileSize(selectionInfo.size)}</span>
          </div>
        )}

        {/* Current Path (Mobile only, truncated) */}
        {isMobile && !isLoading && (
          <div 
            className="current-path"
            style={{
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              maxWidth: '150px',
              color: 'rgba(255, 255, 255, 0.5)'
            }}
            title={currentPath}
          >
            {currentPath}
          </div>
        )}
      </div>

      {/* Right Section - Storage Info */}
      {!isMobile && storageInfo && (
        <div 
          className="status-right"
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            flexShrink: 0
          }}
        >
          {/* Storage Usage */}
          <div 
            className="storage-info"
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}
          >
            <span style={{ color: 'rgba(255, 255, 255, 0.5)' }}>Storage:</span>
            <span>{formatFileSize(storageInfo.used)}</span>
            <span style={{ color: 'rgba(255, 255, 255, 0.5)' }}>of</span>
            <span>{formatFileSize(storageInfo.total)}</span>
          </div>

          {/* Storage Bar */}
          <div
            className="storage-bar"
            style={{
              width: '60px',
              height: '4px',
              background: 'rgba(255, 255, 255, 0.1)',
              borderRadius: '2px',
              overflow: 'hidden'
            }}
          >
            <motion.div
              initial={{ width: 0 }}
              animate={{ 
                width: `${formatStoragePercentage(storageInfo.used, storageInfo.total)}%` 
              }}
              transition={{ duration: 0.5, ease: [0.4, 0, 0.2, 1] }}
              style={{
                height: '100%',
                background: storageInfo.used / storageInfo.total > 0.9
                  ? 'linear-gradient(90deg, #ef4444 0%, #dc2626 100%)'
                  : storageInfo.used / storageInfo.total > 0.7
                    ? 'linear-gradient(90deg, #f59e0b 0%, #d97706 100%)'
                    : 'linear-gradient(90deg, #10b981 0%, #059669 100%)',
                borderRadius: '2px'
              }}
            />
          </div>

          {/* Storage Percentage */}
          <span style={{ 
            minWidth: '35px',
            textAlign: 'right',
            color: storageInfo.used / storageInfo.total > 0.9
              ? '#ef4444'
              : storageInfo.used / storageInfo.total > 0.7
                ? '#f59e0b'
                : 'rgba(255, 255, 255, 0.7)'
          }}>
            {formatStoragePercentage(storageInfo.used, storageInfo.total)}%
          </span>
        </div>
      )}

      {/* Mobile Storage Info (Simplified) */}
      {isMobile && storageInfo && (
        <div 
          className="mobile-storage"
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '6px',
            fontSize: '10px',
            color: 'rgba(255, 255, 255, 0.5)'
          }}
        >
          <span>{formatFileSize(storageInfo.used)}</span>
          <span>/</span>
          <span>{formatFileSize(storageInfo.total)}</span>
        </div>
      )}
    </motion.div>
  )
}

// Additional styles for status bar
const statusBarStyles = `
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.ziexplorer-status-bar {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .ziexplorer-status-bar {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .status-left {
    width: 100%;
    justify-content: space-between;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .ziexplorer-status-bar * {
    animation: none !important;
    transition: none !important;
  }
}
`

// Inject styles
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style')
  styleElement.textContent = statusBarStyles
  document.head.appendChild(styleElement)
}
