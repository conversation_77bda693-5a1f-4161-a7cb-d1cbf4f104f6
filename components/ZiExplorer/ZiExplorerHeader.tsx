'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'

interface ZiExplorerHeaderProps {
  currentPath: string
  onNavigate: (path: string) => void
  onSearch: (query: string) => void
  searchQuery: string
  onViewModeChange: (mode: 'grid' | 'list' | 'details') => void
  viewMode: 'grid' | 'list' | 'details'
  onImportRequest: (section: 'documents' | 'pictures' | 'music') => void
  isLoading: boolean
  onClose?: () => void
  onMinimize?: () => void
  onMaximize?: () => void
  onToggleSidebar?: () => void
  sidebarCollapsed?: boolean
  isMobile?: boolean
  isLowEndDevice?: boolean
}

export function ZiExplorerHeader({
  currentPath,
  onNavigate,
  onSearch,
  searchQuery,
  onViewModeChange,
  viewMode,
  onImportRequest,
  isLoading,
  onClose,
  onMinimize,
  onMaximize,
  onToggleSidebar,
  sidebarCollapsed = false,
  isMobile = false,
  isLowEndDevice = false
}: ZiExplorerHeaderProps) {
  const [isSearchFocused, setIsSearchFocused] = useState(false)

  // Parse path into breadcrumbs
  const pathParts = currentPath.split('/').filter(part => part)
  const breadcrumbs = pathParts.map((part, index) => ({
    name: part,
    path: '/' + pathParts.slice(0, index + 1).join('/')
  }))

  // Add root if not present
  if (breadcrumbs.length === 0 || breadcrumbs[0].name !== 'ZiExplorer') {
    breadcrumbs.unshift({ name: 'ZiExplorer', path: '/' })
  }

  const handleBackClick = () => {
    if (pathParts.length > 1) {
      const parentPath = '/' + pathParts.slice(0, -1).join('/')
      onNavigate(parentPath)
    }
  }

  const handleForwardClick = () => {
    // TODO: Implement forward navigation history
    console.log('Forward navigation not implemented yet')
  }

  const canGoBack = pathParts.length > 1
  const canGoForward = false // TODO: Implement forward history

  return (
    <div
      className="zi-explorer-header"
      style={{
        height: isMobile ? '56px' : '44px',
        background: `
          linear-gradient(180deg, 
            rgba(255,255,255,0.1) 0%, 
            rgba(255,255,255,0.05) 100%
          )
        `,
        borderBottom: '1px solid rgba(255,255,255,0.1)',
        borderRadius: '16px 16px 0 0',
        padding: isMobile ? '0 16px' : '0 16px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        gap: '12px'
      }}
    >
      {/* Left Section - Traffic Lights & Navigation */}
      <div className="flex items-center gap-3">
        {/* Traffic Light Controls (Desktop only) */}
        {!isMobile && (
          <div className="zi-traffic-lights flex gap-2">
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={onClose}
              className="zi-traffic-light close"
              style={{
                width: '12px',
                height: '12px',
                borderRadius: '50%',
                border: 'none',
                cursor: 'pointer',
                background: 'linear-gradient(135deg, #ff5f57 0%, #ff3b30 100%)'
              }}
              aria-label="Close"
            />
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={onMinimize}
              className="zi-traffic-light minimize"
              style={{
                width: '12px',
                height: '12px',
                borderRadius: '50%',
                border: 'none',
                cursor: 'pointer',
                background: 'linear-gradient(135deg, #ffbd2e 0%, #ff9500 100%)'
              }}
              aria-label="Minimize"
            />
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={onMaximize}
              className="zi-traffic-light maximize"
              style={{
                width: '12px',
                height: '12px',
                borderRadius: '50%',
                border: 'none',
                cursor: 'pointer',
                background: 'linear-gradient(135deg, #28ca42 0%, #30d158 100%)'
              }}
              aria-label="Maximize"
            />
          </div>
        )}

        {/* Sidebar Toggle */}
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={onToggleSidebar}
          className="p-2 rounded-lg hover:bg-white/10 transition-colors"
          aria-label={sidebarCollapsed ? 'Show sidebar' : 'Hide sidebar'}
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
          </svg>
        </motion.button>

        {/* Navigation Buttons */}
        <div className="flex gap-1">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleBackClick}
            disabled={!canGoBack}
            className={`p-2 rounded-lg transition-colors ${
              canGoBack 
                ? 'hover:bg-white/10 text-white' 
                : 'text-white/40 cursor-not-allowed'
            }`}
            aria-label="Go back"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"/>
            </svg>
          </motion.button>

          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleForwardClick}
            disabled={!canGoForward}
            className={`p-2 rounded-lg transition-colors ${
              canGoForward 
                ? 'hover:bg-white/10 text-white' 
                : 'text-white/40 cursor-not-allowed'
            }`}
            aria-label="Go forward"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z"/>
            </svg>
          </motion.button>
        </div>
      </div>

      {/* Center Section - Breadcrumbs */}
      <div className="flex-1 flex items-center min-w-0">
        <div className="flex items-center gap-1 min-w-0 overflow-hidden">
          {breadcrumbs.map((crumb, index) => (
            <React.Fragment key={crumb.path}>
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => onNavigate(crumb.path)}
                className={`px-2 py-1 rounded text-sm font-medium transition-colors truncate ${
                  index === breadcrumbs.length - 1
                    ? 'text-white bg-white/10'
                    : 'text-white/70 hover:text-white hover:bg-white/5'
                }`}
              >
                {crumb.name}
              </motion.button>
              {index < breadcrumbs.length - 1 && (
                <svg 
                  width="12" 
                  height="12" 
                  viewBox="0 0 24 24" 
                  fill="currentColor"
                  className="text-white/40 flex-shrink-0"
                >
                  <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/>
                </svg>
              )}
            </React.Fragment>
          ))}
        </div>
      </div>

      {/* Right Section - Search & View Controls */}
      <div className="flex items-center gap-2">
        {/* Search Input */}
        <motion.div
          animate={{
            width: isSearchFocused || searchQuery ? (isMobile ? 160 : 200) : (isMobile ? 120 : 140)
          }}
          transition={{ duration: isLowEndDevice ? 0.2 : 0.3, ease: [0.4, 0, 0.2, 1] }}
          className="relative"
        >
          <input
            type="text"
            placeholder="Search files..."
            value={searchQuery}
            onChange={(e) => onSearch(e.target.value)}
            onFocus={() => setIsSearchFocused(true)}
            onBlur={() => setIsSearchFocused(false)}
            data-search-input
            className="w-full px-3 py-1.5 text-sm bg-white/10 border border-white/20 rounded-lg 
                     text-white placeholder-white/60 focus:outline-none focus:ring-2 
                     focus:ring-blue-400/50 focus:border-blue-400/50 transition-all"
          />
          <svg 
            width="14" 
            height="14" 
            viewBox="0 0 24 24" 
            fill="currentColor"
            className="absolute right-2 top-1/2 transform -translate-y-1/2 text-white/60"
          >
            <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
          </svg>
        </motion.div>

        {/* Import Button */}
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => {
            const pathSegments = currentPath.split('/').filter(Boolean)
            const firstSegment = pathSegments[0]?.toLowerCase()
            let section: 'documents' | 'pictures' | 'music' = 'documents'
            if (firstSegment === 'pictures') section = 'pictures'
            else if (firstSegment === 'music') section = 'music'
            onImportRequest(section)
          }}
          disabled={isLoading}
          className="p-2 rounded-lg hover:bg-white/10 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          aria-label="Import files"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
          </svg>
        </motion.button>

        {/* View Mode Toggle (Desktop only) */}
        {!isMobile && (
          <div className="flex bg-white/10 rounded-lg p-1">
            {(['grid', 'list', 'details'] as const).map((mode) => (
              <motion.button
                key={mode}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => onViewModeChange(mode)}
                className={`p-1.5 rounded transition-colors ${
                  viewMode === mode
                    ? 'bg-white/20 text-white'
                    : 'text-white/60 hover:text-white hover:bg-white/10'
                }`}
                aria-label={`${mode} view`}
              >
                {mode === 'grid' && (
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M4 11h5V5H4v6zm0 7h5v-6H4v6zm6 0h5v-6h-5v6zm6 0h5v-6h-5v6zm-6-7h5V5h-5v6zm6-6v6h5V5h-5z"/>
                  </svg>
                )}
                {mode === 'list' && (
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M4 14h4v-4H4v4zm0 5h4v-4H4v4zM4 9h4V5H4v4zm5 5h12v-4H9v4zm0 5h12v-4H9v4zM9 5v4h12V5H9z"/>
                  </svg>
                )}
                {mode === 'details' && (
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M3 5v14h18V5H3zm4 2v2H5V7h2zm0 4v2H5v-2h2zm0 4v2H5v-2h2zm10 2H9v-2h8v2zm0-4H9v-2h8v2zm0-4H9V7h8v2z"/>
                  </svg>
                )}
              </motion.button>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
