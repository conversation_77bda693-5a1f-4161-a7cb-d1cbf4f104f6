'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { FileItem } from '@/lib/ziexplorer/types'

interface FileDetailsProps {
  folders: FileItem[]
  files: FileItem[]
  selectedFiles: string[]
  onItemClick: (item: FileItem, event: React.MouseEvent) => void
  onContextMenu: (event: React.MouseEvent, item: FileItem) => void
  isMobile?: boolean
  isLowEndDevice?: boolean
}

type SortField = 'name' | 'size' | 'type' | 'modified'
type SortDirection = 'asc' | 'desc'

interface FileDetailsItemProps {
  item: FileItem
  isSelected: boolean
  onClick: (event: React.MouseEvent) => void
  onContextMenu: (event: React.MouseEvent) => void
  isMobile?: boolean
  isLowEndDevice?: boolean
}

function FileDetailsItem({
  item,
  isSelected,
  onClick,
  onContextMenu,
  isMobile = false,
  isLowEndDevice = false
}: FileDetailsItemProps) {
  const getFileIcon = (item: FileItem): string => {
    if (item.type === 'folder') return '📁'
    
    const extension = item.name.toLowerCase().substring(item.name.lastIndexOf('.'))
    
    // Image files
    if (['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'].includes(extension)) {
      return '🖼️'
    }
    
    // Document files
    if (['.pdf', '.doc', '.docx', '.txt', '.rtf'].includes(extension)) {
      return '📄'
    }
    
    // Audio files
    if (['.mp3', '.wav', '.flac', '.aac', '.ogg'].includes(extension)) {
      return '🎵'
    }
    
    // Video files
    if (['.mp4', '.avi', '.mov', '.wmv', '.flv'].includes(extension)) {
      return '🎬'
    }
    
    return '📄'
  }

  const formatFileSize = (bytes?: number): string => {
    if (!bytes) return '—'
    
    const units = ['B', 'KB', 'MB', 'GB']
    let size = bytes
    let unitIndex = 0
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024
      unitIndex++
    }
    
    return `${size.toFixed(unitIndex === 0 ? 0 : 1)} ${units[unitIndex]}`
  }

  const formatDate = (date: Date): string => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date)
  }

  const getFileType = (item: FileItem): string => {
    if (item.type === 'folder') return 'Folder'
    
    const extension = item.name.toLowerCase().substring(item.name.lastIndexOf('.') + 1)
    if (!extension) return 'File'
    
    return extension.toUpperCase() + ' File'
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: isLowEndDevice ? 0.2 : 0.3,
        ease: [0.4, 0, 0.2, 1]
      }
    },
    hover: {
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      transition: {
        duration: isLowEndDevice ? 0.1 : 0.2,
        ease: [0.4, 0, 0.2, 1]
      }
    }
  }

  return (
    <motion.tr
      variants={itemVariants}
      initial="hidden"
      animate="visible"
      whileHover={!isMobile ? "hover" : undefined}
      onClick={onClick}
      onContextMenu={onContextMenu}
      className={`file-details-row ${isSelected ? 'selected' : ''}`}
      style={{
        cursor: 'pointer',
        userSelect: 'none',
        background: isSelected 
          ? 'linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(99, 102, 241, 0.15) 100%)'
          : 'transparent',
        borderLeft: isSelected ? '3px solid rgba(59, 130, 246, 0.6)' : '3px solid transparent'
      }}
    >
      {/* Selection */}
      <td style={{ width: '40px', padding: '8px' }}>
        <div
          className="selection-checkbox"
          style={{
            width: '18px',
            height: '18px',
            borderRadius: '3px',
            border: '2px solid rgba(255,255,255,0.3)',
            background: isSelected 
              ? 'linear-gradient(135deg, #3b82f6 0%, #6366f1 100%)'
              : 'transparent',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            transition: `all ${isLowEndDevice ? '0.15s' : '0.2s'} ease`
          }}
        >
          {isSelected && (
            <motion.span
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              style={{ color: 'white', fontSize: '10px', fontWeight: 'bold' }}
            >
              ✓
            </motion.span>
          )}
        </div>
      </td>

      {/* Icon & Name */}
      <td style={{ padding: '8px 12px' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <span style={{ fontSize: '16px' }}>{getFileIcon(item)}</span>
          <span 
            style={{ 
              color: 'white', 
              fontSize: isMobile ? '14px' : '13px',
              fontWeight: 500 
            }}
            title={item.name}
          >
            {item.name}
          </span>
        </div>
      </td>

      {/* Size */}
      <td style={{ padding: '8px 12px', width: '100px' }}>
        <span style={{ 
          color: 'rgba(255,255,255,0.7)', 
          fontSize: '12px' 
        }}>
          {item.type === 'file' ? formatFileSize(item.size) : '—'}
        </span>
      </td>

      {/* Type */}
      <td style={{ padding: '8px 12px', width: '120px' }}>
        <span style={{ 
          color: 'rgba(255,255,255,0.7)', 
          fontSize: '12px' 
        }}>
          {getFileType(item)}
        </span>
      </td>

      {/* Modified Date */}
      <td style={{ padding: '8px 12px', width: '160px' }}>
        <span style={{ 
          color: 'rgba(255,255,255,0.7)', 
          fontSize: '12px' 
        }}>
          {formatDate(item.modifiedAt)}
        </span>
      </td>

      {/* Created Date */}
      {!isMobile && (
        <td style={{ padding: '8px 12px', width: '160px' }}>
          <span style={{ 
            color: 'rgba(255,255,255,0.7)', 
            fontSize: '12px' 
          }}>
            {formatDate(item.createdAt)}
          </span>
        </td>
      )}
    </motion.tr>
  )
}

export function FileDetails({
  folders,
  files,
  selectedFiles,
  onItemClick,
  onContextMenu,
  isMobile = false,
  isLowEndDevice = false
}: FileDetailsProps) {
  const [sortField, setSortField] = useState<SortField>('name')
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc')

  // Combine and sort items
  const allItems = [...folders, ...files]

  const sortedItems = [...allItems].sort((a, b) => {
    let comparison = 0

    switch (sortField) {
      case 'name':
        comparison = a.name.localeCompare(b.name)
        break
      case 'size':
        comparison = (a.size || 0) - (b.size || 0)
        break
      case 'type':
        if (a.type !== b.type) {
          comparison = a.type === 'folder' ? -1 : 1
        } else {
          comparison = a.name.localeCompare(b.name)
        }
        break
      case 'modified':
        comparison = a.modifiedAt.getTime() - b.modifiedAt.getTime()
        break
    }

    return sortDirection === 'asc' ? comparison : -comparison
  })

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }

  const getSortIcon = (field: SortField): string => {
    if (sortField !== field) return '↕️'
    return sortDirection === 'asc' ? '↑' : '↓'
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: isLowEndDevice ? 0.01 : 0.02,
        delayChildren: 0.1
      }
    }
  }

  return (
    <div
      className="file-details-container"
      style={{
        height: '100%',
        overflowY: 'auto',
        padding: isMobile ? '12px' : '16px'
      }}
    >
      <motion.table
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="file-details-table"
        style={{
          width: '100%',
          borderCollapse: 'collapse',
          fontSize: '13px'
        }}
      >
        {/* Table Header */}
        <thead>
          <tr style={{ 
            borderBottom: '2px solid rgba(255,255,255,0.1)',
            background: 'rgba(255,255,255,0.05)'
          }}>
            <th style={{ width: '40px', padding: '12px 8px' }}></th>
            
            <th style={{ padding: '12px', textAlign: 'left' }}>
              <button
                onClick={() => handleSort('name')}
                style={{
                  background: 'none',
                  border: 'none',
                  color: 'rgba(255,255,255,0.8)',
                  fontSize: '12px',
                  fontWeight: 600,
                  textTransform: 'uppercase',
                  letterSpacing: '0.05em',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px'
                }}
              >
                Name {getSortIcon('name')}
              </button>
            </th>

            <th style={{ width: '100px', padding: '12px', textAlign: 'left' }}>
              <button
                onClick={() => handleSort('size')}
                style={{
                  background: 'none',
                  border: 'none',
                  color: 'rgba(255,255,255,0.8)',
                  fontSize: '12px',
                  fontWeight: 600,
                  textTransform: 'uppercase',
                  letterSpacing: '0.05em',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px'
                }}
              >
                Size {getSortIcon('size')}
              </button>
            </th>

            <th style={{ width: '120px', padding: '12px', textAlign: 'left' }}>
              <button
                onClick={() => handleSort('type')}
                style={{
                  background: 'none',
                  border: 'none',
                  color: 'rgba(255,255,255,0.8)',
                  fontSize: '12px',
                  fontWeight: 600,
                  textTransform: 'uppercase',
                  letterSpacing: '0.05em',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px'
                }}
              >
                Type {getSortIcon('type')}
              </button>
            </th>

            <th style={{ width: '160px', padding: '12px', textAlign: 'left' }}>
              <button
                onClick={() => handleSort('modified')}
                style={{
                  background: 'none',
                  border: 'none',
                  color: 'rgba(255,255,255,0.8)',
                  fontSize: '12px',
                  fontWeight: 600,
                  textTransform: 'uppercase',
                  letterSpacing: '0.05em',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px'
                }}
              >
                Modified {getSortIcon('modified')}
              </button>
            </th>

            {!isMobile && (
              <th style={{ width: '160px', padding: '12px', textAlign: 'left' }}>
                <span style={{
                  color: 'rgba(255,255,255,0.8)',
                  fontSize: '12px',
                  fontWeight: 600,
                  textTransform: 'uppercase',
                  letterSpacing: '0.05em'
                }}>
                  Created
                </span>
              </th>
            )}
          </tr>
        </thead>

        {/* Table Body */}
        <tbody>
          <AnimatePresence>
            {sortedItems.map((item) => (
              <FileDetailsItem
                key={item.path}
                item={item}
                isSelected={selectedFiles.includes(item.path)}
                onClick={(event) => onItemClick(item, event)}
                onContextMenu={(event) => onContextMenu(event, item)}
                isMobile={isMobile}
                isLowEndDevice={isLowEndDevice}
              />
            ))}
          </AnimatePresence>
        </tbody>
      </motion.table>
    </div>
  )
}

// Additional styles for details view
const detailsStyles = `
.file-details-container {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

.file-details-row:hover:not(.selected) {
  background: rgba(255, 255, 255, 0.05) !important;
}

.file-details-row.selected {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(99, 102, 241, 0.15) 100%) !important;
}

.selection-checkbox:hover {
  border-color: rgba(59, 130, 246, 0.6) !important;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .file-details-row {
    touch-action: manipulation;
  }
  
  .file-details-table th,
  .file-details-table td {
    padding: 8px 6px !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .file-details-row {
    transition: none !important;
  }
}
`

// Inject styles
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style')
  styleElement.textContent = detailsStyles
  document.head.appendChild(styleElement)
}
