'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { ZIEXPLORER_SECTIONS } from '@/lib/ziexplorer/types'

interface ZiExplorerSidebarProps {
  currentPath: string
  onNavigate: (path: string) => void
  isMobile?: boolean
  isLowEndDevice?: boolean
}

interface SidebarSection {
  id: string
  title: string
  items: Array<{
    id: string
    name: string
    icon: string
    path: string
    color: string
    isSection?: boolean
  }>
}

export function ZiExplorerSidebar({
  currentPath,
  onNavigate,
  isMobile = false,
  isLowEndDevice = false
}: ZiExplorerSidebarProps) {
  const [clickTimeout, setClickTimeout] = useState<NodeJS.Timeout | null>(null)

  // Define sidebar sections
  const sidebarSections: SidebarSection[] = [
    {
      id: 'main',
      title: 'Main Sections',
      items: ZIEXPLORER_SECTIONS.map(section => ({
        id: section.id,
        name: section.name,
        icon: section.icon,
        path: section.path,
        color: section.color,
        isSection: true
      }))
    },
    {
      id: 'quick-access',
      title: 'Quick Access',
      items: [
        {
          id: 'recent',
          name: 'Recent Files',
          icon: '🕒',
          path: '/Recent',
          color: 'rgba(156, 163, 175, 0.8)'
        },
        {
          id: 'favorites',
          name: 'Favorites',
          icon: '⭐',
          path: '/Favorites',
          color: 'rgba(245, 158, 11, 0.8)'
        },
        {
          id: 'shared',
          name: 'Shared',
          icon: '👥',
          path: '/Shared',
          color: 'rgba(34, 197, 94, 0.8)'
        }
      ]
    },
    {
      id: 'system',
      title: 'System',
      items: [
        {
          id: 'recycle-bin',
          name: 'Recycle Bin',
          icon: '🗑️',
          path: '/RecycleBin',
          color: 'rgba(239, 68, 68, 0.8)'
        }
      ]
    }
  ]

  const handleItemClick = (item: any) => {
    if (item.isSection) {
      // Handle section clicks with single/double click detection
      if (clickTimeout) {
        // Double click
        clearTimeout(clickTimeout)
        setClickTimeout(null)
        onSectionClick(item.id as 'documents' | 'pictures' | 'music', true)
      } else {
        // Single click - set timeout for double click detection
        const timeout = setTimeout(() => {
          onSectionClick(item.id as 'documents' | 'pictures' | 'music', false)
          setClickTimeout(null)
        }, 300)
        setClickTimeout(timeout)
      }
    } else {
      // Regular navigation for non-section items
      onNavigate(item.path)
    }
  }

  const isItemActive = (path: string) => {
    return currentPath === path || currentPath.startsWith(path + '/')
  }

  const itemVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: (index: number) => ({
      opacity: 1,
      x: 0,
      transition: {
        delay: index * 0.05,
        duration: isLowEndDevice ? 0.2 : 0.3,
        ease: [0.4, 0, 0.2, 1]
      }
    })
  }

  return (
    <div
      className="zi-explorer-sidebar h-full overflow-y-auto"
      style={{
        background: `
          linear-gradient(180deg, 
            rgba(255,255,255,0.08) 0%, 
            rgba(255,255,255,0.04) 100%
          )
        `,
        borderRight: '1px solid rgba(255,255,255,0.1)',
        padding: '16px 0'
      }}
    >
      {sidebarSections.map((section, sectionIndex) => (
        <motion.div
          key={section.id}
          initial="hidden"
          animate="visible"
          className="zi-sidebar-section mb-6"
        >
          {/* Section Title */}
          <motion.div
            variants={itemVariants}
            custom={sectionIndex * 3}
            className="zi-sidebar-title"
            style={{
              fontSize: '0.75rem',
              fontWeight: 600,
              textTransform: 'uppercase',
              letterSpacing: '0.05em',
              color: 'rgba(255,255,255,0.6)',
              padding: '0 16px 8px',
              marginBottom: '4px'
            }}
          >
            {section.title}
          </motion.div>

          {/* Section Items */}
          <div className="space-y-1">
            {section.items.map((item, itemIndex) => (
              <motion.button
                key={item.id}
                variants={itemVariants}
                custom={sectionIndex * 3 + itemIndex + 1}
                whileHover={{ 
                  scale: 1.02,
                  x: 4,
                  transition: { duration: isLowEndDevice ? 0.1 : 0.2 }
                }}
                whileTap={{ scale: 0.98 }}
                onClick={() => handleItemClick(item)}
                className={`zi-sidebar-item w-full text-left ${
                  isItemActive(item.path) ? 'active' : ''
                }`}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  padding: isMobile ? '10px 16px' : '8px 16px',
                  margin: '0 8px',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  transition: `all ${isLowEndDevice ? '0.15s' : '0.2s'} ease`,
                  color: isItemActive(item.path) ? 'white' : 'rgba(255,255,255,0.7)',
                  background: isItemActive(item.path) 
                    ? `linear-gradient(135deg, ${item.color}40 0%, ${item.color}20 100%)`
                    : 'transparent',
                  border: isItemActive(item.path) 
                    ? `1px solid ${item.color}60`
                    : '1px solid transparent'
                }}
                onMouseEnter={(e) => {
                  if (!isItemActive(item.path)) {
                    e.currentTarget.style.background = 'rgba(255,255,255,0.1)'
                    e.currentTarget.style.color = 'white'
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isItemActive(item.path)) {
                    e.currentTarget.style.background = 'transparent'
                    e.currentTarget.style.color = 'rgba(255,255,255,0.7)'
                  }
                }}
              >
                {/* Icon */}
                <div
                  className="zi-sidebar-icon"
                  style={{
                    width: '20px',
                    height: '20px',
                    marginRight: '12px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '16px',
                    flexShrink: 0
                  }}
                >
                  {item.icon}
                </div>

                {/* Label */}
                <span 
                  className="truncate"
                  style={{
                    fontSize: isMobile ? '0.9rem' : '0.875rem',
                    fontWeight: isItemActive(item.path) ? 500 : 400
                  }}
                >
                  {item.name}
                </span>

                {/* Active Indicator */}
                {isItemActive(item.path) && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="ml-auto w-2 h-2 rounded-full"
                    style={{ background: item.color }}
                  />
                )}

                {/* Section Click Hint */}
                {item.isSection && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    whileHover={{ opacity: 1 }}
                    className="ml-auto text-xs text-white/40"
                    style={{ fontSize: '0.7rem' }}
                  >
                    {isMobile ? 'Tap' : 'Click'}
                  </motion.div>
                )}
              </motion.button>
            ))}
          </div>
        </motion.div>
      ))}

      {/* Storage Info (Desktop only) */}
      {!isMobile && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5, duration: isLowEndDevice ? 0.2 : 0.3 }}
          className="mt-auto px-4 py-3 mx-2 rounded-lg"
          style={{
            background: 'rgba(255,255,255,0.05)',
            border: '1px solid rgba(255,255,255,0.1)'
          }}
        >
          <div className="text-xs text-white/60 mb-2">Storage</div>
          <div className="flex items-center gap-2">
            <div 
              className="flex-1 h-2 rounded-full overflow-hidden"
              style={{ background: 'rgba(255,255,255,0.1)' }}
            >
              <motion.div
                initial={{ width: 0 }}
                animate={{ width: '65%' }}
                transition={{ delay: 0.7, duration: 0.8, ease: 'easeOut' }}
                className="h-full rounded-full"
                style={{
                  background: 'linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%)'
                }}
              />
            </div>
            <span className="text-xs text-white/60">65%</span>
          </div>
          <div className="text-xs text-white/40 mt-1">
            2.1 GB of 3.2 GB used
          </div>
        </motion.div>
      )}
    </div>
  )
}

// Additional styles for better mobile experience
const mobileStyles = `
@media (max-width: 768px) {
  .zi-sidebar-item {
    min-height: 44px;
  }
  
  .zi-sidebar-icon {
    font-size: 18px !important;
  }
}

/* Smooth scrolling for sidebar */
.zi-explorer-sidebar {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* Custom scrollbar for sidebar */
.zi-explorer-sidebar::-webkit-scrollbar {
  width: 4px;
}

.zi-explorer-sidebar::-webkit-scrollbar-track {
  background: transparent;
}

.zi-explorer-sidebar::-webkit-scrollbar-thumb {
  background: rgba(255,255,255,0.2);
  border-radius: 2px;
}

.zi-explorer-sidebar::-webkit-scrollbar-thumb:hover {
  background: rgba(255,255,255,0.3);
}
`

// Inject mobile styles
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style')
  styleElement.textContent = mobileStyles
  document.head.appendChild(styleElement)
}
