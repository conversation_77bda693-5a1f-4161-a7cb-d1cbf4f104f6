'use client'

import React, { useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { FileItem } from '@/lib/ziexplorer/types'

interface ContextMenuProps {
  isOpen: boolean
  position: { x: number; y: number }
  selectedItem: FileItem | null
  selectedFiles: string[]
  onClose: () => void
  onAction: (action: string, item?: FileItem) => void
  isMobile?: boolean
  isLowEndDevice?: boolean
}

interface MenuItemProps {
  icon: string
  label: string
  shortcut?: string
  onClick: () => void
  disabled?: boolean
  destructive?: boolean
  isMobile?: boolean
  isLowEndDevice?: boolean
}

function MenuItem({
  icon,
  label,
  shortcut,
  onClick,
  disabled = false,
  destructive = false,
  isMobile = false,
  isLowEndDevice = false
}: MenuItemProps) {
  const itemVariants = {
    hidden: { opacity: 0, x: -10 },
    visible: { 
      opacity: 1, 
      x: 0,
      transition: {
        duration: isLowEndDevice ? 0.1 : 0.15,
        ease: [0.4, 0, 0.2, 1]
      }
    },
    hover: {
      backgroundColor: destructive 
        ? 'rgba(239, 68, 68, 0.2)' 
        : 'rgba(255, 255, 255, 0.1)',
      x: 2,
      transition: {
        duration: isLowEndDevice ? 0.1 : 0.15,
        ease: [0.4, 0, 0.2, 1]
      }
    },
    tap: {
      scale: 0.98,
      transition: {
        duration: 0.1,
        ease: [0.4, 0, 0.2, 1]
      }
    }
  }

  return (
    <motion.button
      variants={itemVariants}
      initial="hidden"
      animate="visible"
      whileHover={!disabled && !isMobile ? "hover" : undefined}
      whileTap={!disabled ? "tap" : undefined}
      onClick={disabled ? undefined : onClick}
      disabled={disabled}
      className="context-menu-item"
      style={{
        width: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: isMobile ? '12px 16px' : '8px 12px',
        background: 'transparent',
        border: 'none',
        borderRadius: '6px',
        cursor: disabled ? 'not-allowed' : 'pointer',
        fontSize: isMobile ? '14px' : '13px',
        fontWeight: 500,
        color: disabled 
          ? 'rgba(255,255,255,0.3)' 
          : destructive 
            ? '#ef4444' 
            : 'white',
        textAlign: 'left',
        opacity: disabled ? 0.5 : 1,
        transition: `all ${isLowEndDevice ? '0.1s' : '0.15s'} ease`,
        touchAction: 'manipulation'
      }}
    >
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        <span style={{ fontSize: isMobile ? '16px' : '14px' }}>{icon}</span>
        <span>{label}</span>
      </div>
      
      {shortcut && !isMobile && (
        <span style={{
          fontSize: '11px',
          color: 'rgba(255,255,255,0.5)',
          fontFamily: 'monospace'
        }}>
          {shortcut}
        </span>
      )}
    </motion.button>
  )
}

export function ZiExplorerContextMenu({
  isOpen,
  position,
  selectedItem,
  selectedFiles,
  onClose,
  onAction,
  isMobile = false,
  isLowEndDevice = false
}: ContextMenuProps) {
  const menuRef = useRef<HTMLDivElement>(null)

  // Close menu on outside click
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose()
      }
    }

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      document.addEventListener('keydown', handleEscape)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener('keydown', handleEscape)
    }
  }, [isOpen, onClose])

  // Adjust menu position to stay within viewport
  const getMenuStyle = () => {
    if (!isOpen) return {}

    const menuWidth = isMobile ? 200 : 180
    const menuHeight = isMobile ? 400 : 350
    
    let x = position.x
    let y = position.y

    // Adjust horizontal position
    if (x + menuWidth > window.innerWidth) {
      x = window.innerWidth - menuWidth - 10
    }

    // Adjust vertical position
    if (y + menuHeight > window.innerHeight) {
      y = window.innerHeight - menuHeight - 10
    }

    return {
      left: `${x}px`,
      top: `${y}px`,
      width: `${menuWidth}px`
    }
  }

  const isMultipleSelection = selectedFiles.length > 1
  const isFolder = selectedItem?.type === 'folder'
  const isFile = selectedItem?.type === 'file'

  const menuVariants = {
    hidden: { 
      opacity: 0, 
      scale: 0.95,
      y: -10,
      transition: {
        duration: isLowEndDevice ? 0.1 : 0.15,
        ease: [0.4, 0, 0.2, 1]
      }
    },
    visible: { 
      opacity: 1, 
      scale: 1,
      y: 0,
      transition: {
        duration: isLowEndDevice ? 0.15 : 0.2,
        ease: [0.4, 0, 0.2, 1],
        staggerChildren: 0.02,
        delayChildren: 0.05
      }
    }
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop for mobile */}
          {isMobile && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={onClose}
              style={{
                position: 'fixed',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: 'rgba(0, 0, 0, 0.3)',
                zIndex: 9998
              }}
            />
          )}

          {/* Context Menu */}
          <motion.div
            ref={menuRef}
            variants={menuVariants}
            initial="hidden"
            animate="visible"
            exit="hidden"
            className="context-menu"
            style={{
              position: 'fixed',
              zIndex: 9999,
              background: 'linear-gradient(135deg, rgba(30, 30, 30, 0.95) 0%, rgba(20, 20, 20, 0.95) 100%)',
              backdropFilter: 'blur(20px)',
              border: '1px solid rgba(255, 255, 255, 0.1)',
              borderRadius: '12px',
              padding: '8px',
              boxShadow: '0 20px 40px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.05)',
              minWidth: isMobile ? '200px' : '180px',
              maxHeight: isMobile ? '400px' : '350px',
              overflowY: 'auto',
              ...getMenuStyle()
            }}
          >
            {/* File/Folder Actions */}
            {selectedItem && (
              <>
                <MenuItem
                  icon="👁️"
                  label={isFolder ? "Open" : "Preview"}
                  shortcut="Enter"
                  onClick={() => onAction('open', selectedItem)}
                  isMobile={isMobile}
                  isLowEndDevice={isLowEndDevice}
                />

                {isFile && (
                  <MenuItem
                    icon="📤"
                    label="Share"
                    onClick={() => onAction('share', selectedItem)}
                    isMobile={isMobile}
                    isLowEndDevice={isLowEndDevice}
                  />
                )}

                <div style={{ 
                  height: '1px', 
                  background: 'rgba(255, 255, 255, 0.1)', 
                  margin: '4px 0' 
                }} />
              </>
            )}

            {/* Edit Actions */}
            <MenuItem
              icon="📋"
              label={isMultipleSelection ? `Copy ${selectedFiles.length} items` : "Copy"}
              shortcut="Ctrl+C"
              onClick={() => onAction('copy')}
              disabled={selectedFiles.length === 0}
              isMobile={isMobile}
              isLowEndDevice={isLowEndDevice}
            />

            <MenuItem
              icon="✂️"
              label={isMultipleSelection ? `Cut ${selectedFiles.length} items` : "Cut"}
              shortcut="Ctrl+X"
              onClick={() => onAction('cut')}
              disabled={selectedFiles.length === 0}
              isMobile={isMobile}
              isLowEndDevice={isLowEndDevice}
            />

            <MenuItem
              icon="📄"
              label="Paste"
              shortcut="Ctrl+V"
              onClick={() => onAction('paste')}
              disabled={false} // Will be handled by the action handler
              isMobile={isMobile}
              isLowEndDevice={isLowEndDevice}
            />

            <div style={{ 
              height: '1px', 
              background: 'rgba(255, 255, 255, 0.1)', 
              margin: '4px 0' 
            }} />

            {/* File Operations */}
            <MenuItem
              icon="✏️"
              label="Rename"
              shortcut="F2"
              onClick={() => onAction('rename', selectedItem)}
              disabled={!selectedItem || isMultipleSelection}
              isMobile={isMobile}
              isLowEndDevice={isLowEndDevice}
            />

            <MenuItem
              icon="📁"
              label="New Folder"
              shortcut="Ctrl+Shift+N"
              onClick={() => onAction('newFolder')}
              isMobile={isMobile}
              isLowEndDevice={isLowEndDevice}
            />

            <MenuItem
              icon="📥"
              label="Import Files"
              onClick={() => onAction('import')}
              isMobile={isMobile}
              isLowEndDevice={isLowEndDevice}
            />

            <div style={{ 
              height: '1px', 
              background: 'rgba(255, 255, 255, 0.1)', 
              margin: '4px 0' 
            }} />

            {/* View Options */}
            <MenuItem
              icon="🔄"
              label="Refresh"
              shortcut="F5"
              onClick={() => onAction('refresh')}
              isMobile={isMobile}
              isLowEndDevice={isLowEndDevice}
            />

            <MenuItem
              icon="ℹ️"
              label="Properties"
              onClick={() => onAction('properties', selectedItem)}
              disabled={!selectedItem}
              isMobile={isMobile}
              isLowEndDevice={isLowEndDevice}
            />

            <div style={{ 
              height: '1px', 
              background: 'rgba(255, 255, 255, 0.1)', 
              margin: '4px 0' 
            }} />

            {/* Destructive Actions */}
            <MenuItem
              icon="🗑️"
              label={isMultipleSelection ? `Delete ${selectedFiles.length} items` : "Delete"}
              shortcut="Delete"
              onClick={() => onAction('delete')}
              disabled={selectedFiles.length === 0}
              destructive={true}
              isMobile={isMobile}
              isLowEndDevice={isLowEndDevice}
            />
          </motion.div>
        </>
      )}
    </AnimatePresence>
  )
}

// Additional styles for context menu
const contextMenuStyles = `
.context-menu {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.context-menu-item:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.1) !important;
}

.context-menu-item:active {
  transform: scale(0.98);
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .context-menu {
    touch-action: manipulation;
  }
  
  .context-menu-item {
    min-height: 44px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .context-menu,
  .context-menu-item {
    transition: none !important;
  }
}
`

// Inject styles
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style')
  styleElement.textContent = contextMenuStyles
  document.head.appendChild(styleElement)
}
