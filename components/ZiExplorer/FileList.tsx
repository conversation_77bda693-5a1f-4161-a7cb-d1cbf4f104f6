'use client'

import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { FileItem } from '@/lib/ziexplorer/types'

interface FileListProps {
  folders: FileItem[]
  files: FileItem[]
  selectedFiles: string[]
  onItemClick: (item: FileItem, event: React.MouseEvent) => void
  onContextMenu: (event: React.MouseEvent, item: FileItem) => void
  isMobile?: boolean
  isLowEndDevice?: boolean
}

interface FileListItemProps {
  item: FileItem
  isSelected: boolean
  onClick: (event: React.MouseEvent) => void
  onContextMenu: (event: React.MouseEvent) => void
  isMobile?: boolean
  isLowEndDevice?: boolean
}

function FileListItem({
  item,
  isSelected,
  onClick,
  onContextMenu,
  isMobile = false,
  isLowEndDevice = false
}: FileListItemProps) {
  const getFileIcon = (item: FileItem): string => {
    if (item.type === 'folder') return '📁'
    
    const extension = item.name.toLowerCase().substring(item.name.lastIndexOf('.'))
    
    // Image files
    if (['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'].includes(extension)) {
      return '🖼️'
    }
    
    // Document files
    if (['.pdf', '.doc', '.docx', '.txt', '.rtf'].includes(extension)) {
      return '📄'
    }
    
    // Audio files
    if (['.mp3', '.wav', '.flac', '.aac', '.ogg'].includes(extension)) {
      return '🎵'
    }
    
    // Video files
    if (['.mp4', '.avi', '.mov', '.wmv', '.flv'].includes(extension)) {
      return '🎬'
    }
    
    return '📄'
  }

  const formatFileSize = (bytes?: number): string => {
    if (!bytes) return ''
    
    const units = ['B', 'KB', 'MB', 'GB']
    let size = bytes
    let unitIndex = 0
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024
      unitIndex++
    }
    
    return `${size.toFixed(unitIndex === 0 ? 0 : 1)} ${units[unitIndex]}`
  }

  const formatDate = (date: Date): string => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date)
  }

  const itemVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: { 
      opacity: 1, 
      x: 0,
      transition: {
        duration: isLowEndDevice ? 0.2 : 0.3,
        ease: [0.4, 0, 0.2, 1]
      }
    },
    hover: {
      x: 4,
      transition: {
        duration: isLowEndDevice ? 0.1 : 0.2,
        ease: [0.4, 0, 0.2, 1]
      }
    },
    tap: {
      scale: 0.98,
      transition: {
        duration: 0.1,
        ease: [0.4, 0, 0.2, 1]
      }
    }
  }

  return (
    <motion.div
      variants={itemVariants}
      initial="hidden"
      animate="visible"
      whileHover={!isMobile ? "hover" : undefined}
      whileTap="tap"
      onClick={onClick}
      onContextMenu={onContextMenu}
      className={`file-list-item ${isSelected ? 'selected' : ''}`}
      style={{
        display: 'flex',
        alignItems: 'center',
        padding: isMobile ? '12px 16px' : '8px 16px',
        borderRadius: '8px',
        border: '1px solid rgba(255,255,255,0.1)',
        background: isSelected 
          ? 'linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(99, 102, 241, 0.15) 100%)'
          : 'rgba(255,255,255,0.05)',
        cursor: 'pointer',
        transition: `all ${isLowEndDevice ? '0.15s' : '0.2s'} ease`,
        userSelect: 'none',
        touchAction: 'manipulation',
        marginBottom: '2px'
      }}
    >
      {/* Selection Checkbox */}
      <div
        className="selection-checkbox"
        style={{
          width: '20px',
          height: '20px',
          borderRadius: '4px',
          border: '2px solid rgba(255,255,255,0.3)',
          background: isSelected 
            ? 'linear-gradient(135deg, #3b82f6 0%, #6366f1 100%)'
            : 'transparent',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          marginRight: '12px',
          flexShrink: 0,
          transition: `all ${isLowEndDevice ? '0.15s' : '0.2s'} ease`
        }}
      >
        {isSelected && (
          <motion.span
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            style={{ color: 'white', fontSize: '12px', fontWeight: 'bold' }}
          >
            ✓
          </motion.span>
        )}
      </div>

      {/* File Icon */}
      <div
        className="file-icon"
        style={{
          width: '24px',
          height: '24px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          marginRight: '12px',
          flexShrink: 0,
          fontSize: '18px'
        }}
      >
        {getFileIcon(item)}
      </div>

      {/* File Name */}
      <div
        className="file-name"
        style={{
          flex: 1,
          fontSize: isMobile ? '14px' : '13px',
          fontWeight: 500,
          color: 'white',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap',
          marginRight: '12px'
        }}
        title={item.name}
      >
        {item.name}
      </div>

      {/* File Size */}
      {!isMobile && (
        <div
          className="file-size"
          style={{
            width: '80px',
            fontSize: '12px',
            color: 'rgba(255,255,255,0.6)',
            textAlign: 'right',
            marginRight: '12px',
            flexShrink: 0
          }}
        >
          {item.type === 'file' ? formatFileSize(item.size) : '—'}
        </div>
      )}

      {/* Modified Date */}
      {!isMobile && (
        <div
          className="file-date"
          style={{
            width: '140px',
            fontSize: '12px',
            color: 'rgba(255,255,255,0.6)',
            textAlign: 'right',
            flexShrink: 0
          }}
        >
          {formatDate(item.modifiedAt)}
        </div>
      )}

      {/* Mobile Info */}
      {isMobile && (
        <div
          className="mobile-info"
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'flex-end',
            fontSize: '11px',
            color: 'rgba(255,255,255,0.6)',
            flexShrink: 0
          }}
        >
          {item.type === 'file' && item.size && (
            <div>{formatFileSize(item.size)}</div>
          )}
          <div>{formatDate(item.modifiedAt)}</div>
        </div>
      )}
    </motion.div>
  )
}

export function FileList({
  folders,
  files,
  selectedFiles,
  onItemClick,
  onContextMenu,
  isMobile = false,
  isLowEndDevice = false
}: FileListProps) {
  // Combine and sort items
  const allItems = [...folders, ...files]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: isLowEndDevice ? 0.02 : 0.03,
        delayChildren: 0.1
      }
    }
  }

  return (
    <div
      className="file-list-container"
      style={{
        height: '100%',
        overflowY: 'auto',
        padding: isMobile ? '12px' : '16px'
      }}
    >
      {/* Header (Desktop only) */}
      {!isMobile && (
        <div
          className="file-list-header"
          style={{
            display: 'flex',
            alignItems: 'center',
            padding: '8px 16px',
            marginBottom: '8px',
            fontSize: '12px',
            fontWeight: 600,
            color: 'rgba(255,255,255,0.6)',
            textTransform: 'uppercase',
            letterSpacing: '0.05em',
            borderBottom: '1px solid rgba(255,255,255,0.1)'
          }}
        >
          <div style={{ width: '20px', marginRight: '12px' }}></div>
          <div style={{ width: '24px', marginRight: '12px' }}></div>
          <div style={{ flex: 1, marginRight: '12px' }}>Name</div>
          <div style={{ width: '80px', textAlign: 'right', marginRight: '12px' }}>Size</div>
          <div style={{ width: '140px', textAlign: 'right' }}>Modified</div>
        </div>
      )}

      {/* File List */}
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="file-list"
        style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '2px'
        }}
      >
        <AnimatePresence>
          {allItems.map((item) => (
            <FileListItem
              key={item.path}
              item={item}
              isSelected={selectedFiles.includes(item.path)}
              onClick={(event) => onItemClick(item, event)}
              onContextMenu={(event) => onContextMenu(event, item)}
              isMobile={isMobile}
              isLowEndDevice={isLowEndDevice}
            />
          ))}
        </AnimatePresence>
      </motion.div>
    </div>
  )
}

// Additional styles for list view
const listStyles = `
.file-list-container {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

.file-list-item:hover:not(.selected) {
  background: rgba(255, 255, 255, 0.1) !important;
  border-color: rgba(255, 255, 255, 0.2) !important;
}

.file-list-item.selected {
  border-color: rgba(59, 130, 246, 0.4) !important;
  box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.2) !important;
}

.selection-checkbox:hover {
  border-color: rgba(59, 130, 246, 0.6) !important;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .file-list-item {
    min-height: 56px;
    touch-action: manipulation;
  }
  
  .file-list-item:active {
    transform: scale(0.98);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .file-list-item {
    transition: none !important;
  }
}
`

// Inject styles
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style')
  styleElement.textContent = listStyles
  document.head.appendChild(styleElement)
}
