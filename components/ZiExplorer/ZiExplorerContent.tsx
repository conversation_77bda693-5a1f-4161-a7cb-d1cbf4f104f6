'use client'

import React, { useState, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { FileItem, ImportResult } from '@/lib/ziexplorer/types'
import { useZiExplorer } from '@/hooks/useZiExplorer'
import { FileGrid } from './FileGrid'
import { FileList } from './FileList'
import { FileDetails } from './FileDetails'
import { ZiExplorerContextMenu } from './ZiExplorerContextMenu'
import { ImportDialog } from './ImportDialog'

interface ZiExplorerContentProps {
  files: FileItem[]
  viewMode: 'grid' | 'list' | 'details'
  selectedFiles: string[]
  currentPath: string
  onSelectionChange: (path: string, multiSelect?: boolean) => void
  onFileOpen: (path: string) => void
  onFolderOpen: (path: string) => void
  onContextMenu: (event: React.MouseEvent, item: FileItem) => void
  onRefresh: () => void
  isLoading: boolean
  error: string | null
  isMobile?: boolean
  isLowEndDevice?: boolean
}

export function ZiExplorerContent({
  files,
  viewMode,
  selectedFiles,
  currentPath,
  onSelectionChange,
  onFileOpen,
  onFolderOpen,
  onContextMenu,
  onRefresh,
  isLoading,
  error,
  isMobile = false,
  isLowEndDevice = false
}: ZiExplorerContentProps) {
  const { manager } = useZiExplorer()

  // Context menu state
  const [contextMenu, setContextMenu] = useState<{
    isOpen: boolean
    position: { x: number; y: number }
    selectedItem: FileItem | null
  }>({
    isOpen: false,
    position: { x: 0, y: 0 },
    selectedItem: null
  })

  // Import dialog state
  const [importDialog, setImportDialog] = useState({
    isOpen: false,
    targetPath: currentPath
  })
  // Separate folders and files
  const folders = files.filter(item => item.type === 'folder')
  const fileItems = files.filter(item => item.type === 'file')

  // Handle context menu
  const handleContextMenu = useCallback((event: React.MouseEvent, item: FileItem) => {
    event.preventDefault()
    setContextMenu({
      isOpen: true,
      position: { x: event.clientX, y: event.clientY },
      selectedItem: item
    })
    onContextMenu(event, item)
  }, [onContextMenu])

  // Handle context menu actions
  const handleContextMenuAction = useCallback(async (action: string, item?: FileItem) => {
    if (!manager) return

    try {
      switch (action) {
        case 'open':
          if (item) {
            if (item.type === 'folder') {
              onFolderOpen(item.path)
            } else {
              onFileOpen(item.path)
            }
          }
          break

        case 'copy':
          if (selectedFiles.length > 0) {
            await manager.copyToClipboard(selectedFiles)
          }
          break

        case 'cut':
          if (selectedFiles.length > 0) {
            await manager.cutToClipboard(selectedFiles)
          }
          break

        case 'paste':
          await manager.pasteFromClipboard(currentPath)
          onRefresh()
          break

        case 'delete':
          if (selectedFiles.length > 0) {
            const confirmed = confirm(`Delete ${selectedFiles.length} item${selectedFiles.length > 1 ? 's' : ''}?`)
            if (confirmed) {
              for (const filePath of selectedFiles) {
                await manager.deleteFile(filePath)
              }
              onRefresh()
            }
          }
          break

        case 'rename':
          if (item) {
            const newName = prompt('Enter new name:', item.name)
            if (newName && newName !== item.name) {
              // Implement rename functionality
              console.log('Rename:', item.path, 'to', newName)
            }
          }
          break

        case 'newFolder':
          const folderName = prompt('Enter folder name:')
          if (folderName) {
            await manager.createFolder(currentPath, folderName)
            onRefresh()
          }
          break

        case 'import':
          setImportDialog({ isOpen: true, targetPath: currentPath })
          break

        case 'refresh':
          onRefresh()
          break

        case 'properties':
          if (item) {
            // Implement properties dialog
            console.log('Properties for:', item.path)
          }
          break

        case 'share':
          if (item && item.type === 'file') {
            // Implement file sharing
            console.log('Share file:', item.path)
          }
          break
      }
    } catch (error) {
      console.error('Context menu action failed:', error)
    } finally {
      setContextMenu(prev => ({ ...prev, isOpen: false }))
    }
  }, [manager, selectedFiles, currentPath, onRefresh, onFileOpen, onFolderOpen])

  // Handle import
  const handleImport = useCallback(async (files: FileList, targetPath: string): Promise<ImportResult[]> => {
    if (!manager) return []

    try {
      const results = await manager.importFiles(files, targetPath)
      onRefresh()
      return results
    } catch (error) {
      console.error('Import failed:', error)
      return []
    }
  }, [manager, onRefresh])

  // Handle item click
  const handleItemClick = (item: FileItem, event: React.MouseEvent) => {
    const isMultiSelect = event.ctrlKey || event.metaKey || event.shiftKey

    if (event.detail === 1) {
      // Single click - select item
      onSelectionChange(item.path, isMultiSelect)
    } else if (event.detail === 2) {
      // Double click - open item
      if (item.type === 'folder') {
        onFolderOpen(item.path)
      } else {
        onFileOpen(item.path)
      }
    }
  }

  // Handle drag and drop
  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault()
    event.dataTransfer.dropEffect = 'copy'
  }

  const handleDrop = async (event: React.DragEvent) => {
    event.preventDefault()
    const files = event.dataTransfer.files
    if (files.length > 0) {
      await handleImport(files, currentPath)
    }
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
          className="w-8 h-8 border-2 border-white/20 border-t-white/60 rounded-full"
        />
        <span className="ml-3 text-white/60">Loading files...</span>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <div className="text-4xl mb-4">⚠️</div>
          <div className="text-white/80 mb-2">Error loading files</div>
          <div className="text-white/60 text-sm">{error}</div>
        </div>
      </div>
    )
  }

  // Empty state
  if (files.length === 0) {
    return (
      <div 
        className="flex-1 flex items-center justify-center"
        onDragOver={handleDragOver}
        onDrop={handleDrop}
      >
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center"
        >
          <motion.div
            animate={{ 
              scale: [1, 1.1, 1],
              rotate: [0, 5, -5, 0]
            }}
            transition={{ 
              duration: 2, 
              repeat: Infinity, 
              repeatDelay: 3 
            }}
            className="text-6xl mb-4"
          >
            📁
          </motion.div>
          <div className="text-white/80 mb-2 text-lg">This folder is empty</div>
          <div className="text-white/60 text-sm mb-4">
            Drag and drop files here or use the import button
          </div>
          <motion.div
            animate={{ opacity: [0.5, 1, 0.5] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="text-white/40 text-xs"
          >
            Drop files here to import
          </motion.div>
        </motion.div>
      </div>
    )
  }

  // Content area with files
  return (
    <div 
      className="flex-1 overflow-hidden"
      onDragOver={handleDragOver}
      onDrop={handleDrop}
    >
      <AnimatePresence mode="wait">
        <motion.div
          key={viewMode}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ 
            duration: isLowEndDevice ? 0.2 : 0.3, 
            ease: [0.4, 0, 0.2, 1] 
          }}
          className="h-full"
        >
          {viewMode === 'grid' && (
            <FileGrid
              folders={folders}
              files={fileItems}
              selectedFiles={selectedFiles}
              onItemClick={handleItemClick}
              onContextMenu={handleContextMenu}
              isMobile={isMobile}
              isLowEndDevice={isLowEndDevice}
            />
          )}

          {viewMode === 'list' && (
            <FileList
              folders={folders}
              files={fileItems}
              selectedFiles={selectedFiles}
              onItemClick={handleItemClick}
              onContextMenu={handleContextMenu}
              isMobile={isMobile}
              isLowEndDevice={isLowEndDevice}
            />
          )}

          {viewMode === 'details' && (
            <FileDetails
              folders={folders}
              files={fileItems}
              selectedFiles={selectedFiles}
              onItemClick={handleItemClick}
              onContextMenu={handleContextMenu}
              isMobile={isMobile}
              isLowEndDevice={isLowEndDevice}
            />
          )}
        </motion.div>
      </AnimatePresence>

      {/* Context Menu */}
      <ZiExplorerContextMenu
        isOpen={contextMenu.isOpen}
        position={contextMenu.position}
        selectedItem={contextMenu.selectedItem}
        selectedFiles={selectedFiles}
        onClose={() => setContextMenu(prev => ({ ...prev, isOpen: false }))}
        onAction={handleContextMenuAction}
        isMobile={isMobile}
        isLowEndDevice={isLowEndDevice}
      />

      {/* Import Dialog */}
      <ImportDialog
        isOpen={importDialog.isOpen}
        targetPath={importDialog.targetPath}
        onClose={() => setImportDialog(prev => ({ ...prev, isOpen: false }))}
        onImport={handleImport}
        isMobile={isMobile}
        isLowEndDevice={isLowEndDevice}
      />

      {/* Drop Zone Overlay */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 0 }}
        className="absolute inset-0 bg-blue-500/20 border-2 border-dashed border-blue-400 
                   rounded-lg flex items-center justify-center pointer-events-none"
        style={{ display: 'none' }}
      >
        <div className="text-center">
          <div className="text-4xl mb-2">📁</div>
          <div className="text-white text-lg font-medium">Drop files to import</div>
        </div>
      </motion.div>
    </div>
  )
}

// Enhanced drag and drop styles
const dragDropStyles = `
.zi-explorer-content {
  position: relative;
}

.zi-explorer-content.drag-over {
  background: rgba(59, 130, 246, 0.1);
}

.zi-explorer-content.drag-over::after {
  content: '';
  position: absolute;
  inset: 8px;
  border: 2px dashed rgba(59, 130, 246, 0.5);
  border-radius: 12px;
  pointer-events: none;
  z-index: 10;
}

/* File selection styles */
.file-item.selected {
  background: linear-gradient(135deg, 
    rgba(59, 130, 246, 0.2) 0%, 
    rgba(99, 102, 241, 0.15) 100%
  ) !important;
  border-color: rgba(59, 130, 246, 0.4) !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
}

/* Hover effects */
.file-item:hover:not(.selected) {
  background: rgba(255, 255, 255, 0.1) !important;
  transform: translateY(-1px);
}

/* Focus styles for accessibility */
.file-item:focus-visible {
  outline: 2px solid rgba(59, 130, 246, 0.8);
  outline-offset: 2px;
}

/* Loading animation */
@keyframes shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

.file-item.loading {
  background: linear-gradient(90deg, 
    rgba(255,255,255,0.1) 0%, 
    rgba(255,255,255,0.2) 50%, 
    rgba(255,255,255,0.1) 100%
  );
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .file-item {
    min-height: 44px;
    touch-action: manipulation;
  }
  
  .file-item:active {
    transform: scale(0.98);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .file-item {
    transition: none !important;
  }
  
  .file-item.loading {
    animation: none !important;
  }
}
`

// Inject styles
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style')
  styleElement.textContent = dragDropStyles
  document.head.appendChild(styleElement)
}
