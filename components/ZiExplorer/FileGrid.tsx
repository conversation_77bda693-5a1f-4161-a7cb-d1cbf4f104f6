'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { FileItem } from '@/lib/ziexplorer/types'
import { useZiExplorer } from '@/hooks/useZiExplorer'

interface FileGridProps {
  folders: FileItem[]
  files: FileItem[]
  selectedFiles: string[]
  onItemClick: (item: FileItem, event: React.MouseEvent) => void
  onContextMenu: (event: React.MouseEvent, item: FileItem) => void
  isMobile?: boolean
  isLowEndDevice?: boolean
}

interface FileItemComponentProps {
  item: FileItem
  isSelected: boolean
  onClick: (event: React.MouseEvent) => void
  onContextMenu: (event: React.MouseEvent) => void
  thumbnail?: string | null
  isMobile?: boolean
  isLowEndDevice?: boolean
}

function FileItemComponent({
  item,
  isSelected,
  onClick,
  onContextMenu,
  thumbnail,
  isMobile = false,
  isLowEndDevice = false
}: FileItemComponentProps) {
  const [imageError, setImageError] = useState(false)

  const getFileIcon = (item: FileItem): string => {
    if (item.type === 'folder') return '📁'
    
    const extension = item.name.toLowerCase().substring(item.name.lastIndexOf('.'))
    
    // Image files
    if (['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'].includes(extension)) {
      return '🖼️'
    }
    
    // Document files
    if (['.pdf', '.doc', '.docx', '.txt', '.rtf'].includes(extension)) {
      return '📄'
    }
    
    // Audio files
    if (['.mp3', '.wav', '.flac', '.aac', '.ogg'].includes(extension)) {
      return '🎵'
    }
    
    // Video files
    if (['.mp4', '.avi', '.mov', '.wmv', '.flv'].includes(extension)) {
      return '🎬'
    }
    
    return '📄'
  }

  const formatFileSize = (bytes?: number): string => {
    if (!bytes) return ''
    
    const units = ['B', 'KB', 'MB', 'GB']
    let size = bytes
    let unitIndex = 0
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024
      unitIndex++
    }
    
    return `${size.toFixed(unitIndex === 0 ? 0 : 1)} ${units[unitIndex]}`
  }

  const itemVariants = {
    hidden: { opacity: 0, scale: 0.8, y: 20 },
    visible: { 
      opacity: 1, 
      scale: 1, 
      y: 0,
      transition: {
        duration: isLowEndDevice ? 0.2 : 0.3,
        ease: [0.4, 0, 0.2, 1]
      }
    },
    hover: {
      scale: 1.05,
      y: -4,
      transition: {
        duration: isLowEndDevice ? 0.1 : 0.2,
        ease: [0.4, 0, 0.2, 1]
      }
    },
    tap: {
      scale: 0.95,
      transition: {
        duration: 0.1,
        ease: [0.4, 0, 0.2, 1]
      }
    }
  }

  return (
    <motion.div
      variants={itemVariants}
      initial="hidden"
      animate="visible"
      whileHover={!isMobile ? "hover" : undefined}
      whileTap="tap"
      onClick={onClick}
      onContextMenu={onContextMenu}
      className={`file-item ${isSelected ? 'selected' : ''}`}
      style={{
        width: isMobile ? '80px' : '100px',
        height: isMobile ? '100px' : '120px',
        padding: '8px',
        borderRadius: '12px',
        border: '1px solid rgba(255,255,255,0.1)',
        background: isSelected 
          ? 'linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(99, 102, 241, 0.15) 100%)'
          : 'rgba(255,255,255,0.05)',
        backdropFilter: 'blur(10px)',
        cursor: 'pointer',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        gap: '4px',
        transition: `all ${isLowEndDevice ? '0.15s' : '0.2s'} ease`,
        userSelect: 'none',
        touchAction: 'manipulation'
      }}
    >
      {/* File Icon/Thumbnail */}
      <div
        className="file-icon"
        style={{
          width: isMobile ? '32px' : '40px',
          height: isMobile ? '32px' : '40px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          borderRadius: '8px',
          background: item.type === 'folder' 
            ? 'linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(99, 102, 241, 0.1) 100%)'
            : 'rgba(255,255,255,0.1)',
          marginBottom: '4px'
        }}
      >
        {thumbnail && !imageError ? (
          <img
            src={thumbnail}
            alt={item.name}
            onError={() => setImageError(true)}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
              borderRadius: '6px'
            }}
          />
        ) : (
          <span style={{ fontSize: isMobile ? '18px' : '24px' }}>
            {getFileIcon(item)}
          </span>
        )}
      </div>

      {/* File Name */}
      <div
        className="file-name"
        style={{
          fontSize: isMobile ? '10px' : '11px',
          fontWeight: 500,
          color: 'white',
          textAlign: 'center',
          lineHeight: 1.2,
          maxWidth: '100%',
          overflow: 'hidden',
          display: '-webkit-box',
          WebkitLineClamp: 2,
          WebkitBoxOrient: 'vertical',
          wordBreak: 'break-word'
        }}
        title={item.name}
      >
        {item.name}
      </div>

      {/* File Size (for files only) */}
      {item.type === 'file' && item.size && (
        <div
          className="file-size"
          style={{
            fontSize: '9px',
            color: 'rgba(255,255,255,0.6)',
            textAlign: 'center'
          }}
        >
          {formatFileSize(item.size)}
        </div>
      )}

      {/* Selection Indicator */}
      {isSelected && (
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          className="selection-indicator"
          style={{
            position: 'absolute',
            top: '4px',
            right: '4px',
            width: '16px',
            height: '16px',
            borderRadius: '50%',
            background: 'linear-gradient(135deg, #3b82f6 0%, #6366f1 100%)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '10px',
            color: 'white'
          }}
        >
          ✓
        </motion.div>
      )}
    </motion.div>
  )
}

export function FileGrid({
  folders,
  files,
  selectedFiles,
  onItemClick,
  onContextMenu,
  isMobile = false,
  isLowEndDevice = false
}: FileGridProps) {
  const { manager } = useZiExplorer()
  const [thumbnails, setThumbnails] = useState<Map<string, string>>(new Map())

  // Generate thumbnails for image files
  useEffect(() => {
    const generateThumbnails = async () => {
      if (!manager) return

      const imageFiles = files.filter(file => {
        const extension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'))
        return ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'].includes(extension)
      })

      for (const file of imageFiles.slice(0, 10)) { // Limit to first 10 for performance
        try {
          const thumbnail = await manager.generateThumbnail(file.path, {
            width: isMobile ? 32 : 40,
            height: isMobile ? 32 : 40,
            quality: 0.7
          })
          
          if (thumbnail) {
            setThumbnails(prev => new Map(prev).set(file.path, thumbnail))
          }
        } catch (error) {
          console.debug('Thumbnail generation failed for:', file.path)
        }
      }
    }

    generateThumbnails()
  }, [files, manager, isMobile])

  // Combine and sort items
  const allItems = [...folders, ...files]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: isLowEndDevice ? 0.02 : 0.05,
        delayChildren: 0.1
      }
    }
  }

  return (
    <div
      className="file-grid-container"
      style={{
        height: '100%',
        overflowY: 'auto',
        padding: isMobile ? '12px' : '16px'
      }}
    >
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="file-grid"
        style={{
          display: 'grid',
          gridTemplateColumns: `repeat(auto-fill, minmax(${isMobile ? '80px' : '100px'}, 1fr))`,
          gap: isMobile ? '12px' : '16px',
          justifyContent: 'start',
          alignContent: 'start'
        }}
      >
        <AnimatePresence>
          {allItems.map((item) => (
            <FileItemComponent
              key={item.path}
              item={item}
              isSelected={selectedFiles.includes(item.path)}
              onClick={(event) => onItemClick(item, event)}
              onContextMenu={(event) => onContextMenu(event, item)}
              thumbnail={thumbnails.get(item.path)}
              isMobile={isMobile}
              isLowEndDevice={isLowEndDevice}
            />
          ))}
        </AnimatePresence>
      </motion.div>
    </div>
  )
}

// Additional styles for grid
const gridStyles = `
.file-grid-container {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

.file-item {
  position: relative;
}

.file-item:hover:not(.selected) {
  background: rgba(255, 255, 255, 0.1) !important;
  border-color: rgba(255, 255, 255, 0.2) !important;
}

.file-item.selected {
  border-color: rgba(59, 130, 246, 0.4) !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .file-item {
    touch-action: manipulation;
  }
  
  .file-item:active {
    transform: scale(0.95);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .file-item {
    transition: none !important;
  }
}
`

// Inject styles
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style')
  styleElement.textContent = gridStyles
  document.head.appendChild(styleElement)
}
